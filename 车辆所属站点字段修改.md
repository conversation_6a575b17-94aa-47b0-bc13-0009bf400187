我需要将发送的大象消息中车辆所属组字段修改为车辆所属站点字段

要求：
1.创建新函数实现获取车辆所属站点的功能
2.只创建一个函数即可

信息：

```java
@Slf4j
@Component
public class VinStationQueryServiceImpl implements VinStationQueryService {

    @MdpThriftClient(
        remoteAppKey = "com.sankuai.walledelivery.basic",
        timeout = 5000,
        retryTimes = 2
    )
    private DelivererQueryThriftService delivererQueryThriftService;

    @Override
    @Validated
    public List<String> queryBusinessStationListByVin(@NotBlank String vin) {
        // 1. 参数校验
        if (StringUtils.isBlank(vin)) {
            log.warn("queryBusinessStationListByVin# VIN不能为空");
            throw new IllegalArgumentException("VIN不能为空");
        }

        // 2. 构建请求
        DeliverRpcRequest request = DeliverRpcRequest.builder()
                .account(vin.trim().toUpperCase()) // VIN统一转大写
                .delivererTypeEnum(DelivererTypeEnum.VEHICLE_POSITION_TYPE)
                .build();

        // 3. 记录开始日志
        log.info("queryBusinessStationListByVin# 开始查询, VIN: {}", vin);
        long startTime = System.currentTimeMillis();

        try {
            // 4. 调用远程服务
            ThriftResponse<BusinessStationPositionResponse> response =
                delivererQueryThriftService.queryBusinessStationAndPositionByDeliver(request);

            // 5. 响应检查
            if (response == null || response.getCode() != ErrorCode.StandardErrorCode.OK.getCode()) {
                log.error("queryBusinessStationListByVin# 远程服务返回异常, VIN: {}, response: {}",
                         vin, JacksonUtils.to(response));
                return Collections.emptyList();
            }

            // 6. 数据处理
            List<String> stationNames = extractStationNames(response.getData());

            // 7. 记录成功日志
            long cost = System.currentTimeMillis() - startTime;
            log.info("queryBusinessStationListByVin# 查询成功, VIN: {}, 站点数量: {}, 耗时: {}ms",
                    vin, stationNames.size(), cost);

            return stationNames;

        } catch (Exception e) {
            // 8. 异常处理
            long cost = System.currentTimeMillis() - startTime;
            log.error("queryBusinessStationListByVin# 查询异常, VIN: {}, 耗时: {}ms",
                     vin, cost, e);

            // 根据异常类型决定是否抛出
            if (e instanceof BizThriftException) {
                throw new RemoteServiceException("基础数据服务调用失败", e);
            }

            return Collections.emptyList();
        }
    }

    /**
     * 提取站点名称列表
     */
    private List<String> extractStationNames(BusinessStationPositionResponse data) {
        if (data == null || CollectionUtils.isEmpty(data.getBusinessStationDTO())) {
            return Collections.emptyList();
        }

        return data.getBusinessStationDTO().stream()
                .map(BusinessStationDTO::getName)
                .filter(StringUtils::isNotBlank)
                .distinct() // 去重
                .collect(Collectors.toList());
    }
}
```


