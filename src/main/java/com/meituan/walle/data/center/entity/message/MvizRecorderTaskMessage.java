package com.meituan.walle.data.center.entity.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/03/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MvizRecorderTaskMessage implements Serializable {

    @JsonProperty(value = "header")
    private Header header;

    @JsonProperty(value = "body")
    private Subject subject;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Header implements Serializable {
        @JsonProperty(value = "timestamp")
        private String timestamp;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Subject  implements Serializable {
        @JsonProperty(value = "type")
        private Integer type;

        @JsonProperty(value = "task_id")
        private String taskId;

        @JsonProperty(value = "source")
        private String source;

        @JsonProperty(value = "record_name")
        private String recordName;

        @JsonProperty(value = "start_time")
        private String startTime;

        @JsonProperty(value = "end_time")
        private String endTime;
    }
}

