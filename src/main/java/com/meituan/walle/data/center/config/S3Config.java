package com.meituan.walle.data.center.config;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.exceptions.GetS3CredentialFailedAfterRetryException;
import com.meituan.walle.data.center.util.S3Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class S3Config {

    @Bean(name = "s3ClientBeijingTestInner")
    public AmazonS3 s3ClientBeijingTestInner(@Value("${appkey}") String localAppkey,
                                         @Value("${s3plus.inner.hostname.beijingTest}") String hostname)
            throws GetS3CredentialFailedAfterRetryException {
        return S3Util.amazonS3Plus(localAppkey, hostname);
    }

    @Bean(name = "s3ClientBeijingInner")
    public AmazonS3 s3ClientBeijingInner(@Value("${appkey}") String localAppkey,
                                         @Value("${s3plus.inner.hostname.beijing}") String hostname)
            throws GetS3CredentialFailedAfterRetryException {
        return S3Util.amazonS3Plus(localAppkey, hostname);
    }

    @Bean(name = "s3ClientBeijingBackupInner")
    public AmazonS3 s3ClientBeijingBackupInner(@Value("${appkey}") String localAppkey,
                                               @Value("${s3plus.inner.hostname.beijingBackup}") String hostname)
            throws GetS3CredentialFailedAfterRetryException {
        return S3Util.amazonS3Plus(localAppkey, hostname);
    }

    @Bean(name = "s3ClientZhongweiMADInner")
    public AmazonS3 s3ClientZhongweiMADInner(@Value("${appkey}") String localAppkey,
                                               @Value("${s3plus.inner.hostname.zhongweiMAD}") String hostname)
            throws GetS3CredentialFailedAfterRetryException {
        return S3Util.amazonS3Plus(localAppkey, hostname);
    }

}
