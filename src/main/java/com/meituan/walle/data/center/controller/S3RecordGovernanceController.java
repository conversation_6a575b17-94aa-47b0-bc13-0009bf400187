package com.meituan.walle.data.center.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.enums.S3ClusterEnum;
import com.meituan.walle.data.center.service.S3RecordGovernanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@InterfaceDoc(
        displayName = "S3 Record生命周期管理接口",
        type = "restful",
        description = "S3 Record生命周期管理接口",
        scenarios = "S3 Record生命周期管理接口"
)
@Slf4j
@RestController
@RequestMapping("/s3RecordGovernance")
public class S3RecordGovernanceController {

    @Autowired
    private S3RecordGovernanceService s3RecordGovernanceService;

    @MethodDoc(
            requestMethods = {HttpMethod.POST},
            displayName = "删除S3原始record数据",
            description = "清理范围：半年前的2级record",
            returnValueDescription = "调用成功与否的Response对象，包含code、msg、data信息"
    )
    @PostMapping("/delete")
    public Response deleteRecordToS3Trash() {
        try {
            s3RecordGovernanceService.deleteRecordToS3Trash(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("10020", e.getMessage());
        }
        return Response.succ();
    }

    @MethodDoc(
            requestMethods = {HttpMethod.POST},
            displayName = "从S3 trash中恢复record",
            description = "从S3 trash中恢复record",
            parameters = {
                    @ParamDoc(name = "recordName", description = "record名称",
                            requiredness = Requiredness.REQUIRED)
            },
            restExamplePostData = "{\"recordName\": \"20220121_161519_s20-226\"}",
            returnValueDescription = "调用成功与否的Response对象，包含code、msg、data信息"
    )
    @PostMapping("/restore")
    public Response restoreRecordFromS3Trash(@RequestParam(value = "recordName") String recordName,
                                             @RequestParam(value = "s3ClusterName") String s3ClusterName) {
        log.info("[S3RecordGovernanceController#restoreRecordFromS3Trash] recordName:{}, s3ClusterName:{} ",
                recordName, s3ClusterName);
        if (recordName == null || "".equals(recordName)) {
            return Response.fail("10004", "parameter recordName cannot be empty!");
        }
        S3ClusterEnum s3ClusterEnum = S3ClusterEnum.byName(s3ClusterName);
        if (s3ClusterEnum == null) {
            return Response.fail("10005", "parameter s3ClusterName is invalid!");
        }
        String resultString;
        try {
            resultString = s3RecordGovernanceService.restoreRecordFromS3Trash(recordName, s3ClusterEnum);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("10020", e.getMessage());
        }
        return Response.succ(resultString);
    }

}