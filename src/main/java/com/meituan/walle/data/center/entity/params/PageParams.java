package com.meituan.walle.data.center.entity.params;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/03/22
 */
@Getter
@Setter
@AllArgsConstructor
public class PageParams implements Serializable {

    @Min(value = 0, message = "pageNum cannot be less than 0")
    private Integer pageNum;

    @Min(value = 0, message = "pageSize cannot be less than 0")
    @Max(value = 100, message = "pageSize cannot be more than 100")
    private Integer pageSize;

    private Integer offset;

    public PageParams() {
        this.pageNum = 1;
        this.pageSize = 10;
    }

    public Integer getPageSize() {
        this.pageSize = this.pageSize == null ? 10 : this.pageSize;
        return this.pageSize;
    }

    public Integer getPageNum() {
        this.pageNum = (this.pageNum == null  || this.pageNum <= 1 ) ? 1 : this.pageNum;
        return pageNum;
    }

    public Integer getOffset() {
        return (this.getPageNum() - 1) * this.getPageSize();
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
