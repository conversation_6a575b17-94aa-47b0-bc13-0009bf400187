package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "biz_object_online_history")
@Data
public class BizObjectOnlineHistory {

    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 首次上报时间
     */
    private Date firstReportTime;

    /**
     * 失联时间
     */
    private Date missingTime;

    /**
     * 对象类型, 0:未知对象; 1:ipc; 2:proxy
     */
    private Integer objectType;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private Integer isDeleted;

    private Date createTime;

    private Date updateTime;
}
