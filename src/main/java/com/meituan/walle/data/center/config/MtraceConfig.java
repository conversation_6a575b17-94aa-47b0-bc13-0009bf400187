package com.meituan.walle.data.center.config;

import com.meituan.mtrace.http.TraceFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <NAME_EMAIL> on 2019/4/18 16:08
 */
@Configuration
public class MtraceConfig {
    @Bean
    public FilterRegistrationBean mTraceFilter() {
        TraceFilter filter = new TraceFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/*");
        registration.setName("mtrace-filter");
        registration.setOrder(1);
        return registration;
    }
}
