package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> @Beijing
 * @date 2022/03/21
 * Description:
 * Modified by
 */
@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RecordVersionFilePO implements Serializable {
    private Long id;
    private String versionId;
    private String module;
    private String s3Url;
    private String fileName;
    private String filePath;
    private Integer fileType;
    private Long fileSize;
    private Integer isDeleted;
    private Date createTime;
    private Date updateTime;
}
