package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/07/12
 */
@Getter
@AllArgsConstructor
public enum RemoteVideoCodeEnum {

    SUCCESS(10000, "ok"),
    PARAMS_PROBLEM(10001, "请求参数问题"),
    PARAMS_EMPTY(10002, "请求参数为空"),
    PARAMS_ILLEGAL(10003, "请求参数非法"),
    NO_REMOTE_VIDEO(10007, "该时间段没有云控视频"),
    TOKEN_ILLEGAL(10010, "Token校验错误"),
    SIGNTURE_FAIL(10011, "签名校验失败"),
    CLIENT_NOT_FOUND(10012, "客户不存在"),
    SERVER_ERROR(10020, "服务端执行出错"),
    REQUEST_TIMEOUT(10021, "请求超时"),
    DENIAL_OF_SERVICE(10022, "服务端拒绝服务"),
    RESPONSE_PROBLEM(10023, "返回结果问题"),
    RESPONSE_EMPTY(10024, "返回结果为空"),
    RESPONSE_ILLEGAL(10025, "返回结果非法"),
    CONNECT_EMPTY(10027, "该时间段内坐席无连接记录"),
    NETWORK_ANOMALY(20000, "网络异常"),
    UNKNOWN(99999, "未知原因，请联系数据平台同学解决"),
    ;

    private int code;
    private String msg;

    public static String getMsgByCode(int code) {
        for (RemoteVideoCodeEnum remoteVideoCodeEnum : RemoteVideoCodeEnum.values()) {
            if (remoteVideoCodeEnum.getCode() == code) {
                return remoteVideoCodeEnum.getMsg();
            }
        }
        return UNKNOWN.getMsg();
    }
}
