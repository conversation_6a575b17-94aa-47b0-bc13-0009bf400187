package com.meituan.walle.data.center.entity.pojo;

import java.util.Date;
import javax.persistence.*;

@Table(name = "vehicle_running_info")
public class VehicleRunningInfo {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 0.COMPLETE_MANUAL|1.COMPLETE_AUTO_DRIVE|2.AUTO_STEER_ONLY|3.AUTO_SPEED_ONLY|4.EMERGENCY_MODE
     */
    @Column(name = "drive_mode")
    private Byte driveMode;

    /**
     * 速度（m/s）
     */
    private Double speed;

    /**
     * 精度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取车架号（车辆设备id）
     *
     * @return vin - 车架号（车辆设备id）
     */
    public String getVin() {
        return vin;
    }

    /**
     * 设置车架号（车辆设备id）
     *
     * @param vin 车架号（车辆设备id）
     */
    public void setVin(String vin) {
        this.vin = vin;
    }

    /**
     * 获取表record的record_name外键
     *
     * @return record_name - 表record的record_name外键
     */
    public String getRecordName() {
        return recordName;
    }

    /**
     * 设置表record的record_name外键
     *
     * @param recordName 表record的record_name外键
     */
    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    /**
     * 获取0.COMPLETE_MANUAL|1.COMPLETE_AUTO_DRIVE|2.AUTO_STEER_ONLY|3.AUTO_SPEED_ONLY|4.EMERGENCY_MODE
     *
     * @return drive_mode - 0.COMPLETE_MANUAL|1.COMPLETE_AUTO_DRIVE|2.AUTO_STEER_ONLY|3.AUTO_SPEED_ONLY|4.EMERGENCY_MODE
     */
    public Byte getDriveMode() {
        return driveMode;
    }

    /**
     * 设置0.COMPLETE_MANUAL|1.COMPLETE_AUTO_DRIVE|2.AUTO_STEER_ONLY|3.AUTO_SPEED_ONLY|4.EMERGENCY_MODE
     *
     * @param driveMode 0.COMPLETE_MANUAL|1.COMPLETE_AUTO_DRIVE|2.AUTO_STEER_ONLY|3.AUTO_SPEED_ONLY|4.EMERGENCY_MODE
     */
    public void setDriveMode(Byte driveMode) {
        this.driveMode = driveMode;
    }

    /**
     * 获取速度（m/s）
     *
     * @return speed - 速度（m/s）
     */
    public Double getSpeed() {
        return speed;
    }

    /**
     * 设置速度（m/s）
     *
     * @param speed 速度（m/s）
     */
    public void setSpeed(Double speed) {
        this.speed = speed;
    }

    /**
     * 获取精度
     *
     * @return longitude - 精度
     */
    public String getLongitude() {
        return longitude;
    }

    /**
     * 设置精度
     *
     * @param longitude 精度
     */
    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    /**
     * 获取纬度
     *
     * @return latitude - 纬度
     */
    public String getLatitude() {
        return latitude;
    }

    /**
     * 设置纬度
     *
     * @param latitude 纬度
     */
    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}