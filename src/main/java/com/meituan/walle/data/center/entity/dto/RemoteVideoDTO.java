package com.meituan.walle.data.center.entity.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class RemoteVideoDTO {

    /**
     * 车架号
     */
    private String vin;

    /**
     * 指定获取的视频
     */
    private List<String> stream;

    /**
     * 指定获取的视频视角
     */
    private List<String> position;

    /**
     * 开始时间戳，精确到秒
     */
    @JSONField(name = "start_ts")
    private Long startTs;

    /**
     * 结束时间戳，精确到秒
     */
    @JSONField(name = "end_ts")
    private Long endTs;

    /**
     * 指定bucket中的路径
     */
    @JSONField(name = "custom_path")
    private String customPath;

    /**
     * 指定回应方式，默认为API
     */
    @JSONField(name = "callback_type")
    private String callbackType;

    /**
     * 使用大象回应方式时，需要指定users信息，可以多个
     */
    private List<String> users;

    /**
     * 指定上传的S3 bucket
     */
    private String bucket;

    /**
     * 任务创建人
     */
    private String creator;

    /**
     * 回调URL
     */
    @JSONField(name = "callback_url")
    private String callbackUrl;
}
