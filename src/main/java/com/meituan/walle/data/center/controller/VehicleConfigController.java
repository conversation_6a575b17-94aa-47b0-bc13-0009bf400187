package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.constant.CharConstant;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.pojo.BizVehicleDataRelatedConfig;
import com.meituan.walle.data.center.entity.request.BizVehicleDataRelatedConfigRequest;
import com.meituan.walle.data.center.service.VehicleDataRelatedService;
import com.meituan.walle.data.center.util.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/08/11
 */

@Slf4j
@RestController
@RequestMapping("/vehicle_config")
public class VehicleConfigController {

    @Resource
    private VehicleDataRelatedService vehicleDataRelatedService;

    private static final String API_PREFIX = "/vehicle_config";

    @PostMapping("/update/data_related_config")
    public Object updateDateRelatedConfig(@RequestBody BizVehicleDataRelatedConfigRequest request) {
        String api = "/update/data_related_config";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            if (StringUtils.isBlank(request.getVin())) {
                log.info("traceId: {}, update vehicle data related config failed, " +
                         "vin is null, request is [{}]", request);
                t.setDurationInMillis(System.currentTimeMillis() - start);
                t.setSuccessStatus();
                t.complete();
                return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                        "lack vin param");
            }
            log.info("traceId: {}, start to update vehicle data related config, request is [{}]", traceId, request);
            vehicleDataRelatedService.updateConfig(request);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            log.error("traceId is [{}], Update data related config failed, request is [{}]", traceId, request, e);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "service error");
    }

    @MethodDoc(
            displayName = "向约车系统提供车辆配置信息",
            description = "向约车系统提供车辆配置信息")
    @GetMapping(value = "/query_all_vehicles_config")
    public Response queryAllVehiclesConfig() {
        String api = "/query_all_vehicles_config";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            List<BizVehicleDataRelatedConfig> result = vehicleDataRelatedService.queryAllVehiclesConfig();
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            log.error("traceId is [{}], query all vehicles config failed", traceId, e);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "service error");
    }

    @MethodDoc(
            displayName = "向约车系统提供单盘车对应的约车目的",
            description = "向约车系统提供单盘车对应的约车目的")
    @GetMapping(value = "/query_single_disk_purpose")
    public Response querySingleDiskPurpose() {
        String api = "/query_single_disk_purpose";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            String singleDiskPurpose = SysParamsConfig.single_disk_purpose;
            String[] result = singleDiskPurpose.split(CharConstant.CHAR_DD);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            log.error("traceId is [{}], query single disk purpose failed", traceId, e);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "service error");
    }

}
