package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/18 下午4:04
 * Description:
 * Modified by
 */
@Getter
@AllArgsConstructor
public enum OnesRespCodeEnum {
    /**
     * 返回成功
     */
    OK(200),
    /**
     * 创建成功
     */
    CREATE_TASK_SUCCESS(201);

    private int code;

    public static boolean isCreatedSuccess(Integer code) {
        return code != null && code >= 200 && code < 300;
    }
}