package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/11/21
 */
@Data
@Table(name = "biz_fast_osviz_task")
public class BizFastOsvizTask {

    @Id
    @GeneratedValue(generator = "JDBC")
    /**
     * 主键id
     */
    private Long id;

    /**
     * recordname
     */
    private String recordName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 快速上传的任务id
     */
    private Long fastId;

    /**
     * jobid
     */
    private String jobId;

    /**
     * taskid
     */
    private String taskId;

    /**
     * compilestart：编译开始，compilefailed：编译结束，unsupportedjob：不支持的job，start：job开始执行任务，finished：job结束
     */
    private String jobStatus;

    /**
     * start：开始，finished：结束，incompleted：task信息不完整
     */
    private String taskStatus;

    /**
     * 录屏的视频地址
     */
    private String s3Url;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private int isDeleted;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * update_time
     */
    private Date updateTime;

    /**
     * 使用的webviz的版本号
     */
    private String wvizVersion;

}