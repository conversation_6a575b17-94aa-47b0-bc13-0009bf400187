package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/01/19
 */
@Table(name = "biz_onboard_file_filter_rule")
@Data
public class BizOnboardFileFilterRule {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 规则类型
     */
    private String type;

    /**
     * 规则内容
     */
    private String rule;

    /**
     * 规则状态（1启用 0不启用）
     */
    private Integer status;

    /**
     * 创建人
     */
    @Column(name = "creator")
    private String creator;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}