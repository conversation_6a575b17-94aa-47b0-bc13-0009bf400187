package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * [快速回传2.0]动作类型枚举
 *
 * <AUTHOR>
 * @version 2.0
 */
@AllArgsConstructor
@Getter
public enum VehicleUploadActionTypeEnum {
    /**
     * 以下所有动作均改变状态
     */
    // INIT -> CREATED
    CREATE_VEHICLE_UPLOAD_TASK_ACTION("任务已创建，排队中", true),
    // CREATED -> CANCELED
    CANCEL_TASK_ACTION("任务已取消", true),
    // CREATED -> UPLOADING
    PULL_TASK_ACTION("任务已下发", true),
    // PAUSE -> UPLOADING
    // 车端上传文件和上报文件结果都会执行上传行为
    UPLOADING_TASK_ACTION("任务上传中", true),
    // CREATED -> FINISHED
    DATA_ALREADY_EXISTS_ACTION("任务数据已存在", true),
    // UPLOADING -> PAUSE
    // 暂停状态不应该影响任务的下发，只包含关机和被打断两种情况。此时，车端再次拉取任务，任务状态变为上传中。
    // 暂停状态与拔盘无关，拔盘后进入失败逻辑。
    PAUSE_TASK_ACTION("任务已暂停", true),
    // UPLOADING/PAUSE -> FINISHED
    ACHIEVE_ACTION("任务已结束", true),
    // UPLOADING/CREATED -> CANCELED
    EXPIRE_TASK_ACTION("任务已过期", true),
    // FINISHED -> FINISHED_FAILED
    EXCEPTION_ACTION("上传程序出现异常", true),
    // FINISHED -> FINISHED_FAILED
    FILE_NOT_FOUND_ACTION("车端文件不存在，请检查自动驾驶是否启动或联系近场人员重新插盘", true),
    // FINISHED -> FINISHED_DATA_MISS
    FILE_INCOMPLETE_ACTION( "文件不完整", true),
    // FINISHED -> FINISHED_DATA_MISS
    MODULE_INCOMPLETE_ACTION( "模块不完整", true),
    // FINISHED -> FINISHED_SUCCEED
    DATA_VALIDATED_ACTION( "数据校验通过", true),
    // UPLOADING -> PAUSE
    INTERRUPTED_BY_HIGH_PRIORITY_ACTION( "任务被高优打断", true),
    // UPLOADING -> PAUSE
    VEHICLE_SHUTDOWN_ACTION( "车辆关机", true),
    // 以下动作不改变状态，但是更新描述
    // XXX(code,message,false)
    ;
    private final String message;
    private final boolean shouldChangeState;

}
