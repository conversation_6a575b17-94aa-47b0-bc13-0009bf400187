package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "lane_mi_info")
public class LaneMiInfo {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 车架号
     */
    @Column(name = "vin")
    private String vin;

    /**
     * guid
     */
    @Column(name = "guid")
    private String guid;

    /**
     * lane_id
     */
    @Column(name = "eid")
    private String eid;

    /**
     * 地图版本
     */
    @Column(name = "version")
    private String version;

    /**
     * 转向标志
     */
    @Column(name = "turn_type")
    private String turnType;

    /**
     * 接管次数
     */
    @Column(name = "intervention")
    private int intervention;

    /**
     * 自动驾驶里程
     */
    @Column(name = "meter")
    private double meter;

    /**
     * 驾驶时间
     */
    @Column(name = "trip_time")
    private double tripTime;

    /**
     * 速度
     */
    @Column(name = "velocity")
    private double velocity;

    /**
     * 坐标数量
     */
    @Column(name = "pose_num")
    private int poseNum;

    /**
     * lane长度
     */
    @Column(name = "lane_length")
    private double laneLength;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * record_name
     */
    @Column(name = "record_name")
    private String recordName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getEid() {
        return eid;
    }

    public void setEid(String eid) {
        this.eid = eid;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getTurnType() {
        return turnType;
    }

    public void setTurnType(String turnType) {
        this.turnType = turnType;
    }

    public int getIntervention() {
        return intervention;
    }

    public void setIntervention(int intervention) {
        this.intervention = intervention;
    }

    public double getMeter() {
        return meter;
    }

    public void setMeter(double meter) {
        this.meter = meter;
    }

    public double getTripTime() {
        return tripTime;
    }

    public void setTripTime(double tripTime) {
        this.tripTime = tripTime;
    }

    public double getVelocity() {
        return velocity;
    }

    public void setVelocity(double velocity) {
        this.velocity = velocity;
    }

    public int getPoseNum() {
        return poseNum;
    }

    public void setPoseNum(int poseNum) {
        this.poseNum = poseNum;
    }

    public double getLaneLength() {
        return laneLength;
    }

    public void setLaneLength(double laneLength) {
        this.laneLength = laneLength;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRecordName() {
        return recordName;
    }

    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }
}
