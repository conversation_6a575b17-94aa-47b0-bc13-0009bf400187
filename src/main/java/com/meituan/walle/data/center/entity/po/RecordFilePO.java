package com.meituan.walle.data.center.entity.po;

import lombok.*;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/4 上午11:41
 * Description:
 * Modified by
 */
@<PERSON>uilder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RecordFilePO {

    private Long id;
    private String recordName;
    private Integer recordDate;
    private String fileName;
    private String filePath;
    private String md5;
    private String s3Url;
    private String s3DsnUrl;
    private Integer fileType;
    private Long fileSize;
    private Long fileSizeS3;
    private String hdfsPath;
    private Long fileSizeHdfs;
    private Integer datekey;
    private Timestamp createTime;
    private Timestamp updateTime;
    private Date lastModified;
    private Date uploadTime;
    private Integer cluster;
    private String compressionFormat;

}
