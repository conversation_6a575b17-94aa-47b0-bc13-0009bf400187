package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/14
 */
@Getter
@AllArgsConstructor
public enum AccidentVehicleStatusEnum {

    UNKNOWN(-1, "未知"),
    STRAIGHT(1, "直行"),
    PARKING(2, "停车"),
    STATIC(3, "静止"),
    BACK_UP(4, "倒车"),
    TURN_RIGHT(5, "右转弯"),
    TURN_LEFT(6, "左转弯"),
    STRAIGHT_BRAKE(7, "直行刹车"),
    UTURN(8, "掉头"),
    SLIDE(9, "溜车"),
    STARTING(10, "起步"),
    LANE_CHANGE(11, "变更车道"),
    AVOID_OBSTACLES(12, "躲避障碍"),
    OVERTAKE(13, "超车"),
    ;

    private int code;
    private String msg;

    public static List<Object> getCodeMsgMap() {
        List<Object> result = new ArrayList<>();
        for (AccidentVehicleStatusEnum accidentVehicleStatusEnum : AccidentVehicleStatusEnum.values()) {
            List<Object> tmpResult = new ArrayList<>();
            tmpResult.add(accidentVehicleStatusEnum.getCode());
            tmpResult.add(accidentVehicleStatusEnum.getMsg());
            result.add(tmpResult);
        }
        return result;
    }
}
