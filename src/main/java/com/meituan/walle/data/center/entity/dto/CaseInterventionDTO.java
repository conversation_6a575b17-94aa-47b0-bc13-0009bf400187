package com.meituan.walle.data.center.entity.dto;

import com.meituan.walle.data.center.entity.po.CasesPO;
import com.meituan.walle.data.center.entity.pojo.VehicleInterventionOffline;

/**
 * Created by leihui on 2021/4/26.
 */
public class CaseInterventionDTO {

    private CasesPO casesPO;
    private VehicleInterventionOffline intervention;

    public CaseInterventionDTO(CasesPO casesPO, VehicleInterventionOffline intervention) {
        this.casesPO = casesPO;
        this.intervention = intervention;
    }

    public CasesPO getCasesPO() {
        return casesPO;
    }

    public VehicleInterventionOffline getIntervention() {
        return intervention;
    }

    public void setCasesPO(CasesPO casesPO) {
        this.casesPO = casesPO;
    }

    public void setIntervention(VehicleInterventionOffline intervention) {
        this.intervention = intervention;
    }
}
