package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Table(name = "disk_snapshot")
public class DiskSnapshot {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * s3 bucket
     */
    private String bucket;

    /**
     * s3 key
     */
    @Column(name = "`key`")
    private String key;

    /**
     * 0.初始化|1.已解析|10.进行中
     */
    private Byte status;

    private String batchId;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取s3 bucket
     *
     * @return bucket - s3 bucket
     */
    public String getBucket() {
        return bucket;
    }

    /**
     * 设置s3 bucket
     *
     * @param bucket s3 bucket
     */
    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    /**
     * 获取s3 key
     *
     * @return key - s3 key
     */
    public String getKey() {
        return key;
    }

    /**
     * 设置s3 key
     *
     * @param key s3 key
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * 获取0.初始化|1.已解析
     *
     * @return status - 0.初始化|1.已解析
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置0.初始化|1.已解析
     *
     * @param status 0.初始化|1.已解析
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }
}