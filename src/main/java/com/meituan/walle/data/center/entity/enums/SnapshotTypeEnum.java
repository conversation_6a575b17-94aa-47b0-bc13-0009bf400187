package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Beijing
 * @date 2024/04/16
 */
@Getter
@AllArgsConstructor
public enum SnapshotTypeEnum {

    DISK(0, "disk"),

    RECORD(1, "record");

    private int code;
    private String name;

    public static SnapshotTypeEnum byCode(int code) {
        for (SnapshotTypeEnum en : SnapshotTypeEnum.values()) {
            if (en.code == code) {
                return en;
            }
        }
        return null;
    }

    public static SnapshotTypeEnum byName(String name) {
        for (SnapshotTypeEnum en : SnapshotTypeEnum.values()) {
            if (en.name.equals(name)) {
                return en;
            }
        }
        return null;
    }

}
