package com.meituan.walle.data.center.entity.dto;

import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DiskRecordMetricHistoryDTO {
    private int metricFlag;
    private int diskCntBy600Status;
    private int diskCntBy800Status;
    private int diskCntBy850Status;
    private int diskSnapshotCnt;
    private int recordNameCnt;
    private int effectiveRecordNameCnt;
    private int extractedRecordCnt;
    private int completedRecordCnt;
    private int unexcutedSparkJobCnt;
    private int failedSparkJobCnt;
    private int upload2S3FailedFileCnt;
    private double unextractedRecFileTotalSize;
}
