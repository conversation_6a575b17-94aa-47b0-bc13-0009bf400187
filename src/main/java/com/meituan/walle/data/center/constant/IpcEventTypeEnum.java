package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/09/23
 */
@Getter
@AllArgsConstructor
public enum IpcEventTypeEnum {

    UNKNOWN(-1, "unknown"),
    /**
     * 用于标识AUTOCAR事件，code值小于LAST_AUTOCAR枚举值则认为是AUTOCAR相关事件
     * code值大于AUTOCAR且不等于COREDUMP认为是IPC事件
     * code值等于COREDUMP为coredump事件
     */
    ONBOARD_START(100, "onboard_start"),
    PRECHECK_FAILED(101, "precheck_failed"),
    ONBOARD_FAILED(102, "onboard_failed"),
    MAP_START(103, "map_download_start"),
    MAP_END(104, "map_download_end"),
    DISK_EVENT(106, "disk_event"),
    AUTOCAR_VERSION(107, "autocar_version"),
    COREDUMP(110, "coredump"),
    AUTOCAR_RELEASE(111, "autocar_release");

    private int code;
    private String message;

    public static IpcEventTypeEnum getEnumByCode(int code) {
        for (IpcEventTypeEnum ipcEventTypeEnum : IpcEventTypeEnum.values()) {
            if (code == ipcEventTypeEnum.getCode()) {
                return ipcEventTypeEnum;
            }
        }
        return null;
    }

    public static Integer getCodeByMessage(String message) {
        for (IpcEventTypeEnum ipcEventTypeEnum : IpcEventTypeEnum.values()) {
            if (message.equals(ipcEventTypeEnum.getMessage())) {
                return ipcEventTypeEnum.getCode();
            }
        }
        return null;
    }
}
