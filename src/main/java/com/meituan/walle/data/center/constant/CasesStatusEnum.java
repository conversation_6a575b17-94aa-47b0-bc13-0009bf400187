package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/20 下午2:32
 * Description:
 * Modified by
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CasesStatusEnum {

    /**
     * 初始化
     */
    INIT(0, "初始化"),
    /**
     * 待创建工单
     */
    PENDING(1, "待创建工单"),
    /**
     * 已创建工单
     */
    CREATED(2, "已创建工单"),
    /**
     * 创建工单失败
     */
    FAIL(9, "创建工单失败");

    private int code;
    private String msg;

}
