package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/06/08
 */

@Getter
@AllArgsConstructor
public enum AccidentTypeEnum {

    UNKNOWN(-1, "未知"),
    SINGLE_PART(1, "单方责任事故"),
    THIRD_PART(2, "三方责任事故"),
    ;

    private int code;
    private String msg;

    public static Map<Integer, String> getCodeMsgMap() {
        Map<Integer, String> result = new HashMap<>();
        for (AccidentTypeEnum accidentTypeEnum : AccidentTypeEnum.values()) {
            result.put(accidentTypeEnum.getCode(), accidentTypeEnum.getMsg());
        }
        return result;
    }
}
