package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/12/01
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RecordFragmentedEnum {

    /**
     * 完整的record
     */
    WHOLE(0, "完整的record"),

    /**
     * 碎片化的record
     */
    FRAGMENTED(1, "碎片化的record");

    private Integer code;
    private String msg;
}