package com.meituan.walle.data.center.entity.pojo;

import java.util.Date;
import javax.persistence.*;

@Table(name = "lane_segment_v2")
public class LaneSegmentV2 {
    /**
     * 自增主键
     */
    @Column(name = "primary_id")
    private Long primaryId;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 表passage中id的外键
     */
    @Column(name = "passage_id")
    private String passageId;

    /**
     * road segment id
     */
    @Column(name = "road_segment_id")
    private String roadSegmentId;

    /**
     * 对应高精地图中的lane_segment的id
     */
    @GeneratedValue(generator = "JDBC")
    private String id;

    /**
     * 起始s
     */
    @Column(name = "start_s")
    private Double startS;

    /**
     * 终止s
     */
    @Column(name = "end_s")
    private Double endS;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "order_id")
    private Integer orderId;

    /**
     * 时间（纳秒）
     */
    @Column(name = "header_timestamp")
    private Long headerTimestamp;

    /**
     * 获取自增主键
     *
     * @return primary_id - 自增主键
     */
    public Long getPrimaryId() {
        return primaryId;
    }

    /**
     * 设置自增主键
     *
     * @param primaryId 自增主键
     */
    public void setPrimaryId(Long primaryId) {
        this.primaryId = primaryId;
    }

    /**
     * 获取表record的record_name外键
     *
     * @return record_name - 表record的record_name外键
     */
    public String getRecordName() {
        return recordName;
    }

    /**
     * 设置表record的record_name外键
     *
     * @param recordName 表record的record_name外键
     */
    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    /**
     * 获取表passage中id的外键
     *
     * @return passage_id - 表passage中id的外键
     */
    public String getPassageId() {
        return passageId;
    }

    /**
     * 设置表passage中id的外键
     *
     * @param passageId 表passage中id的外键
     */
    public void setPassageId(String passageId) {
        this.passageId = passageId;
    }

    /**
     * 获取road segment id
     *
     * @return road_segment_id - road segment id
     */
    public String getRoadSegmentId() {
        return roadSegmentId;
    }

    /**
     * 设置road segment id
     *
     * @param roadSegmentId road segment id
     */
    public void setRoadSegmentId(String roadSegmentId) {
        this.roadSegmentId = roadSegmentId;
    }

    /**
     * 获取对应高精地图中的lane_segment的id
     *
     * @return id - 对应高精地图中的lane_segment的id
     */
    public String getId() {
        return id;
    }

    /**
     * 设置对应高精地图中的lane_segment的id
     *
     * @param id 对应高精地图中的lane_segment的id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取起始s
     *
     * @return start_s - 起始s
     */
    public Double getStartS() {
        return startS;
    }

    /**
     * 设置起始s
     *
     * @param startS 起始s
     */
    public void setStartS(Double startS) {
        this.startS = startS;
    }

    /**
     * 获取终止s
     *
     * @return end_s - 终止s
     */
    public Double getEndS() {
        return endS;
    }

    /**
     * 设置终止s
     *
     * @param endS 终止s
     */
    public void setEndS(Double endS) {
        this.endS = endS;
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * @return order_id
     */
    public Integer getOrderId() {
        return orderId;
    }

    /**
     * @param orderId
     */
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    /**
     * 获取时间（纳秒）
     *
     * @return header_timestamp - 时间（纳秒）
     */
    public Long getHeaderTimestamp() {
        return headerTimestamp;
    }

    /**
     * 设置时间（纳秒）
     *
     * @param headerTimestamp 时间（纳秒）
     */
    public void setHeaderTimestamp(Long headerTimestamp) {
        this.headerTimestamp = headerTimestamp;
    }
}