package com.meituan.walle.data.center.dal.recordmanager.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/05/30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "record_file")
public class BRecordFile {
    private Long id;
    private String recordName;
    /**
     * 采集日期
     */
    private Integer recordDate;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 文件内容md5
     */
    private String md5;
    /**
     * s3Url
     */
    private String s3Url;
    /**
     * 文件类型
     */
    private Integer fileType;
    /**
     * 文件大小(B)
     */
    private Long fileSize;
    /**
     * s3上的文件大小(B)
     */
    private Long fileSizeS3;
    /**
     * 上传日期
     */
    private Integer datekey;
    /**
     * 快照中的最后修改时间
     */
    private Date lastModified;
    /**
     * 上传到S3的时间
     */
    private Date uploadTime;
    /**
     * hdfs 路径
     */
    private String hdfsPath;
    /**
     * hdfs 文件大小
     */
    private Long fileSizeHdfs;
    /**
     * S3中是否物理删除
     */
    private Integer s3IsDeleted;
    /**
     * S3中删除时间
     */
    private Date s3DeleteTime;
    /**
     * S3中是否标记删除，0表示否，1表示是
     */
    private Integer s3IsMarkedForDeletion;
    /**
     * S3中删除时间
     */
    private Date s3IsMarkedForDeletionTime;
    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private Integer isDeleted;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 所属集群[0:北京备份|1:中卫自动车]
     */
    private Integer cluster;
    /**
     * 压缩格式
     */
    @Column(name = "compression_format")
    private String compressionFormat;

}