package com.meituan.walle.data.center.controller;

import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.pojo.BatteryCupboardFile;
import com.meituan.walle.data.center.entity.request.BatteryCupboardFileRequest;
import com.meituan.walle.data.center.service.BatteryCupboardService;
import com.meituan.walle.data.center.util.Trace;
import com.mysql.jdbc.exceptions.jdbc4.MySQLIntegrityConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/01/13
 */
@InterfaceDoc(
        displayName = "电池柜相关接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问电池柜数据相关接口，主要用于电池柜数据回收。",
        scenarios = "电池柜数据相关所有接口"
)
@RestController
@RequestMapping(value = "/battery_cupboard")
@Slf4j
public class BatteryCupboardController {

    @Resource
    private BatteryCupboardService batteryCupboardService;

    @RequestMapping(value = "/upload", method = RequestMethod.POST, consumes = "multipart/form-data")
    public Response upload(BatteryCupboardFileRequest request,
                           @RequestParam("file") MultipartFile file) {
        String traceId = Trace.generateId();
        if (request == null || request.getFileName() == null) {
            log.warn("Bad battery upload request, request param is [{}]", request);
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    WebResponseStatusEnum.PARAMETER_ERROR.getMsg());
        }
        try {
            log.info("traceId: {}, start to upload battery file, request param is [{}]", traceId, request);
            batteryCupboardService.uploadFile(request, file);
            return Response.succ();
        } catch (MySQLIntegrityConstraintViolationException | DuplicateKeyException e) {
            log.warn("traceId: {}, this file is already upload, request param is [{}]", traceId, request);
            return Response.fail(WebResponseStatusEnum.DATA_IS_ALREADY_IN_DATABASE.getCode().toString(),
                    WebResponseStatusEnum.DATA_IS_ALREADY_IN_DATABASE.getMsg());
        } catch (Exception e) {
            log.error("traceId: {}, upload battery file failed, request param is [{}]",
                    traceId, request, e);
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping("/last_file_detail")
    public Response lastFileDetail(String batteryCupboardNumber) {
        String traceId = Trace.generateId();
        if (StringUtils.isBlank(batteryCupboardNumber)) {
            log.warn("Bad battery last file detail request, batteryCupboardNumber is [{}]", batteryCupboardNumber);
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    WebResponseStatusEnum.PARAMETER_ERROR.getMsg());
        }
        try {
            log.info("traceId: {}, start to get last battery file detail,  batteryCupboardNumber={}",
                    traceId, batteryCupboardNumber);
            BatteryCupboardFile result =
                    batteryCupboardService.queryLatestUploadFileByBatteryCupboardNumber(batteryCupboardNumber);
            return Response.succ(result);
        } catch (Exception e) {
            log.error("traceId: {}, upload battery file failed, batteryCupboardNumber={}",
                    traceId, batteryCupboardNumber, e);
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }
}
