package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/09/12
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum VehicleUploadStatusEnum {

    /**
     * 默认值
     */
    INIT(-1, "默认值"),
    /**
     * 待上传
     */
    READY(5, "待上传"),
    /**
     * 上传完成
     */
    SUCCEED(100, "上传完成"),
    /**
     * 上传失败
     */
    FAIL(404, "上传失败");

    private int code;
    private String msg;

}
