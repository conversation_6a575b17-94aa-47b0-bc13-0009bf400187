package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "biz_event_autocar_release")
@Data
public class BizEventAutocarRelease {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    private String eventId;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 发版事件发生时间
     */
    private Date eventTime;

    /**
     * autocar发版版本
     */
    private String autocarVersion;

    /**
     * 地图发版版本
     */
    private String mapVersion;

    /**
     * autocar发版版本所在分支
     */
    private String branch;

    /**
     * autocar发版CommitId
     */
    private String commit;

    /**
     * 发版是否成功
     */
    private Boolean isSuccess;

    /**
     * 是否逻辑删除, 0表示否, 1表示是
     */
    private Integer isDeleted;

    private Date createTime;

    private Date updateTime;
}
