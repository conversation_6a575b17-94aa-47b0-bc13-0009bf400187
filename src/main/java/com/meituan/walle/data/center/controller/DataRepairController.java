package com.meituan.walle.data.center.controller;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.walle.data.center.component.DxGroupHandler;
import com.meituan.walle.data.center.entity.dto.ExecuteTaskDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.meituan.walle.data.center.constant.CommonConstant.*;

/**
 * <AUTHOR>
 * @date 2022/09/07
 * 修复数据使用的一次性接口，用完可删除
 */
@RestController
@RequestMapping(value = "/repair")
@Slf4j
public class DataRepairController {

    @Resource
    private DxGroupHandler dxGroupHandler;

    @Resource
    @Qualifier("redisClientBeanFactory")
    RedisStoreClient redisStoreClient;

    @PostMapping("/record/list/transfer")
    public Long transfer() {
        try {
            StoreKey storeKey = new StoreKey(WALLE_DATA_CENTER_CATEGORY, RECORD_TASK_EXECUTE_KEY);
            Long llen = redisStoreClient.llen(storeKey);
            StoreKey setKey = new StoreKey(WALLE_DATA_CENTER_CATEGORY, RECORD_TASK_EXECUTE_SET_KEY);
            for (int i = 0; i < llen; i += 5000) {
                List<ExecuteTaskDTO> lrange = redisStoreClient.lrange(storeKey, i, i + 5000);
                if (!CollectionUtils.isEmpty(lrange)) {
                    Map<String, Double> map = lrange.stream()
                            .collect(Collectors.toMap(ExecuteTaskDTO::getRecordName,
                                    v -> v.getCreateTimestamp().doubleValue(), (a, b) -> a));
                    redisStoreClient.zadd(setKey, map);
                    Thread.sleep(500L);
                }
            }
            return llen;
        } catch (Exception e) {
            log.error("record list transfer error", e);
        }
        return 0L;
    }

    @PostMapping("/record/list/removeKey")
    public String removeKey() {
        try {
            StoreKey storeKey = new StoreKey(WALLE_DATA_CENTER_CATEGORY, RECORD_TASK_EXECUTE_KEY);
            redisStoreClient.delete(storeKey);
            return "success";
        } catch (Exception e) {
            log.error("record list removeKey error", e);
        }
        return "failed";
    }
}
