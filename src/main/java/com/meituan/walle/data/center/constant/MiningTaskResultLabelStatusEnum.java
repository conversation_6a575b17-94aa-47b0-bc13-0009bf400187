package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/04/07
 */
@Getter
@AllArgsConstructor
public enum MiningTaskResultLabelStatusEnum {
    UNMARKED(0, "未标注"),
    MARKED(1, "已标注");

    private int code;
    private String msg;

    public static MiningTaskResultLabelStatusEnum byOrdinal(int ord) {
        for (MiningTaskResultLabelStatusEnum e : MiningTaskResultLabelStatusEnum.values()) {
            if (e.code == ord) {
                return e;
            }
        }
        return null;
    }
}