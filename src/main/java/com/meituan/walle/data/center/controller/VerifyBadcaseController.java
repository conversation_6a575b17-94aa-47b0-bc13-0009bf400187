package com.meituan.walle.data.center.controller;

import com.google.common.base.Preconditions;
import com.meituan.walle.data.center.constant.CharConstant;
import com.meituan.walle.data.center.entity.FunctionReturnCode;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.vo.VerifyBadcaseResultParam;
import com.meituan.walle.data.center.entity.vo.VerifyBadcaseStateParam;
import com.meituan.walle.data.center.service.VerifyBadcaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/verify_badcase")
public class VerifyBadcaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(VerifyBadcaseController.class);

    @Autowired
    @Qualifier(value = "VerifyBadcaseServiceImpl")
    private VerifyBadcaseService verifyBadcaseService;

    @Autowired
    @Qualifier(value = "SimValidatorServiceImpl")
    private VerifyBadcaseService simValidatorService;

    @PostMapping("/update_verify_badcase_state")
    public Response updateVerifyBadcaseState(@RequestBody VerifyBadcaseStateParam verifyBadcaseStateParam) {
        LOGGER.info("[VerifyBadcaseController][updateVerifyBadcaseState] " +
                "Begin, verifyBadcaseStateParam: {}. ", verifyBadcaseStateParam);

        // check legality
        try {
            Preconditions.checkNotNull(verifyBadcaseStateParam,
                    "verifyBadcaseStateParam is null. ");
            Preconditions.checkNotNull(verifyBadcaseStateParam.getJobId(),
                    "verifyBadcaseStateParam jobId is null. ");
        } catch (Exception e) {
            LOGGER.error("[VerifyBadcaseController][updateVerifyBadcaseState] ArgumentException: ", e);
        }

        String jobType = "";
        try {
            jobType = verifyBadcaseStateParam.getJobId().split(CharConstant.CHAR_XH)[0];
        } catch (Exception e) {
            LOGGER.error("[VerifyBadcaseController][updateVerifyBadcaseState] ArgumentException: ", e);
        }

        // service
        FunctionReturnCode result = FunctionReturnCode.SUCCESS;
        try {
            if ("verify-badcase-job".equals(jobType)) {
                result = verifyBadcaseService.updateVerifyBadcaseState(verifyBadcaseStateParam);
            } else if ("offline-validation".equals(jobType)) {
                result = simValidatorService.updateVerifyBadcaseState(verifyBadcaseStateParam);
            }
        } catch (Exception e) {
            LOGGER.error("[VerifyBadcaseController][updateVerifyBadcaseState] Exception: ", e);
        }

        LOGGER.info("[VerifyBadcaseController][updateVerifyBadcaseState] End. ");
        return Response.builder()
                .ret(String.valueOf(result.getCode()))
                .msg(result.getMessage())
                .build();
    }

    @PostMapping("/update_verify_badcase_result")
    public Response updateVerifyBadcaseResult(@RequestBody VerifyBadcaseResultParam verifyBadcaseResultParam) {
        LOGGER.info("[VerifyBadcaseController][updateVerifyBadcaseResult] " +
                "Begin, verifyBadcaseResultParam: {}. ", verifyBadcaseResultParam);

        // check legality
        try {
            Preconditions.checkNotNull(verifyBadcaseResultParam,
                    "verifyBadcaseResultParam is null. ");
            Preconditions.checkNotNull(verifyBadcaseResultParam.getJobId(),
                    "verifyBadcaseResultParam jobId is null. ");
            Preconditions.checkNotNull(verifyBadcaseResultParam.getCaseId(),
                    "verifyBadcaseResultParam caseId is null. ");
        } catch (Exception e) {
            LOGGER.error("[VerifyBadcaseController][updateVerifyBadcaseResult] ArgumentException: ", e);
        }

        String jobType = "";
        try {
            jobType = verifyBadcaseResultParam.getJobId().split(CharConstant.CHAR_XH)[0];
        } catch (Exception e) {
            LOGGER.error("[VerifyBadcaseController][updateVerifyBadcaseState] ArgumentException: ", e);
        }

        // service
        FunctionReturnCode result = FunctionReturnCode.SUCCESS;
        try {
            if ("verify-badcase-job".equals(jobType)) {
                result = verifyBadcaseService.updateVerifyBadcaseResult(verifyBadcaseResultParam);
            } else if ("offline-validation".equals(jobType)) {
                result = simValidatorService.updateVerifyBadcaseResult(verifyBadcaseResultParam);
            }
        } catch (Exception e) {
            LOGGER.error("[VerifyBadcaseController][updateVerifyBadcaseResult] Exception: ", e);
        }

        LOGGER.info("[VerifyBadcaseController][updateVerifyBadcaseResult] End. ");
        return Response.builder()
                .ret(String.valueOf(result.getCode()))
                .msg(result.getMessage())
                .build();
    }
}
