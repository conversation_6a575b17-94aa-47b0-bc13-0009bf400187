package com.meituan.walle.data.center.entity.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/03/31
 */
@Data
public class EventGeneralDTO {

    private String eventId;

    private String recordName;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    private Long eventTimestamp;
    /**
     * 事件发生时间
     */
    private String eventTime;

    /**
     * Utm坐标的x（来源于Localization的x）
     */
    private String x;

    /**
     * Utm坐标的y（来源于Localization的y）
     */
    private String y;

    /**
     * utm的区域号
     */
    private Integer utmZone;

    /**
     * 事件中的额外信息
     */
    private String content;

    private Integer eventType;

    private Integer isDeleted;

    private String createTime;
}
