package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/02/14
 */
@Data
@Table(name = "external_operation_data")
public class ExternalOperationData {

    /**
     * 自增ID
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 实际数据内容
     */
    private String dataContent;

    /**
     * 接口请求时间
     */
    private Date requestTime;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 是否逻辑删除
     */
    private Boolean isDeleted;

    /**
     * 添加时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
