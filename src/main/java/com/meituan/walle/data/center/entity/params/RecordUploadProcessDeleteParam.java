package com.meituan.walle.data.center.entity.params;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/09/22
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RecordUploadProcessDeleteParam implements Serializable {

    @JsonProperty(value = "idc_port")
    private String idcPort;
    @JsonProperty(value = "disk_name")
    private String diskName;
    @JsonProperty(value = "record_name")
    private String recordName;
    @JsonProperty(value = "upload_stage")
    private String uploadStage;
    @JsonProperty(value = "upload_complete_unixtime_s")
    private Long uploadCompleteUnixtimeS;
    @JsonProperty(value = "need_upload_file_count")
    private Integer needUploadFileCount;
    @JsonProperty(value = "has_upload_file_count")
    private Integer hasUploadFileCount;
    @JsonProperty(value = "is_valid")
    private Boolean valid;
    @JsonProperty(value = "invalid_desc")
    private String invalidDesc;

}