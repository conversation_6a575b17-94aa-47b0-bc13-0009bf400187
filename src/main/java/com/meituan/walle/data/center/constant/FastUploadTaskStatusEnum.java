package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/11/05
 */
@Getter
@AllArgsConstructor
public enum FastUploadTaskStatusEnum {

    /**
     * 任务创建/处理中
     */
    CREATED(5, "created", "待回传"),
    /**
     * 任务创建失败
     */
    FAILURE(10, "failure", "失败"),
    /**
     * 任务完成
     */
    FINISHED(100, "finished", "成功"),
    /**
     * 拔盘或其他导致任务中止
     */
    WRONG(404, "wrong", "失败"),
    /**
     * 车辆关机导致任务中止
     */
    SHUTDOWN(401, "shutdown", "关机中断"),
    /**
     * 模块数据不完整，数据段首尾缺失数据
     */
    INCOMPLETE_SIDE(101, "incomplete data in side", "完成-数据段首尾缺失"),
    /**
     * 模块数据不完整，数据段之间不连续
     */
    INCOMPLETE_MIDDLE(102, "incomplete data in middle", "完成-数据段间不连续"),
    /**
     * 模块数据不完整，缺失重要模块
     */
    INCOMPLETE_MISSING_MODULE(103, "incomplete data in module", "完成-缺失重要模块"),

    // 兼容前端显示逻辑
    UPLOADING(50,"uploading", "任务进行中"),

    PAUSED(49,"paused", "暂停"),

    CANCELED(51,"canceled" ,"任务已取消"),

    FINISH(55,"finish" ,"任务结束"),
    ;

    private int code;
    private String msg;
    private String desc;

    public static FastUploadTaskStatusEnum get(int code) {
        for (FastUploadTaskStatusEnum en : FastUploadTaskStatusEnum.values()) {
            if (en.code == code) {
                return en;
            }
        }
        return null;
    }

    public static boolean isWaitUploadingTask(Integer status) {
        return status >= FINISHED.getCode() && status <= INCOMPLETE_MIDDLE.getCode();
    }
}
