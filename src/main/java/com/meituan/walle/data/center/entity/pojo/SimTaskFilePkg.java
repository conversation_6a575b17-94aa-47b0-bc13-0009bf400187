package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.util.Date;

@Data
@Table(name = "sim_task_file_pkg")
public class SimTaskFilePkg {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    private String taskId;
    private String adsId;
    private Integer loopIndex;
    private String fileName;
    private String s3Url;
    private String module;
    private Timestamp startTime;
    private Timestamp endTime;
    private Timestamp firstMsgTime;
    private Integer msgCount;
    private String topics;
    private Long fileSize;
    private Double duration;
    private Date createTime;
    private Date updateTime;
}
