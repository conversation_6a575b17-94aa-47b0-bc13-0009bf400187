package com.meituan.walle.data.center.entity.pojo;

import lombok.Builder;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * table name:  mining_task
 * @author: liuqichun
 * @date: 2022/03/22
 */ 
@Builder
@Data
@Table(name = "mining_task")
public class MiningTask {

	/**
	* 主键id
	*/
	@Id
	@GeneratedValue(generator = "JDBC") 
	private Long id;
	
	/**
	* 任务唯一id：uuid
	*/
	@Column(name = "task_id") 
	private String taskId;
	
	/**
	* 任务英文名
	*/
	@Column(name = "task_name_en") 
	private String taskNameEn;
	
	/**
	* 任务中文名
	*/
	@Column(name = "task_name_ch") 
	private String taskNameCh;
	
	/**
	* 该任务挖掘次数
	*/
	@Column(name = "task_times") 
	private Integer taskTimes;
	
	/**
	* 该任务该次的挖掘规则描述，需要细到规则，如果很复杂就贴wiki链接
	*/
	@Column(name = "task_rule_description")
	private String taskRuleDescription;
	
	/**
	* 该任务该次挖掘的owner是谁（一般是挖掘的PM、RD等）
	*/
	@Column(name = "task_mining_owner") 
	private String taskMiningOwner;
	
	/**
	* 该任务的创建者
	*/
	@Column(name = "task_creator") 
	private String taskCreator;
	
	/**
	* 是否逻辑删除，0表示否，1表示是
	*/
	@Column(name = "is_deleted") 
	private Boolean isDeleted;
	
	/**
	* 创建时间
	*/
	@Column(name = "create_time") 
	private Date createTime;
	
	/**
	* 更新时间
	*/
	@Column(name = "update_time") 
	private Date updateTime;

}

