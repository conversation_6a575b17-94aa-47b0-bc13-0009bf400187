package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2021/7/8 3:17 下午
 */
@Getter
@AllArgsConstructor
public enum RecommendOriginEnum {

    RANDOM(0, "随机推荐"),
    ROAD_COVER(1, "基于道路覆盖的推荐");

    private int id;
    private String msg;

    public static RecommendOriginEnum getByMsg(String msg) {
        for (RecommendOriginEnum type : values()) {
            if (type.getMsg().equals(msg)) {
                return type;
            }
        }
        return RANDOM;
    }
}
