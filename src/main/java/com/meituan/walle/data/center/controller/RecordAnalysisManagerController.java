package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.service.RecordAnalysisManagerService;
import com.meituan.walle.data.center.util.Trace;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/08/22
 */
@Slf4j
@RestController
@RequestMapping("/ra/manager")
public class RecordAnalysisManagerController {
    @Autowired
    private RecordAnalysisManagerService recordAnalysisManagerService;

    @PostMapping("/push_record_list")
    public Response pushRecordList(@RequestBody Set<String> recordNames) {
        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "push_record_list";

        Transaction t = Cat.newTransaction(CatConstant.API, "/ra/manager/" + api);
        log.info("traceId: {}, received new request for {}.", traceId, api);

        try {
            if (CollectionUtils.isEmpty(recordNames)) {
                return Response.fail(WebResponseStatusEnum.COLLECTION_IS_EMPTY.getCode().toString(),
                        WebResponseStatusEnum.COLLECTION_IS_EMPTY.getMsg());
            }

            if (recordNames.size() > SysParamsConfig.ra_batch_push_record_list_size) {
                return Response.fail(WebResponseStatusEnum.COLLECTION_SIZE_LIMIT_UPPER.getCode().toString(),
                        WebResponseStatusEnum.COLLECTION_SIZE_LIMIT_UPPER.getMsg()
                                + SysParamsConfig.ra_batch_push_record_list_size);
            }

            recordAnalysisManagerService.pushRecordList(recordNames);

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            log.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            return Response.succ("");

        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param recordNameList : {}.",
                    traceId, api, recordNames, e);
            return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(),
                    WebResponseStatusEnum.LOGICAL_EXCEPTION.getMsg());
        } finally {
            t.complete();
        }
    }
}