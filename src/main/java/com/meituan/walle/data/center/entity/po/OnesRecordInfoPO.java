package com.meituan.walle.data.center.entity.po;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/4 下午4:17
 * Description:
 * Modified by
 */
@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OnesRecordInfoPO implements Serializable {

    private Long id;
    private Integer issueId;
    private String actTime;
    private Integer logType;
    private String oldValue;
    private String newValue;
    private String name;
    private String variable;
    private Integer assignedOrder;
    private String userName;
    private String displayName;
    private String message;
    private String createdAt;
    private Double costTime;
    private String type;
    private String typeDisplay;
    private Timestamp createTime;
    private Timestamp updateTime;

    @Override
    public String toString(){
        try {
            return new ObjectMapper().writeValueAsString(this);
        } catch (JsonProcessingException e) {
            log.error("OnesRecordInfoPO toString failed", e);
        }
        return "";
    }

}
