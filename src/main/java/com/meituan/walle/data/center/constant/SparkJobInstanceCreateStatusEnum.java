package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/01/20
 */
@Getter
@AllArgsConstructor
public enum SparkJobInstanceCreateStatusEnum {

    /**
     * 未创建
     */
    NOT_CREATED(-1, "not created"),
    /**
     * 创建中
     */
    CREATING(0, "creating"),
    /**
     * 创建成功
     */
    CREATED(1, "created")
    ;

    private int code;
    private String desc;

    public static SparkJobInstanceCreateStatusEnum byOrdinal(int ord) {
        for (SparkJobInstanceCreateStatusEnum e : SparkJobInstanceCreateStatusEnum.values()) {
            if (e.code == ord) {
                return e;
            }
        }
        return null;
    }
}