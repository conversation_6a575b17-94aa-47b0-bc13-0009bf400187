package com.meituan.walle.data.center.controller;

import com.alibaba.fastjson.JSON;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.google.common.collect.Lists;
import com.meituan.walle.data.center.config.mcc.OnesParamConfig;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.CommonConstant;
import com.meituan.walle.data.center.constant.DateTimeFormatterConstant;
import com.meituan.walle.data.center.dal.recordmanager.mapper.BRecordFileMapper;
import com.meituan.walle.data.center.dal.recordmanager.mapper.BRecordPkgMapper;
import com.meituan.walle.data.center.entity.dto.LambdaJobDTO;
import com.meituan.walle.data.center.entity.dto.LambdaSyncModelDTO;
import com.meituan.walle.data.center.entity.enums.S3ClusterEnum;
import com.meituan.walle.data.center.entity.po.RecordPO;
import com.meituan.walle.data.center.entity.po.RecordPkgPO;
import com.meituan.walle.data.center.entity.pojo.LambdaJob;
import com.meituan.walle.data.center.entity.pojo.LambdaSyncModel;
import com.meituan.walle.data.center.entity.pojo.RecordFileV2;
import com.meituan.walle.data.center.entity.request.LambdaJobRequest;
import com.meituan.walle.data.center.entity.request.LambdaSyncModelRequest;
import com.meituan.walle.data.center.entity.response.HttpResponse;
import com.meituan.walle.data.center.entity.vo.LambdaJobVO;
import com.meituan.walle.data.center.entity.vo.SparkJobParamVO;
import com.meituan.walle.data.center.mapper.RecordFileV2Mapper;
import com.meituan.walle.data.center.mapper.RecordMapper;
import com.meituan.walle.data.center.mapper.RecordPkgMapper;
import com.meituan.walle.data.center.service.LambdaJobService;
import com.meituan.walle.data.center.service.LambdaSyncModelService;
import com.meituan.walle.data.center.service.RecordPkgService;
import com.meituan.walle.data.center.service.SparkJobService;
import com.meituan.walle.data.center.util.DatetimeUtil;
import com.meituan.walle.data.center.util.GenerateUtil;
import com.meituan.walle.data.center.util.S3Util;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Log4j2
public class LambdaController {

    @Autowired
    private S3Util s3Util;

    @Resource
    private LambdaJobService lambdaJobService;

    @Resource
    private SparkJobService sparkJobService;

    public static final String LAMBDA_PREFIX = "lambda";

    public static final String LAMBDA_JOB_NAME = "hadoop-wcdp.pyspark.lambda-service";

    public static final String LAMBDA_SYNC_JOB_NAME = "hadoop-wcdp.pyspark.lambda-sync";

    @Resource
    public RecordPkgService service;

    @Resource
    private LambdaSyncModelService lambdaSyncModelService;

    /**
     * lambda本地测试时，获取满足要求的文件时使用
     *
     * @param lambdaJobVO
     * @return
     */
    @GetMapping(value = "/lambda/task/file")
    public Map<String, Object> queryLambdaJobFile(LambdaJobVO lambdaJobVO) {

        List<RecordPkgPO> list = service.queryRecordPkgFile(lambdaJobVO);
        List<Map<String, Object>> resultList = list.stream()
                .filter(item -> StringUtils.isNotBlank(item.getS3Url()))
                .map(item -> {
                    Map<String, Object> map = new LinkedHashMap<>();
                    String s3Download = OnesParamConfig.mcc_s3_public_download_url
                            + item.getS3Url().replace("s3://", "");

                    map.put("s3Url", s3Download);
                    map.put("startTime", item.getStartTime().toLocalDateTime().format(DatetimeUtil.YMDHMS_FORMATTER));
                    map.put("endTime", item.getEndTime().toLocalDateTime().format(DatetimeUtil.YMDHMS_FORMATTER));
                    map.put("duration", item.getDuration());
                    map.put("fileSize", item.getFileSize());
                    return map;
                }).collect(Collectors.toList());
        Map<String, Object> result = new HashMap<>();
        if (list.isEmpty()) {
            result.put("code", 404);
            result.put("msg", "not found");
            return result;
        }
        result.put("code", 200);
        result.put("msg", "ok");
        result.put("result", resultList);
        return result;
    }

    /**
     * 添加lambda任务
     *
     * @param lambdaJobVO
     * @param file
     * @return
     */
    @PostMapping(value = "/lambda/task")
    public HttpResponse submitLambdaJob(LambdaJobVO lambdaJobVO, MultipartFile file) {
        String lambdaId = GenerateUtil.getUuid();
        String today = DatetimeUtil.format(new Date(), "yyyyMMdd");

        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentLength(file.getSize());
        String s3Key = LAMBDA_PREFIX + "/" + today + "/" + lambdaId + ".py";
        try {
            //先把文件存在s3中
            AmazonS3 s3Client = s3Util.getS3Client(S3ClusterEnum.BJ_BAK);
            s3Client.putObject(SysParamsConfig.lambda_bucket, s3Key, file.getInputStream(), objectMetadata);
        } catch (Exception e) {
            log.error("上传py文件至s3失败", e);
        }

        LambdaJob lambdaJob = new LambdaJob();
        lambdaJob.setLambdaId(lambdaId);
        lambdaJob.setOwner(lambdaJobVO.getOwner());
        lambdaJob.setRecordName(lambdaJobVO.getRecordName());
        lambdaJob.setVehicleName(lambdaJobVO.getVehicleName());
        lambdaJob.setModule(lambdaJobVO.getModule());
        lambdaJob.setStartTime(DatetimeUtil.covertToDate(lambdaJobVO.getStartTime(), DateTimeFormatterConstant.ymdhms));
        lambdaJob.setEndTime(DatetimeUtil.covertToDate(lambdaJobVO.getEndTime(), DateTimeFormatterConstant.ymdhms));
        lambdaJob.setPyS3Url(CommonConstant.S3_PREFIX + SysParamsConfig.lambda_bucket + "/" + s3Key);
        lambdaJob.setJobType(lambdaJobVO.getJobType());
        String modelId = lambdaJobVO.getModelId();
        if (StringUtils.isNotBlank(modelId)) {
            lambdaJob.setModelId(modelId);
            //只要有modelId，状态就是未开始
            lambdaJob.setSyncStatus(0); //未开始
        } else {
            lambdaJob.setSyncStatus(-1);
        }

        Integer runNow = lambdaJobVO.getRunNow();
        if (runNow != null && runNow > 0) {
            //如果是提交的时候勾选了立即跑的选项，则先把状态置为进行中
            lambdaJob.setStatus(1);
            startLambdaJob(lambdaId);
        }
        lambdaJobService.addLambdaJob(lambdaJob);

        return new HttpResponse()
                .code(HttpStatus.SC_OK)
                .msg("ok")
                .result(lambdaJob);
    }

    /**
     * 启动lambda任务
     *
     * @param lambdaId
     * @return
     */
    @RequestMapping("/lambda/run")
    public Map<String, Object> runLambdaJob(String lambdaId) {
        int update = lambdaJobService.updateLambdaJobStatus(1, lambdaId);
        Map<String, Object> result = new HashMap<>();
        if (update > 0) {
            //启动任务
            startLambdaJob(lambdaId);
            result.put("code", 200);
            result.put("msg", "ok");
        } else {
            result.put("code", 404);
            result.put("msg", "not found");
        }
        log.info("启动任务: {}", lambdaId);
        return result;
    }

    public void startLambdaJob(String lambdaId) {
        Map<String, Object> param = new HashMap<>();
        param.put("lambdaId", lambdaId);
        int priority = 150;
        try {
            priority = JSON.parseObject(SysParamsConfig.spark_job_priority).getInteger("LambdaTask");
        } catch (Exception e) {
            log.error("get priority from spark_job_priority error, key is: LambdaTask", e);
        }

        startSparkJob(LAMBDA_JOB_NAME, param, priority);
    }

    /**
     * 分页查询lambda任务
     *
     * @param request
     * @return
     */
    @GetMapping("/lambda/task")
    public Map<String, Object> queryLambdaJob(LambdaJobRequest request) {
        Map<String, Object> result = new LinkedHashMap<>();
        if (request.getPage() > 10000 || request.getSize() > 1000) {
            result.put("code", 400);
            result.put("msg", "参数错误");
        }
        int condCnt = lambdaJobService.queryLambdaJobCnt(request);

        if (condCnt > 0) {
            List<LambdaJob> lambdaJobList = lambdaJobService.queryLambdaJobList(request);
            if (!lambdaJobList.isEmpty()) {
                List<LambdaJobDTO> dtoList = lambdaJobList.stream().map(item -> {
                    LambdaJobDTO lambdaJobDTO = new LambdaJobDTO();
                    BeanUtils.copyProperties(item, lambdaJobDTO);
                    String s3Url = item.getResultS3Url();
                    if (StringUtil.isNotEmpty(s3Url)) {
                        String s3Download = new StringBuilder(OnesParamConfig.mcc_s3_public_download_url)
                                .append(s3Url.replace(CommonConstant.S3_PREFIX, "")).toString();
                        lambdaJobDTO.setResultS3Url(s3Download);
                    }
                    return lambdaJobDTO;
                }).collect(Collectors.toList());
                result.put("code", 200);
                result.put("msg", "ok");
                result.put("totalCount", condCnt);
                result.put("result", dtoList);
            }
        }
        if (!result.containsKey("result")) {
            result.put("code", 404);
            result.put("msg", "not found");
        }
        log.info("查询任务: {}", request.toString());
        return result;
    }

    /**
     * 修改lambda任务
     *
     * @param request
     * @param file
     * @return
     */
    @PutMapping("/lambda/task")
    public Map<String, Object> updateLambdaJob(LambdaJobRequest request, MultipartFile file) {
        Map<String, Object> result = new LinkedHashMap<>();
        if (StringUtils.isBlank(request.getLambdaId())) {
            result.put("code", 400);
            result.put("msg", "lambdaId不能为空");
            return result;
        }
        if (file != null && file.getSize() > 0) {
            LambdaJob lambdaJob = lambdaJobService.queryByLambdaId(request.getLambdaId());
            String pyS3Url = lambdaJob.getPyS3Url();

            try {
                String s3Key = pyS3Url
                        .replace(CommonConstant.S3_PREFIX + SysParamsConfig.lambda_bucket + "/", "");
                ObjectMetadata objectMetadata = new ObjectMetadata();
                objectMetadata.setContentLength(file.getSize());
                //先把文件存在s3中
                AmazonS3 s3Client = s3Util.getS3Client(S3ClusterEnum.BJ_BAK);
                s3Client.putObject(SysParamsConfig.lambda_bucket, s3Key, file.getInputStream(), objectMetadata);
            } catch (Exception e) {
                log.error("更新信息，上传py文件至s3失败", e);
            }
        }
        int update = lambdaJobService.updateByLambdaId(request);
        if (update > 0) {
            result.put("code", 200);
            result.put("msg", "ok");
        } else {
            result.put("code", 404);
            result.put("msg", "not found");
        }
        log.info("修改任务: {}", request.toString());
        return result;
    }

    /**
     * 删除lambda任务
     *
     * @param lambdaId
     * @return
     */
    @PutMapping("/lambda/del")
    public Map<String, Object> deleteLambdaJob(String lambdaId) {
        Map<String, Object> result = new LinkedHashMap<>();
        if (StringUtils.isBlank(lambdaId)) {
            result.put("code", 400);
            result.put("msg", "lambdaId不能为空");
            return result;
        }
        //删除s3中的py文件
        try {
            LambdaJob lambdaJob = lambdaJobService.queryByLambdaId(lambdaId);
            String pyS3Url = lambdaJob.getPyS3Url();
            String s3Key = pyS3Url
                    .replace(CommonConstant.S3_PREFIX + SysParamsConfig.lambda_bucket + "/", "");
            AmazonS3 s3Client = s3Util.getS3Client(S3ClusterEnum.BJ_BAK);
            s3Client.deleteObject(SysParamsConfig.lambda_bucket, s3Key);
        } catch (Exception e) {
            log.error("删除s3中的py文件出错", e);
        }

        lambdaJobService.deleteByLambdaId(lambdaId);
        result.put("code", 200);
        result.put("msg", "ok");
        log.info("删除任务: {}", lambdaId);
        return result;
    }

    /**
     * lambda任务同步
     *
     * @param modelId
     * @return
     */
    @PutMapping(value = "/lambda/sync")
    public HttpResponse lambdaSync(@NotNull String lambdaId, @NotNull String modelId) {
        LambdaSyncModel model = lambdaSyncModelService.queryByModelId(modelId);
        HttpResponse response = new HttpResponse();
        if (model == null) {
            response.code(HttpStatus.SC_NOT_FOUND).msg("not found model");
            return response;
        }
        // 提交任务到spark上
        startLambdaSyncJob(lambdaId, model);

        LambdaJobRequest request = new LambdaJobRequest();
        request.setLambdaId(lambdaId);
        request.setSyncStatus(1); //进行中
        lambdaJobService.updateByLambdaId(request);

        return response;
    }

    /**
     * 获取同步模板
     *
     * @param request
     * @return
     */
    @GetMapping("/lambda/sync_model")
    public HttpResponse queryLambdaSyncModel(LambdaSyncModelRequest request) {
        HttpResponse response = new HttpResponse();
        List<LambdaSyncModel> list = lambdaSyncModelService.queryLambdaSyncModel(request);
        if (list.isEmpty()) {
            response.code(HttpStatus.SC_NOT_FOUND).msg("not found");
            return response;
        }
        List<LambdaSyncModelDTO> resultList = list.stream().map(item -> {
            LambdaSyncModelDTO dto = new LambdaSyncModelDTO();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }).collect(Collectors.toList());

        return response.result(resultList);
    }

    public void startLambdaSyncJob(String lambdaId, LambdaSyncModel lambdaSyncModel) {
        Map<String, Object> param = new HashMap<>();
        param.put("source", lambdaId);
        param.put("target", lambdaSyncModel.getTargetTable());
        param.put("column", lambdaSyncModel.getUniqueColumns());
        int priority = 150;
        startSparkJob(LAMBDA_SYNC_JOB_NAME, param, priority);
    }

    private void startSparkJob(String jobName, Map<String, Object> param, int priority) {
        SparkJobParamVO sparkJobParamVO = new SparkJobParamVO();
        sparkJobParamVO.setSparkJobName(jobName);
        sparkJobParamVO.setOtherParam(param);
        sparkJobParamVO.setPriority(priority);
        sparkJobService.startSparkJobInstance(sparkJobParamVO);
    }


    @Resource
    private RecordMapper recordMapper;

    @Resource
    private RecordFileV2Mapper recordFileV2Mapper;

    @Resource
    private BRecordFileMapper bRecordFileMapper;

    @Resource
    private RecordPkgMapper recordPkgMapper;

    @Resource
    private BRecordPkgMapper bRecordPkgMapper;

    @RequestMapping(value = "/record/exists")
    public HttpResponse existsRecord(String recordName) {
        HttpResponse response = new HttpResponse();
        RecordPO recordPO = recordMapper.selectRecordByRecordName(recordName);
        if (recordPO == null || recordPO.getRecordName() == null) {
            response.code(HttpStatus.SC_NOT_FOUND).msg("not found");
        }
        return response;
    }

    @RequestMapping(value = "/record_pkg/count")
    public HttpResponse existsRecordPkg(String recordName) {
        HttpResponse response = new HttpResponse();
        List<String> recordNameList = new ArrayList<>();
        recordNameList.add(recordName);
        List<RecordPkgPO> pkgPOList;
        if (SysParamsConfig.record_pkg_for_read_mysql) {
            pkgPOList = recordPkgMapper.queryRecordPkgFile(recordNameList, null, null, null);
        } else {
            pkgPOList = bRecordPkgMapper.queryRecordPkgFile(recordNameList, null, null, null);
        }
        if (pkgPOList.isEmpty()) {
            response.code(HttpStatus.SC_NOT_FOUND).msg("not fount");
        } else {
            response.code(HttpStatus.SC_OK).msg("ok");
        }
        return response;
    }

    @RequestMapping("/record_file/count")
    public HttpResponse findRecordFile(String recordName) {
        HttpResponse response = new HttpResponse();
        byte fileType = 1;
        RecordFileV2 recordFileV2 = RecordFileV2.builder()
                .recordName(recordName)
                .fileType(fileType)
                .build();
        List<RecordFileV2> list = recordFileV2Mapper.select(recordFileV2);
        if (list.isEmpty()) {
            response.code(HttpStatus.SC_NOT_FOUND).msg("not found");
        } else {
            response.result(list.size());
        }
        return response;
    }

    @RequestMapping(value = "/rec")
    public HttpResponse uploadRecFile(String recordName, String fileName, long fileSize) {
        log.info("开始上传文件, {}, {}", recordName, fileName);
        try {
            String date = recordName.split("_")[0];
            int dateKey = Integer.parseInt(date);

            byte fileType = 1;
            if ("record_info.pb.txt".equals(fileName)) {
                fileType = 8;
            } else if (fileName.endsWith("index")) {
                fileType = 5;
            }
            String s3Key;
            if (fileType == 8) {
                s3Key = date + "/" + recordName.substring(9) + "/" + fileName;
            } else if (fileType == 5) {
                s3Key = date + "/" + recordName.substring(9) + "/index/" + fileName;
            } else {
                s3Key = date + "/" + recordName.substring(9) + "/rec/" + fileName;
            }
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(fileSize);
            String s3Url = "s3://onboard-record-data/" + s3Key;

            RecordFileV2 recordFile = RecordFileV2.builder()
                    .fileSize(fileSize)
                    .fileSizeS3(fileSize)
                    .filePath("/55cd2e41530088a1/" + s3Key.replace("/" + fileName, ""))
                    .md5("")
                    .recordName(recordName)
                    .recordDate(dateKey)
                    .datekey(dateKey)
                    .fileName(fileName)
                    .fileType(fileType)
                    .s3Url(s3Url)
                    .lastModified(new Date())
                    .uploadTime(new Date())
                    .build();
            List<RecordFileV2> pbFile;
            if (SysParamsConfig.record_file_for_read_mysql) {
                pbFile = recordFileV2Mapper.selectFileByFileName(recordName, fileName);
            } else {
                pbFile = bRecordFileMapper.selectFileByFileName(recordName, fileName);
            }
            if (pbFile.isEmpty()) {
                recordFileV2Mapper.insert(recordFile);
            } else {
                RecordFileV2 fileV2 = pbFile.get(0);
                fileV2.setS3Url(s3Url);
                fileV2.setFileSizeS3(fileSize);
                fileV2.setFileSize(fileSize);
                recordFileV2Mapper.updateByPrimaryKey(fileV2);
            }
            log.info("上传文件完成, {}, {}", recordName, fileName);
        } catch (Exception e) {
            log.error("插入数据报错", e);
        }

        return new HttpResponse();
    }

    @GetMapping(value = "/lambda/module")
    public HttpResponse queryLambdaModuleList() {
        List<String> list = Lists.newArrayList(SysParamsConfig.lambda_modules.split(","));
        return new HttpResponse().result(list);
    }
}
