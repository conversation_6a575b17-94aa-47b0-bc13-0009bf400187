package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

public class RemoteRelatedConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.create.url")
    public static volatile String remoteVideoCreateUrl = "https://spider-mad-admin.sankuai.com/api/v1/cockpit/stream";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.stream.url")
    public static volatile String remoteVideoStreamUrl = "https://spider-mad-admin.sankuai.com/dvr/api/vehicle/stream";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.url.rule")
    public static volatile String forwardRule = "walledata";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.callback.url")
    public static volatile String callbackUrl =
            "https://walledata.mad.test.sankuai.com/walledata/remote_video/callback";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.recorder.callback.url")
    public static volatile String recorderCallbackUrl =
            "https://walledata.mad.test.sankuai.com/walledata/remote_video/collision/recorder_callback";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.front.end.url")
    public static volatile String frontEndUrl =
            "http://yujiacheng-drrdt-sl-walledata.mad.test.sankuai.com/app/remoteVideo";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.collision.group.id")
    public static volatile long collisionDetectionGroupId = 64010309598L; // 生产66184562429L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.collision.previous.millis")
    public static volatile Long collisionPreviousMillis = 50_000L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.collision.later.millis")
    public static volatile Long collisionLaterMillis = 10_000L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.collision.event.delay.pull.seconds")
    public static volatile Integer collisionDelayPullSeconds = 180;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.intervention.event.delay.pull.seconds")
    public static volatile Integer interventionDelayPullSeconds = 180;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.intervention.previous.seconds")
    public static volatile Integer interventionPreviousSeconds = 30;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.intervention.after.seconds")
    public static volatile Integer interventionAfterSeconds = 30;
}
