package com.meituan.walle.data.center.entity.po.mgc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/12/23 10:18
 * @description
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class OriginDatasourceConfigPO {
    private Long id;
    // 唯一的
    private String caseType;
    // 唯一键
    private String originDatasourceName;
    private String description;
    private String topicName;
    private String consumerGroup;
    private String consumerAppKey;
    private String bgNamespace;
    private String filterExpression;
    private String personInCharge;
    private Integer isDeleted;
    private Date createTime;
    private Date updateTime;
}
