package com.meituan.walle.data.center.config.mcc;

import com.alibaba.fastjson.JSON;
import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.MtConfigClient;
import com.sankuai.meituan.config.annotation.MtConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2019/7/2 11:00
 */
@Slf4j
@Component
public class CoreDumpDispatcherConfig {
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "coredump.default.owner")
    public static volatile String defaultOwner = "wangjianhua10";

    private static final String COREDUMP_DISPATCHER_KEY = "coredump.dispatch.map";
    private String localPartners = "{}";
    private Map<String, String> module2Owner;

    @Resource
    private MtConfigClient mtConfigClient;

    @PostConstruct
    public void init() {
        String value = mtConfigClient.getValue(COREDUMP_DISPATCHER_KEY);
        if (StringUtils.isBlank(value)) {
            value = localPartners;
        }
        update(value);

        mtConfigClient.addListener(COREDUMP_DISPATCHER_KEY, (key, oldValue, newValue) -> {
            log.info("config {} change to {}, old value is {}", key, newValue, oldValue);
            update(newValue);
        });
    }

    public String getOwner4Module(String module) {
        return module2Owner.get(module);
    }

    public Map<String, String> getModule2Owner() {
        return Collections.unmodifiableMap(module2Owner);
    }

    private void update(String txt) {
        if (StringUtils.isBlank(txt)) {
            log.warn("config value is empty: {}", COREDUMP_DISPATCHER_KEY);
        } else {
            synchronized (CoreDumpDispatcherConfig.class) {
                module2Owner = JSON.parseObject(txt, Map.class);
                if (module2Owner == null) {
                    module2Owner = Collections.emptyMap();
                }
            }
        }
    }

}
