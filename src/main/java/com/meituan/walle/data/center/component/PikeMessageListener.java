package com.meituan.walle.data.center.component;

import com.sankuai.pike.message.api.rpc.business.entity.ClientMessageRequest;
import com.sankuai.pike.message.api.rpc.business.entity.ServerMessageResult;
import com.sankuai.pike.message.sdk.listener.MessageListener;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Component
@Log4j2
public class PikeMessageListener implements MessageListener {

    @Override
    public void onClientMessage(ClientMessageRequest clientMessageRequest) {
        String bizId = clientMessageRequest.getBizId();
        int appId = clientMessageRequest.getAppId();
        String token = clientMessageRequest.getToken();
        String alias = clientMessageRequest.getAlias();
        String messageId = clientMessageRequest.getMessageId();
        String message = new String(clientMessageRequest.getMessage(), StandardCharsets.UTF_8);
        log.info("client send message, bizId: {}, appId: {}, token: {}, alias: {}, messageId: {}, message: {}",
                bizId, appId, token, alias, messageId, message);
    }

    @Override
    public void onSendMessageResult(ServerMessageResult serverMessageResult) {
        String token = serverMessageResult.getToken();
        boolean isSuccess = serverMessageResult.isSuccess();
        log.info("serverMessageResult, token: {}, messageId: {}, success: {}, reason: {}",
                token,serverMessageResult.getMessageId(), isSuccess, serverMessageResult.getReason());
    }

}
