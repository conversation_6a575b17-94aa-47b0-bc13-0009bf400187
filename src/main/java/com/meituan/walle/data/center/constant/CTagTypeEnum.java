package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Beijing
 * @date 2020/02/18 下午2:35
 * Description:
 * Modified by qixinyang 2021/08/02
 */
@Getter
@AllArgsConstructor
public enum CTagTypeEnum {

    CATEGORY_TYPE(1, "CategoryTag"),
    VOICE_TYPE(2, "VoiceTag");

    private int code;
    private String msg;

    public static CTagTypeEnum getCategoryTagTypeEnum(int val) {
        for (CTagTypeEnum type : CTagTypeEnum.values()) {
            if (type.getCode() == val) {
                return type;
            }
        }
        return null;
    }
}
