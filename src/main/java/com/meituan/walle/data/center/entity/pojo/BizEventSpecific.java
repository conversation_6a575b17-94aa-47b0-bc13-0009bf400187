package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "biz_event_specific")
@Data
public class BizEventSpecific {
    //专项事件
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    private String eventId;

    private String recordName;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 事件发生时间
     */
    private Date eventTime;

    /**
     * Utm坐标的x（来源于Localization的x）
     */
    private String x;

    /**
     * Utm坐标的y（来源于Localization的y）
     */
    private String y;

    /**
     * utm的区域号
     */
    private Integer utmZone;


    /**
     * 事件中的额外信息
     */
    private String content;

    private Integer eventType;

    private String uploadTaskId;


    private Boolean isDeleted;

    private Date createTime;

    private Date updateTime;

}
