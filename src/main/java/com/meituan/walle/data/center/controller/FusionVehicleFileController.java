package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.pojo.FusionVehicleFile;
import com.meituan.walle.data.center.entity.request.FusionVehicleFileRequest;
import com.meituan.walle.data.center.service.FusionVehicleFileService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
public class FusionVehicleFileController {
    @Resource
    private FusionVehicleFileService fusionVehicleFileService;

    /**
     * 保存融合车视频文件信息接口，主要是给收集坐席产生的远遥视频数据上传信息用的
     *
     * @param list
     * @return
     */
    @RequestMapping(value = "/walle/data_center/fusion_vehicle_file", method = RequestMethod.POST)
    public Map<String, Object> addFusionVehicleFiles(@RequestBody List<FusionVehicleFileRequest> list) {
        Map<String, Object> result = new HashMap<>();
        if (list == null || list.isEmpty()) {
            result.put("code", 400);
            result.put("msg", "参数为空");
            return result;
        }
        List<FusionVehicleFile> fusionVehicleFiles = list.stream()
                .map(FusionVehicleFileRequest::toEntity)
                .collect(Collectors.toList());
        int response = fusionVehicleFileService.save(fusionVehicleFiles);

        int code = 200;
        String msg = "ok";
        if (response <= 0) {
            code = 400;
            msg = "插入失败, 可能是参数错误";
        }
        result.put("code", code);
        result.put("msg", msg);

        return result;
    }

    /**
     * 查询融合车里s3_url是否存在，供调用端排除重复插入的数据
     *
     * @param list
     * @return
     */
    @RequestMapping(value = "/walle/data_center/s3_url_list", method = RequestMethod.POST)
    public Map<String, Object> queryListByS3Url(@RequestBody List<String> list) {
        Map<String, Object> result = new HashMap<>();
        if (list == null || list.isEmpty()) {
            result.put("code", 400);
            result.put("msg", "参数为空");
            return result;
        }

        result.put("code", 200);
        result.put("msg", "ok");
        result.put("result", fusionVehicleFileService.queryListByS3Url(list));
        return result;
    }
}
