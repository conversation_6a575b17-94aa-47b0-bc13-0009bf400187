package com.meituan.walle.data.center.component;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.config.mcc.FastUploadConfig;
import com.meituan.walle.data.center.constant.*;
import com.meituan.walle.data.center.entity.enums.VehicleEventTypeEnum;
import com.meituan.walle.data.center.entity.pojo.BizEventParking;
import com.meituan.walle.data.center.entity.pojo.BizEvents;
import com.meituan.walle.data.center.entity.pojo.VehicleUploadRequest;
import com.meituan.walle.data.center.entity.request.FastUploadTaskRequest;
import com.meituan.walle.data.center.entity.vo.EventMessageVO;
import com.meituan.walle.data.center.mapper.VehicleInfoMapper;
import com.meituan.walle.data.center.service.VehicleUploadRequestService;
import com.meituan.walle.data.center.util.DatetimeUtil;
import com.meituan.walle.data.center.util.DxAppUtil;
import com.meituan.walle.data.center.util.JacksonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class EventParkingHandler {

    private static final ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("EventParkingHandler-%d").build();
    private static final ThreadPoolExecutor THREAD_POOL =
            new ThreadPoolExecutor(10, 10, 0,
                    TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000), namedThreadFactory);

    private static final long SIMULATION_OBSTACLE_MIN_ID = 20000000;

    @Resource
    private VehicleUploadRequestService vehicleUploadRequestService;

    @Resource
    private VehicleInfoMapper vehicleInfoMapper;

    public void handleParkingAsync(EventMessageVO eventMessageVO) {
        THREAD_POOL.submit(() -> {
            List<Map<String, Object>> msgList = eventMessageVO.getMsgList();
            Map<String, Object> eventMsg;
            if (msgList.size() == 2) {
                eventMsg = msgList.get(0);
            } else {
                eventMsg = msgList.get(1);
            }
            if (isSimulationTest(eventMsg)) {
                //如果是仿真测试，则直接跳过，不继续执行了
                return;
            }

            BizEvents bizEvents = eventMessageVO.getBizEvents();

            try {
                addCollisionFastUploadTask(bizEvents, eventMsg);
            } catch (Exception e) {
                log.error("intervention event id : {},fast upload task create unique value failed.",
                        bizEvents.getEventId(), e);
            }

            String[] eventParts = bizEvents.getEventId().split("_");
            String eventTime = DatetimeUtil.format(bizEvents.getEventTime(), DatetimeUtil.YMDHMS);
            String vehicleName = eventParts[eventParts.length - 1];
            String body = String.format("【车辆可能发生碰撞告警】\n" + "时间: %s\n" + "车辆名称: %s\n" + "碰撞发生时间: %s",
                    DatetimeUtil.format(new Date(), DatetimeUtil.YMDHMS), vehicleName, eventTime);
            DxAppUtil.sendMesToGroup(SysParamsConfig.collision_msg_group, body);
        });
    }

    public void addCollisionFastUploadTask(BizEvents bizEvents, Map<String, Object> eventMsg)
            throws Exception {
        String vin = bizEvents.getVin();
        long measurementTimestamp = EventInterventionHandler.getTimestamp(eventMsg);
        VehicleUploadRequest vehicleUploadRequest = new VehicleUploadRequest();
        vehicleUploadRequest.setVin(vin);

        Date measurementTime;
        Date startTime = DatetimeUtil.covertTimestampToDate(0L);
        Date endTime = DatetimeUtil.covertTimestampToDate(0L);
        if (measurementTimestamp > 0) {
            measurementTime = DatetimeUtil.covertTimestampToDate(measurementTimestamp);

            //前10后5
            startTime = DateUtils.addSeconds(measurementTime,
                    -FastUploadConfig.VEHICLE_UPLOAD_REQUEST_TASK_GENERATE_STEP);
            endTime = DateUtils.addSeconds(measurementTime,
                    FastUploadConfig.VEHICLE_UPLOAD_REQUEST_TASK_GENERATE_STEP / 2);
        }

        vehicleUploadRequest.setMeasurementTimestamp(measurementTimestamp);
        vehicleUploadRequest.setStart(startTime);
        vehicleUploadRequest.setEnd(endTime);
        vehicleUploadRequest.setModule(FastUploadConfig.VEHICLE_UPLOAD_REQUEST_MODULE_LIST);
        vehicleUploadRequest.setStatus(FastUploadTaskStatusEnum.CREATED.getCode());
        vehicleUploadRequest.setPriority(FastUploadTaskPriorityConstant.NON_OPERATIONAL);
        vehicleUploadRequest.setTaskType(FastUploadTaskTypeEnum.COLLISION.getCode());
        vehicleUploadRequest.setOneCase(false);
        vehicleUploadRequest.setIsDiscern(1);
        vehicleUploadRequest.setEventId(bizEvents.getEventId());
        vehicleUploadRequest.setCreator(CommonConstant.EVENT_SYSTEM_FAST_UPLOAD_CREATOR_PREFIX + "-" +
                VehicleEventTypeEnum.PARKING.getCode());
        vehicleUploadRequestService.insertVehicleUploadRequest(vehicleUploadRequest);
    }

    public void addMstatFastTask(BizEventParking bizEventParking, List<Map<String, Object>> msgList) {
        log.info("[EventParkingHandler#addMstatFastTask] start");
        FastUploadTaskRequest fastUploadTaskRequest = new FastUploadTaskRequest();
        try {
            Map<String, Object> eventMsg;
            if (msgList.size() == 2) {
                eventMsg = msgList.get(0);
            } else {
                eventMsg = msgList.get(1);
            }

            long measurementTimestamp = EventInterventionHandler.getTimestamp(eventMsg);
            Date measurementTime;
            Date startTime = DatetimeUtil.covertTimestampToDate(0L);
            Date endTime = DatetimeUtil.covertTimestampToDate(0L);
            if (measurementTimestamp > 0) {
                measurementTime = DatetimeUtil.covertTimestampToDate(measurementTimestamp);
                startTime = DateUtils.addSeconds(measurementTime,
                        -FastUploadConfig.VEHICLE_MSTAT_UPLOAD_TASK_START_PADDING);
                endTime = DateUtils.addSeconds(measurementTime,
                        FastUploadConfig.VEHICLE_MSTAT_UPLOAD_TASK_END_PADDING);
            }

            fastUploadTaskRequest.setVin(bizEventParking.getVin());
            fastUploadTaskRequest.setModule(CommonConstant.MSTAT_PREFIX);
            fastUploadTaskRequest.setStart(DatetimeUtil.format(startTime, DatetimeUtil.YMDHMS));
            fastUploadTaskRequest.setEnd(DatetimeUtil.format(endTime, DatetimeUtil.YMDHMS));

            Date startDate = DatetimeUtil.covertToDate(fastUploadTaskRequest.getStart(), DatetimeUtil.YMDHMS);
            Date endDate = DatetimeUtil.covertToDate(fastUploadTaskRequest.getEnd(), DatetimeUtil.YMDHMS);
            fastUploadTaskRequest.setStartDate(startDate);
            fastUploadTaskRequest.setEndDate(endDate);

            fastUploadTaskRequest.setTaskType(FastUploadTaskTypeEnum.FILE_PATH.getCode());
            fastUploadTaskRequest.setCreator(CommonConstant.EVENT_SYSTEM_FAST_UPLOAD_CREATOR_PREFIX + "-" +
                    VehicleEventTypeEnum.PARKING.getCode());
            fastUploadTaskRequest.setPriority(SysParamsConfig.parking_event_mstat_fast_task_priority);
            fastUploadTaskRequest.setEventId(bizEventParking.getEventId());

            vehicleUploadRequestService.createMstatFastTask(fastUploadTaskRequest);
            log.info("[EventParkingHandler#addMstatFastTask] create fast upload task successfully");
        } catch (Exception e) {
            log.error("[EventParkingHandler#addMstatFastTask] get performance data failed, parking event: {}，" +
                            "upload request: {}, error: {}",
                    JacksonUtil.serialize(bizEventParking), JacksonUtil.serialize(fastUploadTaskRequest), e);
        }
    }

    /**
     * 判断是否为仿真测试，仿真测试的障碍物的id非常大，达到1千万以上，正常行驶，目前看到最大的障碍物id为10多万
     * 两者没有交集，这里取阈值为2千万
     */
    public boolean isSimulationTest(Map<String, Object> eventMsg) {
        if (!eventMsg.containsKey("controlArbitrationInfo")) {
            return false;
        }
        Map<String, Object> arbitrationInfo = (Map<String, Object>) eventMsg.get("controlArbitrationInfo");
        if (!arbitrationInfo.containsKey("obstacleInfo")) {
            return false;
        }
        Map<String, Object> obstacleInfo = (Map<String, Object>) arbitrationInfo.get("obstacleInfo");
        if (!obstacleInfo.containsKey("obstacleMetric")) {
            return false;
        }
        List<Map<String, Object>> obstacleMetric = (List<Map<String, Object>>) obstacleInfo.get("obstacleMetric");
        for (Map<String, Object> objectMap : obstacleMetric) {
            if (!objectMap.containsKey("obstacleId")) {
                continue;
            }
            long obstacleId = Long.parseLong(objectMap.get("obstacleId").toString());
            if (obstacleId > SIMULATION_OBSTACLE_MIN_ID) {
                return true;
            }
        }

        return false;
    }
}
