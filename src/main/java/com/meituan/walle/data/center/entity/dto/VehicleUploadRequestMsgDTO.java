package com.meituan.walle.data.center.entity.dto;

import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/28 14:21
 */
@Data
public class VehicleUploadRequestMsgDTO {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 车架号（车辆设备id）
     */
    private String vin;
    /**
     * 起始时间
     */
    private Date start;
    /**
     * 终止时间
     */
    private Date end;
    /**
     * 底盘时间（case 发生时间）
     */
    private Long measurementTimestamp;
    /**
     * module列表(以英文逗号分隔)
     */
    private String module;
    /**
     * 快速上传额外信息
     */
    private String extraInfo;
    /**
     * 0.初始|10.处理中|100.完成
     */
    private Integer status;
    /**
     * 状态描述
     */
    private String statusDesc;
    private Integer priority;
    private Boolean oneCase;
    private Integer isDiscern;
    private String uuid;
    private Date createTime;
    private Date updateTime;
    private String recordName;
    /**
     * 根据record_name + intervention_time生成的caseId
     */
    private String interventionId;
    /**
     * 事件ID
     */
    private String eventId;
    /**
     * 任务类别，1.实时Case回传|2.事故数据回传|3.指定模块基础数据回传|4用户自定义数据回传
     */
    private Integer taskType;
    /**
     * 是否删除
     */
    private Boolean isDeleted;
    /**
     * 创建人
     */
    private String creator;
    private String taskMd5;
    private String packageVersion;
    private String gitBranch;
    private String gitCommit;
    private String hdmapVersion;
    private String hdmapName;
    private String admapName;
    /**
     * 高精地图版本
     */
    private String admapVersion;
    /**
     * 传到MQ中的补充信息Map
     */
    private Map<String, Object> content;

    /**
     * 任务上传耗时，单位s
     */
    private Integer costTime;
}
