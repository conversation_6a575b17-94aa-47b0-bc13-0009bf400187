package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

public class DiskListConf {
    /**
     * 硬盘列表黑名单设置，多个以;分割
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "disk.blacklist")
    public static volatile String blacklist = "65448100f3817e00;5000c500b0af32f4";

    /**
     * 硬盘列表黑名单设置，多个以;分割
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "disk.whitelist")
    public static volatile String whitelist = "ata-2.5__SATA_SSD_3MG2-P_.*";
}
