package com.meituan.walle.data.center.entity.dto;

import com.meituan.walle.data.center.entity.pojo.SparkJobInstance;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Data
@Slf4j
public class SparkJobInstanceDTO {
    private Integer id;
    private String instanceUuid;
    private String jobUuid;
    private String testArgs;
    private Integer status;
    private Integer runTimes;
    private Date updateTime;
    private Date createTime;

    public SparkJobInstanceDTO fromEntity(SparkJobInstance sparkJobInstance) {
        try {
            Field[] instanceFields = sparkJobInstance.getClass().getDeclaredFields();
            Map<String, Object> valueMap = new HashMap<>();
            for (Field field : instanceFields) {
                field.setAccessible(true);
                valueMap.put(field.getName(), field.get(sparkJobInstance));
            }

            Field[] fields = this.getClass().getDeclaredFields();
            for (Field field : fields) {
                String fieldName = field.getName();
                if (!"log".equals(fieldName)) {
                    field.setAccessible(true);
                    field.set(this, valueMap.get(fieldName));
                }
            }
        } catch (Exception e) {
            log.info("复制SparkJobInstance到SparkJobInfoDTO失败");
            log.error("复制SparkJobInstance到SparkJobInfoDTO失败", e);
        }
        return this;
    }
}
