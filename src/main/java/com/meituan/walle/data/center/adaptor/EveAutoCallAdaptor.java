package com.meituan.walle.data.center.adaptor;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.walle.data.center.exception.RemoteErrorException;
import com.sankuai.carosscan.request.AutoCallRequest;
import com.sankuai.carosscan.response.AutoCallVO;
import com.sankuai.carosscan.service.IThriftAutoCallService;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EveAutoCallAdaptor {

    @MdpThriftClient(remoteAppKey = "com.sankuai.carosscan.common.output", timeout = 2000)
    private IThriftAutoCallService iThriftAutoCallService;

    /**
     * 事故自动外呼场景标识
     */
    private static final String CATEGORY = "ACCIDENT";

    /**
     * 自动外呼
     * @param businessCallId
     * @param callMisList
     * @param mediaBody
     */
    public void autoCall(String businessCallId, List<String> callMisList, String mediaBody) {
        AutoCallRequest autoCallRequest = AutoCallRequest.builder().businessCallId(businessCallId)
                .callMisList(callMisList).category(CATEGORY).mediaBody(mediaBody).build();
        log.info("EveAutoCallAdaptor#autoCall, autoCallRequest:{}", autoCallRequest);
        if (Objects.isNull(autoCallRequest)) {
            log.info("EveAutoCallAdaptor#autoCallRequest is null");
            return;
        }
        try {
            EveThriftResponse<AutoCallVO> response = iThriftAutoCallService.autoCall(autoCallRequest);
            log.info("EveAutoCallAdaptor#autoCall, response:{}", response);
            if (Objects.isNull(response) || response.getCode() != 0) {
                throw new RemoteErrorException("autoCall error");
            }
        } catch (Exception e) {
            log.error("EveAutoCallAdaptor#autoCall error", e);
        }
    }

}
