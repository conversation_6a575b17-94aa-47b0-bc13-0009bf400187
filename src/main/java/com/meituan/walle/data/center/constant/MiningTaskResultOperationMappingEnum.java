package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/04/24
 */
@Getter
@AllArgsConstructor
public enum MiningTaskResultOperationMappingEnum {
    UNKNOWN(-1, "未知"),
    UPDATE_TYPE(0, "修改类型"),
    UPDATE_TAG(1, "修改标签");

    private int code;
    private String msg;

    public static MiningTaskResultOperationMappingEnum byOrdinal(int ord) {
        for (MiningTaskResultOperationMappingEnum e : MiningTaskResultOperationMappingEnum.values()) {
            if (e.code == ord) {
                return e;
            }
        }
        return UNKNOWN;
    }
}