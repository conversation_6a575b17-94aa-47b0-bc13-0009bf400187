package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.dal.recordmanager.entity.BRecordFile;
import com.meituan.walle.data.center.dal.recordmanager.entity.BRecordPkg;
import com.meituan.walle.data.center.dal.recordmanager.mapper.BRecordFileMapper;
import com.meituan.walle.data.center.dal.recordmanager.mapper.BRecordPkgMapper;
import com.meituan.walle.data.center.entity.params.CompressionAddParam;
import com.meituan.walle.data.center.entity.params.S3CleanupFileParam;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.handle.impl.S3FileCleanupHandler;
import com.meituan.walle.data.center.service.RecordFileV2Service;
import com.meituan.walle.data.center.service.RecordPkgService;
import com.meituan.walle.data.center.util.JacksonUtil;
import com.meituan.walle.data.center.util.SparkUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Spark[S3FileTransferBetweenClustersByRecordFile] s3存量数据迁移调用
 */
@Slf4j
@RestController
@RequestMapping("/s3Migrate")
public class S3MigrateController {

    @Autowired
    private S3FileCleanupHandler s3FileCleanHandler;

    @Autowired
    private RecordFileV2Service recordFileV2Service;

    @Autowired
    private RecordPkgService recordPkgService;

    @Autowired
    BRecordFileMapper bRecordFileMapper;

    @Autowired
    BRecordPkgMapper bRecordPkgMapper;

    @PostMapping("/cleanup/addBatch")
    public CommonResponse cleanupFileAddBatch(@RequestBody List<S3CleanupFileParam> cleanupFileParamList) {
        if (CollectionUtils.isEmpty(cleanupFileParamList)) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "The param cannot be null or empty");
        }
        try {
            Integer rows = s3FileCleanHandler.insertBatch(cleanupFileParamList);
            if (cleanupFileParamList.size() != rows) {
                log.info("[/s3Migrate/cleanup/addBatch] cleanupFileParamList.size() != rows; input: {}, output: {}, cleanupFileParamList: {}",
                        cleanupFileParamList.size(), rows, JacksonUtil.serialize(cleanupFileParamList));
            }
            String data = String.format("The file list contains %d files. Successfully inserted %d rows into table. ",
                    cleanupFileParamList.size(), rows);
            log.info("[/s3Migrate/cleanup/addBatch] result: {}, param: {}", data, JacksonUtil.serialize(cleanupFileParamList));
            return CommonResponse.success(rows);
        } catch (Exception e) {
            log.error("[/s3Migrate/cleanup/addBatch] exception: {}, param: {}",
                    e.getMessage(), JacksonUtil.serialize(cleanupFileParamList), e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/recordFile/updateCluster")
    public CommonResponse recordFileUpdateCluster(@RequestBody List<CompressionAddParam> paramList) { // 复用CompressionAddParam
        if (CollectionUtils.isEmpty(paramList)) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "The param cannot be null or empty");
        }
        try {
            List<BRecordFile> recordFileList = paramList.stream()
                    .map(param -> BRecordFile.builder()
                            .recordName(param.getRecordName())
                            .fileName(param.getFileName())
                            .filePath(param.getFilePath())
                            .s3Url(param.getS3Url())
                            .build())
                    .collect(Collectors.toList());
            Integer rows = recordFileV2Service.updateCluster(recordFileList);
            if (recordFileList.size() != rows) {
                log.info("[/s3Migrate/recordFile/updateCluster] recordFileList.size() != rows; input: {}, output: {}, recordFileList: {}",
                        recordFileList.size(), rows, JacksonUtil.serialize(recordFileList));
            }
            String data = String.format("The file list contains %d files. Successfully updated %d rows to table. ",
                    paramList.size(), rows);
            log.info("[/s3Migrate/recordFile/updateCluster] result: {}, param: {}", data, JacksonUtil.serialize(paramList));
            return CommonResponse.success(rows);
        } catch (Exception e) {
            log.error("[/s3Migrate/recordFile/updateCluster] exception: {}, param: {}",
                    e.getMessage(), JacksonUtil.serialize(paramList), e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/recordPkg/updateCluster")
    public CommonResponse recordPkgUpdateCluster(@RequestBody List<CompressionAddParam> paramList) { // 复用CompressionAddParam
        if (CollectionUtils.isEmpty(paramList)) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "The param cannot be null or empty");
        }
        try {
            List<BRecordPkg> recordPkgList = paramList.stream()
                    .map(param -> BRecordPkg.builder()
                            .s3Url(param.getS3Url())
                            .build())
                    .collect(Collectors.toList());
            Integer rows = recordPkgService.updateCluster(recordPkgList);
            if (recordPkgList.size() != rows) {
                log.info("[/s3Migrate/recordPkg/updateCluster] recordPkgList.size() != rows; input: {}, output: {}, recordPkgList: {}",
                        recordPkgList.size(), rows, JacksonUtil.serialize(recordPkgList));
            }
            String data = String.format("The file list contains %d files. Successfully updated %d rows to table. ",
                    paramList.size(), rows);
            log.info("[/s3Migrate/recordPkg/updateCluster] result: {}, param: {}", data, JacksonUtil.serialize(paramList));
            return CommonResponse.success(rows);
        } catch (Exception e) {
            log.error("[/s3Migrate/recordPkg/updateCluster] exception: {}, param: {}",
                    e.getMessage(), JacksonUtil.serialize(paramList), e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

    @GetMapping("/validation")
    public CommonResponse validation(@RequestParam String startDateKey, @RequestParam String endDateKey) {
        try {
            List<String> recordFileList = bRecordFileMapper.selectMigrateRecordNameByRecordDate(startDateKey, endDateKey);
            List<BRecordPkg> recordPkgList = bRecordPkgMapper.selectRecordPkgByRecordDate(startDateKey, endDateKey);
            Map<String, List<BRecordPkg>> recordPkgMap = recordPkgList.stream().collect(Collectors.groupingBy(BRecordPkg::getRecordName));
            String result = String.format("startDateKey: %s, endDateKey: %s, recordFileList.size: %d, recordPkgList.size: %d, " +
                            "recordFile2recordNameList: %s, recordPkg2recordNameList.size: %d, recordPkg2recordNameList: %s",
                    startDateKey, endDateKey, recordFileList.size(), recordPkgList.size(),
                    JacksonUtil.serialize(recordFileList), recordPkgMap.keySet().size(), JacksonUtil.serialize(recordPkgMap.keySet()));
            return CommonResponse.success(result);
        } catch (Exception e) {
            log.error("[/s3Migrate/validation] exception: {}, startDateKey: {}, endDateKey: {}",
                    e.getMessage(), startDateKey, endDateKey, e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/job/submit")
    public CommonResponse jobSubmit(@RequestBody Map<String, Object> jobParamMap) {
        try {
            log.info("[/s3Migrate/job/submit] start submit, jobParamMap: {}", JacksonUtil.serialize(jobParamMap));
            Map<String, Object> resultMap = SparkUtil.startSparkJob(jobParamMap);
            log.info("[/s3Migrate/job/submit] submit finished, jobParamMap: {}, resultMap: {}",
                    JacksonUtil.serialize(jobParamMap), JacksonUtil.serialize(resultMap));
            return CommonResponse.success("submit finished, resultMap: " + JacksonUtil.serialize(resultMap));
        } catch (Exception e) {
            log.error("[/s3Migrate/job/submit] exception: {}, jobParamMap: {}",
                    e.getMessage(), JacksonUtil.serialize(jobParamMap), e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

    @GetMapping("/job/status")
    public CommonResponse jobStatus(@RequestParam Integer executeId) {
        try {
            Map<String, Object> resultMap = SparkUtil.querySparkJobStatus(executeId);
            return CommonResponse.success("job status: " + JacksonUtil.serialize(resultMap));
        } catch (Exception e) {
            log.error("[/s3Migrate/job/status] exception: {}, executeId: {}", e.getMessage(), executeId, e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

}