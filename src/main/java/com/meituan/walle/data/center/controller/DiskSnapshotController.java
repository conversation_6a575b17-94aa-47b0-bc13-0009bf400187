package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.service.DiskSnapshotService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/disk_snapshot")
public class DiskSnapshotController {
    @Resource
    private DiskSnapshotService diskSnapshotService;

}
