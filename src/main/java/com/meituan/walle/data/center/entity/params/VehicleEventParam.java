package com.meituan.walle.data.center.entity.params;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/06/28
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleEventParam {

    private Integer noticeType;
    private Long sendTimestamp;
    private Long transmitTimestamp;
    private String vin;
    private List<String> message;
    private String datasource;

}
