package com.meituan.walle.data.center.entity.dto;

import com.sankuai.walle.wcdp.core.entity.page.Page;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/03/22
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MiningTaskDTO extends Page implements Serializable {

    private String taskId;
    private String beginTime;
    private String endTime;
    private String taskNameEn;
    private String taskCreator;

    private String taskNameCh;
    private String taskRuleDescription;
    private String taskMiningOwner;
    private Integer taskTimes;

    private String taskNameEnAccurateMatching;

}
