package com.meituan.walle.data.center.entity.po;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> @Beijing
 * @date 2022/03/21
 * Description:
 * Modified by
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RecordVersionPO implements Serializable {
    @JsonIgnore
    private Long id;
    private String versionId;
    private String commitId;
    private String moduleList;
    private String recordName;
    private Long startTime;
    private Long endTime;
    private String vehicleName;
    private String outputHdfsPath;
    private Integer status;
    @JsonIgnore
    private Integer isDeleted;
    @JsonIgnore
    private Date createTime;
    @JsonIgnore
    private Date updateTime;

    @Override
    public String toString() {
        return "RecordVersionPO{" +
                "id=" + id +
                ", versionId='" + versionId + '\'' +
                ", commitId='" + commitId + '\'' +
                ", moduleList='" + moduleList + '\'' +
                ", recordName='" + recordName + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", vehicleName='" + vehicleName + '\'' +
                ", outputHdfsPath='" + outputHdfsPath + '\'' +
                ", status=" + status +
                ", isDeleted=" + isDeleted +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
