package com.meituan.walle.data.center.component;

import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class HdmapRange {
    private static Map<String, Map<String, double[]>> hdmapUtmRange;
    private static final String YIZHUANG = "yizhuang";
    private static final String ZHONGLIHAI = "zhonglihai";
    private static final String MAPO = "mapo";

    static {
        hdmapUtmRange = new HashMap<>();

        Map<String, double[]> yizhuang = new HashMap<>();
        yizhuang.put("x", new double[]{453180.00d, 453861.9});
        yizhuang.put("y", new double[]{4398539.00d, 4399507.9d});
        hdmapUtmRange.put(YIZHUANG, yizhuang);

        Map<String, double[]> zhonglihai = new HashMap<>();
        zhonglihai.put("x", new double[]{461988.00d, 465342.9d});
        zhonglihai.put("y", new double[]{4445741.00d, 4447719.9d});
        hdmapUtmRange.put(ZHONGLIHAI, zhonglihai);

        Map<String, double[]> mapo = new HashMap<>();
        mapo.put("x", new double[]{469171.00d, 471673.9d});
        mapo.put("y", new double[]{4446501.00d, 4448540.9d});
        hdmapUtmRange.put(MAPO, mapo);

        hdmapUtmRange.put("", Collections.emptyMap());
    }

    public static double[] getXRange(String hdmap) {
        String place = getPlace(hdmap);
        return hdmapUtmRange.get(place).get("x");
    }

    public static double[] getYRange(String hdmap) {
        String place = getPlace(hdmap);
        return hdmapUtmRange.get(place).get("y");
    }

    private static String getPlace(String hdmap) {
        String hdmapLowerCase = hdmap.toLowerCase();
        if (hdmapLowerCase.indexOf(YIZHUANG) >= 0) {
            return YIZHUANG;
        } else if (hdmapLowerCase.indexOf(MAPO) >= 0) {
            return MAPO;
        } else if (hdmapLowerCase.indexOf(ZHONGLIHAI) >= 0
                || hdmapLowerCase.indexOf("shunyiceshichang") >= 0
                || hdmapLowerCase.indexOf("shunyi") >= 0) {
            return ZHONGLIHAI;
        } else {
            log.warn("not found utm range for hdmap {}", hdmap);
            return "";
        }
    }
}
