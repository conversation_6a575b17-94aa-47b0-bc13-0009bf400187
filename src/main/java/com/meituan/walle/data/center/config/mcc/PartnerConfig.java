package com.meituan.walle.data.center.config.mcc;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.meituan.config.MtConfigClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2019/7/2 11:00
 */
@Slf4j
@Component
public class PartnerConfig {
    private static final String PARTNER_KEY = "partners";
    private String localPartners = "{\"partners\": {}}";
    private Map<String, String> partners;

    @Resource
    private MtConfigClient mtConfigClient;

    @PostConstruct
    public void init() {
        String value = mtConfigClient.getValue(PARTNER_KEY);
        if (StringUtils.isBlank(value)) {
            value = localPartners;
        }
        update(value);

        mtConfigClient.addListener(PARTNER_KEY, (key, oldValue, newValue) -> {
            log.info("config {} change to {}, old value is {}", key, newValue, oldValue);
            update(newValue);
        });
    }

    public String getAccessKey(String accessName) {
        return partners.get(accessName);
    }

    private void update(String txt) {
        if (StringUtils.isBlank(txt)) {
            log.warn("config value is empty: {}", PARTNER_KEY);
        } else {
            synchronized (PartnerConfig.class) {
                partners = (Map<String, String>) JSONObject.parseObject(txt).get(PARTNER_KEY);
            }
        }
    }

}
