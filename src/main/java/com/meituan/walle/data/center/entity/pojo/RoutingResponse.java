package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "routing_response")
public class RoutingResponse {
    /**
     * 自增主键
     */
    @Id
    @Column(name = "primary_id")
    private Long primaryId;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 长度
     */
    private Double distance;

    /**
     * 高精地图版本
     */
    @Column(name = "hdmap_version")
    private String hdmapVersion;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 获取自增主键
     *
     * @return primary_id - 自增主键
     */
    public Long getPrimaryId() {
        return primaryId;
    }

    /**
     * 设置自增主键
     *
     * @param primaryId 自增主键
     */
    public void setPrimaryId(Long primaryId) {
        this.primaryId = primaryId;
    }

    /**
     * 获取表record的record_name外键
     *
     * @return record_name - 表record的record_name外键
     */
    public String getRecordName() {
        return recordName;
    }

    /**
     * 设置表record的record_name外键
     *
     * @param recordName 表record的record_name外键
     */
    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    /**
     * 获取长度
     *
     * @return distance - 长度
     */
    public Double getDistance() {
        return distance;
    }

    /**
     * 设置长度
     *
     * @param distance 长度
     */
    public void setDistance(Double distance) {
        this.distance = distance;
    }

    /**
     * 获取高精地图版本
     *
     * @return hdmap_version - 高精地图版本
     */
    public String getHdmapVersion() {
        return hdmapVersion;
    }

    /**
     * 设置高精地图版本
     *
     * @param hdmapVersion 高精地图版本
     */
    public void setHdmapVersion(String hdmapVersion) {
        this.hdmapVersion = hdmapVersion;
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}