package com.meituan.walle.data.center.config;

import com.meituan.mtrace.http.HttpAsyncClients;
import com.meituan.mtrace.http.HttpClients;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @data 2019/4/23 14:26
 */
@Configuration
public class HttpClientConfig {
    @Bean
    public CloseableHttpClient createHttpClient() {
        return HttpClients.createDefault();
    }

    @Bean
    public CloseableHttpAsyncClient createAsyncHttpClient() {
        return HttpAsyncClients.createDefault();
    }

}
