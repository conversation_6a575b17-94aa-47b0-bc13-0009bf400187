package com.meituan.walle.data.center.entity.pojo;

import lombok.Builder;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/11/18
 */
@Builder
@Data
@Table(name = "big_circumvent_offline")
public class BigCircumventOffline {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 接管id
     */
    @Column(name = "intervention_id")
    private String interventionId;

    /**
     * 车架号
     */
    @Column(name = "vin")
    private String vin;

    /**
     * 障碍物信息
     */
    @Column(name = "obstacle_info")
    private String obstacleInfo;

    /**
     * record name
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 是否逻辑删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}

