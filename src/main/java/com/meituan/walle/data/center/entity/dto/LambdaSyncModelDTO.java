package com.meituan.walle.data.center.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meituan.walle.data.center.constant.DateTimeFormatterConstant;
import lombok.Data;

import java.util.Date;

@Data
public class LambdaSyncModelDTO {
    private Long id;
    private String modelId;
    private String name;
    private String targetTable;
    private String uniqueColumns;
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateTimeFormatterConstant.ymdhms, timezone="GMT+8")
    private Date createTime;
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern=DateTimeFormatterConstant.ymdhms, timezone="GMT+8")
    private Date updateTime;
}
