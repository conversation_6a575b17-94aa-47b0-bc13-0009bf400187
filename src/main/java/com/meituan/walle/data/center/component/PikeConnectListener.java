package com.meituan.walle.data.center.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.RedisConstant;
import com.meituan.walle.data.center.entity.vo.PikeConnectInfo;
import com.meituan.walle.data.center.handle.IRedisHandler;
import com.meituan.walle.data.center.service.VehicleInfoService;
import com.meituan.walle.data.center.service.VresvRecordHistoryService;
import com.meituan.walle.data.center.util.CommonUtil;
import com.sankuai.pike.message.api.common.enums.DeviceStatusEnum;
import com.sankuai.pike.message.api.common.enums.MessagePushStatusEnum;
import com.sankuai.pike.message.api.rpc.business.entity.ConnectRequest;
import com.sankuai.pike.message.api.rpc.business.entity.ConnectResponse;
import com.sankuai.pike.message.api.rpc.business.entity.DisconnectRequest;
import com.sankuai.pike.message.sdk.PikeMessageServerClient;
import com.sankuai.pike.message.sdk.listener.ConnectListener;
import com.sankuai.pike.message.sdk.lite.entity.MessageRequest;
import com.sankuai.pike.message.sdk.lite.entity.MessageResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;

@Component
@Log4j2
public class PikeConnectListener implements ConnectListener {
    private static final ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("PikeConnectListener-%d").build();
    private static final ThreadPoolExecutor THREAD_POOL =
            new ThreadPoolExecutor(32, 32, 0,
                    TimeUnit.SECONDS, new ArrayBlockingQueue<>(10000), namedThreadFactory);

    @Resource
    private VehicleInfoService vehicleInfoService;

    @Resource
    private IRedisHandler redisHandler;

    @Resource
    private VresvRecordHistoryService vresvRecordHistoryService;

    private static final int SLEEP_MILLIS = 100;

    private static final int TRY_TIMES = 5;

    private static final int PIKE_TIMEOUT = 5;

    @Override
    public ConnectResponse onConnect(ConnectRequest connectRequest) {
        // 连接登录，开始自定义鉴权。
        // 如果response.success设置为false，客户端将登录建连失败。
        ConnectResponse response = new ConnectResponse();
        String alias = connectRequest.getAlias();
        if (StringUtils.isBlank(alias)) {
            response.setSuccess(false);
            return response;
        }
        String token = connectRequest.getToken();
        String bizId = connectRequest.getBizId();
        log.info("连接建立, alias(用户ID): {}, token: {}", alias, token);

        response.setAlias(alias);
        response.setBizId(bizId);

        Map<String, String> extra = connectRequest.getExtra();
        String vin = extra.get("vin");
        String deviceId = connectRequest.getDeviceId();
        String vn = vehicleInfoService.getVehicleName(vin);
        if (StringUtils.isBlank(vin) || StringUtils.isBlank(vn)) {
            log.info("传入的车架号为空, 连接无效, vin: {}, vehicleName: {}, alias: {}, token: {}, deviceId: {}",
                    vin, vn, alias, token, deviceId);
            response.setSuccess(false);
            return response;
        }
        String vehicleName = vn.toLowerCase();

        PikeConnectInfo pikeConnectInfo = new PikeConnectInfo();
        BeanUtils.copyProperties(connectRequest, pikeConnectInfo);
        pikeConnectInfo.setVin(vin);
        String value = JSON.toJSONString(pikeConnectInfo);


        int i = 0;
        // 获取一下锁，这样不会引起发送失败删除缓存时误删新建立的连接
        while (i < TRY_TIMES || !redisHandler.setIfNotExists(RedisConstant.PIKE_DEVICE_CONNECT_LOCK,
                pikeConnectInfo.getDeviceId(), 0, SysParamsConfig.event_cache_seconds)) {
            i++;
            try {
                Thread.sleep(SLEEP_MILLIS);
            } catch (InterruptedException e) {
                log.error("PIKE_DEVICE_CONNECT_LOCK InterruptedException", e);
                Thread.currentThread().interrupt();
            }
        }
        redisHandler.set(RedisConstant.PIKE_CONNECTION, vin, deviceId, value,
                SysParamsConfig.device_connection_cache_seconds);
        redisHandler.delete(RedisConstant.PIKE_DEVICE_CONNECT_LOCK, pikeConnectInfo.getDeviceId());

        log.info("连接信息, token: {}, vehicleName: {}, vin: {}, deviceId: {}", token, vehicleName, vin, deviceId);
        response.setSuccess(true);

        sendEventToFreshDevice(vin, vehicleName, pikeConnectInfo);
        return response;
    }

    @Override
    public void onDisconnect(DisconnectRequest disconnectRequest) {
        // 连接断开
        String token = disconnectRequest.getToken();
        log.info("连接断开, alias(用户ID): {}, token: {}", disconnectRequest.getAlias(), token);
    }

    public Set<PikeConnectInfo> getConnectionSet(String vin) {
        Map<String, Object> tokenMapConnection = redisHandler.getAllByKey(RedisConstant.PIKE_CONNECTION, vin);
        if (tokenMapConnection == null || tokenMapConnection.isEmpty()) {
            return Sets.newHashSet();
        }
        Set<PikeConnectInfo> pikeConnectInfoSet = new HashSet<>();
        tokenMapConnection.forEach((key, value) -> {
            PikeConnectInfo pikeConnectInfo = JSON.parseObject(value.toString(), PikeConnectInfo.class);
            pikeConnectInfoSet.add(pikeConnectInfo);
        });
        return pikeConnectInfoSet;
    }

    public PikeConnectInfo getConnection(String vin, String deviceId) {
        String connectJson = redisHandler.hGet(RedisConstant.PIKE_CONNECTION, vin, deviceId);
        return JSON.parseObject(connectJson, PikeConnectInfo.class);
    }

    public void sendEventToFreshDevice(String vin, String vehicleName,
                                       PikeConnectInfo pikeConnectInfo) {
        while (THREAD_POOL.getQueue().remainingCapacity() == 0) {
            try {
                Thread.sleep(SLEEP_MILLIS);
            } catch (InterruptedException e) {
                log.warn("建立连接时，等待处理缓存消息时被打断", e);
                Thread.currentThread().interrupt();
            }
        }

        Set<PikeConnectInfo> pikeConnectInfoSet = new HashSet<>();
        pikeConnectInfoSet.add(pikeConnectInfo);
        THREAD_POOL.submit(() -> {
            String waitingEmitEvent = redisHandler.get(RedisConstant.WAITING_EVENT, vin);
            if (waitingEmitEvent == null) {
                log.info("vehicleName: {}, vin: {} 没有数据需要下发打标签", vehicleName, vin);
                return;
            }
            String[] values = waitingEmitEvent.split(",");

            sendMessages(vin, values[0], Integer.parseInt(values[1]), pikeConnectInfoSet);
        });
    }

    public void sendFreshEvent(String vin, String eventId, int interventionType) {
        Set<PikeConnectInfo> pikeConnectInfoSet = getConnectionSet(vin);
        if (pikeConnectInfoSet.isEmpty()) {
            log.info("vin: {}, eventId: {} 对应的数据没有连接, 直接返回", vin, eventId);
            return;
        }

        sendMessages(vin, eventId, interventionType, pikeConnectInfoSet);
    }

    public void sendCloseWindow(String vin, String eventId) {
        THREAD_POOL.submit(() -> {
            Set<PikeConnectInfo> pikeConnectInfoSet = getConnectionSet(vin);
            if (pikeConnectInfoSet.isEmpty()) {
                log.info("vin: {}, eventId: {} 对应的数据没有连接, 无需下发弹窗", vin, eventId);
                return;
            }
            String content = buildCloseContent(eventId);

            for (PikeConnectInfo pikeConnectInfo : pikeConnectInfoSet) {
                boolean sendResult = sendMessage(pikeConnectInfo, content);

                if (!sendResult) {
                    redisHandler.del(RedisConstant.PIKE_CONNECTION, pikeConnectInfo.getVin(),
                            pikeConnectInfo.getDeviceId());
                }
            }
        });
    }


    public void sendMessages(String vin, String eventId, int interventionType,
                             Set<PikeConnectInfo> pikeConnectInfoSet) {
        int sendCnt = pikeConnectInfoSet.size();
        Set<PikeConnectInfo> failedSet = new HashSet<>();
        Set<PikeConnectInfo> repeatEmitSet = new HashSet<>();
        Set<PikeConnectInfo> authFailed = new HashSet<>();
        String content = buildEventContent(vin, eventId, interventionType);
        for (PikeConnectInfo pikeConnectInfo : pikeConnectInfoSet) {
            String eventIdDeviceLock = String.format("%s,%s", eventId, pikeConnectInfo.getDeviceId());
            boolean eventEmitDeviceLock = redisHandler.setIfNotExists(RedisConstant.EVENT_DEVICE_LOCK,
                    eventIdDeviceLock, 0, SysParamsConfig.device_connection_cache_seconds * 2);
            if (!eventEmitDeviceLock) {
                repeatEmitSet.add(pikeConnectInfo);
                log.warn("设备正在或者已经下发过该事件了，无需再下发: {}", pikeConnectInfo);
                continue;
            }
            boolean sendResult = sendMessage(pikeConnectInfo, content);
            if (!sendResult) {
                redisHandler.delete(RedisConstant.EVENT_DEVICE_LOCK, eventIdDeviceLock);
                //如果获取不到锁，则不用删除该连接信息，说明此时此刻正在添加新的连接
                if (redisHandler.setIfNotExists(RedisConstant.PIKE_DEVICE_CONNECT_LOCK,
                        pikeConnectInfo.getDeviceId(), 0, SysParamsConfig.event_cache_seconds)) {
                    String token = pikeConnectInfo.getToken();
                    PikeConnectInfo maybeFresh = getConnection(vin, pikeConnectInfo.getDeviceId());
                    if (PikeMessageServerClient.checkConnection(token, PIKE_TIMEOUT) == DeviceStatusEnum.OFFLINE
                            && maybeFresh.getToken().equals(token)) {
                        // 发送失败，则把缓存的连接信息删除
                        redisHandler.del(RedisConstant.PIKE_CONNECTION, pikeConnectInfo.getVin(),
                                pikeConnectInfo.getDeviceId());
                    }
                    //删除该锁
                    redisHandler.delete(RedisConstant.PIKE_DEVICE_CONNECT_LOCK, pikeConnectInfo.getDeviceId());
                }
                failedSet.add(pikeConnectInfo);
            }
        }

        log.info("消息发送总设备数: {}, 失败设备数: {}, 重复发送设备数: {}, 鉴权失败数: {}, " +
                        "失败信息:{}, 重复发送信息: {}, 鉴权失败信息: {}, 连接信息: {}, 消息内容: {}",
                sendCnt, failedSet.size(), repeatEmitSet.size(), authFailed.size(),
                failedSet, repeatEmitSet, authFailed, pikeConnectInfoSet, content);
    }

    public boolean sendMessage(PikeConnectInfo pikeConnectInfo, String content) {
        MessageRequest request = MessageRequest.newTokenMessageRequest(pikeConnectInfo.getBizId(),
                pikeConnectInfo.getToken());
        request.setStringContent(content);

        boolean emitResult = CommonUtil.tryTimesIfFailed(TRY_TIMES, SLEEP_MILLIS, () -> {
            MessageResponse messageResponse = PikeMessageServerClient.sendMessageToSDK(request);
            MessagePushStatusEnum result = messageResponse.getResult();
            boolean isSuccess = result == MessagePushStatusEnum.ACK_SUCCESS;
            if (!isSuccess) {
                PikeConnectInfo maybeFresh = getConnection(pikeConnectInfo.getVin(),
                        pikeConnectInfo.getDeviceId());
                //如果在发送过程中老链接断开了，新链接来了，则应该使用新链接发送数据
                if (!maybeFresh.getToken().equals(pikeConnectInfo.getToken())) {
                    request.setStringContent(maybeFresh.getToken());
                }

                String exString = String.format("sendMessageByToken failed: %s, %s, %s",
                        pikeConnectInfo, content, result.desc);
                log.info("send msg failed: {}", exString);
            }
            return isSuccess;
        });

        if (!emitResult) {
            log.warn("尝试多次后单个发送消息最终失败: {}, {}", pikeConnectInfo, content);
        }
        return emitResult;
    }

    public String buildEventContent(String vin, String eventId, int interventionType) {
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("msgType", 1);
        contentMap.put("vin", vin);
        contentMap.put("eventId", eventId);
        contentMap.put("interventionType", interventionType);
        return JSON.toJSONString(contentMap);
    }

    public String buildCloseContent(String eventId) {
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("msgType", 0);
        contentMap.put("eventId", eventId);
        return JSON.toJSONString(contentMap);
    }

}