package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/11/05
 */
@Getter
@AllArgsConstructor
public enum FastUploadTaskTypeEnum {

    /**
     * 1.实时Case回传
     */
    CASE(1, "case"),
    /**
     * 2.事故数据回传
     */
    ACCIDENT(2, "accident"),
    /**
     * 3.指定模块基础数据回传
     */
    BASE(3, "base"),
    /**
     * 4.用户自定义数据回传
     */
    DEFINE(4, "define"),
    /**
     * 碰撞检测回传
     */
    COLLISION(5, "collision"),
    /**
     * 数据降采样回传
     */
    MAP_COLLECT_DOWN_SAMPLING(6, "map_collect_down_sampling"),
    /**
     * COREDUMP数据回传
     */
    COREDUMP(7, "coredump"),

    FILE_PATH(8, "file_path");

    private int code;
    private String msg;

    public static FastUploadTaskTypeEnum byOrdinal(int ord) {
        for (FastUploadTaskTypeEnum e : FastUploadTaskTypeEnum.values()) {
            if (e.code == ord) {
                return e;
            }
        }
        return null;
    }

}
