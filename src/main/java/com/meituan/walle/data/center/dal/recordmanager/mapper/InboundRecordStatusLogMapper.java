package com.meituan.walle.data.center.dal.recordmanager.mapper;

import com.meituan.walle.data.center.dal.recordmanager.entity.InboundRecordStatusLogPO;
import org.apache.ibatis.annotations.Insert;

public interface InboundRecordStatusLogMapper {

    @Insert({
            "<script>",
            "insert into inbound_record_status_log",
            "<trim prefix='(' suffix=')' suffixOverrides=','>",
            "<if test='recordName != null'>record_name,</if>",
            "<if test='inboundStatus != null'>inbound_status,</if>",
            "<if test='timestamp != null'>timestamp,</if>",
            "<if test='belongTo != null'>belong_to,</if>",
            "<if test='dateTime != null'>date_time,</if>",
            "</trim>",
            "values",
            "<trim prefix='(' suffix=')' suffixOverrides=','>",
            "<if test='recordName != null'>#{recordName},</if>",
            "<if test='inboundStatus != null'>#{inboundStatus},</if>",
            "<if test='timestamp != null'>#{timestamp},</if>",
            "<if test='belongTo != null'>#{belongTo},</if>",
            "<if test='dateTime != null'>#{dateTime},</if>",
            "</trim>",
            "</script>"
    })
    int insert(InboundRecordStatusLogPO inboundRecordStatusLogPO);

}
