package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "vehicle_speed")
@Data
public class VehicleSpeed {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    private Date driverTime;

    private Float meterSec;

    private Float totalTimeSec;

    private Float speed;

    private String vin;

    private String recordName;

    private Date createTime;

    private Date updateTime;
}