package com.meituan.walle.data.center.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/26
 */
@Data
@NoArgsConstructor
public class ProgressDTO implements Serializable {
    protected String progressType;
    protected Date startTime;
    protected Date endTime;
    protected Long totalSteps;
    protected Long currentStep;
    protected ProgressStatus progressStatus;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ProgressStatus {
        private String code;
        private String desc;

    }
}
