package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.net.HttpHeaders;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.*;
import com.meituan.walle.data.center.entity.dto.AutodriveEventDTO;
import com.meituan.walle.data.center.entity.enums.VehicleUploadTaskStatusEnum;
import com.meituan.walle.data.center.entity.params.AutodriveEventPageParam;
import com.meituan.walle.data.center.entity.po.FastRecordFilePO;
import com.meituan.walle.data.center.entity.pojo.RecordV2;
import com.meituan.walle.data.center.entity.pojo.VehicleUploadRequest;
import com.meituan.walle.data.center.entity.request.ListMvizDataRequest;
import com.meituan.walle.data.center.entity.request.MvizDataRangeRequest;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.entity.response.HttpResponse;
import com.meituan.walle.data.center.entity.response.MvizEventSegmentInfoResponse;
import com.meituan.walle.data.center.entity.vo.MvizDataListVO;
import com.meituan.walle.data.center.entity.vo.RecordCommonFileVO;
import com.meituan.walle.data.center.entity.vo.RecordFileRangeVO;
import com.meituan.walle.data.center.entity.vo.RecordFileResultVO;
import com.meituan.walle.data.center.handle.impl.RecordDownloadValidatorHandler;
import com.meituan.walle.data.center.handle.impl.S3DsnUrlHandler;
import com.meituan.walle.data.center.mapper.VehicleUploadRequestMapper;
import com.meituan.walle.data.center.service.*;
import com.meituan.walle.data.center.util.BaAuthUtil;
import com.meituan.walle.data.center.util.DatetimeUtil;
import com.meituan.walle.data.center.util.JacksonUtil;
import com.meituan.walle.data.center.util.S3SignatureUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/5
 */
@Slf4j
@RestController
@RequestMapping("/mviz")
public class MvizController {

    @Resource
    private VehicleRecFileService vehicleRecFileService;

    @Resource
    private MvizService mvizService;

    @Resource
    private RecordV2Service recordV2Service;

    @Resource
    private S3DsnUrlHandler s3DsnUrlHandler;

    @Resource
    private RecordDownloadValidatorHandler downloadValidatorHandler;

    @Resource
    private AutodriveEventService autodriveEventService;

    @Resource
    private VehicleUploadRequestMapper vehicleUploadRequestMapper;

    @Autowired
    private FastRecordFileService fastRecordFileService;

    /**
     * !! 临时接口，预计25年快速回传接入records版本管理系统后，这块重新设计
     * https://km.sankuai.com/collabpage/2638385712
     *
     * 给录屏服务、webviz、case工作台调用，用于分拣同学分拣case使用
     * 若仅快速回传的数据就绪时，返回event对应快速回传任务的时间区间信息；
     * 若离线回收的数据就绪时，返回event时间前后padding的时间区间信息；
     * 例如：接管事件，快速回传返回其匹配的降级事件所对应的时间区间；离线回收返回其接管事件本身所对应的时间区间
     * @param eventId 事件id
     * @return 时间区间、数据所属等信息
     */
    @GetMapping("/segment_info/event")
    public HttpResponse eventSegmentInfo(@RequestParam("event_id") String eventId,
                                         @RequestParam("record_name") String recordNameParam,
                                         @RequestParam("start_timestamp") Long startTimestampParam,
                                         @RequestParam("end_timestamp") Long endTimestampParam) {
        if (StringUtils.isEmpty(eventId) || StringUtils.isEmpty(recordNameParam) || Objects.isNull(startTimestampParam) || Objects.isNull(endTimestampParam)) {
            return new HttpResponse().code(WebResponseStatusEnum.PARAMETER_ERROR.getCode()).msg(WebResponseStatusEnum.PARAMETER_ERROR.getMsg());
        }
        try {
            RecordV2 recordV2 = mvizService.getRecordByRequest(recordNameParam, null, null);
            if (Objects.isNull(recordV2)) {
                return new HttpResponse().code(WebResponseStatusEnum.DATA_NOT_FOUND.getCode()).msg("record不存在，recordName: " + recordNameParam);
            }
            // 判断数据回收情况
            if (mvizService.checkOfflineDataReady(recordV2) &&
                    InboundStatusEnum.COMPLETED.getCode().equals(recordV2.getInboundStatus()) &&
                    startTimestampParam <= endTimestampParam &&
                    recordV2.getBeginTime().getTime() <= endTimestampParam &&
                    startTimestampParam <= recordV2.getEndTime().getTime()) { // 离线ready
                MvizEventSegmentInfoResponse response = MvizEventSegmentInfoResponse.builder()
                        .recordName(recordNameParam)
                        .belongTo(recordV2.getBelongTo())
                        .startTimestamp(startTimestampParam)
                        .endTimestamp(endTimestampParam)
                        .build();
                return new HttpResponse().code(WebResponseStatusEnum.MVIZ_SUCCESS.getCode()).msg(WebResponseStatusEnum.MVIZ_SUCCESS.getMsg()).result(response);
            }
            // 离线没有ready或者没有命中离线时间区间，找快速回传
            List<AutodriveEventDTO> autodriveEventDTOList = autodriveEventService.pageQuery(AutodriveEventPageParam.builder().eventId(eventId).build());
            if (CollectionUtils.isEmpty(autodriveEventDTOList)) { // event_id不存在，按照时间再去匹配
                return matchFastUploadTaskByTime(recordNameParam, startTimestampParam, endTimestampParam);
            }
            List<VehicleUploadRequest> vehicleUploadRequestList;
            vehicleUploadRequestList = vehicleUploadRequestMapper.selectByEventId(autodriveEventDTOList.get(0).getEventId());
            if (CollectionUtils.isEmpty(vehicleUploadRequestList)) { // 没有快速回传任务任务，按照时间再去匹配
                return matchFastUploadTaskByTime(recordNameParam, startTimestampParam, endTimestampParam);
            }
            if (VehicleUploadTaskStatusEnum.FINISHED_SUCCEED.getCode() == vehicleUploadRequestList.get(0).getStatus() ||
                    VehicleUploadTaskStatusEnum.FINISHED_DATA_MISS.getCode() == vehicleUploadRequestList.get(0).getStatus() ||
                    FastUploadTaskStatusEnum.INCOMPLETE_MIDDLE.getCode() == vehicleUploadRequestList.get(0).getStatus() ||
                    VehicleUploadTaskStatusEnum.FINISHED_MODULE_MISS.getCode() == vehicleUploadRequestList.get(0).getStatus()) { // 可用
                MvizEventSegmentInfoResponse response = MvizEventSegmentInfoResponse.builder()
                        .recordName(recordNameParam)
                        .belongTo(RecordBelongToEnum.FAST_COLLECT.getCode())
                        .startTimestamp(vehicleUploadRequestList.get(0).getStart().getTime())
                        .endTimestamp(vehicleUploadRequestList.get(0).getEnd().getTime())
                        .fastId(vehicleUploadRequestList.get(0).getId())
                        .fastIdList(String.valueOf(vehicleUploadRequestList.get(0).getId()))
                        .build();
                return new HttpResponse().code(WebResponseStatusEnum.MVIZ_SUCCESS.getCode()).msg(WebResponseStatusEnum.MVIZ_SUCCESS.getMsg()).result(response);
            } else if (VehicleUploadTaskStatusEnum.CANCELED.getCode() == vehicleUploadRequestList.get(0).getStatus() ||
                    VehicleUploadTaskStatusEnum.FINISHED_FAILED.getCode() == vehicleUploadRequestList.get(0).getStatus()) { // 不可用，按照时间再去匹配
                return matchFastUploadTaskByTime(recordNameParam, startTimestampParam, endTimestampParam);
            } else { // 等待回传
                return new HttpResponse().code(WebResponseStatusEnum.DATA_NOT_FOUND.getCode())
                        .msg("快速回传任务未完成，recordName: " + recordNameParam + ", eventId: " + eventId + ", fastId: " + vehicleUploadRequestList.get(0).getId());
            }
        } catch (Exception exception) {
            log.error("[/mviz/segment_info/event] exception: {}, eventId: {}, recordName: {}, startTimestamp: {}, endTimestamp: {}",
                    exception.getMessage(), eventId, recordNameParam, startTimestampParam, endTimestampParam, exception);
            return new HttpResponse().code(WebResponseStatusEnum.SYSTEM_ERROR.getCode()).msg(exception.getMessage());
        }
    }

    private HttpResponse matchFastUploadTaskByTime(String recordNameParam, Long startTimestampParam, Long endTimestampParam) {
        List<FastRecordFilePO> recordFileList = fastRecordFileService.queryFastRecordFile(
                null, recordNameParam, new Date(startTimestampParam), new Date(endTimestampParam),
                Collections.singletonList(String.valueOf(OnBoardTypeEnum.RECORD.getId())), null, null);
        recordFileList = recordFileList.stream().filter(file -> !"Routing".equals(file.getModule())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(recordFileList)) {
            return new HttpResponse().code(WebResponseStatusEnum.DATA_NOT_FOUND.getCode())
                    .msg("没有对应的快速回传任务，recordName: " + recordNameParam + ", startTimestamp: " + startTimestampParam + ", endTimestamp: " + endTimestampParam);
        }
        Set<String> uploadTaskIdSet = recordFileList.stream().map(FastRecordFilePO::getUploadTaskId).collect(Collectors.toSet());
        List<VehicleUploadRequest> uploadTaskList = uploadTaskIdSet.stream().map(id -> vehicleUploadRequestMapper.selectById(Long.valueOf(id))).collect(Collectors.toList());

        List<VehicleUploadRequest> availableTaskList = uploadTaskList.stream()
                .filter(task ->
                        VehicleUploadTaskStatusEnum.CANCELED.getCode() != task.getStatus() ||
                                VehicleUploadTaskStatusEnum.FINISHED_FAILED.getCode() != task.getStatus()) // 先过滤掉失败的任务
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(availableTaskList)) {
            return new HttpResponse().code(WebResponseStatusEnum.DATA_NOT_FOUND.getCode())
                    .msg("快速回传任务失败，recordName: " + recordNameParam + ", startTimestamp: " + startTimestampParam +
                            ", endTimestamp: " + endTimestampParam + ", fastIdList: " + String.join(CharConstant.CHAR_DD, uploadTaskIdSet));
        }

        List<VehicleUploadRequest> finishTaskList = availableTaskList.stream()
                .filter(task ->
                        VehicleUploadTaskStatusEnum.FINISHED_SUCCEED.getCode() == task.getStatus() ||
                                VehicleUploadTaskStatusEnum.FINISHED_DATA_MISS.getCode() == task.getStatus() ||
                                FastUploadTaskStatusEnum.INCOMPLETE_MIDDLE.getCode() == task.getStatus() ||
                                VehicleUploadTaskStatusEnum.FINISHED_MODULE_MISS.getCode() == task.getStatus()) // 再筛选出可用的任务
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(finishTaskList)) {
            Set<String> availableTaskIdSet = availableTaskList.stream().map(task -> String.valueOf(task.getId())).collect(Collectors.toSet());
            return new HttpResponse().code(WebResponseStatusEnum.DATA_NOT_FOUND.getCode())
                    .msg("快速回传任务未完成，recordName: " + recordNameParam + ", startTimestamp: " + startTimestampParam +
                            ", endTimestamp: " + endTimestampParam + ", fastIdList: " + String.join(CharConstant.CHAR_DD, availableTaskIdSet));
        }
        List<String> finishTaskIdList = finishTaskList.stream().map(task -> String.valueOf(task.getId())).collect(Collectors.toList());
        MvizEventSegmentInfoResponse response = MvizEventSegmentInfoResponse.builder()
                .recordName(recordNameParam)
                .belongTo(RecordBelongToEnum.FAST_COLLECT.getCode())
                .startTimestamp(startTimestampParam)
                .endTimestamp(endTimestampParam)
                .fastId(Long.valueOf(finishTaskIdList.get(0)))
                .fastIdList(String.join(CharConstant.CHAR_DD, finishTaskIdList))
                .build();
        return new HttpResponse().code(WebResponseStatusEnum.MVIZ_SUCCESS.getCode()).msg(WebResponseStatusEnum.MVIZ_SUCCESS.getMsg()).result(response);
    }

    @GetMapping("/fast_task_file")
    public HttpResponse listFastTaskFile(ListMvizDataRequest listMvizDataRequest, HttpServletRequest httpServletRequest) {
        long start = System.currentTimeMillis();
        Transaction t = Cat.newTransaction(CatConstant.API, CatConstant.FAST_TASK_FILE);
        try {
            String requestAuth = httpServletRequest.getHeader(HttpHeaders.AUTHORIZATION);
            String clientId = BaAuthUtil.getBasicAuthClientIdByRequestAuth(requestAuth);

            Long uploadTaskId = listMvizDataRequest.getFastId();
            if (uploadTaskId == null || uploadTaskId < 1) {
                return null;
            }
            VehicleUploadRequest vehicleUploadRequest = vehicleRecFileService.getById(uploadTaskId);
            if (vehicleUploadRequest == null) {
                return new HttpResponse().code(WebResponseStatusEnum.MVIZ_FAILED.getCode())
                        .msg("the vehicleUploadRequest does not exist in the record table!");
            }
            RecordV2 recordV2Info = null;
            if ((listMvizDataRequest.getForceDownload() == null || !listMvizDataRequest.getForceDownload()) &&
                    StringUtils.isNotBlank(vehicleUploadRequest.getRecordName())) {
                recordV2Info = recordV2Service.selectByRecordName(vehicleUploadRequest.getRecordName());
                if (recordV2Info != null) {
                    HttpResponse httpResponse = downloadValidatorHandler.checkRecordBeDownload(recordV2Info,
                            listMvizDataRequest.getMisId(),
                            clientId,
                            false,
                            new ArrayList<>());
                    if (httpResponse != null) {
                        return httpResponse;
                    }
                }
            }

            RecordFileResultVO resultVO = mvizService.listFastTaskFileById(listMvizDataRequest, vehicleUploadRequest,
                                                                           recordV2Info, clientId);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            if (resultVO == null) {
                return new HttpResponse().code(WebResponseStatusEnum.MVIZ_FAILED.getCode()).msg("file list is empty!");
            }
            resultVO.setIsFastUpload(Boolean.TRUE);

            HttpResponse s3DsnUrlCheckResponse = setS3DsnUrl(resultVO, clientId);
            if (s3DsnUrlCheckResponse != null) return s3DsnUrlCheckResponse;

            return new HttpResponse().code(WebResponseStatusEnum.MVIZ_SUCCESS.getCode()).result(resultVO);
        } catch (Exception e) {
            log.error("listFastTaskFile error: {}", listMvizDataRequest, e);
            Cat.logError(e);
            t.setStatus(e);
            return new HttpResponse().code(WebResponseStatusEnum.MVIZ_FAILED.getCode()).msg(e.getMessage());
        } finally {
            t.complete();
        }
    }

    private HttpResponse setS3DsnUrl(RecordFileResultVO resultVO, String clientId) {
        HttpResponse response = null;
        // 获取S3 DSN URL
        Set<String> s3DsnUrlSet = new HashSet<>();
        for (RecordCommonFileVO recordCommonFileVO : resultVO.getFiles()) {
            s3DsnUrlSet.add(recordCommonFileVO.getS3DsnUrl());
        }
        for (RecordCommonFileVO recordCommonFileVO : resultVO.getRecordFiles()) {
            s3DsnUrlSet.add(recordCommonFileVO.getS3DsnUrl());
        }
        Map<String, Object> s3DsnUrl = s3DsnUrlHandler.getS3DsnUrl(s3DsnUrlSet);
        // 校验S3 DSN URL
        String missingUrls = s3DsnUrl.entrySet().stream()
                .filter(entry -> entry.getValue() == null)
                .map(Map.Entry::getKey)
                .collect(Collectors.joining(CharConstant.CHAR_DD));
        if (!missingUrls.isEmpty()) {
            log.error("[MvizController#]listFastTaskFile]DSN URL:[{}], could not get connection info", missingUrls);
            response = new HttpResponse()
                    .code(WebResponseStatusEnum.MVIZ_S3_DSN_URL_COULD_NOT_GET_CONNECTION_INFO.getCode())
                    .msg(String.format(WebResponseStatusEnum.MVIZ_S3_DSN_URL_COULD_NOT_GET_CONNECTION_INFO.getMsg(), missingUrls));
        }

        // 临时限流方案
        if (!org.springframework.util.StringUtils.isEmpty(clientId) && SysParamsConfig.datalist_api_qps_limit_whitelist.contains(clientId)) {
            // 覆写
            if (Objects.nonNull(s3DsnUrl.get(S3DsnUrlEnum.ONBOARD_RECORD_DATA_ZW_MAD.getId()))) {
                Object dsnObject = s3DsnUrl.get(S3DsnUrlEnum.ONBOARD_RECORD_DATA_ZW_MAD.getId());
                if (Objects.nonNull(dsnObject)) {
                    Map<String, Object> dsnInfoMap = JacksonUtil.convertValue(dsnObject, Map.class);
                    if (!CollectionUtils.isEmpty(dsnInfoMap)) {
                        dsnInfoMap.put("s3_ak", S3SignatureUtil.getAccessKeyHadoop());
                        dsnInfoMap.put("s3_sk", S3SignatureUtil.getSecretKeyHadoop());
                    }
                }
            }
        }

        // 设置S3 DSN URL
        resultVO.setDsnUrlMap(s3DsnUrl);
        return response;
    }

    @GetMapping("/data_list")
    public HttpResponse dataList(ListMvizDataRequest request, HttpServletRequest httpServletRequest) {
        String requestAuth = httpServletRequest.getHeader(HttpHeaders.AUTHORIZATION);
        String clientId = BaAuthUtil.getBasicAuthClientIdByRequestAuth(requestAuth);
        Cat.logMetricForCount("data_list_request", ImmutableMap.of("type", "mviz", "clientId", clientId));
        try {
            String modules = request.getModules();
            String noModules = request.getNoModules();
            if (StringUtils.isNotBlank(modules) && StringUtils.isNotBlank(noModules)) {
                Set<String> moduleSet = new HashSet<>(Splitter.on(CharConstant.CHAR_DD).splitToList(modules));
                Set<String> noModuleSet = new HashSet<>(Splitter.on(CharConstant.CHAR_DD).splitToList(noModules));
                moduleSet.removeAll(noModuleSet);
                request.setModules(String.join(CharConstant.CHAR_DD, moduleSet));
            }
            // 标记record为is_deleted=1时，不能同时将s3_is_deleted设置为1，因为文件没有被删除。
            RecordV2 record = mvizService.getRecordByRequest(request.getRecordName(), request.getCarName(), request.getStartTime());
            if (record == null) {
                Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "mviz", "clientId", clientId, "status", "failed", "reason", "record_not_exist"));
                return new HttpResponse().code(WebResponseStatusEnum.MVIZ_FAILED.getCode())
                        .msg("the record does not exist in the record table!");
            }

            MvizDataListVO mvizDataListVO;
            if (request.getDownloadFromOffline() != null && request.getDownloadFromOffline()) { // 离线寄盘回收参数高优
                mvizDataListVO = mvizService.getRecordDataListFromOfflineOnly(request, record, clientId);
            } else if (request.getDownloadFromOnline() != null && request.getDownloadFromOnline()) {
                mvizDataListVO = mvizService.getRecordDataListFromOnlineOnly(request, record, clientId);
            } else {
                mvizDataListVO = mvizService.getDataList(request, record, clientId);
            }

            RecordFileResultVO resultVO = mvizDataListVO.getRecordFileResultVO();
            if (resultVO == null) {
                Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "mviz", "clientId", clientId, "status", "failed", "reason", "file_list_is_empty"));
                return new HttpResponse().code(WebResponseStatusEnum.MVIZ_FAILED.getCode()).msg("file list is empty!");
            }

            if ((request.getForceDownload() == null || !request.getForceDownload())) {
                HttpResponse httpResponse;
                if (resultVO.getIsFastUpload()) {
                    httpResponse = downloadValidatorHandler.checkRecordBeDownload(record, request.getMisId(), clientId, false, new ArrayList<>());
                } else {
                    httpResponse = downloadValidatorHandler.checkRecordBeDownload(record, request.getMisId(), clientId, true, mvizDataListVO.getRecordPkgCheckList());
                }
                if (httpResponse != null) {
                    String reason = String.format("WebResponseStatusEnum[%d]", httpResponse.getCode());
                    Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "mviz", "clientId", clientId, "status", "failed", "reason", reason));
                    return httpResponse;
                }
            }

            HttpResponse s3DsnUrlCheckResponse = setS3DsnUrl(resultVO, clientId);
            if (s3DsnUrlCheckResponse != null) {
                Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "mviz", "clientId", clientId, "status", "failed", "reason", "s3_dsn_url_could_not_get_connection_info"));
                return s3DsnUrlCheckResponse;
            }

            Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "mviz", "clientId", clientId, "status", "success"));
            return new HttpResponse().code(WebResponseStatusEnum.MVIZ_SUCCESS.getCode()).result(resultVO);
        } catch (Exception e) {
            Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "mviz", "clientId", clientId, "status", "failed", "reason", "exception"));
            log.error("[/mviz/data_list] exception: {}, clientId: {}, param: {}", e.getMessage(), clientId, request, e);
            return new HttpResponse().code(WebResponseStatusEnum.MVIZ_FAILED.getCode()).msg(e.getMessage());
        }
    }

    @GetMapping("/get_record_info")
    public String getRecordInfo(ListMvizDataRequest request) {
        try {
            byte[] result = mvizService.getRecordInfo(request.getRecordName());
            return Base64.getEncoder().encodeToString(result);
        } catch (Exception e) {
            log.error("get_record_info error, recordName={}", request.getRecordName(), e);
        }
        return null;
    }

    @GetMapping("/data_range")
    public CommonResponse getDataRange(MvizDataRangeRequest request) {
        if (request.getCarName() == null || request.getStartTime() == null || request.getEndTime() == null) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(),
                                       "Missing required parameters");
        }
        try {
            Date startTimeDate = new Date(request.getStartTime());
            Date endTimeDate = new Date(request.getEndTime());
            List<RecordFileRangeVO> recordFileRangeVO =
                mvizService.getRecordFileRange(request.getCarName(), startTimeDate, endTimeDate, request.getModules());
            return CommonResponse.success(recordFileRangeVO);
        } catch (Exception e) {
            log.error("[/mviz/get_data_range] exception: {}, param: {}", e.getMessage(), request, e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }
}
