package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "vehicle_intervention_offline")
@Data
public class VehicleInterventionOffline {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 接管时间
     */
    @Column(name = "intervention_time")
    private Date interventionTime;

    /**
     * Utm坐标的x（来源于Localization的x）
     */
    private String x;

    /**
     * Utm坐标的y（来源于Localization的y）
     */
    private String y;

    /**
     * 当前接管的自动驾驶里程数
     */
    private BigDecimal meter;

    /**
     * 0.不可用|1.可用|2.归档|3.已废弃|4.已删除
     */
    private Byte status;

    /**
     * 数据对应的时间，例如：20200204
     */
    @Column(name = "record_date")
    private Integer recordDate;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    private Long litId;

    private Boolean isCase;

    private Integer filterReason;

    private Integer isDiscern;

    private String uuid;

    /**
     * 根据record_name + intervention_time生成的caseId
     */
    @Column(name = "intervention_id")
    private String interventionId;

    @Column(name = "sim_verified_state")
    private String simVerifiedState;

    private String level;

    @Column(name = "sim_verified_detail")
    private String simVerifiedDetail;

    private transient Long interventionTimeNanoSecond;

    public Long getInterventionTimeNanoSecond() {
        if (this.interventionTimeNanoSecond == null) {
            this.interventionTimeNanoSecond = interventionTime == null ?
                    0L : interventionTime.getTime() * 1000000L;
        }
        return this.interventionTimeNanoSecond;
    }

}