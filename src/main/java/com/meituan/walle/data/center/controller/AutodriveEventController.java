package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.dto.AutodriveEventDTO;
import com.meituan.walle.data.center.entity.params.AutodriveEventPageParam;
import com.meituan.walle.data.center.entity.response.CommonPageResponse;
import com.meituan.walle.data.center.service.AutodriveEventService;
import com.meituan.walle.data.center.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/06/28
 */
@Slf4j
@RestController
@RequestMapping("/autodriveEvent")
public class AutodriveEventController {

    @Resource
    private AutodriveEventService autodriveEventService;

    @GetMapping("/pageQuery")
    public CommonPageResponse pageQuery(@ModelAttribute AutodriveEventPageParam autodriveEventPageParam) {
        try {
            List<AutodriveEventDTO> autodriveEventDTOList = autodriveEventService.pageQuery(autodriveEventPageParam);
            int totalCount = 0;
            if (!CollectionUtils.isEmpty(autodriveEventDTOList)) {
                totalCount = autodriveEventService.countQuery(autodriveEventPageParam);
            }
            return CommonPageResponse.success(
                    autodriveEventDTOList, totalCount, autodriveEventPageParam.getPage(), autodriveEventPageParam.getSize());
        } catch (Exception e) {
            log.error("[/autodriveEvent/pageQuery] exception: {}, param: {}",
                    e.getMessage(), JacksonUtil.serialize(autodriveEventPageParam), e);
            return CommonPageResponse.error(e.getMessage());
        }
    }

}
