package com.meituan.walle.data.center.controller;

import com.google.common.net.HttpHeaders;
import com.meituan.walle.data.center.entity.request.QueryCoreDumpFileRequest;
import com.meituan.walle.data.center.entity.response.QueryCoreDumpFileResponse;
import com.meituan.walle.data.center.service.CoreDumpService;
import com.meituan.walle.data.center.util.BaAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/core_dump")
public class CoreDumpController {

    @Autowired
    private CoreDumpService coreDumpService;

    @PostMapping("/query_file")
    public QueryCoreDumpFileResponse queryFile(@Validated @RequestBody QueryCoreDumpFileRequest request,
                                               HttpServletRequest httpServletRequest) {
        String requestAuth = httpServletRequest.getHeader(HttpHeaders.AUTHORIZATION);
        String clientId = BaAuthUtil.getBasicAuthClientIdByRequestAuth(requestAuth);
        return coreDumpService.queryFile(request, clientId);
    }
}
