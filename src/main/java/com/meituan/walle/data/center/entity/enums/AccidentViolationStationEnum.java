package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/14
 */

@Getter
@AllArgsConstructor
public enum AccidentViolationStationEnum {

    REMOTE("1", "云代驾"),
    ON_SITE("2", "近场安全员"),
    OTHER("3", "其他"),
    ;

    private String code;
    private String msg;

    public static List<Object> getCodeMsgMap() {
        List<Object> result = new ArrayList<>();
        for (AccidentViolationStationEnum accidentViolationStationEnum : AccidentViolationStationEnum.values()) {
            List<Object> tmpResult = new ArrayList<>();
            tmpResult.add(accidentViolationStationEnum.getCode());
            tmpResult.add(accidentViolationStationEnum.getMsg());
            result.add(tmpResult);
        }
        return result;
    }
}
