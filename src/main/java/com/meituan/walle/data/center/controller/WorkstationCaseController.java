package com.meituan.walle.data.center.controller;

import com.alibaba.fastjson.JSON;
import com.dianping.rhino.cluster.common.util.AssertUtil;
import com.meituan.walle.data.center.constant.CasesSourceEnum;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.pojo.BizEventAutodriveHardbrake;
import com.meituan.walle.data.center.entity.request.CaseSearchRequest;
import com.meituan.walle.data.center.entity.request.CaseSync2EsRequest;
import com.meituan.walle.data.center.entity.vo.CaseWorkstationPage;
import com.meituan.walle.data.center.entity.vo.SearchCaseDetailVO;
import com.meituan.walle.data.center.service.WorkstationCaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/18 11:10
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/workstation")
public class WorkstationCaseController {

    @Resource
    private WorkstationCaseService workstationCaseService;

    @PostMapping("/case/list")
    public Response caseList(@RequestBody CaseSearchRequest request) {
        Response response = Response.succ();
        try {
            CaseWorkstationPage<SearchCaseDetailVO> search = workstationCaseService.search(request);
            response.setData(search);
        } catch (Exception e) {
            log.error("search case list error, param: {} ", JSON.toJSONString(request), e);
            response = Response.fail(String.valueOf(WebResponseStatusEnum.FAILED.getCode()),
                    WebResponseStatusEnum.FAILED.getMsg());
        }
        return response;
    }

    @PostMapping("/case/aggregation")
    public Response aggregation(@RequestBody CaseSearchRequest request) {
        Response response = Response.succ();
        try {
            AssertUtil.assertNotBlank(request.getTermField(), "term field can`t be null");
            AssertUtil.assertNotBlank(request.getStatisticsField(), "statistics field can`t be null");
            Map map = workstationCaseService.bulkAggregation(request);
            response.setData(map);
        } catch (Exception e) {
            log.error("search case aggregation error, param: {} ", JSON.toJSONString(request), e);
            response = Response.fail(String.valueOf(WebResponseStatusEnum.FAILED.getCode()),
                    WebResponseStatusEnum.FAILED.getMsg());
        }
        return response;
    }

    /**
     * 同步es索引数据，谨慎操作
     * @param request
     * @return
     */
    @PostMapping("/case/sync2Es")
    public Response sync2Es(@RequestBody CaseSync2EsRequest request) {
        Response response = Response.succ();
        try {
            AssertUtil.assertNotBlank(request.getStartDate(), "start date can`t be null");
            AssertUtil.assertNotBlank(request.getEndDate(), "end date can`t be null");
            workstationCaseService.sync2Es(request);
        } catch (Exception e) {
            log.error("sync case index error, param: {} ", JSON.toJSONString(request), e);
            response = Response.fail(String.valueOf(WebResponseStatusEnum.FAILED.getCode()),
                    WebResponseStatusEnum.FAILED.getMsg());
        }
        return response;
    }


    @PostMapping("/case/hardbrake/infos")
    public Response hardBrakeCaseInfos(@RequestBody List<String> caseIds) {
        Response response = Response.succ();
        try {
            if (CollectionUtils.isEmpty(caseIds)) {
                return response;
            }
            caseIds = caseIds.stream().filter(id -> id.contains(CasesSourceEnum.HARD_BREAK.getMsg()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(caseIds)) {
                return response;
            }
            CaseWorkstationPage<SearchCaseDetailVO> search =
                    workstationCaseService.search(CaseSearchRequest.builder().caseIds(caseIds).build());
            Map<String, List<BizEventAutodriveHardbrake>> caseIdMap =
                    workstationCaseService.queryHardbrakeInfos(caseIds).stream()
                            .collect(Collectors.groupingBy(BizEventAutodriveHardbrake::getEventId));
            List<SearchCaseDetailVO> result = search.getResult();
            if (!CollectionUtils.isEmpty(result)) {
                result.forEach(cas -> {
                    List<BizEventAutodriveHardbrake> hardbrakes = caseIdMap.get(cas.getCaseId());
                    if (CollectionUtils.isEmpty(hardbrakes)) {
                        return;
                    }
                    cas.setHardBrakeObstacleInfo(JSON.parse(hardbrakes.get(0).getObstaclesInfo()));
                });
            }
            response.setData(search);
        } catch (Exception e) {
            log.error("query hardbrake case infos error, param: {} ", JSON.toJSONString(caseIds), e);
            response = Response.fail(String.valueOf(WebResponseStatusEnum.FAILED.getCode()),
                    WebResponseStatusEnum.FAILED.getMsg());
        }
        return response;
    }
}
