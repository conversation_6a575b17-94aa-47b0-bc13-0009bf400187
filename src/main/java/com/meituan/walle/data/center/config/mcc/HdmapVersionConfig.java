package com.meituan.walle.data.center.config.mcc;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.config.MtConfigClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @data 2019/7/2 11:00
 */
@Slf4j
@Component
public class HdmapVersionConfig {
    private static final String HDMAP_VERSION_KEY = "hdmap.version.to.use.hdmap";
    private String localPartners = "{}";
    private Map<String, String> hdmapVersion2UsedHdmap = new ConcurrentHashMap<>();

    @Resource
    private MtConfigClient mtConfigClient;

    @PostConstruct
    public void init() {
        String value = mtConfigClient.getValue(HDMAP_VERSION_KEY);
        if (StringUtils.isBlank(value)) {
            value = localPartners;
        }
        update(value);

        mtConfigClient.addListener(HDMAP_VERSION_KEY, (key, oldValue, newValue) -> {
            log.info("config {} change to {}, old value is {}", key, newValue, oldValue);
            update(newValue);
        });
    }

    public String getUsedHdmap(String hdmapVersion) {
        return hdmapVersion2UsedHdmap.get(hdmapVersion);
    }

    private synchronized void update(String txt) {
        if (StringUtils.isBlank(txt)) {
            log.warn("config value is empty: {}", HDMAP_VERSION_KEY);
            return;
        }
        Map<String, String> map = JSON.parseObject(txt, Map.class);
        for (Map.Entry<String, String> entry : map.entrySet()) {
            hdmapVersion2UsedHdmap.put(entry.getKey(), entry.getValue());
        }
    }

}
