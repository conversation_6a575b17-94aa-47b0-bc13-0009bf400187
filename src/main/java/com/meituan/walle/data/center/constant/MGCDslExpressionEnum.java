package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2022/12/26 15:23
 * @description
 */
@Getter
@AllArgsConstructor
public enum MGCDslExpressionEnum {
    LIST("$list$", "标识key对应值合成一个List<String>格式的json字符"),
    STAMP_TO_DATETIME("$stamp_to_datetime$", "将13位时间戳转换为date数据"),
    OBJECT_TO_JSON("$object_to_json$", "将key对应value转为json字符串"),
    LIST_TO_STRING_BY_DH("$list_to_string_by_dh$", "List<String>转为逗号分隔的字符串"),
    ;
    private String name;
    private String desc;

    public static MGCDslExpressionEnum getByName(String name) {
        return Arrays.stream(MGCDslExpressionEnum.values()).filter(en -> en.getName().equals(name))
                .findFirst().orElse(null);
    }
}
