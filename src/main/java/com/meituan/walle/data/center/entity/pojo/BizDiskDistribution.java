package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "biz_disk_distribution")
@Data
public class BizDiskDistribution {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 硬盘WWN编码
     */
    private String diskId;

    /**
     * 硬盘分布情况
     */
    private String diskDistribution;

    /**
     * 是否逻辑删除, 0表示否, 1表示是
     */
    private Integer isDeleted;

    private Date createTime;

    private Date updateTime;
}
