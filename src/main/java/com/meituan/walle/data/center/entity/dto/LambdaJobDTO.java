package com.meituan.walle.data.center.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meituan.walle.data.center.constant.DateTimeFormatterConstant;
import lombok.Data;

import java.util.Date;

@Data
public class LambdaJobDTO {
    private Long id;
    private String lambdaId;
    private String owner;
    private String recordName;
    private String vehicleName;
    private String module;
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern= DateTimeFormatterConstant.ymdhms, timezone="GMT+8")
    private Date startTime;
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern=DateTimeFormatterConstant.ymdhms, timezone="GMT+8")
    private Date endTime;
    private String pyS3Url;
    private Integer jobType;
    private Integer status;
    private String resultHdfsPath;
    private String resultS3Url;
    private String modelId;
    private Integer syncStatus;
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern=DateTimeFormatterConstant.ymdhms, timezone="GMT+8")
    private Date createTime;
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern=DateTimeFormatterConstant.ymdhms, timezone="GMT+8")
    private Date updateTime;
}
