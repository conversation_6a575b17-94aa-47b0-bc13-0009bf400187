package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.params.PageParams;
import com.meituan.walle.data.center.service.RecordCTagService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/record_ctag")
public class RecordCTagController {
    @Resource
    private RecordCTagService recordCTagService;

    @GetMapping("/get")
    public Object get(@Validated PageParams params,
                      @RequestParam(value = "recordName", required = false) String recordName) {
        return Response.succ(recordCTagService.getRecordCTags(params.getPageNum(), params.getPageSize(), recordName));
    }
}
