package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "sim_task")
public class SimTask {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    private String primaryTaskId;
    private String taskId;
    private String adsId;
    private Integer loopIndex;
    private String outputHdfsPath;
    private Integer taskResult;
    private Integer status;
    private Long fileSize;
    private Integer fileCount;
    private Date createTime;
    private Date updateTime;
}
