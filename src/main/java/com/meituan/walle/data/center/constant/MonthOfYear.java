package com.meituan.walle.data.center.constant;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum MonthOfYear {
    January("Jan", "01"),
    February("Feb", "02"),
    March("Mar", "03"),
    April("Apr", "04"),
    May("May", "05"),
    June("Jun", "06"),
    July("Jul", "07"),
    August("Aug", "08"),
    September("Sep", "09"),
    October("Oct", "10"),
    November("Nov", "11"),
    December("Dec", "12"),

    Chinese_January("1月", "01"),
    Chinese_February("2月", "02"),
    Chinese_March("3月", "03"),
    Chinese_April("4月", "04"),
    Chinese_May("5月", "05"),
    Chinese_June("6月", "06"),
    Chinese_July("7月", "07"),
    Chinese_August("8月", "08"),
    Chinese_September("9月", "09"),
    Chinese_October("10月", "10"),
    Chinese_November("11月", "11"),
    Chinese_December("12月", "12"),
    ;

    private String mmm;
    private String code;

    MonthOfYear(String mmm, String code) {
        this.mmm = mmm;
        this.code = code;
    }

    public static MonthOfYear getMonth(String mmm) {
        for (MonthOfYear monthOfYear : values()) {
            if (monthOfYear.getMmm().equals(mmm)) {
                return monthOfYear;
            }
        }
        log.warn("Month: {} is error", mmm);
        return null;
    }

    public String getMmm() {
        return mmm;
    }

    public String getCode() {
        return code;
    }
}
