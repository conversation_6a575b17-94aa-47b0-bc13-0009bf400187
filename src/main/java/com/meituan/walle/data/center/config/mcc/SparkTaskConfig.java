package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/3/14
 */
@Component
public class SparkTaskConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "cache.realtime.record.expiration.in.seconds")

    public static volatile int CACHE_REALTIME_RECORD_EXPIRATION_IN_SECONDS = 60 * 60;
    /**
     * 14day
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "realtime.record.expiration.in.days")
    public static volatile long REALTIME_RECORD_EXPIRATION_IN_DAYS = 14L;
}