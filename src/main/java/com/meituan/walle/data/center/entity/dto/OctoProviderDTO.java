package com.meituan.walle.data.center.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class OctoProviderDTO {

    private String name;
    private String appkey;
    private String version;
    private String ip;
    private Integer port;
    private Integer weight;
    private Double fweight;
    private Integer status;
    private String statusDesc;
    private Integer enabled;
    private Integer role;
    private Integer env;
    private Integer lastUpdateTime;
    private String extend;
    private String cextend;
    private Integer serverType;
    private String protocol;
    private Map<String, Object> serviceInfo;
    private Integer heartbeatSupport;
    private String swimlane;
    private String cell;
    private String groupInfo;
    private String ipv6;
    private String ipv6port;
    private Map<String, Object> properties;

}
