package com.meituan.walle.data.center.entity.enums;

public enum ReturnCodeEnum {
    // 参考https://docs.sankuai.com/dp/hbar/mdp-docs/master/web-response
    SUCCESS(0, "ok"),
    SYSTEM_ERROR(1001, "系统错误"),
    PARAM_ERROR(10004, "参数错误"),
    INVALID_REQUEST(10007, "非法请求"),
    ILLEGAL_USER(10008, "不合法的用户"),
    AUTH_FAILED(10009, "没有权限"),
    ;

    private int code;
    private String msg;

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    ReturnCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
