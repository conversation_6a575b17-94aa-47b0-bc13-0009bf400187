package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.service.VehiclePositionService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@RestController
@RequestMapping("/vehicle_position")
public class VehiclePositionController {
    @Resource
    private VehiclePositionService vehiclePositionService;

    @GetMapping("/get")
    public Object get(@Min(value = 1, message = "pageNum cannot be less than 0") int pageNum,
                      @Min(value = 1, message = "pageSize cannot be less than 0")
                      @Max(value = 10000, message = "pageSize cannot be more than 10000")
                              int pageSize,
                      @RequestParam(value = "recordName") String recordName,
                      @RequestParam(value = "vin", required = false) String vin) {
        return Response.succ(vehiclePositionService.getPosition(pageNum, pageSize, recordName, vin));
    }

    @GetMapping("/get_group_second")
    public Object getGroupBySeconde(@RequestParam(value = "recordName") String recordName) {
        return Response.succ(vehiclePositionService.getPointGroupBySeconde(recordName));
    }
}
