package com.meituan.walle.data.center.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory;
import com.meituan.hotel.dlm.model.SquirrelConfigModel;
import com.meituan.hotel.dlm.service.impl.DistributedLockManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
@Configuration
public class CerberusConfig {
    @Value("${appkey}")
    private String appKey;

    @Value("${redis.cluster-name}")
    private String redisClusterName;

    @Value("${cerberus.category.inboundFlow}")
    private String category;

    @Bean(initMethod = "init", destroyMethod = "destroy")
    public DistributedLockManager distributedLockManager(SquirrelConfigModel squirrelConfigModel) {
        DistributedLockManager distributedLockManager = new DistributedLockManager("squirrel");
        distributedLockManager.setAppKey(appKey);
        distributedLockManager.setSquirrelConfigModel(squirrelConfigModel);
        return distributedLockManager;
    }

    @Bean
    public SquirrelConfigModel squirrelConfigModel(
            @Qualifier(value = "redisClientBeanFactory")RedisClientBeanFactory redisClientBeanFactory) throws Exception {
        SquirrelConfigModel squirrelConfigModel = new SquirrelConfigModel();
        squirrelConfigModel.setRedisStoreClient(redisClientBeanFactory.getObject());
        squirrelConfigModel.setCategory(category);
        return squirrelConfigModel;
    }
}
