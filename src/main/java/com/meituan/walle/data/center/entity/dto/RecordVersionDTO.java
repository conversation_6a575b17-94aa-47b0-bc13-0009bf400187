package com.meituan.walle.data.center.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.meituan.walle.data.center.entity.po.RecordVersionFilePO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> @Beijing
 * @date 2022/03/21
 * Description:
 * Modified by
 */
@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RecordVersionDTO implements Serializable {
    private String versionIdList;
    private String moduleList;
    private List<RecordVersionFilePO> status;
}
