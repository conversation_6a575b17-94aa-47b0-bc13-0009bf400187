package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/03/08
 */
@Getter
@AllArgsConstructor
public enum RoutingPointTypeEnum {

    /**
     * 可忽略
     */
    NEGLIGIBLE(0, "Negligible"),
    /**
     * 必经点
     */
    MUST_PASS(1, "MustPass"),
    /**
     * 非必经点
     */
    RESERVED(2, "Reserved");

    private int code;
    private String message;
}
