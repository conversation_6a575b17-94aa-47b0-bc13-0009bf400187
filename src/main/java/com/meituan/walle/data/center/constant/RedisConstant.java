package com.meituan.walle.data.center.constant;

public interface RedisConstant {
    // 连接信息缓存
    String PIKE_CONNECTION = "pike-connection";

    // 待下发事件缓存
    String WAITING_EVENT = "waiting-emit-event";

    // 事件-设备锁，只发送一次，锁存在时则不发送，不存在才发送
    String EVENT_DEVICE_LOCK = "event-device-lock";

    String FAST_UPLOAD_KEY = "fast_upload";

    //连接锁，在删除和添加时，只能一个线程获得锁
    String PIKE_DEVICE_CONNECT_LOCK = "pike-device-connect-lock";

    String EVENT_AVOID_REPEAT_LOCK = "event_avoid_repeat_lock";

    String CATEGORY_WCDP_DISK_SERVER_CACHE = "wcdp-diskserver-cache";

    String RECORDNAME_BATCHID_MAPPING = "RecordName2BatchIdMapping";

    String REAL_UPLOAD_PROGRESS = "RealUploadProgress";

    String BATCHID = "BatchId";

    String RECORDNAME_BATCHID = "RecordNameBatchId";

    String CATEGORY_INTERVENTION_BATCH = "intervention_batch";

    String CATEGORY_INTERVENTION_FLAG = "intervention_tag_flag";
}
