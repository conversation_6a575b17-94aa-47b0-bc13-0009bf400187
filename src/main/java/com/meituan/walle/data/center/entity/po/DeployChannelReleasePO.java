package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2022/12/29 19:18
 * @description
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Table(name = "biz_deploy_channel_release")
public class DeployChannelReleasePO {
    @Column(name = "module")
    private String module;
    @Column(name = "version")
    private String version;
    @Column(name = "channel")
    private String channel;
}
