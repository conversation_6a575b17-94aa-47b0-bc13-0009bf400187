package com.meituan.walle.data.center.dal.recordmanager.mapper;

import com.meituan.walle.data.center.dal.recordmanager.entity.InboundRecordSnapshotPO;
import org.apache.ibatis.annotations.*;

/**
 * <AUTHOR>
 * @date 2024/10/29
 */

public interface InboundRecordSnapshotMapper {

    @Insert({
            "<script>" +
                    "    INSERT ignore INTO `inbound_record_snapshot` " +
                    "    <trim prefix='(' suffix=')' suffixOverrides=','>" +
                    "        <if test='null != recordName and &apos;&apos; != recordName'>" +
                    "            `record_name`," +
                    "        </if>" +
                    "        <if test='null != bucketName and &apos;&apos; != bucketName'>" +
                    "            `bucket_name`," +
                    "        </if>" +
                    "        <if test='null != s3Path and &apos;&apos; != s3Path'>" +
                    "            `s3_path`," +
                    "        </if>" +
                    "        <if test='null != fileSize'>" +
                    "            `file_size`," +
                    "        </if>" +
                    "        <if test='null != status'>" +
                    "            `status`," +
                    "        </if>" +
                    "        <if test='null != statusDetail'>" +
                    "            `status_detail`," +
                    "        </if>" +
                    "        <if test='null != isDeleted'>" +
                    "            `is_deleted`," +
                    "        </if>" +
                    "    </trim>" +
                    "    <trim prefix='values (' suffix=')' suffixOverrides=','>" +
                    "        <if test='null != recordName and &apos;&apos; != recordName'>" +
                    "            #{recordName}," +
                    "        </if>" +
                    "        <if test='null != bucketName and &apos;&apos; != bucketName'>" +
                    "            #{bucketName}," +
                    "        </if>" +
                    "        <if test='null != s3Path and &apos;&apos; != s3Path'>" +
                    "            #{s3Path}," +
                    "        </if>" +
                    "        <if test='null != fileSize'>" +
                    "            #{fileSize}," +
                    "        </if>" +
                    "        <if test='null != status'>" +
                    "            #{status}," +
                    "        </if>" +
                    "        <if test='null != statusDetail'>" +
                    "            #{statusDetail}," +
                    "        </if>" +
                    "        <if test='null != isDeleted'>" +
                    "            #{isDeleted}," +
                    "        </if>" +
                    "    </trim>" +
                    "</script>"})
    int insert(InboundRecordSnapshotPO inboundRecordSnapshotPO);

    @Update({
            "<script>" +
                    "    UPDATE `inbound_record_snapshot` SET " +
                    "        <if test='null != bucketName and &apos;&apos; != bucketName'>" +
                    "            `bucket_name`=#{bucketName}," +
                    "        </if> " +
                    "        <if test='null != s3Path and &apos;&apos; != s3Path'>" +
                    "            `s3_path`=#{s3Path}," +
                    "        </if> " +
                    "        <if test='null != status'>" +
                    "            `status`=#{status}," +
                    "        </if> " +
                    "        <if test='null != fileSize'>" +
                    "            `file_size`=#{fileSize}," +
                    "        </if> " +
                    "        <if test='null != statusDetail'>" +
                    "            `status_detail`=#{statusDetail}," +
                    "        </if> " +
                    "        <if test='null != isDeleted'>" +
                    "            `is_deleted`=#{isDeleted}," +
                    "        </if> " +
                    "        `update_time`=CURRENT_TIMESTAMP " +
                    "    WHERE `record_name`=#{recordName} " +
                    "</script>"})
    int update(InboundRecordSnapshotPO inboundRecordSnapshotPO);

    @Select({"select * from `inbound_record_snapshot` where record_name = #{recordName}"})
    InboundRecordSnapshotPO getInboundRecordSnapshot(@Param("recordName") String recordName);

    @Delete("delete from `inbound_record_snapshot` where `record_name` = #{recordName}")
    int deleteByRecordName(@Param("recordName") String recordName);
}
