package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class CraneConfig {
    /**
     * 调度任务scanRecordFileList的参数配置：扫描s3上x天内record的文件列表
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.scanRecordFileList.day.before")
    public static volatile int SCAN_DAY_BEFORE = 7;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.triggerSpark.day.before")
    public static volatile int TRIGGER_SPARK_DAY_BEFORE = 7;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.triggerSpark.upload.interval")
    public static volatile long TRIGGER_SPARK_UPLOAD_INTERVAL = 6 * 3600L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.trigger.extract.spark.name")
    public static volatile String EXTRACT_SPARK_NAME = "hadoop-wcdp.spark.ods.data-extraction-pipeline";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.trigger.ctor.index.spark.name")
    public static volatile String CTOR_INDEX_SPARK_NAME = "hadoop-wcdp.spark.ods.onboard-message-index-extractor-2-s3";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.scanIntervention.day.before")
    public static volatile int SCAN_INTERVENTION_DAY_BEFORE = 3;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.scanIntervention.max.day.before")
    public static volatile int SCAN_INTERVENTION_MAX_DAY_BEFORE = 30;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.fillRecordEndTime.day.before")
    public static volatile int SCAN_RECORD_END_TIME_BEFORE = 2;

    /**
     * 调度任务scanSnapshot的参数配置：扫描s3上x天内快照的文件列表
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.scanSnapshot.day.before")
    public static volatile int SCAN_SNAPSHOT_DAY_BEFORE = 0;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.renameReocrdFileS3Url.day.before")
    public static volatile int SCAN_RENAME_RECORD_FILE_BEFORE = 3;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.simTaskJobExtract.batch.size")
    public static volatile int SIM_TASK_JOB_EXTRACT_BATCH_SIZE = 1000;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.online.operate.intervention.tag.category.code.list")
    public static volatile String ONLINE_OPERATE_INTERVENTION_TAG_CATEGORY_CODE_LIST = "BASE";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.online.operate.intervention.tag.name.white.list")
    public static volatile String ONLINE_OPERATE_INTERVENTION_TAG_NAME_LIST = "运营接管";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.scanS3.day.before")
    public static volatile int SCAN_S3_DAY_BEFORE = 2;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.scan.before.day")
    public static volatile int SCAN_BEFORE_DAY = 3;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.triggerLabelSync.day.before")
    public static volatile int TRIGGER_LABEL_SYNC_DAY_BEFORE = 1;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.triggerLabelSync.job.count")
    public static volatile long TRIGGER_LABEL_SYNC_JOB_COUNT = 100L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID,
            key = "com.meituan.walle.data.center.initializeOnCallHistory.beginTime")
    public static volatile String INITIALIZE_ON_CALL_HISTORY_BEGIN_TIME = "2021-08-05";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID,
            key = "com.meituan.walle.data.center.initializeOnCallHistory.endTime")
    public static volatile String INITIALIZE_ON_CALL_HISTORY_END_TIME = "2021-08-06";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID,
            key = "com.meituan.walle.data.center.recoveryRecordInfoFromSnapshot.diskSnapshotId")
    public static volatile String DISK_SNAPSHOT_ID = "1044580";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "scan.record.day.before")
    public static volatile int SCAN_RECORD_DAY_BEFORE = 30;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID,
            key = "com.meituan.walle.data.center.dxNoticeOnCallGroup.groupId")
    public static volatile long DX_NOTICE_ON_CALL_GROUP_ID = 64010309598L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "scan.fragmented.record.end.time.day.before")
    public static volatile int SCAN_FRAGMENTED_RECORD_END_TIME_DAY_BEFORE = 3;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.scan.derive.intervention.day.before")
    public static volatile int SCAN_DERIVE_INTERVENTION_DAY_BEFORE = 1;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.scan.common.case.day.before")
    public static volatile int SCAN_CASE_DAY_BEFORE = 1;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.scan.common.case.day.after")
    public static volatile int SCAN_CASE_DAY_AFTER = 1;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.record.spark.job.wait.time")
    public static volatile long TASK_EXECUTE_FAILED_TIME_MILLS = 48 * 60 * 60 * 1000L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.destination.event.wait.time")
    public static volatile long TASK_DESTINATION_EVENT_WAIT_MILLIS = 60 * 1000L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.sync.case.day.before")
    public static volatile int SYNC_CASE_DAY_BEFORE = 2;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "crane.sync.case.day.after")
    public static volatile int SYNC_CASE_DAY_AFTER = 1;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "data.manager.record.tag.query.url")
    public static volatile String DATA_MANAGER_RECORD_TAG_QUERY_URL
            = "http://10.34.98.209:8080/data_manager/interface/general_case/batch_get_case_usage";
}
