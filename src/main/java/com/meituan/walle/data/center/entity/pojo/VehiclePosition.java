package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "vehicle_position")
public class VehiclePosition {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 时间
     */
    private Date t;

    /**
     * UTM坐标x
     */
    private Double x;

    /**
     * UTM坐标y
     */
    private Double y;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取车架号（车辆设备id）
     *
     * @return vin - 车架号（车辆设备id）
     */
    public String getVin() {
        return vin;
    }

    /**
     * 设置车架号（车辆设备id）
     *
     * @param vin 车架号（车辆设备id）
     */
    public void setVin(String vin) {
        this.vin = vin;
    }

    /**
     * 获取表record的record_name外键
     *
     * @return record_name - 表record的record_name外键
     */
    public String getRecordName() {
        return recordName;
    }

    /**
     * 设置表record的record_name外键
     *
     * @param recordName 表record的record_name外键
     */
    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    /**
     * 获取时间
     *
     * @return t - 时间
     */
    public Date getT() {
        return t;
    }

    /**
     * 设置时间
     *
     * @param t 时间
     */
    public void setT(Date t) {
        this.t = t;
    }

    /**
     * 获取UTM坐标x
     *
     * @return x - UTM坐标x
     */
    public Double getX() {
        return x;
    }

    /**
     * 设置UTM坐标x
     *
     * @param x UTM坐标x
     */
    public void setX(Double x) {
        this.x = x;
    }

    /**
     * 获取UTM坐标y
     *
     * @return y - UTM坐标y
     */
    public Double getY() {
        return y;
    }

    /**
     * 设置UTM坐标y
     *
     * @param y UTM坐标y
     */
    public void setY(Double y) {
        this.y = y;
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}