package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2022/06/01
 */
@Getter
@AllArgsConstructor
public enum FreeSpaceScenesEnum {

    UNKNOWN(1, "unknown"),
    BLOCK_BY_OBSTACLE(2, "block_by_obstacle"),
    UTURN(3, "uturn"),
    ROADBLOCK(5, "roadblock"),
    PARKING(6, "parking"),
    STARTING(7, "starting"),
    MEETING(8, "meeting"),
    MEETING_WAITING(9, "meeting_waiting"),
    FREESPACE_LINK(10, "freespace_link"),
    TELEOPERATION_REVERSE(11, "teleoperation_reverse"),
    TELEOPERATION_REVERSE_WAITING(12, "teleoperation_reverse_waiting"),
    RESCUE(13, "rescue"),
    ;

    private int code;
    private String msg;
}
