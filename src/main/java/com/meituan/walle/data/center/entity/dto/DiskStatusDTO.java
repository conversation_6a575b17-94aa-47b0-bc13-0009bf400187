package com.meituan.walle.data.center.entity.dto;

import com.meituan.walle.data.center.constant.CharConstant;
import lombok.*;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DiskStatusDTO implements Serializable {
    // diskserver上报字段
    private String diskId;
    private String diskLabel = CharConstant.CHAR_EMPTY;
    private String batchId = CharConstant.CHAR_EMPTY;
    private String serverLabel = CharConstant.CHAR_EMPTY;
    private String slotLabel = CharConstant.CHAR_EMPTY;
    private Integer status;
    private Long timestamp;
    // dataview上报字段
    private String loginName = CharConstant.CHAR_EMPTY;
    private String userName = CharConstant.CHAR_EMPTY;
    private String vehicleName = CharConstant.CHAR_EMPTY;
}