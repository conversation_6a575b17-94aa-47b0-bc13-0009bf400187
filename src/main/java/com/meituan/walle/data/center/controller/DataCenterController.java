package com.meituan.walle.data.center.controller;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.walle.data.center.component.SpringApplicationContext;
import com.meituan.walle.data.center.config.mcc.CraneConfig;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.dto.ProgressDTO;
import com.meituan.walle.data.center.entity.enums.RecordExtractFixModeEnum;
import com.meituan.walle.data.center.entity.enums.S3ClusterEnum;
import com.meituan.walle.data.center.entity.params.EventRelationParam;
import com.meituan.walle.data.center.entity.params.InterventionLabelParam;
import com.meituan.walle.data.center.entity.params.TagParam;
import com.meituan.walle.data.center.entity.po.EventRelationPO;
import com.meituan.walle.data.center.entity.po.RealUploadProgressPO;
import com.meituan.walle.data.center.entity.pojo.DiskExceptionHistory;
import com.meituan.walle.data.center.entity.pojo.InterventionLabel;
import com.meituan.walle.data.center.entity.pojo.LiveIssueTag;
import com.meituan.walle.data.center.entity.pojo.RealUploadProgress;
import com.meituan.walle.data.center.entity.request.FixEvent2CaseMaebRequest;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.handle.impl.OfflineInboundRecordSnapshotHandler;
import com.meituan.walle.data.center.handle.impl.S3FileCleanupHandler;
import com.meituan.walle.data.center.service.EventRelationService;
import com.meituan.walle.data.center.service.InterventionLabelService;
import com.meituan.walle.data.center.service.LiveIssueTagService;
import com.meituan.walle.data.center.service.SnapshotContentService;
import com.meituan.walle.data.center.service.impl.DataCenterServiceImpl;
import com.meituan.walle.data.center.task.AutoCarJobExtract;
import com.meituan.walle.data.center.task.autocar.AutoCarJob;
import com.meituan.walle.data.center.task.autocar.RecordFromHDFSJobExtract;
import com.meituan.walle.data.center.task.autocar.RecordFromS3JobExtract;
import com.meituan.walle.data.center.util.S3SignatureUtil;
import com.meituan.walle.data.center.util.S3Util;
import com.meituan.walle.data.center.util.Trace;
import com.sankuai.walle.wcdp.core.entity.request.RespCodeEnum;
import com.sankuai.walle.wcdp.data.center.iface.Result;
import com.sankuai.walle.wcdp.data.center.iface.TDiskStatusDTO;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@InterfaceDoc(
        displayName = "dataCenterService对应接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，为无法通过MTthrift方式调用的服务提供接口",
        scenarios = "dataCenterService部分方法暴露出的接口"
)
@Log4j2
@RestController
@RequestMapping("/dataCenter")
public class DataCenterController {

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private SpringApplicationContext context;

    @Autowired
    private S3Util s3Util;

    @Value("${s3.bucket.extractJob}")
    private String extractJobBucket;

    @Resource
    private DataCenterServiceImpl dataCenterService;

    @Autowired
    private HttpServletResponse httpServletResponse;

    @Autowired
    private S3FileCleanupHandler s3FileCleanHandler;

    @Resource
    private LiveIssueTagService tagService;

    @Resource
    private InterventionLabelService interventionLabelService;

    @Resource
    private EventRelationService eventRelationService;

    @Autowired
    private SnapshotContentService snapshotContentService;

    @MethodDoc(
            requestMethods = {HttpMethod.POST},
            displayName = "报告磁盘状态",
            description = "报告磁盘状态，信息入库",
            parameters = {
                    @ParamDoc(name = "diskStatusDTO", description = "磁盘状态对象", requiredness = Requiredness.REQUIRED)
            },
            returnValueDescription = "调用成功与否的Result对象，包含code、message等信息"
    )
    @PostMapping("/reportDiskStatus")
    public Result reportDiskStatus(@RequestBody TDiskStatusDTO diskStatusDTO) {
        return dataCenterService.reportDiskStatus(diskStatusDTO);
    }

    @MethodDoc(
            requestMethods = {HttpMethod.GET},
            displayName = "根据diskId获取磁盘数据上传进度",
            description = "根据diskId获取磁盘数据上传进度",
            parameters = {
                    @ParamDoc(name = "diskId", description = "磁盘ID", requiredness = Requiredness.REQUIRED)
            },
            returnValueDescription = "调用成功与否的Result对象，包含code、message等信息"
    )
    @GetMapping("/getDiskUploadProgressByDiskId")
    public Response getDiskUploadProgressByDiskId(@RequestParam @NotBlank(message = "diskId不能为空")
                                                  String diskId) throws Exception {
        try {
            RealUploadProgressPO realUploadProgressPO = dataCenterService.getDiskUploadProgress(diskId);
            if (Objects.nonNull(realUploadProgressPO)) {
                return Response.succ(realUploadProgressPO);
            }
            return Response.fail("" + RespCodeEnum.RESPONSE_EMPTY.getCode(), RespCodeEnum.RESPONSE_EMPTY.getMsg());
        } catch (Exception exception) {
            log.error("diskId : {} getDiskUploadProgressByDiskId Exception : {}",
                    diskId, exception.getMessage(), exception);
            return Response.fail("" + RespCodeEnum.SERVER_EXECUTE_ERROR.getCode(), exception.getMessage());
        }
    }

    @MethodDoc(
            requestMethods = {HttpMethod.GET},
            displayName = "根据recordName获取磁盘数据上传进度",
            description = "根据recordName获取磁盘数据上传进度",
            parameters = {
                    @ParamDoc(name = "recordName", description = "record名称", requiredness = Requiredness.REQUIRED)
            },
            returnValueDescription = "调用成功与否的Result对象，包含code、message等信息"
    )

    @Deprecated
    @GetMapping("/getDiskUploadProgressByRecordName")
    public Response getDiskUploadProgressByRecordName(@RequestParam @NotBlank(message = "recordName不能为空")
                                                      String recordName) {
        try {
            Map<String, RealUploadProgress> resultMap = dataCenterService.getRecordNameUploadProgress(recordName);
            log.info("data.center.service.impl # getDiskUploadProgressByRecordName resultMap {} ", resultMap);
            if (Objects.nonNull(resultMap)) {
                return Response.succ(resultMap);
            }
            return Response.fail("" + RespCodeEnum.RESPONSE_EMPTY.getCode(), RespCodeEnum.RESPONSE_EMPTY.getMsg());
        } catch (Exception exception) {
            log.error("recordName : {} getDiskUploadProgressByRecordName Exception : {}",
                    recordName, exception.getMessage(), exception);
            return Response.fail("" + RespCodeEnum.SERVER_EXECUTE_ERROR.getCode(), exception.getMessage());
        }
    }

    @GetMapping("/getRecordProgressByRecordName")
    public Response getRecordProgressByRecordName(@RequestParam @NotBlank(message = "recordName不能为空")
                                                  String recordName) {
        try {
            Map<String, ProgressDTO> resultMap = dataCenterService.getRecordProgress(recordName);
            log.info("data.center.service.impl # getRecordProgressByRecordName resultMap {} ", resultMap);
            if (Objects.nonNull(resultMap)) {
                return Response.succ(resultMap);
            }
            return Response.fail("" + RespCodeEnum.RESPONSE_EMPTY.getCode(), RespCodeEnum.RESPONSE_EMPTY.getMsg());
        } catch (Exception exception) {
            log.error("recordName : {} getRecordProgressByRecordName Exception : {}",
                    recordName, exception.getMessage(), exception);
            return Response.fail("" + RespCodeEnum.SERVER_EXECUTE_ERROR.getCode(), exception.getMessage());
        }
    }

    @MethodDoc(
            requestMethods = {HttpMethod.POST},
            displayName = "上报磁盘异常",
            description = "上报磁盘异常，信息入库与状态数据修正",
            parameters = {
                    @ParamDoc(name = "DiskExceptionHistory", description = "磁盘异常对象", requiredness = Requiredness.REQUIRED)
            },
            returnValueDescription = "调用成功与否的Result对象，包含code、message等信息"
    )
    @PostMapping("/reportDiskException")
    public Response reportDiskException(@RequestBody DiskExceptionHistory diskExceptionHistory) {
        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();
        String api = "reportDiskException";
        Transaction t = Cat.newTransaction(CatConstant.API, "/dataCenter/" + api);
        log.info("traceId: 【{}】, received new request for 【{}】 , request param is: 【{}】.",
                traceId, api, diskExceptionHistory);
        httpServletResponse.addHeader("TraceId", traceId);

        if (Objects.isNull(diskExceptionHistory)) {
            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setSuccessStatus();
            t.complete();
            log.info("traceId: 【{}】, complete 【{}】 request, overall cost 【{}】 ms.", traceId, api, durationTime);
            return Response.fail("" + RespCodeEnum.PARAMS_EMPTY.getCode(), RespCodeEnum.PARAMS_EMPTY.getMsg());
        }

        try {
            dataCenterService.reportDiskException(diskExceptionHistory);

            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setSuccessStatus();
            log.info("traceId: 【{}】, complete 【{}】 request, overall cost 【{}】 ms.", traceId, api, durationTime);
            return Response.succ();
        } catch (Exception e) {
            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setStatus(e);
            Cat.logError(e);
            log.error("traceId: 【{}】, 【{}】 request fail, request param is: 【{}】.",
                    traceId, api, diskExceptionHistory, e);
            return Response.fail("" + RespCodeEnum.SERVER_EXECUTE_ERROR.getCode(), e.getMessage());
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            requestMethods = {HttpMethod.POST},
            displayName = "根据磁盘ID获取批次ID",
            description = "从表中查询，根据磁盘ID获取批次ID",
            parameters = {
                    @ParamDoc(name = "jsonStr", description = "json字符串参数", requiredness = Requiredness.REQUIRED)
            },
            restExamplePostData = "{\"diskId\": \"55cd2e415367aaf0\"}",
            returnValueDescription = "调用成功与否的Result对象，包含code、message等信息"
    )
    @PostMapping("/getBatchIdByDiskId")
    public Result getBatchIdByDiskId(@RequestBody String jsonStr) {
        String diskId = null;
        try {
            diskId = objectMapper.readTree(jsonStr).get("diskId").asText();
        } catch (IOException e) {
            log.error("DataCenterController getBatchIdByDiskId jsonStr resolving exception: ", e);
        }
        return dataCenterService.getBatchIdByDiskId(diskId);
    }

    @MethodDoc(
            requestMethods = {HttpMethod.POST},
            displayName = "根据磁盘ID刷新批次ID",
            description = "根据磁盘ID刷新批次ID到表中",
            parameters = {
                    @ParamDoc(name = "jsonStr", description = "json字符串参数", requiredness = Requiredness.REQUIRED)
            },
            restExamplePostData = "{\"diskId\": \"55cd2e415367aaf0\"}",
            returnValueDescription = "调用成功与否的Result对象，包含code、message等信息"
    )
    @PostMapping("/refreshBatchIdByDiskId")
    public Result refreshBatchIdByDiskId(@RequestBody String jsonStr) {
        String diskId = null;
        try {
            diskId = objectMapper.readTree(jsonStr).get("diskId").asText();
        } catch (IOException e) {
            log.error("DataCenterController refreshBatchIdByDiskId jsonStr resolving exception: ", e);
        }
        return dataCenterService.refreshBatchIdByDiskId(diskId);
    }

    @MethodDoc(
            requestMethods = {HttpMethod.POST},
            displayName = "上报rename信息",
            description = "上报rename信息",
            parameters = {
                    @ParamDoc(name = "jsonStr", description = "json字符串参数", requiredness = Requiredness.REQUIRED)
            },
            restExamplePostData =
                    "{\"sourceFile\": \"/55cd2e414df1c603/20200828/110226_vv6-00/coredump/core_PLAN.27517\", " +
                            "\"destFile\": \"/55cd2e414df1c603/20200828/110226_vv6-00/coredump/" +
                            "rename-8945e80fde1e492181c005cefa7d68a6.27517\"}",
            returnValueDescription = "调用成功与否的Result对象，包含code、message等信息"
    )
    @PostMapping("/reportMessyCodeFile")
    public Result reportMessyCodeFile(@RequestBody String jsonStr) {
        Result result;
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            String sourceFile = jsonNode.get("sourceFile").asText();
            String destFile = jsonNode.get("destFile").asText();
            result = dataCenterService.reportMessyCodeFile(sourceFile, destFile);
        } catch (Exception e) {
            log.error("DataCenterController reportMessyCodeFile exception: ", e);
            result = new Result();
            result.code = 10020;
            result.message = e.getMessage();
        }
        return result;
    }

    @PostMapping("/fix/record/s3Path")
    public Result fixRecordS3Path(@RequestBody List<String> recordNameList) {
        Result result = new Result();
        try {
            int totalRows = dataCenterService.fixRecordS3Path(recordNameList);
            result.code = 10000;
            result.message = "success, update total rows: " + totalRows;
        } catch (Exception e) {
            log.error("dataCenterService.fixRecordS3Path exception: {}, recordNameList: {}",
                    e.getMessage(), recordNameList, e);
            result.code = 10020;
            result.message = e.getMessage();
        }
        return result;
    }

    @PostMapping("/fix/record/cluster")
    public CommonResponse fixRecordCluster(@RequestBody List<String> recordNameList) {
        try {
            log.info("[/dataCenter/fix/record/cluster] manual trigger fixRecordCluster begin...");
            Map<String, List<String>> failRecordNameMap = dataCenterService.fixRecordCluster(recordNameList);
            log.info("[/dataCenter/fix/record/cluster] manual trigger fixRecordCluster end...");
            return CommonResponse.success(failRecordNameMap);
        } catch (Exception e) {
            log.error("dataCenterService.fixRecordCluster exception: {}, recordNameList: {}",
                    e.getMessage(), recordNameList, e);
            return CommonResponse.fail(WebResponseStatusEnum.FAILED.getCode(), e.getMessage());
        }
    }

    @PostMapping("/fix/headerFile/cluster")
    public CommonResponse fixHeaderFileCluster() {
        try {
            log.info("[/dataCenter/fix/headerFile/cluster] manual trigger fixHeaderFileCluster begin...");
            Integer rows = dataCenterService.fixHeaderFileCluster();
            log.info("[/dataCenter/fix/headerFile/cluster] manual trigger fixHeaderFileCluster end... rows: {}", rows);
            return CommonResponse.success("rows: " + rows);
        } catch (Exception e) {
            log.error("[/dataCenter/fix/headerFile/cluster] exception: {}, ", e.getMessage(), e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/fix/migrate/cluster")
    public CommonResponse fixMigrateCluster(@RequestParam String startDate, @RequestParam String endDate, @RequestParam String recordName) {
        try {
            log.info("[/fix/migrate/cluster] manual trigger fixMigrateCluster begin... startDate: {}, endDate: {}, recordName: {}",
                    startDate, endDate, recordName);
            String result = dataCenterService.fixMigrateCluster(startDate, endDate, recordName);
            log.info("[/fix/migrate/cluster] manual trigger fixMigrateCluster end... startDate: {}, endDate: {}, recordName: {}, result: {}",
                    startDate, endDate, recordName, result);
            return CommonResponse.success("result: " + result);
        } catch (Exception e) {
            log.error("[/fix/migrate/cluster] exception: {}, ", e.getMessage(), e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/fix/migrate/cluster/pkg")
    public CommonResponse fixMigrateClusterPkg(@RequestParam String startDate, @RequestParam String endDate, @RequestParam String recordName) {
        try {
            log.info("[/fix/migrate/cluster/pkg] manual trigger fixMigrateCluster begin... startDate: {}, endDate: {}, recordName: {}",
                    startDate, endDate, recordName);
            String result = dataCenterService.fixMigrateClusterPkg(startDate, endDate, recordName);
            log.info("[/fix/migrate/cluster/pkg] manual trigger fixMigrateCluster end... startDate: {}, endDate: {}, recordName: {}, result: {}",
                    startDate, endDate, recordName, result);
            return CommonResponse.success("result: " + result);
        } catch (Exception e) {
            log.error("[/fix/migrate/cluster/pkg] exception: {}, ", e.getMessage(), e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/fix/recordFile/deletedMark")
    public CommonResponse fixRecordFileDeletedMark(@RequestParam String startDateKey, @RequestParam String endDateKey, @RequestParam String recordName) {
        try {
            log.info("[/fix/recordFile/deletedMark] manual trigger fixRecordFileDeletedMark begin... startDateKey: {}, endDateKey: {}, recordName: {}",
                    startDateKey, endDateKey, recordName);
            String result = dataCenterService.fixRecordFileDeletedMark(startDateKey, endDateKey, recordName);
            log.info("[/fix/recordFile/deletedMark] manual trigger fixRecordFileDeletedMark end... startDateKey: {}, endDateKey: {}, recordName: {}, result: {}",
                    startDateKey, endDateKey, recordName, result);
            return CommonResponse.success("result: " + result);
        } catch (Exception e) {
            log.error("[/fix/recordFile/deletedMark] exception: {}, ", e.getMessage(), e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/fix/recordFile/existInS3")
    public CommonResponse fixRecordFileExistInS3(@RequestParam String startDateKey, @RequestParam String endDateKey) {
        try {
            log.info("[/fix/recordFile/existInS3] manual trigger fixRecordFileExistInS3 begin... startDateKey: {}, endDateKey: {}",
                    startDateKey, endDateKey);
            String result = dataCenterService.fixRecordFileExistInS3(startDateKey, endDateKey);
            log.info("[/fix/recordFile/existInS3] manual trigger fixRecordFileExistInS3 end... startDateKey: {}, endDateKey: {}, result: {}",
                    startDateKey, endDateKey, result);
            return CommonResponse.success("result: " + result);
        } catch (Exception e) {
            log.error("[/fix/recordFile/existInS3] exception: {}, ", e.getMessage(), e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping("/fix/record/triggerExtract")
    public Result fixRecordTriggerExtract(@RequestBody List<String> recordNameList) {
        Result result = new Result();
        try {
            log.info("[/dataCenter/fix/record/triggerExtract] manual trigger autoCarJobExtract begin...");
            AutoCarJob job = new AutoCarJob(
                    "AutoCarJobExtract_Manual_Mode2",
                    CraneConfig.TRIGGER_SPARK_UPLOAD_INTERVAL,
                    s3Util.getS3Client(S3ClusterEnum.BJ_BAK),
                    extractJobBucket,
                    RecordExtractFixModeEnum.RECORD_LIST,
                    null,
                    recordNameList);
            AutoCarJobExtract hdfsJobExtract = context.getBean(RecordFromHDFSJobExtract.class);
            hdfsJobExtract.trigger(job);
            AutoCarJobExtract s3JobExtract = context.getBean(RecordFromS3JobExtract.class);
            s3JobExtract.trigger(job);
            log.info("[/dataCenter/fix/record/triggerExtract] manual trigger autoCarJobExtract end...");
            result.code = 10000;
            result.message = "success";
        } catch (Exception e) {
            log.error("dataCenterService.fixRecordTriggerExtract exception: {}, recordNameList: {}",
                    e.getMessage(), recordNameList, e);
            result.code = 10020;
            result.message = e.getMessage();
        }
        return result;
    }

    @PostMapping("/fix/event2case/maeb")
    public Result fixEvent2CaseMaeb(@RequestBody FixEvent2CaseMaebRequest request) {
        Result result = new Result();
        try {
            int caseAmount = dataCenterService.fixEvent2CaseMaeb(request);
            result.code = 10000;
            result.message = "success, event to case amount: " + caseAmount;
        } catch (Exception e) {
            log.error("dataCenterService.fixEvent2CaseMaeb exception: {}, request: {}",
                    e.getMessage(), request, e);
            result.code = 10020;
            result.message = e.getMessage();
        }
        return result;
    }

    @PostMapping("/s3/cleanup/file")
    public Response cleanupFileToS3Trash(@RequestParam(value = "startDate") String startDate,
                                         @RequestParam(value = "endDate") String endDate) {
        try {
            s3FileCleanHandler.deleteFileToS3Trash(startDate, endDate);
        } catch (Exception e) {
            log.error("s3 cleanup file exception : {}", e.getMessage(), e);
            return Response.fail("10020", e.getMessage());
        }
        return Response.succ();
    }

    @GetMapping("/tag/list")
    public Response tagList(TagParam param, HttpServletRequest httpServletRequest) {
        try {
            List<LiveIssueTag> liveIssueTags = tagService.queryTagList(param);
            return Response.succ(WebResponseStatusEnum.SUCCESS.getCode().toString(), "ok", liveIssueTags);
        } catch (Exception e) {
            log.error("dataCenterService.tagList exception: {}, param: {}", e.getMessage(), param, e);
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), e.getMessage());
        }
    }

    @GetMapping("/intervention_label/list")
    public Response interventionLabelList(InterventionLabelParam param, HttpServletRequest httpServletRequest) {
        try {
            List<InterventionLabel> interventionLabels = interventionLabelService.queryInterventionLabelList(param);
            return Response.succ(WebResponseStatusEnum.SUCCESS.getCode().toString(), "ok", interventionLabels);
        } catch (Exception e) {
            log.error("dataCenterService.interventionLabelList exception: {}, param: {}", e.getMessage(), param, e);
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), e.getMessage());
        }
    }

    @GetMapping("/event_relation/list")
    public Response eventRelationList(EventRelationParam param, HttpServletRequest httpServletRequest) {
        try {
            List<EventRelationPO> eventRelationPOS = eventRelationService.queryEventRelationList(param);
            return Response.succ(WebResponseStatusEnum.SUCCESS.getCode().toString(), "ok", eventRelationPOS);
        } catch (Exception e) {
            log.error("dataCenterService.eventRelationList exception: {}, param: {}", e.getMessage(), param, e);
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), e.getMessage());
        }
    }

    @PostMapping("/record/file_meta/sync")
    public Response recordFileMetaSync(@RequestParam(value = "recordName") String recordName) {
        try {
            Integer result = snapshotContentService.recordFileMetaSync(recordName);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("sync_count", result);
            return Response.succ(WebResponseStatusEnum.SUCCESS.getCode().toString(), "ok", jsonObject);
        } catch (Exception e) {
            log.error("record file meta sync exception : {}", e.getMessage(), e);
            return Response.fail("10020", e.getMessage());
        }
    }

    @PostMapping("/manual/s3Sign")
    public CommonResponse manualS3Sign(@RequestBody Map<String, Object> paramMap) {
        try {
            String s3SignUrl = S3SignatureUtil.signUrl(
                    S3ClusterEnum.byCode((int) paramMap.get("cluster")), (String) paramMap.get("s3Url"), (String) paramMap.get("userClientId"),
                    (String) paramMap.get("accessKey"), (String) paramMap.get("secretKey"));
            return CommonResponse.success(s3SignUrl);
        } catch (Exception e) {
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

}