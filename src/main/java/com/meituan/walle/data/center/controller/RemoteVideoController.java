package com.meituan.walle.data.center.controller;

import com.amazonaws.util.json.Jackson;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.walle.data.center.constant.*;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.dto.BizRemoteVideoDTO;
import com.meituan.walle.data.center.entity.dto.RemoteVideoCallbackDTO;
import com.meituan.walle.data.center.entity.pojo.BizRemoteVideoRequest;
import com.meituan.walle.data.center.entity.request.RemoteVideoCallbackRequest;
import com.meituan.walle.data.center.entity.request.RemoteVideoRequest;
import com.meituan.walle.data.center.exception.RemoteVideoException;
import com.meituan.walle.data.center.mapper.BizRemoteVideoRequestMapper;
import com.meituan.walle.data.center.service.RemoteVideoService;
import com.meituan.walle.data.center.util.DatetimeUtil;
import com.meituan.walle.data.center.util.JacksonUtil;
import com.meituan.walle.data.center.util.Trace;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/05/16
 */
@InterfaceDoc(
        displayName = "远遥视频相关接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供远遥视频管理的能力。",
        scenarios = "远遥视频处理相关所有接口"
)
@RestController
@RequestMapping(value = "/remote_video")
@Slf4j
public class RemoteVideoController {

    @Resource
    private RemoteVideoService remoteVideoService;

    @Resource
    private BizRemoteVideoRequestMapper bizRemoteVideoRequestMapper;

    private static final String API_PREFIX = "/remote_video";

    private static final Long EXCEED_DURATION_TIME = 30 * 60 * 1000L;

    private static final String DEFAULT_CLOUD_STREAM = "front,back,left,right,loop";

    @PostMapping("/list")
    public Response getVideoList(@RequestBody RemoteVideoRequest remoteVideoRequest) {
        String api = "/list";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        if (remoteVideoRequest == null) {
            log.warn("traceId: {}, get list failed, for param is null!", traceId);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    "param is null!");
        }
        if (StringUtils.isBlank(remoteVideoRequest.getVin())) {
            log.warn("traceId: {}, get list failed, for lack of vin parameter", traceId);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    "lack vin parameter!");
        }

        try {
            if (remoteVideoRequest.getStream() == null) {
                log.info("traceId: {}, stream is null, set stream to default value, origin param is {}",
                        traceId, remoteVideoRequest);
                remoteVideoRequest.setStream(RemoteVideoConstant.COCKPIT);
            }
            log.info("traceId: {}, start to get remote video list, param is [{}]", traceId, remoteVideoRequest);
            List<BizRemoteVideoDTO> requestList = remoteVideoService.getRemoteVideoRequestList(remoteVideoRequest);
            log.info("traceId: {}, end get remote video list, result is [{}]", traceId, requestList);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(requestList);
        } catch (Exception e) {
            log.error("traceId: {}, failed to get remote video list, vin is {}",
                    traceId, remoteVideoRequest.getVin(), e);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(),
                "Failed to get remote video list!");
    }

    @PostMapping("/create")
    public Response create(@RequestBody RemoteVideoRequest remoteVideoRequest) {
        String traceId = Trace.generateId();
        if (remoteVideoRequest == null) {
            log.warn("traceId: {}, create get remote video task failed, for request param is null", traceId);
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    "param is null!");
        }
        if (StringUtils.isBlank(remoteVideoRequest.getVin())) {
            log.warn("traceId: {}, create get remote video task failed, for lack of vin param", traceId);
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    "lack vin parameter!");
        }
        Date startTime = DatetimeUtil.covertToDate(remoteVideoRequest.getStartTime(), DatetimeUtil.YMDHMS);
        Date endTime = DatetimeUtil.covertToDate(remoteVideoRequest.getEndTime(), DatetimeUtil.YMDHMS);
        if (endTime.getTime() - startTime.getTime() >= EXCEED_DURATION_TIME) {
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    "The duration exceed 30 minutes");
        }
        try {
            if (remoteVideoRequest.getStream() == null) {
                log.info("traceId: {}, when create task, stream is null, set stream to default value, " +
                         "origin param is {}", traceId, remoteVideoRequest);
                remoteVideoRequest.setStream(RemoteVideoConstant.COCKPIT);
            }
            User user = UserUtils.getUser();
            remoteVideoRequest.setCreator(user.getLogin());
            log.info("traceId: {}, start to create remote video task, param is [{}]", traceId, remoteVideoRequest);
            remoteVideoService.createVideoRequest(remoteVideoRequest, CharConstant.CHAR_EMPTY);
            log.info("traceId: {}, end create remote video task", traceId);
            return Response.succ();
        } catch (RemoteVideoException e) {
            log.warn("traceId: {}, failed to create remote video task, param is [{}], error msg is [{}]",
                    traceId, remoteVideoRequest, e.getMessage());
            return Response.fail(e.getCode().toString(), e.getMessage());
        } catch (Exception e) {
            log.error("traceId: {}, failed to create remote video task, param is [{}]", traceId, remoteVideoRequest, e);
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(),
                "service error");
    }

    @RequestMapping(value = "/callback", method = RequestMethod.POST)
    public Response callback(@RequestBody RemoteVideoCallbackRequest remoteVideoCallbackRequest) {
        String api = "/callback";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            RemoteVideoCallbackDTO data = remoteVideoCallbackRequest.getData();
            BizRemoteVideoRequest request = null;
            if (StringUtils.isNotBlank(data.getUid())) {
                request = bizRemoteVideoRequestMapper.selectBySubTaskId(data.getUid());
            }
            if (Objects.isNull(request)) { // 强制读主库
                ZebraForceMasterHelper.forceMasterInLocalContext();
                request = bizRemoteVideoRequestMapper.selectBySubTaskId(data.getUid());
                ZebraForceMasterHelper.clearLocalContext();
                if (Objects.isNull(request)) {
                    log.warn("bizRemoteVideoRequest still is null! callback data: {}", JacksonUtil.serialize(data));
                }
            }
            log.info("traceId: {}, this remote video task success, callback data: {}, bizRemoteVideoRequest: {}",
                    traceId, JacksonUtil.serialize(data), JacksonUtil.serialize(request));
            if (remoteVideoCallbackRequest.getCode() == RemoteVideoCodeEnum.SUCCESS.getCode() &&
                    data.getS3Url() != null && !data.getS3Url().isEmpty()) {
                log.info("traceId: {}, this remote video task success, uuid is {}", traceId, data.getUid());
                remoteVideoService.remoteVideoCallback(data, request);
            } else {
                remoteVideoService.remoteVideoExceptionCallback(data, request);
            }
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, remote video callback failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @RequestMapping(value = "/collision/recorder_callback", method = RequestMethod.POST)
    public Response collisionRecorderCallback(@RequestBody RemoteVideoCallbackRequest remoteVideoCallbackRequest) {
        String api = "/collision/recorder_callback";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            log.info("traceId: {}, collision video callback request parameter: [{}]",
                    traceId, remoteVideoCallbackRequest);
            RemoteVideoCallbackDTO data = remoteVideoCallbackRequest.getData();
            BizRemoteVideoRequest request = null;
            if (StringUtils.isNotBlank(data.getUid())) {
                request = bizRemoteVideoRequestMapper.selectBySubTaskId(data.getUid());
            }
            log.info("traceId: {}, this remote video task success, callback data: {}, bizRemoteVideoRequest: {}",
                    traceId, JacksonUtil.serialize(data), JacksonUtil.serialize(request));
            if (remoteVideoCallbackRequest.getCode() == RemoteVideoCodeEnum.SUCCESS.getCode() &&
                    data.getS3Url() != null && !data.getS3Url().isEmpty()) {
                remoteVideoService.collisionRemoteVideoCallback(data, request);
            } else {
                remoteVideoService.collisionRemoteVideoExceptionCallback(data, request);
            }
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, collision video callback failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @RequestMapping(value = "/get_cloud_stream", method = RequestMethod.POST)
    public Response getCloudStream(@RequestBody RemoteVideoRequest request) {
        String api = "/get_cloud_stream";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        if (StringUtils.isBlank(request.getVin()) ||
                StringUtils.isBlank(request.getStartTime()) ||
                StringUtils.isBlank(request.getEndTime())) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(), "请求参数为空");
        }
        if (!DatetimeUtil.checkDateValid(request.getStartTime()) ||
                !DatetimeUtil.checkDateValid(request.getEndTime())) {
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(), "请求时间格式异常");
        }
        if (StringUtils.isBlank(request.getStream())) {
            request.setStream(DEFAULT_CLOUD_STREAM);
        }
        try {
            log.info("traceId: {}, start to get cloud stream: [{}]", traceId, request);
            Map<String, Object> result = remoteVideoService.getRemoteVideoStream(request);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (RemoteVideoException e) {
            log.warn("traceId: {}, failed to get cloud stream, error msg is [{}]", traceId, e.getMessage());
            t.setDurationInMillis(System.currentTimeMillis() - start);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(e.getCode().toString(), e.getMessage());
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, get cloud stream failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @RequestMapping(value = "/delete_request", method = RequestMethod.POST)
    public Response deleteRequest(@RequestBody RemoteVideoRequest request) {
        String api = "/delete_request";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        if (StringUtils.isBlank(request.getTaskId())) {
            log.warn("traceId: {}, delete remote video request failed, for lack of vin param", traceId);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    "lack vin parameter!");
        }
        String taskId = request.getTaskId();
        try {
            User user = UserUtils.getUser();
            log.info("traceId: {}, start to logic delete remote video request by taskId [{}], user is [{}]",
                    traceId, taskId, user.getLogin());
            remoteVideoService.logicDeleteRequest(taskId);
            log.info("traceId: {}, end logic delete remote video request by taskId [{}], user is [{}]",
                    traceId, taskId, user.getLogin());
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, delete remote video request failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

}
