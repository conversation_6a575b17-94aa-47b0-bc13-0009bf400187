package com.meituan.walle.data.center.entity.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/03
 */
@Builder
@Data
public class StandardEventMessage implements Serializable {

    @JsonProperty(value = "event_type")
    private String eventType;

    @JsonProperty(value = "abstract_event_message")
    private AbstractEventMessage abstractEventMessage;
}