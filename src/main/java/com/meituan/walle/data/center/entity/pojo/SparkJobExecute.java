package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Table(name = "spark_job_execute")
@Entity
@Data
public class SparkJobExecute {
    @Id
    @GeneratedValue(strategy= GenerationType.IDENTITY)
    private Integer id;
    private String instanceUuid;
    private Integer executeId;
    private String applicationId;
    private Integer code;
    private String msg;
    private Integer pendingSeconds;
    private Integer executeSeconds;
    private Integer executeType;

    @Column(name = "update_time", insertable = false, updatable = false)
    private Date updateTime;
    @Column(name = "create_time", insertable = false, updatable = false)
    private Date createTime;
    @Column(name = "end_time")
    private Date endTime;

}
