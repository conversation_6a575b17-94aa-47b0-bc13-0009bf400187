package com.meituan.walle.data.center.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/21
 */
@Data
public class OsvizDTO {
    @JsonProperty("app_id")
    private String appId;
    @JsonProperty("app_job_id")
    private String appJobId;
    @JsonProperty("job_type")
    private String jobType;
    @JsonProperty("job_priority_tag")
    private String jobPriorityTag;
    @JsonProperty("job_name")
    private String jobName;
    @JsonProperty("task_result_callback")
    private String taskResultCallback;
    @JsonProperty("job_status_callback")
    private String jobStatusCallback;
    @JsonProperty("job_config")
    private OsvizJobConfigDTO jobConfig;
}
