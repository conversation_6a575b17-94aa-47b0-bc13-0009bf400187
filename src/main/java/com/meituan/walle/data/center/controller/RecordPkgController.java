package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.dal.recordmanager.entity.BRecordPkg;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.service.RecordPkgService;
import com.meituan.walle.data.center.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/recordPkg")
@Slf4j
public class RecordPkgController {

    @Resource
    private RecordPkgService recordPkgService;

    @GetMapping(value = "/streamServer/getRecordPkg")
    public CommonResponse getRecordPkgForStreamServer(@RequestParam String recordName,
                                                      @RequestParam String module,
                                                      @RequestParam Long startMillis) {
        log.info("[RecordPkgController#getRecordPkgForStreamServer] " +
                "call /recordPkg/streamServer/getRecordPkg api, recordName: {}, module: {}, startMillis: {}",
                recordName, module, startMillis);
        if (StringUtils.isBlank(recordName) || StringUtils.isBlank(module) || Objects.isNull(startMillis)) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "参数不能为空");
        }
        try {
            List<BRecordPkg> recordPkgList = recordPkgService.getRecordPkgForStreamServer(recordName, module, startMillis);
            return CommonResponse.success(recordPkgList);
        } catch (Exception exception) {
            log.error("[RecordPkgController#getRecordPkgForStreamServer] api /recordPkg/streamServer/getRecordPkg exception, {}",
                    exception.getMessage(), exception);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), exception.getMessage());
        }
    }

    @GetMapping(value = "/streamServer/getRecordPkgMore")
    public CommonResponse getRecordPkgMoreForStreamServer(@RequestParam String recordName,
                                                          @RequestParam String module,
                                                          @RequestParam String fileName) {
        log.info("[RecordPkgController#getRecordPkgMoreForStreamServer] " +
                "call /recordPkg/streamServer/getRecordPkgMore api, recordName: {}, module: {}, fileName: {}",
                recordName, module, fileName);
        if (StringUtils.isBlank(recordName) || StringUtils.isBlank(module) || StringUtils.isBlank(fileName)) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "参数不能为空");
        }
        try {
            List<BRecordPkg> recordPkgList = recordPkgService.getRecordPkgMoreForStreamServer(recordName, module, fileName);
            return CommonResponse.success(recordPkgList);
        } catch (Exception exception) {
            log.error("[RecordPkgController#getRecordPkgMoreForStreamServer] api /recordPkg/streamServer/getRecordPkgMore exception, {}",
                    exception.getMessage(), exception);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), exception.getMessage());
        }
    }

    @GetMapping(value = "/flinkJob/getOnboardNoticePublisherPkgInfo")
    public CommonResponse getOnboardNoticePublisherPkgInfoForFlinkJob(@RequestParam String recordName) {
        log.info("[RecordPkgController#getOnboardNoticePublisherPkgInfoForFlinkJob] " +
                "call /recordPkg/flinkJob/getOnboardNoticePublisherPkgInfo api, recordName: {}", recordName);
        if (StringUtils.isBlank(recordName)) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "参数不能为空");
        }
        try {
            List<BRecordPkg> recordPkgList = recordPkgService.getOnboardNoticePublisherPkgInfoForFlinkJob(recordName);
            return CommonResponse.success(recordPkgList);
        } catch (Exception exception) {
            log.error("[RecordPkgController#getOnboardNoticePublisherPkgInfoForFlinkJob] " +
                            "api /recordPkg/flinkJob/getOnboardNoticePublisherPkgInfo exception, {}",
                    exception.getMessage(), exception);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), exception.getMessage());
        }
    }

    /**
     * 获取record所包含的moduleList
     * 调用方：DataManager；调用方负责人：luoshiying
     */
    @GetMapping(value = "/getModuleList")
    public CommonResponse getModuleList(@RequestParam List<String> recordNameList) {
        if (CollectionUtils.isEmpty(recordNameList)) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "The param 'recordNameList' cannot be null or empty");
        }
        if (recordNameList.size() > 20) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "The number of parameters cannot exceed 20! ");
        }
        try {
            Map<String, List<String>> resultMap = recordPkgService.getModuleListOfMap(recordNameList);
            return CommonResponse.success(resultMap);
        } catch (Exception e) {
            log.error("[/recordPkg/getModuleList] exception: {}, param: {}",
                    e.getMessage(), JacksonUtil.serialize(recordNameList), e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

}
