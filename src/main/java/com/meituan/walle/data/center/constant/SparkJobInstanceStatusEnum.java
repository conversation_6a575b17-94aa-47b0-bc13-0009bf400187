package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/01/20
 */
@Getter
@AllArgsConstructor
public enum SparkJobInstanceStatusEnum {
    NOT_CREATED(-1, "not created"),
    /**
     * 未执行
     */
    PREPARE(0, "prepared"),
    /**
     * 执行中
     */
    EXECUTING(1, "executing"),
    /**
     * 成功
     */
    SUCCESS(2, "succeed"),
    /**
     * 失败
     */
    FAIL(3, "failed"),
    /**
     * 未知
     */
    UNKNOWN(4, "unknown");

    private int code;
    private String desc;

    public static SparkJobInstanceStatusEnum byOrdinal(int ord) {
        for (SparkJobInstanceStatusEnum e : SparkJobInstanceStatusEnum.values()) {
            if (e.code == ord) {
                return e;
            }
        }
        return null;
    }
}