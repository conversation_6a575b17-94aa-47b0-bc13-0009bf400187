package com.meituan.walle.data.center.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> @Beijing
 * @date 2020/3/17 下午1:51
 * Description:
 * Modified by
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RecordFileDTO {

    private Long id;
    private String recordName;
    private Integer recordDate;
    private String fileName;
    private String filePath;
    private Integer fileType;
    private String md5;
    private Integer datekey;
    private String s3Url;
    private Long fileSize;
    /**
     * 所属集群[0:北京备份|1:中卫自动车]
     */
    private Integer cluster;
    private Date createTime;
    private Date updateTime;

}
