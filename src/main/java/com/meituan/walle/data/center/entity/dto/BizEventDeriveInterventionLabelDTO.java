package com.meituan.walle.data.center.entity.dto;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/08/20
 */
public class BizEventDeriveInterventionLabelDTO {
    /**
     * 表record的record_name外键
     */
    private String recordName;

    /**
     * 接管时间
     */
    private Date interventionTime;

    /**
     * 根据record_name + intervention_time生成的caseId
     */
    private String eventId;
    /**
     * 标签类型
     * 1 tag，2 系统接管，3 终点前接管，4 电子围栏接管， 5 进场接管，6 远遥接管
     */
    private Byte labelType;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 区分标签是自动打的还是人工打的 0 自动，1 人工
     */
    private Byte source;

    /**
     * 车架号
     */
    private String vin;

    public String getRecordName() {
        return recordName;
    }

    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    public Date getInterventionTime() {
        return interventionTime;
    }

    public void setInterventionTime(Date interventionTime) {
        this.interventionTime = interventionTime;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public Byte getLabelType() {
        return labelType;
    }

    public void setLabelType(Byte labelType) {
        this.labelType = labelType;
    }

    public String getLabelName() {
        return labelName;
    }

    public void setLabelName(String labelName) {
        this.labelName = labelName;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public interface BizEventDeriveInterventionLabelDTOBuilder {
        BizEventDeriveInterventionLabelDTOBuilder recordName(String value);
        BizEventDeriveInterventionLabelDTOBuilder interventionTime(Date value);
        BizEventDeriveInterventionLabelDTOBuilder eventId(String value);
        BizEventDeriveInterventionLabelDTOBuilder labelType(Byte value);
        BizEventDeriveInterventionLabelDTOBuilder labelName(String value);
        BizEventDeriveInterventionLabelDTOBuilder source(Byte value);
        BizEventDeriveInterventionLabelDTOBuilder vin(String value);

        BizEventDeriveInterventionLabelDTO build();
    }

    public static final class DefaultBizEventDeriveInterventionLabelDTOBuilder
            implements BizEventDeriveInterventionLabelDTOBuilder {

        private BizEventDeriveInterventionLabelDTO bizEventDeriveInterventionLabelDTO =
                new BizEventDeriveInterventionLabelDTO();

        @Override
        public BizEventDeriveInterventionLabelDTOBuilder recordName(String value) {
            bizEventDeriveInterventionLabelDTO.setRecordName(value);
            return this;
        }

        @Override
        public BizEventDeriveInterventionLabelDTOBuilder interventionTime(Date value) {
            bizEventDeriveInterventionLabelDTO.setInterventionTime(value);
            return this;
        }

        @Override
        public BizEventDeriveInterventionLabelDTOBuilder eventId(String value) {
            bizEventDeriveInterventionLabelDTO.setEventId(value);
            return this;
        }

        @Override
        public BizEventDeriveInterventionLabelDTOBuilder labelType(Byte value) {
            bizEventDeriveInterventionLabelDTO.setLabelType(value);
            return this;
        }

        @Override
        public BizEventDeriveInterventionLabelDTOBuilder labelName(String value) {
            bizEventDeriveInterventionLabelDTO.setLabelName(value);
            return this;
        }

        @Override
        public BizEventDeriveInterventionLabelDTOBuilder source(Byte value) {
            bizEventDeriveInterventionLabelDTO.setSource(value);
            return this;
        }

        @Override
        public BizEventDeriveInterventionLabelDTOBuilder vin(String value) {
            bizEventDeriveInterventionLabelDTO.setVin(value);
            return this;
        }

        @Override
        public BizEventDeriveInterventionLabelDTO build() {
            return bizEventDeriveInterventionLabelDTO;
        }
    }

}
