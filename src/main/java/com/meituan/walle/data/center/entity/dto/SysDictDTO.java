package com.meituan.walle.data.center.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/04/13
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SysDictDTO {

    private Long id;

    private String dictCode;

    private String dictName;

    private String codeIndex;

    private String indexNameCn;

    private String indexNameEn;

    private String description;

    private Integer sortOrder;

    private Integer status;

    private Integer isDeleted;

    private Date createTime;

    private Date updateTime;
}