package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "record_batch")
public class RecordBatchV2 {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 硬盘使用记录id
     */
    private String bid;

    /**
     * 磁盘设备id
     */
    private String did;

    /**
     * 最后一条record产生的时间
     */
    @Column(name = "record_finish_time")
    private Date recordFinishTime;

    /**
     * 寄到机房时间
     */
    @Column(name = "checkin_time")
    private Date checkinTime;

    /**
     * 处理完成时间
     */
    @Column(name = "proc_done_time")
    private Date procDoneTime;

    /**
     * 磁盘使用量（单位：byte）
     */
    private Long used;

    /**
     * 磁盘容量（单位：byte）
     */
    private Long capacity;

    /**
     * 数据大小（单位：byte）
     */
    @Column(name = "data_size")
    private Long dataSize;

    /**
     * 自动驾驶记录总数
     */
    @Column(name = "record_count")
    private Integer recordCount;

    /**
     * 0.初始化|1.上车|2.录入数据中|3.下车|4.寄送中|5.挂载到云端|6.数据传输中|7.数据传输结束|8.从云端卸载
     */
    private Short status;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取硬盘使用记录id
     *
     * @return bid - 硬盘使用记录id
     */
    public String getBid() {
        return bid;
    }

    /**
     * 设置硬盘使用记录id
     *
     * @param bid 硬盘使用记录id
     */
    public void setBid(String bid) {
        this.bid = bid;
    }

    /**
     * 获取磁盘设备id
     *
     * @return did - 磁盘设备id
     */
    public String getDid() {
        return did;
    }

    /**
     * 设置磁盘设备id
     *
     * @param did 磁盘设备id
     */
    public void setDid(String did) {
        this.did = did;
    }

    /**
     * 获取最后一条record产生的时间
     *
     * @return record_finish_time - 最后一条record产生的时间
     */
    public Date getRecordFinishTime() {
        return recordFinishTime;
    }

    /**
     * 设置最后一条record产生的时间
     *
     * @param recordFinishTime 最后一条record产生的时间
     */
    public void setRecordFinishTime(Date recordFinishTime) {
        this.recordFinishTime = recordFinishTime;
    }

    /**
     * 获取寄到机房时间
     *
     * @return checkin_time - 寄到机房时间
     */
    public Date getCheckinTime() {
        return checkinTime;
    }

    /**
     * 设置寄到机房时间
     *
     * @param checkinTime 寄到机房时间
     */
    public void setCheckinTime(Date checkinTime) {
        this.checkinTime = checkinTime;
    }

    /**
     * 获取处理完成时间
     *
     * @return proc_done_time - 处理完成时间
     */
    public Date getProcDoneTime() {
        return procDoneTime;
    }

    /**
     * 设置处理完成时间
     *
     * @param procDoneTime 处理完成时间
     */
    public void setProcDoneTime(Date procDoneTime) {
        this.procDoneTime = procDoneTime;
    }

    /**
     * 获取磁盘使用量（单位：byte）
     *
     * @return used - 磁盘使用量（单位：byte）
     */
    public Long getUsed() {
        return used;
    }

    /**
     * 设置磁盘使用量（单位：byte）
     *
     * @param used 磁盘使用量（单位：byte）
     */
    public void setUsed(Long used) {
        this.used = used;
    }

    /**
     * 获取磁盘容量（单位：byte）
     *
     * @return capacity - 磁盘容量（单位：byte）
     */
    public Long getCapacity() {
        return capacity;
    }

    /**
     * 设置磁盘容量（单位：byte）
     *
     * @param capacity 磁盘容量（单位：byte）
     */
    public void setCapacity(Long capacity) {
        this.capacity = capacity;
    }

    /**
     * 获取数据大小（单位：byte）
     *
     * @return data_size - 数据大小（单位：byte）
     */
    public Long getDataSize() {
        return dataSize;
    }

    /**
     * 设置数据大小（单位：byte）
     *
     * @param dataSize 数据大小（单位：byte）
     */
    public void setDataSize(Long dataSize) {
        this.dataSize = dataSize;
    }

    /**
     * 获取自动驾驶记录总数
     *
     * @return record_count - 自动驾驶记录总数
     */
    public Integer getRecordCount() {
        return recordCount;
    }

    /**
     * 设置自动驾驶记录总数
     *
     * @param recordCount 自动驾驶记录总数
     */
    public void setRecordCount(Integer recordCount) {
        this.recordCount = recordCount;
    }

    /**
     * 获取0.初始化|1.上车|2.录入数据中|3.下车|4.寄送中|5.挂载到云端|6.数据传输中|7.数据传输结束|8.从云端卸载
     *
     * @return status - 0.初始化|1.上车|2.录入数据中|3.下车|4.寄送中|5.挂载到云端|6.数据传输中|7.数据传输结束|8.从云端卸载
     */
    public Short getStatus() {
        return status;
    }

    /**
     * 设置0.初始化|1.上车|2.录入数据中|3.下车|4.寄送中|5.挂载到云端|6.数据传输中|7.数据传输结束|8.从云端卸载
     *
     * @param status 0.初始化|1.上车|2.录入数据中|3.下车|4.寄送中|5.挂载到云端|6.数据传输中|7.数据传输结束|8.从云端卸载
     */
    public void setStatus(Short status) {
        this.status = status;
    }
}