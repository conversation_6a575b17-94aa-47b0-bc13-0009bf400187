package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/11/5
 */
@Table(name = "biz_accident_review")
@Data
public class BizAccidentReview {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 事故id
     */
    @Column(name = "accident_id")
    private String accidentId;

    /**
     * 事故等级，grey|blue|yellow|orange|red
     */
    @Column(name = "level")
    private String level;

    /**
     * 事故原因
     */
    @Column(name = "cause")
    private String cause;

    /**
     * 事故损伤
     */
    @Column(name = "damage")
    private String damage;

    /**
     * 事故赔偿
     */
    @Column(name = "compensation")
    private String compensation;

    /**
     * 事故影响
     */
    @Column(name = "impact")
    private String impact;

    /**
     * 责任认定
     */
    @Column(name = "confirm_responsibility")
    private String confirmResponsibility;

    /**
     * 事故奖惩
     */
    @Column(name = "reward_punishment")
    private String rewardPunishment;

    /**
     * 整改建议
     */
    @Column(name = "suggestion")
    private String suggestion;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
