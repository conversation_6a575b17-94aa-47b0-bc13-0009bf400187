package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "biz_tag_picture")
@Data
public class BizTagPicture {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    private String eventId;

    /**
     * 图片url
     */
    private String pictureUrl;

    private Date createTime;

    private Date updateTime;
}
