package com.meituan.walle.data.center.controller;

import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.enums.EventUploadTaskCreatedEnum;
import com.meituan.walle.data.center.entity.pojo.BizEventUploadTaskConfig;
import com.meituan.walle.data.center.entity.pojo.VehicleUploadRequest;
import com.meituan.walle.data.center.entity.vo.BizEventUploadTaskConfigVO;
import com.meituan.walle.data.center.entity.vo.CustomEventFastUploadTaskVO;
import com.meituan.walle.data.center.exception.EventUploadTaskCreatedException;
import com.meituan.walle.data.center.service.EventUploadTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.meituan.walle.data.center.constant.CommonConstant.NO_ROWS_INFLUENCED;

@InterfaceDoc(
    displayName = "事件回收任务",
    type = "restful",
    description = "根据事件自动创建快速回收任务",
    scenarios = "研发人员自定义事件配置，自动创建快速回收任务"
)
@Slf4j
@RestController
@RequestMapping("/event_upload_task")
public class EventUploadTaskController {

    @Resource
    private EventUploadTaskService eventUploadTaskService;

    /**
     * 添加根据事件创建快速回收任务配置，内部接口非外部调用
     * @param bizEventUploadTaskConfigVO 事件回收任务配置对象
     * @return Response 添加配置的响应结果
     */
    @RequestMapping(value = "/add_config", method = RequestMethod.POST)
    public Response addConfig(@RequestBody BizEventUploadTaskConfigVO bizEventUploadTaskConfigVO) {
        try {
            BizEventUploadTaskConfig bizEventUploadTaskConfig = new BizEventUploadTaskConfig();
            BeanUtils.copyProperties(bizEventUploadTaskConfigVO, bizEventUploadTaskConfig);
            if (eventUploadTaskService.addEventConfig(bizEventUploadTaskConfig) != NO_ROWS_INFLUENCED) {
                return Response.succ("");
            }
            return Response.fail("-1", "Add event config failed");
        } catch (Exception e) {
            log.error("[FastUploadController/addEventConfig] Add event config failed, request param is {}, "
                + "exception message is {}", bizEventUploadTaskConfigVO, e);
        }
        return Response.fail("-1", "Add event config failed");
    }

    /**
     * 根据事件创建快速回收任务
     * @param customEventFastUploadTaskVO 事件相关信息
     * @return Response 创建任务的响应结果
     */
    @RequestMapping(value = "/create_task", method = RequestMethod.POST)
    public Response createCustomFastUploadTask(@RequestBody CustomEventFastUploadTaskVO customEventFastUploadTaskVO) {
        try {
            Response response = checkCustomTaskParams(customEventFastUploadTaskVO);
            if (response != null) {
                return response;
            }
            VehicleUploadRequest vehicleUploadRequest =
                eventUploadTaskService.createFastUploadTaskForEvent(customEventFastUploadTaskVO);
            if (vehicleUploadRequest == null) {
                log.warn("[EventUploadTaskController/createCustomFastUploadTask] Create task failed, request is {}",
                    customEventFastUploadTaskVO);
                return Response.fail(EventUploadTaskCreatedEnum.NO_EFFECTIVE_CONFIG.getCode().toString(),
                    EventUploadTaskCreatedEnum.NO_EFFECTIVE_CONFIG.getMessage());
            }
            Map<String, Object> data = new HashMap<>();
            data.put("id", vehicleUploadRequest.getId());
            return Response.succ(EventUploadTaskCreatedEnum.TASK_CREATED_SUCCESSFULLY.getCode().toString(),
                EventUploadTaskCreatedEnum.TASK_CREATED_SUCCESSFULLY.getMessage(), data);
        } catch (EventUploadTaskCreatedException e) {
            log.warn("[EventUploadTaskController/createCustomFastUploadTask] Create task failed, request param is {}, "
                + "exception message is {}", customEventFastUploadTaskVO, e);
            return Response.fail(e.getCode().toString(), e.getMessage());
        } catch (Exception e) {
            log.error("[EventUploadTaskController/createCustomFastUploadTask] Create task failed, request param is {}, "
                + "exception message is {}", customEventFastUploadTaskVO, e);
            return Response.fail(EventUploadTaskCreatedEnum.TASK_CREATED_FAILED.getCode().toString(),
                EventUploadTaskCreatedEnum.TASK_CREATED_FAILED.getMessage());
        }
    }

    /**
     * 更新根据事件创建快速回收任务配置，内部接口非外部调用
     * @param bizEventUploadTaskConfigVO 事件回收任务配置对象
     * @return Response 更新加配置的响应结果
     */
    @RequestMapping(value = "/update_config", method = RequestMethod.POST)
    public Response updateConfig(@RequestBody BizEventUploadTaskConfigVO bizEventUploadTaskConfigVO) {
        try {
            BizEventUploadTaskConfig bizEventUploadTaskConfig = new BizEventUploadTaskConfig();
            BeanUtils.copyProperties(bizEventUploadTaskConfigVO, bizEventUploadTaskConfig);
            if (eventUploadTaskService.updateEventConfig(bizEventUploadTaskConfig) != NO_ROWS_INFLUENCED) {
                return Response.succ("");
            }
            return Response.fail("-1", "Update event config failed");
        } catch (Exception e) {
            log.error("[FastUploadController/updateEventConfig] Update event config failed, request param is {}, "
                    + "exception message is {}", bizEventUploadTaskConfigVO, e);
        }
        return Response.fail("-1", "Update event config failed");
    }

    private Response checkCustomTaskParams(CustomEventFastUploadTaskVO customEventFastUploadTaskVO) {
        if (customEventFastUploadTaskVO == null || customEventFastUploadTaskVO.getEventType() == null ||
            customEventFastUploadTaskVO.getEventTime() == null ||
            (customEventFastUploadTaskVO.getVin() == null && customEventFastUploadTaskVO.getVehicleName() == null)) {
            log.info("[EventUploadTaskController/createCustomFastUploadTask] Create task failed, "
                + "invalid parameters {}", customEventFastUploadTaskVO);
            return Response.fail(EventUploadTaskCreatedEnum.INVALID_TASK_PARAMETERS.getCode().toString(),
                EventUploadTaskCreatedEnum.INVALID_TASK_PARAMETERS.getMessage());
        }
        return null;
    }
}
