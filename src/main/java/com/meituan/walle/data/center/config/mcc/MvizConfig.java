package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

/**
 * <AUTHOR>
 * @Date 2022/06/30
 */
public class MvizConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "mviz.check.integrity.modules")
    public static volatile String MVIZ_CHECK_INTEGRITY_MODULES =
            "LidarPacketCollector,Perception,Planning,Prediction,Canbus,Control,Localization,Routing";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "mviz.direct.return.task.id")
    public static volatile String MVIZ_DIRECT_RETURN_TASK_ID = "3711968,3711969,3711970,3711971,3711972";
}
