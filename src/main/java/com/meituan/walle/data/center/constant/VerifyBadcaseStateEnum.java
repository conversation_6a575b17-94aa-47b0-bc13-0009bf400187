package com.meituan.walle.data.center.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * 2021/03/22
 */
public enum VerifyBadcaseStateEnum {
    INITIALIZE(1, "initialize"),
    SUBMIT(2, "submit"),
    SUBMIT_ERROR(3, "submit_error"),
    SIM_START(5, "start"),
    FINISH(6, "finish");

    @Getter
    private Integer type;

    @Getter
    private String name;

    VerifyBadcaseStateEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getNameByType(int type) {
        return Stream.of(VerifyBadcaseStateEnum.values())
                .filter(value -> value.getType() == type)
                .findFirst().orElse(INITIALIZE).getName();
    }

    public static int getTypeByName(String name) {
        return Stream.of(VerifyBadcaseStateEnum.values())
                .filter(value -> value.getName().equalsIgnoreCase(name))
                .findFirst().orElse(INITIALIZE).getType();
    }

    public static boolean isVerifyBadcaseState(String str) {
        if (Arrays.stream(VerifyBadcaseStateEnum.values())
                .anyMatch(value -> value.getName().equalsIgnoreCase(str))) {
            return true;
        }
        return false;
    }

}
