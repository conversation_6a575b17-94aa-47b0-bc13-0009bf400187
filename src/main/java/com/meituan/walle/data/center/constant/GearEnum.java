package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/05/31
 */
@Getter
@AllArgsConstructor
public enum GearEnum {

    UNKNOWN(-1, "unknown"),
    GEAR_NEUTRAL(0, "gear_neutral"),
    GEAR_DRIVE(1, "gear_drive"),
    GEAR_REVERSE(2, "gear_reverse"),
    GEAR_PARKING(3, "gear_parking"),
    GEAR_LOW(4, "gear_low"),
    GEAR_INVALID(5, "gear_invalid"),
    GEAR_NONE(6, "gear_none"),
    ;

    private int code;
    private String msg;
}
