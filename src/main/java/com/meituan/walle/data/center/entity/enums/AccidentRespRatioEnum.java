package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/06/08
 */

@Getter
@AllArgsConstructor
public enum AccidentRespRatioEnum {

    UNKNOWN(-1, "未知"),
    FIRST_LEVEL(1, "无责"),
    SECOND_LEVEL(2, "次责"),
    THIRD_LEVEL(3, "同责"),
    FOURTH_LEVEL(4, "主责"),
    FIFTH_LEVEL(5, "全责"),
    ;

    private int code;
    private String msg;

    public static Map<Integer, String> getCodeMsgMap() {
        Map<Integer, String> result = new HashMap<>();
        for (AccidentRespRatioEnum accidentRespRatioEnum : AccidentRespRatioEnum.values()) {
            result.put(accidentRespRatioEnum.getCode(), accidentRespRatioEnum.getMsg());
        }
        return result;
    }
}
