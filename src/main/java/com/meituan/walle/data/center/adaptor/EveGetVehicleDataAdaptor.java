package com.meituan.walle.data.center.adaptor;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.walle.data.center.exception.RemoteErrorException;
import com.sankuai.wallecmdb.data.eve.replay.inquire.api.thrift.IThriftVehicleHistoryInfoQueryService;
import com.sankuai.wallecmdb.data.eve.replay.inquire.api.thrift.request.VehicleDataQueryRequest;
import com.sankuai.wallecmdb.data.eve.replay.inquire.api.thrift.response.VehicleDataInfoVO;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class EveGetVehicleDataAdaptor {

    @MdpThriftClient(remoteAppKey = "com.sankuai.wallecmdb.data.replay", timeout = 2000)
    private IThriftVehicleHistoryInfoQueryService iThriftVehicleHistoryInfoQueryService;


    /**
     * 从Walle Thrift获取车辆数据
     * @param vin
     * @param startTime
     * @param endTime
     * @return
     */
    public List<VehicleDataInfoVO> getVehicleData(String vin, Long startTime, Long endTime) {
        VehicleDataQueryRequest vehicleDataQueryRequest = VehicleDataQueryRequest.builder().vin(vin)
                .startTime(startTime).endTime(endTime).build();
        log.info("EveGetVehicleDataAdaptor#getVehicleData, vehicleDataQueryRequest:{}", vehicleDataQueryRequest);
        try {
            EveThriftResponse<List<VehicleDataInfoVO>> response = iThriftVehicleHistoryInfoQueryService.getVehicleData(vehicleDataQueryRequest);
            log.info("EveGetVehicleDataAdaptor#getVehicleDataResponse, response:{}", response);
            if (Objects.isNull(response) || response.getCode() != 0 || Objects.isNull(response.getData())) {
                throw new RemoteErrorException("getVehicleData error");
            }
            return response.getData();
        } catch (Exception e) {
            log.error("EveGetVehicleDataAdaptor.getVehicleData error", e);
        }
        return new ArrayList<VehicleDataInfoVO>();
    }
}
