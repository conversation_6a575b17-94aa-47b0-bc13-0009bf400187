package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "core_dump_dispatch")
public class CoreDumpDispatch {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 表core_dump的id外键
     */
    @Column(name = "core_dump_id")
    private Long coreDumpId;

    /**
     * 分拣的module
     */
    private String module;

    @Column(name = "owner_mis_id")
    private String ownerMisId;

    @Column(name = "case_id")
    private String caseId;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取表core_dump的id外键
     *
     * @return core_dump_id - 表core_dump的id外键
     */
    public Long getCoreDumpId() {
        return coreDumpId;
    }

    /**
     * 设置表core_dump的id外键
     *
     * @param coreDumpId 表core_dump的id外键
     */
    public void setCoreDumpId(Long coreDumpId) {
        this.coreDumpId = coreDumpId;
    }

    /**
     * 获取分拣的module
     *
     * @return module - 分拣的module
     */
    public String getModule() {
        return module;
    }

    /**
     * 设置分拣的module
     *
     * @param module 分拣的module
     */
    public void setModule(String module) {
        this.module = module;
    }

    /**
     * @return owner_mis_id
     */
    public String getOwnerMisId() {
        return ownerMisId;
    }

    /**
     * @param ownerMisId
     */
    public void setOwnerMisId(String ownerMisId) {
        this.ownerMisId = ownerMisId;
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCaseId() {
        return caseId;
    }

    public void setCaseId(String caseId) {
        this.caseId = caseId;
    }
}