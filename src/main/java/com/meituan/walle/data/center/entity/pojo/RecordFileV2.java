package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "record_file")
public class RecordFileV2 {
    /**
     * 自增ID
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * RecordName
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 采集日期
     */
    @Column(name = "record_date")
    private Integer recordDate;

    /**
     * 文件名
     */
    @Column(name = "file_name")
    private String fileName;

    /**
     * 文件路径
     */
    @Column(name = "file_path")
    private String filePath;

    /**
     * 文件内容md5
     */
    private String md5;

    /**
     * s3Url
     */
    @Column(name = "s3_url")
    private String s3Url;

    /**
     * 文件类型
     */
    @Column(name = "file_type")
    private Byte fileType;

    /**
     * 文件大小(B)
     */
    @Column(name = "file_size")
    private Long fileSize;

    /**
     * s3上的文件大小(B)
     */
    @Column(name = "file_size_s3")
    private Long fileSizeS3;

    /**
     * 上传日期
     */
    private Integer datekey;

    /**
     * 首次添加时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 最新更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 快照中的最后修改时间
     */
    @Column(name = "last_modified")
    private Date lastModified;

    /**
     * 上传到S3的时间
     */
    @Column(name = "upload_time")
    private Date uploadTime;

    /**
     * hdfs 路径
     */
    @Column(name = "hdfs_path")
    private String hdfsPath;

    /**
     * hdfs 文件大小
     */
    @Column(name = "file_size_hdfs")
    private Long fileSizeHdfs;

    /**
     * S3中是否物理删除
     */
    @Column(name = "s3_is_deleted")
    private Integer s3IsDeleted;

    /**
     * 所属集群[0:北京备份|1:中卫自动车]
     */
    @Column(name = "cluster")
    private Integer cluster;

    /**
     * 压缩格式
     */
    @Column(name = "compression_format")
    private String compressionFormat;

    /**
     * 获取自增ID
     *
     * @return id - 自增ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置自增ID
     *
     * @param id 自增ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取RecordName
     *
     * @return record_name - RecordName
     */
    public String getRecordName() {
        return recordName;
    }

    /**
     * 设置RecordName
     *
     * @param recordName RecordName
     */
    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    /**
     * 获取采集日期
     *
     * @return record_date - 采集日期
     */
    public Integer getRecordDate() {
        return recordDate;
    }

    /**
     * 设置采集日期
     *
     * @param recordDate 采集日期
     */
    public void setRecordDate(Integer recordDate) {
        this.recordDate = recordDate;
    }

    /**
     * 获取文件名
     *
     * @return file_name - 文件名
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * 设置文件名
     *
     * @param fileName 文件名
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * 获取文件路径
     *
     * @return file_path - 文件路径
     */
    public String getFilePath() {
        return filePath;
    }

    /**
     * 设置文件路径
     *
     * @param filePath 文件路径
     */
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    /**
     * 获取文件内容md5
     *
     * @return md5 - 文件内容md5
     */
    public String getMd5() {
        return md5;
    }

    /**
     * 设置文件内容md5
     *
     * @param md5 文件内容md5
     */
    public void setMd5(String md5) {
        this.md5 = md5;
    }

    /**
     * 获取s3Url
     *
     * @return s3_url - s3Url
     */
    public String getS3Url() {
        return s3Url;
    }

    /**
     * 设置s3Url
     *
     * @param s3Url s3Url
     */
    public void setS3Url(String s3Url) {
        this.s3Url = s3Url;
    }

    /**
     * 获取文件类型
     *
     * @return file_type - 文件类型
     */
    public Byte getFileType() {
        return fileType;
    }

    /**
     * 设置文件类型
     *
     * @param fileType 文件类型
     */
    public void setFileType(Byte fileType) {
        this.fileType = fileType;
    }

    /**
     * 获取文件大小(B)
     *
     * @return file_size - 文件大小(B)
     */
    public Long getFileSize() {
        return fileSize;
    }

    /**
     * 设置文件大小(B)
     *
     * @param fileSize 文件大小(B)
     */
    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    /**
     * 获取s3上的文件大小(B)
     *
     * @return file_size_s3 - s3上的文件大小(B)
     */
    public Long getFileSizeS3() {
        return fileSizeS3;
    }

    /**
     * 设置s3上的文件大小(B)
     *
     * @param fileSizeS3 s3上的文件大小(B)
     */
    public void setFileSizeS3(Long fileSizeS3) {
        this.fileSizeS3 = fileSizeS3;
    }

    /**
     * 获取上传日期
     *
     * @return datekey - 上传日期
     */
    public Integer getDatekey() {
        return datekey;
    }

    /**
     * 设置上传日期
     *
     * @param datekey 上传日期
     */
    public void setDatekey(Integer datekey) {
        this.datekey = datekey;
    }

    /**
     * 获取首次添加时间
     *
     * @return create_time - 首次添加时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置首次添加时间
     *
     * @param createTime 首次添加时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取最新更新时间
     *
     * @return update_time - 最新更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置最新更新时间
     *
     * @param updateTime 最新更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取快照中的最后修改时间
     *
     * @return last_modified - 快照中的最后修改时间
     */
    public Date getLastModified() {
        return lastModified;
    }

    /**
     * 设置快照中的最后修改时间
     *
     * @param lastModified 快照中的最后修改时间
     */
    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    /**
     * 获取上传到S3的时间
     *
     * @return upload_time - 上传到S3的时间
     */
    public Date getUploadTime() {
        return uploadTime;
    }

    /**
     * 设置上传到S3的时间
     *
     * @param uploadTime 上传到S3的时间
     */
    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public String getHdfsPath() {
        return hdfsPath;
    }

    public void setHdfsPath(String hdfsPath) {
        this.hdfsPath = hdfsPath;
    }

    public Long getFileSizeHdfs() {
        return fileSizeHdfs;
    }

    public void setFileSizeHdfs(Long fileSizeHdfs) {
        this.fileSizeHdfs = fileSizeHdfs;
    }

    public Integer getS3IsDeleted() {
        return s3IsDeleted;
    }

    public void setS3IsDeleted(Integer s3IsDeleted) {
        this.s3IsDeleted = s3IsDeleted;
    }

    public Integer getCluster() {
        return cluster;
    }

    public void setCluster(Integer cluster) {
        this.cluster = cluster;
    }

    public String getCompressionFormat() {
        return compressionFormat;
    }

    public void setCompressionFormat(String compressionFormat) {
        this.compressionFormat = compressionFormat;
    }
}