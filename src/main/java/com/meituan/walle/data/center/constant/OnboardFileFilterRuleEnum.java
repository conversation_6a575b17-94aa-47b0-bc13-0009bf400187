package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/01/20
 */
@Getter
@AllArgsConstructor
public enum OnboardFileFilterRuleEnum {
    /**
     * 模块
     */
    MODULE(0, "module"),
    /**
     * 文件类型
     */
    FILE_TYPE(1, "file type"),
    /**
     * 目录
     */
    DIRECTORY(2, "directory"),

    /**
     * 固定车辆和模块
     */
    FIXED_VEHICLE_MODULE(3, "fixed vehicle module"),

    /**
     * 固定车辆和目录
     */
    FIXED_VEHICLE_FILE_TYPE(4, "fixed vehicle file type"),

    /**
     * 固定车辆和目录
     */
    FIXED_VEHICLE_DIRECTORY(5, "fixed vehicle directory"),
    ;

    private int code;
    private String msg;
}