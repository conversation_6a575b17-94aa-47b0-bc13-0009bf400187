package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/12/3.
 */
public class SimulationConstant {
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "simulation.token")
    public static volatile String SIMULATION_TOKEN;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "simulation.survive.days")
    public static volatile int SIMULATION_SURVIVE_DAYS = 180;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "simulation.delete.batch")
    public static volatile int SIMULATION_DELETE_BATCH = 100;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "simulation.publish.mpci.task.vins")
    public static volatile String SIMULATION_PUBLISH_MPCI_TASK_VINS = "";
}
