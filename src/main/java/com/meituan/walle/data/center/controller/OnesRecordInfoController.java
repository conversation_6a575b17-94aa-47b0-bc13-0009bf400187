package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.po.OnesRecordInfoPO;
import com.meituan.walle.data.center.service.OnesRecordInfoService;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/ones_record_info")
@Log4j2
public class OnesRecordInfoController {
    @Resource
    private OnesRecordInfoService onesRecordInfoService;

    @GetMapping("/put")
    public Object put(OnesRecordInfoPO onesRecordInfoVO) {

        String success;
        try {
            onesRecordInfoService.insert(onesRecordInfoVO);
            success = "success";
        } catch (Exception ex) {
            success = ex.getMessage();
            log.error("insert onesRecordInfoVO error", ex);
        }
        return Response.succ(success);
    }
}
