package com.meituan.walle.data.center.dal.config;

import com.dianping.zebra.group.jdbc.GroupDataSource;
import com.github.pagehelper.PageInterceptor;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * <AUTHOR>
 * @date 2023/05/30
 */
@Configuration
@MapperScan(basePackages = "com.meituan.walle.data.center.mapper",
        sqlSessionFactoryRef = "datacenterSqlSessionFactory")
@EnableTransactionManagement
public class DataCenterDataSourceConfig {

    @Value("${db.datacenter.jdbcref}")
    private String jdbcref;

    @Bean(name = "datacenterDataSource", initMethod = "init", destroyMethod = "close")
    public GroupDataSource zebraDataSource() {
        GroupDataSource dataSource = new GroupDataSource();
        dataSource.setJdbcRef(jdbcref);
        dataSource.setExtraJdbcUrlParams("zeroDateTimeBehavior=convertToNull");
        return dataSource;
    }

    @Bean(name = "datacenterSqlSessionFactory")
    public SqlSessionFactory setSqlSessionFactory(
            @Qualifier(value = "datacenterDataSource") GroupDataSource dataSource,
            PageInterceptor pageInterceptor) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(
                new PathMatchingResourcePatternResolver()
                        .getResources("classpath*:mapper/*Mapper.xml"));
        bean.setPlugins(new Interceptor[]{pageInterceptor});
        bean.setTypeAliasesPackage("com.meituan.walle.data.center.entity");
        tk.mybatis.mapper.session.Configuration configuration = new tk.mybatis.mapper.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        bean.setConfiguration(configuration);
        return bean.getObject();
    }

    @Bean
    @Primary
    public DataSourceTransactionManager dataSourceTransactionManager(
            @Qualifier(value = "datacenterDataSource") GroupDataSource dataSource) {
        DataSourceTransactionManager dataSourceTransactionManager = new DataSourceTransactionManager();
        dataSourceTransactionManager.setDataSource(dataSource);
        return dataSourceTransactionManager;
    }

    @Bean("datacenterTransactionTemplate")
    @Primary
    public TransactionTemplate datacenterTransactionTemplate(
            @Qualifier(value = "datacenterDataSource") GroupDataSource dataSource) {
        return new TransactionTemplate(dataSourceTransactionManager(dataSource));
    }

}
