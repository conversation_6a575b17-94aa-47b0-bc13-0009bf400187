package com.meituan.walle.data.center.config.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.meituan.walle.data.center.config.mcc.AutodriveEventConfig;
import com.meituan.walle.data.center.constant.CharConstant;
import com.meituan.walle.data.center.constant.CommonConstant;
import com.meituan.walle.data.center.constant.LiveIssueTagClientTypeEnum;
import com.meituan.walle.data.center.dal.recordmanager.entity.AutodriveEventPO;
import com.meituan.walle.data.center.entity.enums.AutodriveEventDataSourceEnum;
import com.meituan.walle.data.center.entity.enums.VehicleEventTableEnum;
import com.meituan.walle.data.center.entity.enums.VehicleEventTypeEnum;
import com.meituan.walle.data.center.entity.po.InterventionSourceEventPO;
import com.meituan.walle.data.center.entity.pojo.*;
import com.meituan.walle.data.center.entity.vo.VehicleEventVO;
import com.meituan.walle.data.center.service.VehicleInfoService;
import com.meituan.walle.data.center.util.AutodriveEventUtil;
import com.meituan.walle.data.center.util.DatetimeUtil;
import com.meituan.walle.data.center.util.JacksonUtil;
import com.meituan.walle.data.center.util.NumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;

@Component
@Slf4j
public class InterventionConverter {

    @Resource
    private VehicleInfoService vehicleInfoService;

    @Resource
    private AutodriveEventConfig autodriveEventConfig;

    private static final String DEMOTION_TAG = "自动驾驶能力问题";

    private static final String SYSTEM_NAME = "walle";

    private InterventionConverter() {}

    public InterventionSourceEventPO convertVehicleEventVoToInterventionSourceEventPO(VehicleEventVO vehicleEventVO)
            throws IOException {
        Long receiverTimestamp = System.currentTimeMillis();

        List<String> messageList = vehicleEventVO.getMessage();
        String content = messageList.get(messageList.size() - 2);
        String recordMessage = messageList.get(messageList.size() - 1);

        Long eventTimestamp = 0L;
        JsonNode contentNode = JacksonUtil.getMapper().readTree(content);
        if (Objects.nonNull(contentNode.findValue("interventionReport")) &&
                Objects.nonNull(contentNode.findValue("interventionReport").findValue("timestamp"))) {
            eventTimestamp = contentNode.findValue("interventionReport").findValue("timestamp").asLong();
        }
        if (Objects.isNull(eventTimestamp) || eventTimestamp == 0L) {
            if (String.valueOf(vehicleEventVO.getSendTimestamp()).length() == NumberUtils.NANO_LEN) {
                eventTimestamp = vehicleEventVO.getSendTimestamp() / CommonConstant.NANO_TO_MILLIS;
            } else {
                eventTimestamp = vehicleEventVO.getSendTimestamp();
            }
        }

        String recordName;
        Integer utmZone = -1;
        String utmX = CharConstant.CHAR_EMPTY;
        String utmY = CharConstant.CHAR_EMPTY;

        JsonNode recordMessageNode = JacksonUtil.getMapper().readTree(recordMessage);
        recordName = Objects.nonNull(recordMessageNode.findValue("recordName")) ?
                recordMessageNode.findValue("recordName").asText() : CharConstant.CHAR_EMPTY;

        if (Objects.nonNull(recordMessageNode.findValue("pose"))) {
            JsonNode poseNode = recordMessageNode.findValue("pose");
            utmZone = Objects.nonNull(poseNode.findValue("utmZone")) ? poseNode.findValue("utmZone").asInt() : -1;

            if (Objects.nonNull(poseNode.findValue("position"))) {
                JsonNode positionNode = recordMessageNode.findValue("position");
                utmX = Objects.nonNull(positionNode.findValue("x")) ?
                        positionNode.findValue("x").asText() : CharConstant.CHAR_EMPTY;
                utmY = Objects.nonNull(positionNode.findValue("y")) ?
                        positionNode.findValue("y").asText() : CharConstant.CHAR_EMPTY;
            }
        }

        String vehicleName = CharConstant.CHAR_EMPTY;
        String vehicleId = CharConstant.CHAR_EMPTY;
        List<VehicleInfo> vehicleInfoList = vehicleInfoService.getByVins(Collections.singletonList(vehicleEventVO.getVin()));
        if (!CollectionUtils.isEmpty(vehicleInfoList)) {
            vehicleId = vehicleInfoList.get(0).getVehicleId();
            vehicleName = vehicleInfoList.get(0).getName();
        }

        String eventName = "common" + vehicleEventVO.getNoticeType();
        String datasource = AutodriveEventDataSourceEnum.UNKNOWN.getName();
        String autodriveEventInfo = autodriveEventConfig.getAutodriveEventMap().get(String.valueOf(vehicleEventVO.getNoticeType()));
        if (!StringUtils.isEmpty(autodriveEventInfo)) {
            try {
                String[] autodriveEventInfoArray = autodriveEventInfo.split(CharConstant.CHAR_DD);
                eventName = autodriveEventInfoArray[0].trim();
                AutodriveEventDataSourceEnum dataSourceEnum = AutodriveEventDataSourceEnum.byCode(Integer.parseInt(autodriveEventInfoArray[1].trim()));
                datasource = Objects.nonNull(dataSourceEnum) ? dataSourceEnum.getName() : AutodriveEventDataSourceEnum.UNKNOWN.getName();
            } catch (Exception e) {
                log.error("[AutodriveEventServiceImpl#handle] parse autodriveEventInfo exception: {}, autodriveEventInfo: {}",
                        e.getMessage(), autodriveEventInfo, e);
            }
        }
        if (StringUtils.hasText(vehicleEventVO.getDatasource())) {
            datasource = vehicleEventVO.getDatasource();
        }

        String eventId = AutodriveEventUtil.genEventId(new Date(eventTimestamp), eventName, vehicleName);
        Map<String, Object> contentMap = new HashMap<>();
        if (!StringUtils.isEmpty(content)) {
            try {
                contentMap = JacksonUtil.getMapper().readValue(content, new TypeReference<Map<String, Object>>() {});
            } catch (Exception e) {
                log.warn("[InterventionConverter#convertVehicleEventVoToInterventionSourceEventPO] " +
                                "parse content exception: {}, content: {}",
                        e.getMessage(), content, e);
                contentMap.put("message", content);
            }
        }
        contentMap.put("vehicleEventVOSendTimestamp", vehicleEventVO.getSendTimestamp());
        contentMap.put("vehicleEventVOTransmitTimestamp", vehicleEventVO.getTransmitTimestamp());

        InterventionSourceEventPO sourceEventPO = InterventionSourceEventPO.builder()
                .eventId(eventId)
                .eventCode(vehicleEventVO.getNoticeType())
                .eventName(eventName)
                .eventTimestamp(new Timestamp(eventTimestamp))
                .senderTimestamp(new Timestamp(vehicleEventVO.getTransmitTimestamp()))
                .receiverTimestamp(new Timestamp(receiverTimestamp))
                .vin(vehicleEventVO.getVin().toUpperCase(Locale.ROOT))
                .vehicleId(vehicleId)
                .vehicleName(vehicleName)
                .recordName(recordName)
                .utmZone(utmZone)
                .utmX(utmX)
                .utmY(utmY)
                .datasource(datasource)
                .content(CollectionUtils.isEmpty(contentMap) ? CharConstant.CHAR_EMPTY : JacksonUtil.serialize(contentMap))
                .build();

        return sourceEventPO;
    }

    public BizEvents convertInterventionToBizEvents(InterventionSourceEventPO intervention,
                                                    String interventionEventId) {
        BizEvents bizEvents = new BizEvents();
        BeanUtils.copyProperties(intervention, bizEvents);
        bizEvents.setSource(VehicleEventTableEnum.DERIVE_INTERVENTION.getSource());
        bizEvents.setTransmitTime(DatetimeUtil.covertTimestampToDate13(intervention.getSenderTimestamp().getTime()));
        bizEvents.setX(intervention.getUtmX());
        bizEvents.setY(intervention.getUtmY());
        bizEvents.setSendTime(DatetimeUtil.covertTimestampToDate13(intervention.getSenderTimestamp().getTime()));
        bizEvents.setEventTime(DatetimeUtil.covertTimestampToDate13(intervention.getEventTimestamp().getTime()));
        bizEvents.setEventId(interventionEventId);
        return bizEvents;
    }

    public BizEventDeriveInterventionTemp convertItoITemp(BizEventDeriveIntervention intervention) {
        BizEventDeriveInterventionTemp interventionTemp = new BizEventDeriveInterventionTemp();
        BeanUtils.copyProperties(intervention, interventionTemp);
        log.info("[convertItoITemp] success");
        return interventionTemp;
    }

    public AutodriveEventPO convertInterventionToAutodriveEvent(InterventionSourceEventPO interventionSourceEventPO,
                                                                BizEventDeriveIntervention intervention) {
        AutodriveEventPO autodriveEvent = new AutodriveEventPO();
        BeanUtils.copyProperties(interventionSourceEventPO, autodriveEvent);

        JSONObject content = autodriveEvent.getContent() != null ?
                JSON.parseObject(autodriveEvent.getContent()) : new JSONObject();
        if (intervention.getInterventionContent() != null) {
            Object interventionContentJson = JSON.parse(intervention.getInterventionContent());
            content.put("interventionContent", interventionContentJson);
        }
        content.put("interventionDuration", intervention.getDuration());
        autodriveEvent.setContent(content.toJSONString());
        autodriveEvent.setEventId(intervention.getEventId());
        autodriveEvent.setEventCode(VehicleEventTypeEnum.INTERVENTION.getCode());
        autodriveEvent.setEventName(VehicleEventTypeEnum.INTERVENTION.name());
        return autodriveEvent;
    }

    public LiveIssueTag conertInterventionToLiveIssuetag(BizEventDeriveIntervention intervention) {
        LiveIssueTag liveIssueTag = new LiveIssueTag();
        BeanUtils.copyProperties(intervention, liveIssueTag);
        liveIssueTag.setMeasurementTimestamp(intervention.getInterventionTimestamp());
        liveIssueTag.setT(new Date(intervention.getInterventionTimestamp()));
        liveIssueTag.setTagName(DEMOTION_TAG);
        liveIssueTag.setClientType(LiveIssueTagClientTypeEnum.DEMOTION_AUTO.getRet());
        liveIssueTag.setDescription(LiveIssueTagClientTypeEnum.DEMOTION_AUTO.getMsg());
        liveIssueTag.setMisid(SYSTEM_NAME);
        liveIssueTag.setVehicleName(vehicleInfoService.getVehicleName(intervention.getVin()));
        liveIssueTag.setUuid(UUID.randomUUID().toString().replace("-", ""));
        return liveIssueTag;
    }

}
