package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/04/03
 */
@Table(name = "biz_accident_call_duty")
@Data
public class BizAccidentCallDuty {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 事故打电话场景
     */
    private Integer callType;

    /**
     * 值班日期
     */
    private Integer dutyDate;

    /**
     * 当日值班时间区间
     */
    private String dutyDayInterval;

    /**
     * 值班人misIds
     */
    private String dutyPersonMisIdList;

    /**
     * 是否逻辑删除
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
