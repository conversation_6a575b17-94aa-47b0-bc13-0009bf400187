package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SpeedLimitEnum {
    SPEED_LIMIT_SUCCESS(0, ""),

    SPEED_LIMIT_UNKNOWN_EXCEPTION(-1, "未知异常，车辆限速未解除，如需解除限速，请创建RE工单"),

    SPEED_LIMIT_CACHE_EXCEPTION(-200, "缓存异常，请刷新重新操作"),

    SPEED_LIMIT_VEHICLE_NOT_ONLINE(-201, "车辆未在线，请确保车辆处于连接状态"),

    SPEED_LIMIT_NO_PERMISSION(-202,
        "无操作权限，请确保在正确的用户组，或座席是否连接，或者是否为当前约车时段安排的安全员"),

    SPEED_LIMIT_COMMAND_TIMEOUT(-203, "命令执行超时"),

    SPEED_LIMIT_COMMAND_IS_EXECUTING(-204, "有命令执行，请等待2分钟重试"),

    SPEED_LIMIT_SYSTEM_ERROR(-205, "系统错误"),

    SPEED_LIMIT_RPC_ERROR(-206, "远程操作Thrift入口关闭"),

    SPEED_LIMIT_NO_RESULT(-207, "车辆网络条件较差，请求超时，请创建RE工单"),

    SPEED_LIMIT_PARAMETER_ERROR(-115, "参数错误, 请创建RE工单"),

    SPEED_LIMIT_SEND_MESSAGE_FAILED(-303, "发送消息失败，请创建RE工单"),

    SPEED_LIMIT_OPERATE_FREQUENT(-116, "操作过于频繁，请等待10s秒后重试"),

    SPEED_LIMIT_VEHICLE_MOVING(-304, "车辆正在行驶，请停车后重试"),

    SPEED_LIMIT_CONTROL_MODE_NON_COMPLIANT(-305, "车辆控制模式不符合要求，请切无控制后重试"),

    SPEED_LIMIT_VEHICLE_GEAR_NON_COMPLIANT(-306, "车辆档位不符合要求, 请更换P档后重试");

    private int code;

    private String message;

    public static String getMsgByCode(int code) {
        for (SpeedLimitEnum en : SpeedLimitEnum.values()) {
            if (en.getCode() == code) {
                return en.getMessage();
            }
        }
        return null;
    }

}
