package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2022/12/27 19:57
 * @description
 */
@AllArgsConstructor
@Getter
public enum MadGeneralCaseTypeEnum {
    DEPARTURE_FAILED("departure-failed", "发车失败"),
    SIMULATION("simulation", "仿真CASE"),
    ;
    private String name;
    private String desc;

    public static MadGeneralCaseTypeEnum lookup(String caseType) {
        return Arrays.stream(MadGeneralCaseTypeEnum.values()).filter(e -> e.getName().equals(caseType))
                .findFirst().orElse(null);
    }
}
