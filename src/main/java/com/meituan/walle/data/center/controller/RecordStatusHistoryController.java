package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.po.RecordStatusHistoryPO;
import com.meituan.walle.data.center.entity.pojo.RecordStatusHistory;
import com.meituan.walle.data.center.service.RecordStatusHistoryService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping(value = "/record_status_history")
public class RecordStatusHistoryController {
    @Resource
    private RecordStatusHistoryService service;

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Map<String, Object> manualAddRecordStatus(@RequestBody @Valid RecordStatusHistoryPO po) {
        Map<String, Object> result = new HashMap<>();
        RecordStatusHistory recordStatusHistory = RecordStatusHistory.builder()
                .recordName(po.getRecordName())
                .status(po.getStatus())
                .build();

        int response = service.insert(recordStatusHistory);
        if (response > 0) {
            result.put("code", 200);
            result.put("msg", "ok");
        } else {
            result.put("code", 500);
            result.put("msg", "插入失败，可能是参数错误");
        }
        return result;
    }
}
