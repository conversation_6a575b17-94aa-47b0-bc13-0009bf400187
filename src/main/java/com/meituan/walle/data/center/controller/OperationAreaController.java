package com.meituan.walle.data.center.controller;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.S3Object;
import com.google.protobuf.TextFormat;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.CharConstant;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.request.SignalTimingInfoRequest;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.grpc.operation.area.OperationAreaServiceGrpc;
import com.meituan.walle.data.center.grpc.operation.area.OperationAreaServiceOuterClass;
import io.grpc.Grpc;
import io.grpc.ManagedChannel;
import io.grpc.TlsChannelCredentials;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import walle.OperationAreaOuterClass;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping(value = "/operation/area")
public class OperationAreaController {

    @Resource(name = "s3ClientBeijingInner")
    private AmazonS3 s3ClientBeijingInner;

    private static final String OPERATION_AREA_SIGNAL_SOURCE = "Auto-TLTiming";


    private OperationAreaServiceGrpc.OperationAreaServiceBlockingStub operationAreaStub;

    @PostConstruct
    public void initGrpcClient() {
        String ipPort = SysParamsConfig.live_exchange_service_ip_port;
        String[] ipPortArr = ipPort.split(CharConstant.CHAR_MH);
        String ip = ipPortArr[0];
        int port = Integer.parseInt(ipPortArr[1]);


        try (
                InputStream trustCertCollectionInputStream = OperationAreaController.class
                        .getResourceAsStream("/auth/ca.cer");
                InputStream clientCertChainInputStream = OperationAreaController.class
                        .getResourceAsStream("/auth/hmi-common.cer");
                InputStream clientPrivateKeyInputStream = OperationAreaController.class
                        .getResourceAsStream("/auth/hmi-common.pem");
        ) {
            TlsChannelCredentials.Builder tlsBuilder = TlsChannelCredentials.newBuilder();
            if (trustCertCollectionInputStream != null) {
                tlsBuilder.trustManager(trustCertCollectionInputStream);
            }
            if (clientCertChainInputStream != null && clientPrivateKeyInputStream != null) {
                tlsBuilder.keyManager(clientCertChainInputStream, clientPrivateKeyInputStream);
            }
            ManagedChannel channel = Grpc.newChannelBuilderForAddress(ip, port, tlsBuilder.build()).build();
            operationAreaStub = OperationAreaServiceGrpc.newBlockingStub(channel).withWaitForReady();
        } catch (Exception e) {
            log.error("set key error", e);
        }

    }

    @PostMapping("/add/signal_timing_info")
    public CommonResponse addSignalTimingInfoBatch(@RequestBody SignalTimingInfoRequest request) {
        try (
                S3Object object = s3ClientBeijingInner.getObject(request.getBucket(), request.getS3Key());
                InputStreamReader reader = new InputStreamReader(object.getObjectContent());
        ) {
            OperationAreaOuterClass.OperationAreas.Builder areaBuilder =
                    OperationAreaOuterClass.OperationAreas.newBuilder();
            TextFormat.merge(reader, areaBuilder);

            List<OperationAreaOuterClass.OperationArea> operationAreaList = areaBuilder.getOperationAreaList();
            Map<String, List<OperationAreaOuterClass.OperationArea>> adMapNameMapOperationList = new HashMap<>();
            operationAreaList.forEach(item -> {
                String admapName = item.getAdmapInfo().getAdmapName();
                List<OperationAreaOuterClass.OperationArea> operationAreas =
                        adMapNameMapOperationList.computeIfAbsent(admapName, i -> new ArrayList<>());
                operationAreas.add(item);
            });
            adMapNameMapOperationList.forEach((key, value) -> {
                OperationAreaServiceOuterClass.AddOperationAreaBatchRequest.Builder requestBuilder =
                        OperationAreaServiceOuterClass.AddOperationAreaBatchRequest.newBuilder();
                requestBuilder.setSource(OPERATION_AREA_SIGNAL_SOURCE);
                requestBuilder.addAllItem(value);
                requestBuilder.setAdmapInfo(value.get(0).getAdmapInfo());
                OperationAreaServiceOuterClass.AddOperationAreaBatchResponse addResponse =
                        operationAreaStub.addOperationAreaBatch(requestBuilder.build());
                if (addResponse.getCode() != 0) {
                    log.error("AddOperationAreaBatchRequest error: {}", addResponse);
                }
            });

            return CommonResponse.success();
        } catch (Exception e) {
            log.error("add batch signal timing info exception", e);
        }
        return CommonResponse.fail(WebResponseStatusEnum.FAILED.getCode(), WebResponseStatusEnum.FAILED.getMsg());
    }

    @PutMapping(value = "/del/signal_timing_info")
    public CommonResponse deleteBatchSignalTimingInfo() {
        try {
            OperationAreaServiceOuterClass.DeleteOperationAreaBatchRequest.Builder builder =
                    OperationAreaServiceOuterClass.DeleteOperationAreaBatchRequest.newBuilder();
            builder.setSource(OPERATION_AREA_SIGNAL_SOURCE);
            OperationAreaServiceOuterClass.DeleteOperationAreaBatchResponse delResponse =
                    operationAreaStub.deleteOperationAreaBatch(builder.build());
            if (delResponse.getCode() == 0) {
                return CommonResponse.success();
            }
        } catch (Exception e) {
            log.error("delete batch signal timing info exception", e);
        }
        return CommonResponse.fail(WebResponseStatusEnum.FAILED.getCode(), WebResponseStatusEnum.FAILED.getMsg());
    }

}
