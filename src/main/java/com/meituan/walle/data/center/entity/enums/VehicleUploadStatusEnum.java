package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/04
 */
@AllArgsConstructor
@Getter
public enum VehicleUploadStatusEnum {

    START_UPLOADING(5, "开始上传"),
    UPLOAD_FINISHED( 100, "上传完成"),
    UPLOAD_FINISHED_MISSING( 101, "成功-不完整"),
    UPLOAD_FINISHED_DISCONTINUITY( 102, "成功-不连续"),
    UPLOAD_INTERRUPT( 401, "上传关机中断"),
    UPLOAD_FAILED_FILE_NOT_FOUND(404, "文件找不到");

    private Integer code;
    private String msg;

}
