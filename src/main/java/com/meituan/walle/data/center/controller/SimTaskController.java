package com.meituan.walle.data.center.controller;

import com.alibaba.fastjson.JSONArray;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.dto.SimTaskStatusDTO;
import com.meituan.walle.data.center.entity.vo.SimTaskFileVO;
import com.meituan.walle.data.center.entity.vo.SimTaskStatusVO;
import com.meituan.walle.data.center.entity.vo.SimTaskVO;
import com.meituan.walle.data.center.service.SimTaskService;
import com.meituan.walle.data.center.service.impl.DataCenterServiceImpl;
import com.meituan.walle.data.center.util.ExceptionHelper;
import com.meituan.walle.data.center.util.Trace;
import com.sankuai.walle.wcdp.data.center.iface.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/sim_task")
public class SimTaskController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SimTaskController.class);

    @Resource
    private DataCenterServiceImpl dataCenterService;

    @Autowired
    private SimTaskService simTaskService;

    @PostMapping("/syncSimTaskBatch")
    public Object syncSimTaskBatch(@RequestBody SimTaskVO simTaskVO) {
        String traceId = Trace.generateId();
        Result result = new Result();
        try {
            result = dataCenterService.syncSimTaskBatch(simTaskVO.getList());
        } catch (Exception exp) {
            result.code = 10020;
            result.message = MessageFormat.format("traceId: {0}, error details: {1}",
                    traceId, exp.getMessage());
            LOGGER.error("traceId: {}, syncSimTaskBatch encounter Exception : {}",
                    traceId, ExceptionHelper.getStackTraceString(exp));
        }
        return Response.builder().ret(String.valueOf(result.getCode())).msg(result.getMessage()).build();
    }

    @GetMapping("/querySimTaskStatusByPrimaryTaskId")
    public Response querySimTaskStatusByPrimaryTaskId(@RequestParam String primaryTaskId) {
        try {
            if (StringUtils.isBlank(primaryTaskId)) {
                return Response.fail("101", "abnormal input");
            }

            List<SimTaskStatusDTO> resultSimTaskStatusDTOList =
                    simTaskService.querySimTaskStatusByPrimaryTaskId(primaryTaskId);

            JSONArray data = ListObjectToString(resultSimTaskStatusDTOList);

            return Response.succ(data);
        } catch (Exception e) {
            LOGGER.error("querySimTaskStatusByPrimaryTaskId error:", e);
            return Response.fail("102", e.getMessage());
        }
    }


    @GetMapping("/querySimTaskStatusByTaskId")
    public Response querySimTaskStatusByTaskId(@RequestParam String taskId) {
        try {
            if (StringUtils.isBlank(taskId)) {
                return Response.fail("101", "abnormal input");
            }

            List<SimTaskStatusDTO> resultSimTaskStatusDTOList = simTaskService.querySimTaskStatusByTaskId(taskId);

            JSONArray data = ListObjectToString(resultSimTaskStatusDTOList);

            return Response.succ(data);
        } catch (Exception e) {
            LOGGER.error("querySimTaskStatusByTaskId error:", e);
            return Response.fail("102", e.getMessage());
        }
    }


    @GetMapping("/querySimTaskStatusBatch")
    public Response querySimTaskStatusBatch(
            @Valid @RequestBody SimTaskStatusVO.SimTaskStatusParam simTaskStatusParam) {
        try {
            List<SimTaskStatusDTO> simTaskStatusDTOS = new ArrayList<>();

            for (SimTaskStatusVO vo : simTaskStatusParam.getList()) {
                SimTaskStatusDTO dto = new SimTaskStatusDTO();
                dto.setTaskId(vo.getTaskId());
                dto.setAdsId(vo.getAdsId());
                dto.setLoopIndex(vo.getLoopIndex());
                simTaskStatusDTOS.add(dto);
            }

            if (CollectionUtils.isEmpty(simTaskStatusDTOS)) {
                return Response.fail("101", "abnormal input");
            }

            List<SimTaskStatusDTO> resultSimTaskStatusDTOList =
                    simTaskService.querySimTaskStatusBatch(simTaskStatusDTOS);

            JSONArray data = ListObjectToString(resultSimTaskStatusDTOList);

            return Response.succ(data);
        } catch (Exception e) {
            LOGGER.error("querySimTaskStatusBatch error:", e);
            return Response.fail("102", e.getMessage());
        }
    }


    private JSONArray ListObjectToString(List<SimTaskStatusDTO> resultSimTaskStatusDTOList) {
        List<SimTaskStatusVO> simTaskStatusVOList = new ArrayList<>();

        for (SimTaskStatusDTO dto : resultSimTaskStatusDTOList) {
            SimTaskStatusVO vo = new SimTaskStatusVO();
            vo.setPrimaryTaskId(dto.getPrimaryTaskId());
            vo.setTaskId(dto.getTaskId());
            vo.setAdsId(dto.getAdsId());
            vo.setLoopIndex(dto.getLoopIndex());
            vo.setSyncToWopState(dto.getSyncToWopState());

            simTaskStatusVOList.add(vo);
        }

        return (JSONArray) JSONArray.toJSON(simTaskStatusVOList);
    }

    @PostMapping("/syncSimTaskFileBatch")
    public Object syncSimTaskFileBatch(@RequestBody SimTaskFileVO simTaskFileVO) {
        String traceId = Trace.generateId();
        Result result = new Result();
        try {
            result = dataCenterService.insertSimTaskFileBatch(simTaskFileVO.getList());
        } catch (Exception exp) {
            result.code = 10020;
            result.message = MessageFormat.format("traceId: {0}, error details: {1}",
                    traceId, exp.getMessage());
            LOGGER.error("traceId: {}, syncSimTaskFileBatch encounter Exception : {}",
                    traceId, ExceptionHelper.getStackTraceString(exp));
        }
        return Response.builder().ret(String.valueOf(result.getCode())).msg(result.getMessage()).build();
    }
}
