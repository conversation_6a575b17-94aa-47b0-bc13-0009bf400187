package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/01/18
 */
@Table(name = "biz_onboard_file")
@Data
public class BizOnboardFile {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件类型(文件所属目录)
     */
    private Integer fileType;

    /**
     * 文件大小(b)
     */
    private Long fileSize;

    /**
     * 文件创建时间
     */
    private Date fileCreateTime;

    /**
     * 文件最后修改时间
     */
    private Date fileLastModifyTime;

    /**
     * recordname
     */
    private String recordName;

    /**
     * 硬盘WWN编码
     */
    private String diskId;

    /**
     * 车端文件全路径
     */
    private String fileAbsolutePath;

    /**
     * 文件是否在车端被删除，0表示否，1表示是
     */
    private Integer isDeleteOnboard;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}