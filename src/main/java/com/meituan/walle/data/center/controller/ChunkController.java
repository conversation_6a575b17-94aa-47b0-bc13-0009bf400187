package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.service.ChunkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/26 下午12:05
 * Description:
 * Modified by
 */
@RestController
@RequestMapping("/chunkServer")
@Validated
public class ChunkController {
    
    @Autowired
    ChunkService chunkService;

    /**
     * 根据{@param recordDate}和{@param vehicleName}获取recordList
     *
     * @param recordDate
     * @param vehicleName
     * @return
     */
    @GetMapping("/recordList")
    public Object getRecordList(@RequestParam("recordDate") int recordDate,
                                @RequestParam("vehicleName") String vehicleName) {
        return Response.succ(chunkService.getRecordList(recordDate, vehicleName));
    }

    /**
     * 根据{@param recordName}和{@param recordName}获取recordFileList
     *
     * @param recordName
     * @param fileType
     * @return
     */
    @GetMapping("/recordFileList")
    public Object getRecordFileList(@NotBlank(message = "recordName cannot be null") String recordName,
                                    @RequestParam(required = false, defaultValue = "-1") int fileType) {
        return Response.succ(chunkService.getRecordFileList(recordName, fileType));
    }

    /**
     * 获取recordCaseList
     *
     * @param recordName
     * @param vehicleName
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/recordCaseList")
    public Object getRecordCaseList(@RequestParam(required = false, defaultValue = "") String recordName,
                                    @RequestParam(required = false, defaultValue = "") String vehicleName,
                                    @RequestParam(required = false, defaultValue = "-1") long startTime,
                                    @RequestParam(required = false, defaultValue = "-1") long endTime) {
        return Response.succ(chunkService.getRecordCaseList(recordName, vehicleName, startTime, endTime));
    }

    /**
     * 根据{@param onesIssueId}获取recordName
     *
     * @param onesIssueId
     * @return
     */
    @GetMapping("/recordNameByOnesIssueId")
    public Object getRecordNameByOnesIssueId(@RequestParam("onesIssueId") String onesIssueId) {
        return Response.succ(chunkService.getRecordNameByOnesIssueId(onesIssueId));
    }

    /**
     * 获取recordPkgList
     *
     * @param recordName
     * @param module
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/recordPkgList")
    public Object getRecordPkgList(@RequestParam(required = false, defaultValue = "") String recordName,
                                   @RequestParam(required = false, defaultValue = "") String module,
                                   @RequestParam(required = false, defaultValue = "-1") long startTime,
                                   @RequestParam(required = false, defaultValue = "-1") long endTime) {
        return Response.succ(chunkService.getRecordPkgList(recordName, module, startTime, endTime));
    }

    /**
     * 根据{@param onesIssueId}获取 record pkg 列表
     *
     * @param onesIssueId
     * @param beforeSeconds
     * @param afterSeconds
     * @return
     */
    @GetMapping("/recordPkgList/onesIssueIdAndTimeRange")
    public Object getRecordPkgListByOnesIssueIdAndTimeRange(@RequestParam("onesIssueId") String onesIssueId,
                                                            @RequestParam("beforeSeconds") int beforeSeconds,
                                                            @RequestParam("afterSeconds") int afterSeconds) {
        return Response.succ(chunkService
                .getRecordPkgListByOnesIssueIdAndTimeRange(onesIssueId, beforeSeconds, afterSeconds));
    }
}
