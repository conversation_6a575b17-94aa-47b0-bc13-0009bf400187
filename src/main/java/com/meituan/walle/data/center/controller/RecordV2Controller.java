package com.meituan.walle.data.center.controller;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mtrace.instrument.util.JsonUtil;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.walle.data.center.component.SpringApplicationContext;
import com.meituan.walle.data.center.config.mcc.CraneConfig;
import com.meituan.walle.data.center.constant.*;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.enums.S3ClusterEnum;
import com.meituan.walle.data.center.entity.request.UpdateRecordRequest;
import com.meituan.walle.data.center.entity.enums.RecordExtractFixModeEnum;
import com.meituan.walle.data.center.entity.message.RecordForCreateTaskMessage;
import com.meituan.walle.data.center.entity.pojo.BizVehicleDataRelatedConfig;
import com.meituan.walle.data.center.entity.pojo.RecordV2;
import com.meituan.walle.data.center.entity.vo.RecordParamVO;
import com.meituan.walle.data.center.handle.IRedisHandler;
import com.meituan.walle.data.center.mapper.BizVehicleDataRelatedConfigMapper;
import com.meituan.walle.data.center.mapper.RecordV2Mapper;
import com.meituan.walle.data.center.service.RecordV2Service;
import com.meituan.walle.data.center.task.AutoCarJobExtract;
import com.meituan.walle.data.center.task.autocar.AutoCarJob;
import com.meituan.walle.data.center.task.autocar.RealtimeRecordFromS3JobExtract;
import com.meituan.walle.data.center.task.autocar.RecordFromHDFSJobExtract;
import com.meituan.walle.data.center.task.autocar.RecordFromS3JobExtract;
import com.meituan.walle.data.center.util.JacksonUtil;
import com.meituan.walle.data.center.util.S3Util;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

@RestController
@RequestMapping("/record_v2")
public class RecordV2Controller {

    private static final Logger log = LoggerFactory.getLogger(RecordV2Controller.class);

    @Resource
    private SpringApplicationContext context;

    @Autowired
    private S3Util s3Util;

    @Resource
    private RecordV2Service recordV2Service;

    @Value("${s3.bucket.extractJob}")
    private String extractJobBucket;

    @Autowired
    private RealtimeRecordFromS3JobExtract realtimeRecordFromS3JobExtract;

    @Autowired
    private IRedisHandler redisHandler;

    @Resource
    private RecordV2Mapper recordV2Mapper;

    @Lazy
    @Resource(name = "realtimeRecordProducer")
    private IProducerProcessor<String, String> realtimeRecordProducer;

    @Resource
    private BizVehicleDataRelatedConfigMapper bizVehicleDataRelatedConfigMapper;

    @PostMapping("/parse_record_info")
    public Response parseRecordInfo(@RequestBody List<String> recordNames) {
        if (!CollectionUtils.isEmpty(recordNames)) {
            recordNames.stream().forEach(recordName -> {
                try {
                    recordV2Service.parseRecordInfo(recordName);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
        return Response.succ();
    }

    @MethodDoc(
            displayName = "更新record碎片化标识",
            description = "车端上报已经碎片化的根record列表，云端进行记录标识")
    @PostMapping(value = "/fragmented")
    public Response fragmented(@RequestBody RecordParamVO param) {
        log.info("[RecordV2Controller] fragmented, param: {}", JsonUtil.serialize(param));
        if (param == null) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "param is null !");
        }
        String vin = param.getVin();
        if (StringUtils.isBlank(vin)) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "vin is null or '' !");
        }
        List<String> listFragmentedRecordName = param.getRecordFragmented();
        if (CollectionUtils.isEmpty(listFragmentedRecordName)) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "recordFragmented is null or empty !");
        }
        Transaction t = Cat.newTransaction(CatConstant.Record, "fragmented");
        long start = System.currentTimeMillis();
        try {
            BizVehicleDataRelatedConfig config = bizVehicleDataRelatedConfigMapper.selectByVin(vin);
            boolean isNeedDeleted = config != null && !config.getIsSendDisk();
            if (!isNeedDeleted) {
                return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode()
                        .toString(), "this vehicle is not business vehicle !");
            }
            final List<String> listExistRecord = new ArrayList<>();
            final List<String> listNotExistRecord = new ArrayList<>();
            for (String recordName : listFragmentedRecordName) {
                RecordV2 recordV2 = recordV2Service.selectByRecordName(recordName);
                if (recordV2 == null) {
                    listNotExistRecord.add(recordName);
                } else {
                    // 如果当前record已经标识为碎片化，就不再更新。
                    if (RecordFragmentedEnum.FRAGMENTED.getCode().equals(recordV2.getFragmented())) {
                        continue;
                    }
                    listExistRecord.add(recordName);
                }
            }
            if (!CollectionUtils.isEmpty(listNotExistRecord)) {
                String s = Joiner.on(",").join(listNotExistRecord);
                log.info("this record {} not reported !", s);
            }
            List<List<String>> listRecordNamePartition =
                    Lists.partition(listExistRecord, CommonConstant.PARTITION_SIZE);
            listRecordNamePartition.forEach(partition -> recordV2Service.updateFragmentedByRecordName(partition));
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ("");
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("fragmented fail, request param is {}", param, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "fragmented fail !");
        } finally {
            t.complete();
        }
    }

    @PostMapping("/autoCarJobExtract")
    public Response autoCarJobExtract(@RequestParam String recordCreateDate) {
        try {
            log.info("[/record_v2/autoCarJobExtract] manual trigger autoCarJobExtract begin...");
            AutoCarJob job = new AutoCarJob(
                    "AutoCarJobExtract_Manual_Mode1",
                    CraneConfig.TRIGGER_SPARK_UPLOAD_INTERVAL,
                    s3Util.getS3Client(S3ClusterEnum.BJ_BAK),
                    extractJobBucket,
                    RecordExtractFixModeEnum.RECORD_CRETE_TIME,
                    recordCreateDate,
                    null);
            AutoCarJobExtract hdfsJobExtract = context.getBean(RecordFromHDFSJobExtract.class);
            hdfsJobExtract.trigger(job);
            AutoCarJobExtract s3JobExtract = context.getBean(RecordFromS3JobExtract.class);
            s3JobExtract.trigger(job);
            log.info("[/record_v2/autoCarJobExtract] manual trigger autoCarJobExtract end...");
        } catch (Exception e) {
            log.error("api /record_v2/autoCarJobExtract execute excepted", e);
            return Response.fail("10020", e.getMessage());
        }
        return Response.succ();
    }

    @GetMapping("/getCacheCreateTaskRecord")
    public Response getCacheCreateTaskRecord() {
        final Map<String, String> result = new HashMap<>();
        try {
            JSONObject jsonObject = new JSONObject();
            Map<String, String> allCreateTaskRecordMap =
                    redisHandler.getAllRealtimeRecord();
            Set<String> allTreeSet = new TreeSet<>();
            for (Map.Entry<String, String> entry : allCreateTaskRecordMap.entrySet()) {
                allTreeSet.add(entry.getKey());
            }
            jsonObject.put("all_create_task_record_count", allTreeSet.size());
            jsonObject.put("all_create_task_record_name_list", JacksonUtil.toJsonArray(allTreeSet));

            Map<String, String> dissatisfyCreateTaskRecordMap =
                    redisHandler.getAllDissatisfyRealtimeRecord();
            Set<String> dissatisfyTreeSet = new TreeSet<>();
            for (Map.Entry<String, String> entry : dissatisfyCreateTaskRecordMap.entrySet()) {
                dissatisfyTreeSet.add(entry.getKey());
            }
            jsonObject.put("dissatisfy_create_task_record_count", dissatisfyTreeSet.size());
            jsonObject.put("dissatisfy_create_task_record_name_list", JacksonUtil.toJsonArray(dissatisfyTreeSet));

            result.put(ResponseConstant.RESULT_RET_NAME, WebResponseStatusEnum.SUCCESS.getCode().toString());
            result.put(ResponseConstant.RESULT_MSG_NAME, WebResponseStatusEnum.SUCCESS.getMsg());
            return Response.result(result, jsonObject);
        } catch (Exception e) {
            result.put(ResponseConstant.RESULT_RET_NAME, WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString());
            result.put(ResponseConstant.RESULT_MSG_NAME, WebResponseStatusEnum.LOGICAL_EXCEPTION.getMsg());
            result.put(ResponseConstant.RESULT_DATA_NAME, e.getMessage());
            log.error("getCacheCreateTaskRecord fail !", e);
            return Response.result(result);
        }
    }


    @PostMapping("/refreshRealTimeRecord")
    public Response refreshRealTimeRecord(@RequestParam String beginTime, @RequestParam String endTime) {
        final Map<String, String> result = new HashMap<>();
        try {
            List<RecordV2> list = recordV2Mapper.selectByDateInterval(beginTime, endTime);
            if (CollectionUtils.isEmpty(list)) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("send_count", 0);
                result.put(ResponseConstant.RESULT_RET_NAME, WebResponseStatusEnum.SUCCESS.getCode().toString());
                result.put(ResponseConstant.RESULT_MSG_NAME, WebResponseStatusEnum.SUCCESS.getMsg());
                return Response.result(result, jsonObject);
            }

            int count = 0;
            for (RecordV2 recordV2 : list) {
                String message = JacksonUtil.toJson(RecordForCreateTaskMessage
                        .builder()
                        .vin(recordV2.getVin())
                        .recordName(recordV2.getRecordName())
                        .build());
                realtimeRecordProducer.sendMessage(message);
                count++;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("send_count", count);

            result.put(ResponseConstant.RESULT_RET_NAME, WebResponseStatusEnum.SUCCESS.getCode().toString());
            result.put(ResponseConstant.RESULT_MSG_NAME, WebResponseStatusEnum.SUCCESS.getMsg());
            return Response.result(result, jsonObject);
        } catch (Exception e) {
            result.put(ResponseConstant.RESULT_RET_NAME, WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString());
            result.put(ResponseConstant.RESULT_MSG_NAME, WebResponseStatusEnum.LOGICAL_EXCEPTION.getMsg());
            result.put(ResponseConstant.RESULT_DATA_NAME, e.getMessage());
            log.error("refreshRealTimeRecord fail !", e);
            return Response.result(result);
        }
    }

    @PostMapping("/batch_update_record")
    public Response batchUpdateRecord(@RequestBody List<UpdateRecordRequest> updateRecordRequestList) {
        final Map<String, String> result = new HashMap<>();
        try {
            if (CollectionUtils.isEmpty(updateRecordRequestList)) {
                result.put(ResponseConstant.RESULT_RET_NAME, WebResponseStatusEnum.COLLECTION_IS_EMPTY.getCode().toString());
                result.put(ResponseConstant.RESULT_MSG_NAME, "Record list is empty.");
            } else {
                int i = recordV2Service.batchUpdateRecord(updateRecordRequestList);
                result.put(ResponseConstant.RESULT_RET_NAME, WebResponseStatusEnum.SUCCESS.getCode().toString());
                result.put(ResponseConstant.RESULT_MSG_NAME, WebResponseStatusEnum.SUCCESS.getMsg());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("updateCount", i);
                return Response.result(result, jsonObject);
            }
        } catch (Exception e) {
            result.put(ResponseConstant.RESULT_RET_NAME, WebResponseStatusEnum.FAILED.getCode().toString());
            result.put(ResponseConstant.RESULT_MSG_NAME, e.getMessage());
        }
        return Response.result(result);
    }

    @GetMapping("/query_record")
    public Response queryRecordByRecordName(@RequestParam String recordName) {
        if (StringUtils.isEmpty(recordName)) {
            return Response.fail(ResponseCodeEnum.LACK_PARAMETER.getCode(), "Record name cannot be empty.");
        }
        try {
            RecordV2 recordV2 = recordV2Service.selectByRecordName(recordName);
            if (recordV2 == null) {
                return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), "Record not found.");
            }
            return Response.succ(recordV2);
        } catch (Exception e) {
            log.error("[/record_v2/query_record] query record info failed, recordName {}", recordName, e);
            return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), "Request record info failed.");
        }
    }

    @PostMapping("/refresh/resvInfo")
    public Response refreshResvInfo(@RequestParam String beginDate, @RequestParam String endDate) {
        try {
            String result = recordV2Service.refreshResvInfo(beginDate, endDate);
            return Response.succ(result);
        } catch (Exception exception) {
            log.error("[/record_v2/refresh/resvInfo] refresh resvInfo exception: {}", exception.getMessage(), exception);
            return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), exception.getMessage());
        }
    }

}
