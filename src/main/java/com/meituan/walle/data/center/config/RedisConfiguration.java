package com.meituan.walle.data.center.config;

import com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> @Beijing
 * @date 2018/10/19 下午6:14
 * Description:
 * Modified by
 */
@Configuration
public class RedisConfiguration {
    /**
     * SquirrelClient
     *
     * @param clusterName
     * @param readTimeout
     * @param routerType
     * @param poolMaxIdle
     * @param poolMaxTotal
     * @param poolWaitMillis
     * @param poolMinIdle
     * @param serializeType
     * @return
     */
    @Bean(name = "redisClientBeanFactory")
    public RedisClientBeanFactory redisClientBeanFactory(@Value("${redis.cluster-name}") String clusterName,
                                                         @Value("${redis.read-timeout}") int readTimeout,
                                                         @Value("${redis.router-type}") String routerType,
                                                         @Value("${redis.pool-max-idle}") int poolMaxIdle,
                                                         @Value("${redis.pool-max-total}") int poolMaxTotal,
                                                         @Value("${redis.pool-wait-millis}") int poolWaitMillis,
                                                         @Value("${redis.pool-min-idle}") int poolMinIdle,
                                                         @Value("${redis.serialize-type}") String serializeType) {
        RedisClientBeanFactory factory = new RedisClientBeanFactory();
        factory.setClusterName(clusterName);
        factory.setReadTimeout(readTimeout);
        factory.setRouterType(routerType);
        factory.setPoolMaxIdle(poolMaxIdle);
        factory.setPoolMaxTotal(poolMaxTotal);
        factory.setPoolWaitMillis(poolWaitMillis);
        factory.setPoolMinIdle(poolMinIdle);
        factory.setSerializeType(serializeType);
        return factory;
    }
}
