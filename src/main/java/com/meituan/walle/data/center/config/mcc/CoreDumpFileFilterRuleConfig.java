package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/02/22
 */
@Slf4j
@Component
public class CoreDumpFileFilterRuleConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "core.dump.full.match.file.name.list")
    public static volatile String FULL_MATCH_FILE_NAME_LIST = "coredump_info.txt,onboard_main.out";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "core.dump.front.match.file.name.list")
    public static volatile String FRONT_MATCH_FILE_NAME_LIST = "core_simulation_main.";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "core.dump.middle.match.file.name.list")
    public static volatile String MIDDLE_MATCH_FILE_NAME_LIST = "coredump_info.txt";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "core.dump.after.match.file.name.list")
    public static volatile String AFTER_MATCH_FILE_NAME_LIST = "coredump_info.txt";


    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "core.dump.full.match.file.name.list.for.case.sortable")
    public static volatile String FULL_MATCH_FILE_NAME_LIST_FOR_CASE_SORTABLE = "coredump_info.txt,onboard_main.out,record_info.pb.txt,onboard_main.out.zip";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "core.dump.front.match.file.name.list.for.case.sortable")
    public static volatile String FRONT_MATCH_FILE_NAME_LIST_FOR_CASE_SORTABLE = "core_simulation_main.";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "core.dump.middle.match.file.name.list.for.case.sortable")
    public static volatile String MIDDLE_MATCH_FILE_NAME_LIST_FOR_CASE_SORTABLE = "coredump_info.txt,.txt";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "core.dump.after.match.file.name.list.for.case.sortable")
    public static volatile String AFTER_MATCH_FILE_NAME_LIST_FOR_CASE_SORTABLE = "coredump_info.txt";


}