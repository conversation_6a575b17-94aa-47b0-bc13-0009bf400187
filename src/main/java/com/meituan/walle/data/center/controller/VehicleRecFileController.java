package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.MetricHelper;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.constant.CharConstant;
import com.meituan.walle.data.center.constant.FastUploadTaskStatusEnum;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.pojo.VehicleUploadRequest;
import com.meituan.walle.data.center.entity.request.FastRecordFileRequest;
import com.meituan.walle.data.center.entity.request.FinishTaskRequest;
import com.meituan.walle.data.center.entity.vo.VehicleServiceFailVO;
import com.meituan.walle.data.center.entity.vo.VehicleUploadRequestVO;
import com.meituan.walle.data.center.handle.impl.VehicleUploadVersionAdapter;
import com.meituan.walle.data.center.service.VehicleRecFileService;
import com.meituan.walle.data.center.service.VehicleUploadMonitorService;
import com.meituan.walle.data.center.util.ExceptionHelper;
import com.meituan.walle.data.center.util.Trace;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/vehicle_rec_file")
public class VehicleRecFileController {

    private static final Logger LOGGER = LoggerFactory.getLogger(VehicleRecFileController.class);

    @Resource
    private VehicleRecFileService vehicleRecFileService;

    @Resource
    private VehicleUploadMonitorService monitorService;

    @Resource
    private VehicleUploadVersionAdapter vehicleUploadVersionAdapter;

    @GetMapping("/get_data_param")
    public Response getDataParam(@RequestParam("vin") String vin) {
        String traceId = Trace.generateId();
        Transaction t = Cat.newTransaction(CatConstant.VehicleUploadRequest, "getDataParam");
        Map<String, Object> result = new HashMap<>();
        long start = System.currentTimeMillis();
        try {
            List<VehicleUploadRequest> requests = vehicleRecFileService.selectByVinAndStatus(vin,
                    FastUploadTaskStatusEnum.CREATED.getCode());
            //取数时上报状态
            monitorService.report(vin);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();

            MetricHelper.build().name(CatConstant.VehicleUploadGetDataParamCount)
                    .tag("ret", "success")
                    .count(1);
            MetricHelper.build().name(CatConstant.VehicleUploadGetDataParamDuration)
                    .tag("ret", "success")
                    .duration(System.currentTimeMillis() - start);

            return Response.succ(requests);
        } catch (Exception e) {
            result.put("success", false);
            result.put("msg", e.getMessage());
            LOGGER.error("traceId: {}, getDataParam Exception : {}",
                    traceId, ExceptionHelper.getStackTraceString(e));
            Cat.logError(e);
            t.setStatus(e);
        }
        return Response.fail("-1", (String) result.get("msg"));
    }

    @PostMapping("/upload_file")
    public Response uploadFile(FastRecordFileRequest fileInfo,
                               @RequestParam("data") MultipartFile file) {
        Transaction t = Cat.newTransaction(CatConstant.VehicleUploadRequest, "uploadFile");
        long start = System.currentTimeMillis();
        try {
            log.info("Begin to upload file: {}, in task {}", fileInfo.getFileAbsPath(), fileInfo.getUploadTaskId());
            if (fileInfo.getFileSize() != null && fileInfo.getFileSize() <= 0L) {
                log.warn("Size of file {} is zero, in task {}", fileInfo.getFileAbsPath(), fileInfo.getUploadTaskId());
            }

            if (file == null) {
                log.warn("upload {} file equals null, in task {}", fileInfo.getFileAbsPath(), fileInfo.getUploadTaskId());
                return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(),
                        WebResponseStatusEnum.FAILED.getMsg());
            }

            if (file.getSize() <= 0L) {
                log.warn("upload {} file equals ${} and Size of {} and file class ${} , in task {}",
                        fileInfo.getFileAbsPath(),
                        file,
                        file.getSize(), file.getClass().getName(), fileInfo.getUploadTaskId());
            }
            boolean succeed = vehicleUploadVersionAdapter.uploadFileCase(fileInfo, file);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();

            MetricHelper.build().name(CatConstant.VehicleUploadRecFileCount)
                    .tag("ret", "success")
                    .count(1);
            MetricHelper.build().name(CatConstant.VehicleUploadRecFileDuration)
                    .tag("ret", "success")
                    .duration(System.currentTimeMillis() - start);

            if (succeed) {
                return Response.succ();
            } else {
                return Response.fail("-1", "upload error");
            }
        } catch (Exception e) {
            log.error("Failed to upload file: {}, in task {}", fileInfo.getFileAbsPath(), fileInfo.getUploadTaskId(), e);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(),
                "Failed to upload file!");
    }

    // curl -XPOST "localhost:8080/vehicle_rec_file/succeed_upload_file" -d "id=1"
    @PostMapping("/succeed_upload_file")
    public Response succeedUploadFile(@RequestParam("vin") String vin,
                                      @RequestParam("id") Long id,
                                      @RequestParam("recordName") String recordName) {
        String traceId = Trace.generateId();
        Transaction t = Cat.newTransaction(CatConstant.VehicleUploadRequest, "succeedUploadFile");
        Map<String, Object> result = new HashMap<>();
        long start = System.currentTimeMillis();
        try {
            log.info("Task succeed, vin is {}, id is {}, recordName is {}", vin, id, recordName);
            vehicleUploadVersionAdapter.finishTask(new FinishTaskRequest(vin, id,
                    FinishTaskRequest.TaskResult.TASK_SUCCESS, recordName, null));
            // vehicleRecFileService.succeedUploadFile(vin, id, recordName);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();

            MetricHelper.build().name(CatConstant.VehicleUploadSucceedUploadFileCount)
                    .tag("ret", "success")
                    .count(1);
            MetricHelper.build().name(CatConstant.VehicleUploadSucceedUploadFileDuration)
                    .tag("ret", "success")
                    .duration(System.currentTimeMillis() - start);

            return Response.succ();
        } catch (Exception e) {
            result.put("success", false);
            result.put("msg", e.getMessage());
            LOGGER.error("traceId: {}, succeedUploadFile Exception : {}",
                    traceId, ExceptionHelper.getStackTraceString(e));
            Cat.logError(e);
            t.setStatus(e);
        }
        return Response.fail("-1", (String) result.get("msg"));
    }

    @PostMapping("/upload_fail")
    public Response vehicleFileUPloadFail(@RequestBody VehicleServiceFailVO failVO) {
        if (failVO.getVin() == null) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "lack vin parameter");
        }
        Transaction t = Cat.newTransaction(CatConstant.VEHICLE_UPLOAD_FILE_FAIL, "vehicle.upload.file.fail");
        long start = System.currentTimeMillis();
        try {
            vehicleUploadVersionAdapter.finishTask(new FinishTaskRequest(failVO.getVin(), null,
                    FinishTaskRequest.TaskResult.TASK_FAIL, null, failVO.getMsg()));
            // vehicleRecFileService.uploadFail(failVO);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            log.error("Failed to report upload vehicle file fail event, vin is {}", failVO.getVin(), e);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(),
                "Failed to report upload vehicle file fail event!");
    }

    @PostMapping("/not_found")
    public Response notFoundUploadFile(
            @RequestParam("vin") String vin,
            @RequestParam("id") Long id,
            @RequestParam(name = "message", defaultValue = CharConstant.CHAR_EMPTY) String message) {
        String traceId = Trace.generateId();
        Transaction t = Cat.newTransaction(CatConstant.VehicleUploadRequest, "notFoundUploadFile");
        Map<String, Object> result = new HashMap<>();
        long start = System.currentTimeMillis();
        try {
            vehicleUploadVersionAdapter.finishTask(new FinishTaskRequest(vin, id,
                    FinishTaskRequest.TaskResult.TASK_NOT_FOUND, null, message));
            // vehicleRecFileService.notFoundUploadFile(vin, id, message);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();

            MetricHelper.build().name(CatConstant.VehicleUploadNotFoundUploadFileCount)
                    .tag("ret", "success")
                    .count(1);
            MetricHelper.build().name(CatConstant.VehicleUploadNotFoundUploadFileDuration)
                    .tag("ret", "success")
                    .duration(System.currentTimeMillis() - start);
            return Response.succ();
        } catch (Exception e) {
            result.put("success", false);
            result.put("msg", e.getMessage());
            LOGGER.error("traceId: {}, notFoundUploadFile Exception : {}",
                    traceId, ExceptionHelper.getStackTraceString(e));
            Cat.logError(e);
            t.setStatus(e);
        }
        return Response.fail("-1", (String) result.get("msg"));
    }

    @GetMapping("/record_upload")
    public Response<Boolean> checkUpload(@RequestParam String recordName) {
        String traceId = Trace.generateId();
        Transaction t = Cat.newTransaction(CatConstant.VehicleUploadRequest, "checkUpload");
        Map<String, Object> result = new HashMap<>();
        long start = System.currentTimeMillis();
        try {
            Boolean res = vehicleRecFileService.checkUpload(recordName);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();

            MetricHelper.build().name(CatConstant.VehicleUploadCheckUploadCount)
                    .tag("ret", "success")
                    .count(1);
            MetricHelper.build().name(CatConstant.VehicleUploadCheckUploadDuration)
                    .tag("ret", "success")
                    .duration(System.currentTimeMillis() - start);
            return Response.succ(res);
        } catch (Exception e) {
            result.put("success", false);
            result.put("msg", e.getMessage());
            LOGGER.error("traceId: {}, checkUpload Exception : {}",
                    traceId, ExceptionHelper.getStackTraceString(e));
            Cat.logError(e);
            t.setStatus(e);
        }
        return Response.fail("-1", (String) result.get("msg"));
    }

    @PostMapping("/sendVehicleUploadRequest")
    public Response sendVehicleUploadRequest(@RequestBody VehicleUploadRequestVO vehicleUploadRequestVO) {
        // todo 暂时返回空值给live_exchange，等车端事件上报完全覆盖后删除该接口
        Map<String, Object> result = new HashMap<>(2);
        result.put("success", true);
        result.put("msg", "success");
        return Response.succ(result);
    }

    @GetMapping(value = "/vehicle/reports")
    public Response listVehicleReports() {
        List<Map<String, Object>> vehicleReports = monitorService.listVehicleReport();

        return Response.succ(vehicleReports);
    }

    @GetMapping(value = "/vehicle/upload/report")
    public Response report(String vin) {
        monitorService.report(vin);
        return Response.succ();
    }

    @GetMapping(value = "/vehicle/upload/del")
    public Response delVin(String vin) {
        if (monitorService.deleteByVin(vin)) {
            return Response.succ();
        } else {
            return Response.fail("1", "delete vin failed");
        }
    }

    /**
     * 临时接口
     */
    @PostMapping("/update_file_time")
    public Response updateFileTime() {
        vehicleRecFileService.updateFileTime();
        return Response.succ();
    }
}
