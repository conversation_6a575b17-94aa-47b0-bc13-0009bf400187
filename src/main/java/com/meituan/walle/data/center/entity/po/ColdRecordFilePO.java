package com.meituan.walle.data.center.entity.po;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/12/02
 */
@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ColdRecordFilePO implements Serializable {

    private Long id;
    private String recordName;
    private String fileName;
    private String filePath;
    private String s3Url;
    private Long fileSize;
    private Integer cluster;
    private Integer isDeleted;
    private Date createTime;
    private Date updateTime;
}