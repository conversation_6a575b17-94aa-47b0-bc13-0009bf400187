package com.meituan.walle.data.center.dal.config;

import com.dianping.zebra.group.jdbc.GroupDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * <AUTHOR>
 * @date 2024/01/08
 */
@Configuration
@MapperScan(basePackages = "com.meituan.walle.data.center.dal.wcdpbasedata.mapper",
        sqlSessionFactoryRef = "wcdpbasedataSqlSessionFactory")
@EnableTransactionManagement
public class WcdpBaseDataDataSourceConfig {

    @Value("${db.wcdpbasedata.jdbcref}")
    private String jdbcref;

    @Bean(name = "wcdpbasedataDataSource", initMethod = "init", destroyMethod = "close")
    public GroupDataSource zebraDataSource() {
        GroupDataSource dataSource = new GroupDataSource();
        dataSource.setJdbcRef(jdbcref);
        dataSource.setExtraJdbcUrlParams("zeroDateTimeBehavior=convertToNull");
        return dataSource;
    }

    @Bean(name = "wcdpbasedataSqlSessionFactory")
    public SqlSessionFactory setSqlSessionFactory(
            @Qualifier(value = "wcdpbasedataDataSource") GroupDataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setTypeAliasesPackage("com.meituan.walle.data.center.dal.wcdpbasedata.entity");
        tk.mybatis.mapper.session.Configuration configuration = new tk.mybatis.mapper.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        bean.setConfiguration(configuration);
        return bean.getObject();
    }

    @Bean
    @Primary
    public DataSourceTransactionManager dataSourceTransactionManager(
            @Qualifier(value = "wcdpbasedataDataSource") GroupDataSource dataSource) {
        DataSourceTransactionManager dataSourceTransactionManager = new DataSourceTransactionManager();
        dataSourceTransactionManager.setDataSource(dataSource);
        return dataSourceTransactionManager;
    }

    @Bean("wcdpbasedataTransactionTemplate")
    @Primary
    public TransactionTemplate datacenterTransactionTemplate(
            @Qualifier(value = "wcdpbasedataDataSource") GroupDataSource dataSource) {
        return new TransactionTemplate(dataSourceTransactionManager(dataSource));
    }

}
