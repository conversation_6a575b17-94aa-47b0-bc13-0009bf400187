package com.meituan.walle.data.center.adaptor;



import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.walle.data.center.entity.request.EventUpdateRequest;
import com.meituan.walle.data.center.util.HttpClientUtil;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;



@Component
@Slf4j
public class CloudTriageAdaptor {

    @Resource
    private HttpClientUtil httpClientUtil;

    /**
     * 更新事件状态接口
     */
    public static final String UPDATE_EVENT_STATUS_URL = "/event/eventStatus/update";

    /**
     * 云分诊服务域名
     */
    @Value("${cloudTriageHost}")
    private String cloudTriageHost;

    /**
     * 更新事件状态重试
     * @param request
     */
    public void updateEventStatusWithRetry(EventUpdateRequest request) {
        int retryTime = 3;
        Boolean isSuccess = updateEventStatus(request);
        while (Objects.equals(isSuccess, false) && retryTime > 0) {
            retryTime--;
            isSuccess = updateEventStatus(request);;
        }
    }

    /**
     * 更新事件状态
     */
    private Boolean updateEventStatus(EventUpdateRequest  request) {
        try {
            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            log.info("updateEventStatus request:{}", JSON.toJSONString(request));
            String url = cloudTriageHost + UPDATE_EVENT_STATUS_URL;
            // 发送POST请求
            String responseJSONStr = httpClientUtil.postWithBody(url, JSON.toJSONString(request),
                    headers, String.class);
            log.info("updateEventStatus response:{}", JSON.toJSONString(responseJSONStr));
            if (StringUtils.isNotBlank(responseJSONStr)) {
                JSONObject responseJSON = JSON.parseObject(responseJSONStr);
                Integer ret = responseJSON.getIntValue("ret");
                if (Objects.equals(ret, 0)) {
                    return true;
                }
            }
            log.error("updateEventStatus fail", new RuntimeException("updateEventStatus fail"));
        }
        catch (Exception e) {
            log.error("updateEventStatus error", e);
        }
        return false;
    }

}
