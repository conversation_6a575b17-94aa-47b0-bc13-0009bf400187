package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/09/20
 */
@Table(name = "biz_trace")
@Data
public class BizTrace {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 唯一跟踪id
     */
    private String traceId;

    /**
     * 跟踪事件类别
     */
    private String category;

    /**
     * 上报时间
     */
    private Date reportTime;

    /**
     * trace来源
     */
    private String reportSource;

    /**
     * trace前一个来源
     */
    private String preSource;

    /**
     * 标签信息
     */
    private String tags;

    /**
     * 补充信息
     */
    private String message;

    /**
     * 是否逻辑删除
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最近一次更新时间
     */
    private Date updateTime;
}
