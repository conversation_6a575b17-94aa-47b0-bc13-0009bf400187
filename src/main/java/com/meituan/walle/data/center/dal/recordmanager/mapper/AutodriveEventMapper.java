package com.meituan.walle.data.center.dal.recordmanager.mapper;

import com.meituan.walle.data.center.dal.recordmanager.entity.AutodriveEventPO;
import com.meituan.walle.data.center.entity.params.AutodriveEventPageParam;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/06/28
 */
public interface AutodriveEventMapper {

    @Insert({
            "insert ignore into autodrive_event",
            "(event_id, event_code, event_name, event_timestamp, sender_timestamp, receiver_timestamp, ",
            "vin, vehicle_id, vehicle_name, record_name, utm_zone, utm_x, utm_y, datasource, content)",
            "values",
            "(#{eventId}, #{eventCode}, #{eventName}, #{eventTimestamp}, #{senderTimestamp}, #{receiverTimestamp}, ",
            "#{vin}, #{vehicleId}, #{vehicleName}, #{recordName}, #{utmZone}, #{utmX}, #{utmY}, #{datasource}, #{content})"
    })
    int insert(AutodriveEventPO autodriveEventPO);

    @Select({
            "<script>",
            "select * from autodrive_event where is_deleted = 0",
            "<if test='null != eventId and &apos;&apos; != eventId'>and event_id = #{eventId}</if>",
            "<if test='null != eventCodeList and eventCodeList.size() > 0'>",
            "and event_code in",
            "<foreach item='eventCode' collection='eventCodeList' open='(' separator=',' close=')'>",
            "#{eventCode}",
            "</foreach>",
            "</if>",
            "<if test='null != vinList and vinList.size() > 0'>",
            "and vin in",
            "<foreach item='vin' collection='vinList' open='(' separator=',' close=')'>",
            "#{vin}",
            "</foreach>",
            "</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>and record_name = #{recordName}</if>",
            "<if test='null != datasource and &apos;&apos; != datasource'>and datasource = #{datasource}</if>",
            "<if test='null != startTimestamp'>and event_timestamp <![CDATA[ >= ]]> from_unixtime(#{startTimestamp} / 1000)</if>",
            "<if test='null != endTimestamp'>and event_timestamp <![CDATA[ <= ]]> from_unixtime(#{endTimestamp} / 1000)</if>",
            "order by event_timestamp desc",
            "<if test='null != page and null != size'>limit #{offset}, #{size}</if>",
            "</script>"
    })
    List<AutodriveEventPO> pageQuery(AutodriveEventPageParam autodriveEventPageParam);

    @Select({
            "<script>",
            "select count(1) from autodrive_event where is_deleted = 0",
            "<if test='null != eventId and &apos;&apos; != eventId'>and event_id = #{eventId}</if>",
            "<if test='null != eventCodeList and eventCodeList.size() > 0'>",
            "and event_code in",
            "<foreach item='eventCode' collection='eventCodeList' open='(' separator=',' close=')'>",
            "#{eventCode}",
            "</foreach>",
            "</if>",
            "<if test='null != vinList and vinList.size() > 0'>",
            "and vin in",
            "<foreach item='vin' collection='vinList' open='(' separator=',' close=')'>",
            "#{vin}",
            "</foreach>",
            "</if>",
            "<if test='null != recordName and &apos;&apos; != recordName'>and record_name = #{recordName}</if>",
            "<if test='null != datasource and &apos;&apos; != datasource'>and datasource = #{datasource}</if>",
            "<if test='null != startTimestamp'>and event_timestamp <![CDATA[ >= ]]> from_unixtime(#{startTimestamp} / 1000)</if>",
            "<if test='null != endTimestamp'>and event_timestamp <![CDATA[ <= ]]> from_unixtime(#{endTimestamp} / 1000)</if>",
            "</script>"
    })
    int countQuery(AutodriveEventPageParam autodriveEventPageParam);

}
