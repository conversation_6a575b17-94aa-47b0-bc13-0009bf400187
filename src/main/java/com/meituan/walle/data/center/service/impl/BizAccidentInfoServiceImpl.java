package com.meituan.walle.data.center.service.impl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.walle.data.center.adaptor.CloudTriageAdaptor;
import com.meituan.walle.data.center.adaptor.EveAutoCallAdaptor;
import com.meituan.walle.data.center.adaptor.EveGetVehicleDataAdaptor;
import com.meituan.walle.data.center.adaptor.EveTTRgOncallAdaptor;
import com.meituan.walle.data.center.adaptor.WalleDeliveryBasicAdaptor;
import com.meituan.walle.data.center.config.mcc.AccidentConfig;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.config.mcc.VehicleTagReportConfig;
import com.meituan.walle.data.center.config.mcc.FastUploadConfig;
import com.meituan.walle.data.center.constant.*;
import com.meituan.walle.data.center.entity.dto.*;
import com.meituan.walle.data.center.entity.enums.AccidentRoadTypeEnum;
import com.meituan.walle.data.center.entity.enums.AccidentRoadTypeWithHDMapEnum;
import com.meituan.walle.data.center.entity.enums.AffiliationEnum;
import com.meituan.walle.data.center.entity.enums.VehiclePurposeEnum;
import com.meituan.walle.data.center.entity.pojo.*;
import com.meituan.walle.data.center.entity.request.AutoCallRequest;
import com.meituan.walle.data.center.entity.request.BizAccidentInfoRequest;
import com.meituan.walle.data.center.entity.request.EventUpdateRequest;
import com.meituan.walle.data.center.entity.request.FastUploadTaskRequest;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.entity.response.VehicleGroupResponse;
import com.meituan.walle.data.center.entity.vo.BizAccidentInfoVO;
import com.meituan.walle.data.center.entity.request.RemoteVideoRequest;
import com.meituan.walle.data.center.entity.vo.VehicleUploadRequestResultVO;
import com.meituan.walle.data.center.entity.vo.VresvVO;
import com.meituan.walle.data.center.exception.RemoteErrorException;
import com.meituan.walle.data.center.handle.HdmapHandler;
import com.meituan.walle.data.center.handle.impl.IIIRCOpenAPIHandler;
import com.meituan.walle.data.center.mapper.*;
import com.meituan.walle.data.center.producer.AccidentMessageProducer;
import com.meituan.walle.data.center.service.*;
import com.meituan.walle.data.center.util.*;
import com.sankuai.carosscan.eve_common_output.sdk.DO.LockInParams;
import com.sankuai.carosscan.eve_common_output.sdk.service.RedisGLLock;
import java.util.concurrent.ExecutorService;

import com.sankuai.wallecmdb.data.eve.replay.inquire.api.thrift.response.VehicleDataInfoVO;
import com.sankuai.walledelivery.basic.client.enums.DelivererTypeEnum;
import com.sankuai.walledelivery.basic.client.request.deliverer.DeliverRpcRequest;
import com.sankuai.walledelivery.basic.client.response.businessStation.BusinessStationPositionResponse;
import com.sankuai.walledelivery.basic.client.response.businessStation.dto.BusinessStationDTO;
import com.sankuai.walledelivery.basic.client.thrift.inner.deliverer.DelivererQueryThriftService;
import com.sankuai.walledelivery.commons.enums.ErrorCode;
import com.sankuai.walledelivery.thrift.exception.BizThriftException;
import com.sankuai.walledelivery.thrift.response.ThriftResponse;
import com.sankuai.walledelivery.utils.JacksonUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;
import com.dianping.vc.sdk.concurrent.threadpool.ExecutorServices;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/5
 */
@Slf4j
@Service
public class BizAccidentInfoServiceImpl implements BizAccidentInfoService {

    @MdpConfig("timeGap")
    private ArrayList<Integer> timeGap;

    @MdpConfig("system.accident.avoid.repeat.millis")
    private Integer avoidRepeatMillis = 60000;

    @Resource
    private AccidentMessageProducer accidentMessageProducer;

    @Resource
    private CloudTriageAdaptor cloudTriageAdaptor;

    @Resource
    private EveGetVehicleDataAdaptor eveGetVehicleDataAdaptor;

    /**
     * 异步线程池
     */
    private static final ExecutorService executorService = ExecutorServices.forThreadPoolExecutor(
            CommonConstant.ACCIDENT_UPDATE_EVENT_STATUS_THREAD_POOL);


    public static final StoreKey STORE_KEY = new StoreKey(RedisConstant.FAST_UPLOAD_KEY, "fast_upload_id");

    @Resource
    @Qualifier("redisClientBeanFactory")
    RedisStoreClient redisClient;

    @Resource(name = "s3ClientBeijingInner")
    private AmazonS3 s3ClientBeijingInner;

    @Value("${s3.bucket.vehicleObject}")
    private String vehicleObject;

    @MdpConfig("accident.location.name.api.key")
    public static String accidentLocationNameApiKey;

    @MdpConfig("accident.report.redis.lock.switch")
    private static Boolean accidentReportRedisLockSwitch = true;

    @MdpConfig("accident.update.related.event.status.switch")
    private static Boolean accidentUpdateRelatedEventStatusSwitch = true;

    @Resource
    private BizAccidentInfoMapper accidentInfoMapper;

    @Resource
    private BizAccidentReviewMapper accidentReviewMapper;

    @Resource
    private BizFileInfoMapper fileInfoMapper;

    @Resource
    private VehicleUploadRequestMapper uploadRequestMapper;

    @Resource
    private BizEventDeriveInterventionMapper interventionMapper;

    @Autowired
    private VresvRecordHistoryServiceImpl vresvRecordHistoryService;

    @Autowired
    private VehicleEventTagReportServiceImpl vehicleEventTagReportService;

    @Autowired
    private BizEventIpcService bizEventIpcService;

    @Resource
    private FastRecordFileMapper fastRecordFileMapper;

    @Resource
    private VehicleInfoService vehicleInfoService;

    @Resource
    private BizRecordRemoteVideoMapper bizRecordRemoteVideoMapper;

    @Resource
    private RemoteVideoService remoteVideoService;

    @Resource
    private RecordV2Mapper recordV2Mapper;

    @Resource
    private BizMapAreaMapper bizMapAreaMapper;

    @Resource
    private VehicleUploadRequestMapper vehicleUploadRequestMapper;

    @Resource
    private VehicleUploadRequestService vehicleUploadRequestService;

    @Resource
    private BizAccidentCallRuleMapper bizAccidentCallRuleMapper;

    @Resource
    private BizAccidentCallDutyMapper bizAccidentCallDutyMapper;

    @Resource
    private DataCenterAutoCallService dataCenterAutoCallService;

    @Resource
    private OsvizService osvizService;

    @Resource
    private VehicleRecFileService vehicleRecFileService;

    @Resource
    private OnCallListService onCallListService;

    @Resource
    private HdmapHandler hdmapHandler;

    @Resource
    private UserEncryptPhoneNumberMapper userEncryptPhoneNumberMapper;

    @Autowired
    RedisGLLock redisLock;

    @Resource
    private EveAutoCallAdaptor eveAutoCallAdaptor;

    @Resource
    private EveTTRgOncallAdaptor eveTTRgOncallAdaptor;

    @Resource
    private IIIRCOpenAPIHandler iiircOpenAPIHandler;
    @Resource
    private WalleDeliveryBasicAdaptor walleDeliveryBasicAdaptor;

    private long HOUR_TO_MS = 60 * 60 * 1000L;

    private static final String RECORD_VIDEO = "record-video";

    private static final String DEFAULT_TIME = "1970-01-01 08:00:01";

    private static final String ALL = "all";

    private static final String ACCIDENT_INFO_COMMIT_URL_PREFIX = "https://walle.meituan.com/oncall/index.html#/accident/";

    private static final Double INVALID_SPEED = -100.0D;

    /**
     * 上报实时数据到数据总线服务接口PATH
     */
    private static final String REPORT_REAL_DATA = "/eve/online/rest/data/bus/batch/incoming";
    



    @Override
    public List<BizAccidentInfo> queryByPage(BizAccidentInfoRequest bizAccidentInfo) {
        return accidentInfoMapper.queryByPage(bizAccidentInfo);
    }

    @Override
    public int countByPage(BizAccidentInfoRequest bizAccidentInfo) {
        return accidentInfoMapper.countByPage(bizAccidentInfo);
    }

    @Override
    public BizAccidentInfo getById(Long id) {
        return accidentInfoMapper.getExtendedAccidentInfoById(id);
    }

    @Override
    public void updateVehicleStatusForAccidentInfo(BizAccidentInfo bizAccidentInfo) {
        Map<String, Object> vehicleStatus = getVehicleStatus(bizAccidentInfo.getVin(),
                bizAccidentInfo.getAccidentTime());
        try{
            if (vehicleStatus == null) {
                bizAccidentInfo.setSpeed(INVALID_SPEED);
                bizAccidentInfo.setDriveModeDesc("未知");
                log.error("getVehicleStatus is null , vin = {}, time = {}", bizAccidentInfo.getVin(),
                        bizAccidentInfo.getAccidentTime());
            } else {
                // 在 updateVehicleStatusForAccidentInfo 方法中
                bizAccidentInfo.setSpeed(
                        Double.valueOf(Optional.ofNullable(vehicleStatus.get("speed"))
                                .map(Object::toString)
                                .orElse("0")) * CommonConstant.MPH_TO_KMPH);

                bizAccidentInfo.setDriveModeDesc(
                        Optional.ofNullable(vehicleStatus.get("drive_mode_desc"))
                                .map(Object::toString)
                                .orElse(""));

                // 当事故上报端没有经纬度信息时，从历史状态上获取该信息并进行格式转化
                String locationGps = String.valueOf(vehicleStatus.getOrDefault("longitude", "-1")) + ","
                        + String.valueOf(vehicleStatus.getOrDefault("latitude", "-1"));

                if (StringUtils.isBlank(bizAccidentInfo.getLocationGps())) {
                    bizAccidentInfo.setLocationGps(wgs84ToGjc02(locationGps));
                }
                if (StringUtils.isBlank(bizAccidentInfo.getLocationName())) {
                    bizAccidentInfo.setLocationName(getLocationNameByGps(locationGps));
                }
            }
        }catch (Exception e){
            log.error("updateVehicleStatusForAccidentInfo error, vin = {}, time = {}", bizAccidentInfo.getVin(),
                    bizAccidentInfo.getAccidentTime(), e);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AccidentInfoDTO add(BizAccidentInfo bizAccidentInfo) throws Exception {
        Date taskTime = bizAccidentInfo.getAccidentTime();
        bizAccidentInfo.setReportAccidentTime(bizAccidentInfo.getAccidentTime());
        BizEventDeriveIntervention intervention = getNearestInterventionTime(bizAccidentInfo);
        if (intervention != null) {
            log.info("report accident time: {}, NearestInterventionTime: {}", bizAccidentInfo.getAccidentTime(),
                    intervention.getInterventionTime());
            taskTime = intervention.getInterventionTime();
        }
        createFastUploadTaskForAccident(bizAccidentInfo, taskTime);
        Date currentTime = new Date();
        long currentTimestamp = currentTime.getTime();
        int dateKey = Integer.parseInt(DatetimeUtil.dateFormat(DateTimeFormatterConstant.ymd, currentTime));
        List<VresvVO> memberList = vresvRecordHistoryService.callVresvTask(bizAccidentInfo.getVin(), dateKey);
        if (memberList != null && !memberList.isEmpty()) {
            for (VresvVO vresvVO : memberList) {
                long startTime = (vresvVO.getStartTime() == null ? 0 : DatetimeUtil
                        .covertToDate(vresvVO.getStartTime(), DateTimeFormatterConstant.ymdhms).getTime());
                long endTime = (vresvVO.getEndTime() == null ? 0
                        : DatetimeUtil.covertToDate(vresvVO.getEndTime(), DateTimeFormatterConstant.ymdhms).getTime());
                if (currentTimestamp >= startTime && currentTimestamp <= endTime) {
                    bizAccidentInfo.setRemoteMisId(vresvVO.getTelecontrol());
                    bizAccidentInfo.setSiteMisId(vresvVO.getSubstitute());
                }
            }
        }
        RecordV2 recordV2 = recordV2Mapper.queryLatestRecordByVin(bizAccidentInfo.getVin());
        String date = DatetimeUtil.dateFormat(DateTimeFormatterConstant.md, bizAccidentInfo.getAccidentTime());
        String city = CommonConstant.UNKNOWN;
        String affiliationString = CommonConstant.UNKNOWN;
        String purpose = "";
        if (recordV2 != null) {
            purpose = recordV2.getPurpose();
            bizAccidentInfo.setRecordName(recordV2.getRecordName());
            if (bizAccidentInfo.getPlace() == null) {
                bizAccidentInfo.setPlace(recordV2.getPlace());
            }
            BizMapArea bizMapArea = bizMapAreaMapper.queryByCode(recordV2.getPlace());
            if (bizMapArea != null) {
                city = bizMapArea.getCity();
                affiliationString = AffiliationEnum.getMsgByCode(bizMapArea.getAffiliation());
            }
        }
        String vehicleId = vehicleInfoService.getVehicleId(bizAccidentInfo.getVin());
        String title = String.format("%s-%s-%s-%s", date, city, affiliationString, "交通事故");
        String groupTitle = String.format("%s-%s-%s-%s-%s", "待定级", date, city, vehicleId, affiliationString);
        bizAccidentInfo.setTitle(title);
        bizAccidentInfo.setEventId(genAccidentId(bizAccidentInfo.getAccidentTime(), bizAccidentInfo.getVin()));
        onCallListService.insertOncallListForAccident(bizAccidentInfo);
        updateVehicleStatusForAccidentInfo(bizAccidentInfo);

        if (accidentReportRedisLockSwitch) {
            LockInParams params = new LockInParams();
            params.setKey(bizAccidentInfo.getVin());
            params.setWaitTime(3000);
            params.setExpireTime(3000);
            params.setCategory(CommonConstant.accidentRedisLockCategory);
            AtomicBoolean isInsert = new AtomicBoolean(false);
            // 双重校验，先查主库后上锁
            try {
                if (!isOccurAccidentInLatestTime(bizAccidentInfo.getVin(), bizAccidentInfo.getAccidentTime(),
                        avoidRepeatMillis)) {
                    redisLock.lockWait(params, new Runnable() {
                        @Override
                        public void run() {
                            isInsert.set(isCheckAndInsertData(bizAccidentInfo));
                        }
                    });
                }
            } catch (Exception e) {
                log.error("insert data failed, bizAccidentInfo = [{}]", bizAccidentInfo, e);
            }
            // 当无须插入数据后直接返回null值即可
            if (!isInsert.get()) {
                log.info("data is exits,don't insert! bizAccidentInfo = [{}]", bizAccidentInfo);
                return null;
            }
        } else {
            accidentInfoMapper.insert(bizAccidentInfo);
        }
        // 插入数据后同步发送mafka
        sendAccidentMsgToMQ(bizAccidentInfo, AccidentStatusEnum.CREATED);

        // 更新关联事件状态
        updateAccidentRelatedEventStatus(bizAccidentInfo);

        if (AccidentConfig.accident_test_vin.equalsIgnoreCase(bizAccidentInfo.getVin())) {
            return null;
        }
        String accidentMsg = makeMsgAndSendToDx(bizAccidentInfo, purpose);
        AccidentInfoDTO accidentInfoDTO = new AccidentInfoDTO();
        accidentInfoDTO.setBizAccidentInfo(bizAccidentInfo);
        accidentInfoDTO.setMsg(accidentMsg);
        accidentInfoDTO.setGroupTitle(groupTitle);
        uploadTaskByIntervention(getNearestInterventionId(bizAccidentInfo));
        return accidentInfoDTO;
    }

    @Override
    public void update(BizAccidentInfo bizAccidentInfo) {
        log.info("Start to update biz_accident_info table, params is [{}]", bizAccidentInfo);
        if (StringUtils.isNotBlank(bizAccidentInfo.getReporter())) {
            log.info("Not update reporter when update accident info, mender is [{}]", bizAccidentInfo.getReporter());
            bizAccidentInfo.setReporter(null);
        }
        accidentInfoMapper.update(bizAccidentInfo);
        log.info("End update biz_accident_info table, params is [{}]", bizAccidentInfo);
    }

    @Override
    public List<BizMapArea> queryMapArea() {
        return bizMapAreaMapper.selectAll();
    }

    @Override
    @Async
    public void getAccidentRemoteVideo(BizAccidentInfo bizAccidentInfo) {
        Date accidentTime = bizAccidentInfo.getAccidentTime();
        Date startTime = new Date(accidentTime.getTime() - AccidentConfig.accident_rv_previous_time);
        Date endTime = new Date(accidentTime.getTime() + AccidentConfig.accident_rv_later_time);
        remoteVideoService.getRemoteVideoWithVinAndTime(bizAccidentInfo.getVin(), startTime, endTime, bizAccidentInfo.getEventId());
    }

    @Override
    @Async
    public void autoCallForAccident(BizAccidentInfo bizAccidentInfo) {
        try {
            // todo: 替换新的呼叫逻辑
            // 获取值班人员misId列表
            List<String> misIds = getAccidentCallDutyPersonMisIds();
            // 构造呼叫文本
            String callText = String.format("有%s事故发生，请及时在大象群内处置", AffiliationEnum.getMsgByCode(bizAccidentInfo.getAffiliation()));
            eveAutoCallAdaptor.autoCall(bizAccidentInfo.getEventId(), misIds, callText);
        } catch (Exception e) {
            log.error("Auto call for accident failed, accident info is [{}]", bizAccidentInfo, e);
        }
    }

    private String getAccidentCallDutyPersonMisIds(Date accidentTime, Integer callType) {
        int dutyDate = Integer.parseInt(DatetimeUtil.format(accidentTime, DateTimeFormatterConstant.ymd));
        List<BizAccidentCallDuty> bizAccidentCallDutyList = bizAccidentCallDutyMapper
                .selectByCallTypeAndDutyDate(callType, dutyDate);
        if (bizAccidentCallDutyList == null || bizAccidentCallDutyList.isEmpty()) {
            return null;
        }
        for (BizAccidentCallDuty bizAccidentCallDuty : bizAccidentCallDutyList) {
            String[] dutyDayInterval = bizAccidentCallDuty.getDutyDayInterval().split(CharConstant.CHAR_HX);
            String accidentTimePoint = DatetimeUtil.format(accidentTime, DateTimeFormatterConstant.hm);
            if (accidentTimePoint.compareTo(dutyDayInterval[0]) >= 0
                    && accidentTimePoint.compareTo(dutyDayInterval[1]) < 0) {
                return bizAccidentCallDuty.getDutyPersonMisIdList();
            }
        }
        return null;
    }

    @Override
    @Async
    public void updateAccidentExtendedInfo(BizAccidentInfo accidentInfo) {
        BizAccidentInfo updateAccidentInfo = new BizAccidentInfo();
        updateAccidentInfo.setId(accidentInfo.getId());
        String locationGps = accidentInfo.getLocationGps();
        if (StringUtils.isBlank(locationGps)) {
            return;
        }
        RecordV2 recordV2 = recordV2Mapper.selectByRecordName(accidentInfo.getRecordName());
        if (StringUtils.isBlank(recordV2.getHdmapVersion())) {
            return;
        }
        String[] locationGpsList = locationGps.split(CharConstant.CHAR_DD);
        String url = CommonConstant.HDMAP_BASE_URL + String.format("/api/road/info/gcj02?lon=%s&lat=%s&version=%s",
                locationGpsList[0], locationGpsList[1], recordV2.getHdmapVersion());
        String roadInfo = hdmapHandler.getRoadInfo(url);
        if (StringUtils.isBlank(roadInfo)) {
            return;
        }
        AccidentRoadTypeEnum roadTypeEnum = AccidentRoadTypeWithHDMapEnum.getAccidentRoadTypeByHDMapMsg(roadInfo);
        updateAccidentInfo.setRoadType(roadTypeEnum.getCode());
        accidentInfoMapper.update(updateAccidentInfo);
    }

    private BizAccidentCallRule getAccidentCallRule(BizAccidentInfo bizAccidentInfo) {
        List<BizAccidentCallRule> bizAccidentCallRuleList = bizAccidentCallRuleMapper.queryAll();
        String city = CommonConstant.UNKNOWN;
        Integer affiliation = 0;
        RecordV2 recordV2 = recordV2Mapper.selectByRecordName(bizAccidentInfo.getRecordName());
        BizMapArea bizMapArea = bizMapAreaMapper.queryByCode(recordV2.getPlace());
        if (bizMapArea != null) {
            city = bizMapArea.getCity();
            affiliation = bizMapArea.getAffiliation();
            bizAccidentInfo.setAffiliation(affiliation);
        }
        String purpose = "";
        if (StringUtils.isNotBlank(recordV2.getPurpose())) {
            purpose = recordV2.getPurpose();
        }
        for (BizAccidentCallRule callRule : bizAccidentCallRuleList) {
            if (callRule.getAffiliation().equals(affiliation)
                    || callRule.getAffiliation().equals(AffiliationEnum.ALL.getCode())) {
                if (callRule.getCity().equals(city) || ALL.equals(callRule.getCity())) {
                    if (ALL.equals(callRule.getPurpose()) || purpose.equals(callRule.getPurpose())) {
                        return callRule;
                    } else if (!VehiclePurposeEnum.OPERATION_PURPOSE.getMsg().equals(purpose)
                            && VehiclePurposeEnum.OTHER.getMsg().equals(callRule.getPurpose())) {
                        return callRule;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public void noticeDisposedDelay() {
        long now = new Date().getTime();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -AccidentConfig.accident_undisposed_scanned_days);
        Date startTime = calendar.getTime();
        String startTimeStr = DatetimeUtil.format(startTime);
        List<BizAccidentInfo> bizAccidentInfoList = accidentInfoMapper.queryUndisposedAccidentInfo(startTimeStr);
        for (BizAccidentInfo bizAccidentInfo : bizAccidentInfoList) {
            long accidentCreateTime = bizAccidentInfo.getCreateTime().getTime();
            if (!isUndisposedAccidentNeedToNotice(now, accidentCreateTime)) {
                continue;
            }
            long undisposedMinute = (now - accidentCreateTime) / TimeConstant.MS_TO_MINUTE;
            callAndNoticeUndisposedAccident(bizAccidentInfo, undisposedMinute);
        }
    }

    private void callAndNoticeUndisposedAccident(BizAccidentInfo bizAccidentInfo, long undisposedMinute) {
        String vehicleName = vehicleInfoService.getVehicleName(bizAccidentInfo.getVin());
        String callText = String.format("%s车事故超%s分钟未填写风险解除时间", vehicleName, undisposedMinute);
        String title = String.format("⚠️ %s", callText);
        String group = "";
        GroupQueryResultDTO groupQueryResultDTO = getVehicleGroupInfo(bizAccidentInfo.getVin());
        if (groupQueryResultDTO != null) {
            group = groupQueryResultDTO.getPurpose();
        }
        String bodyText = makeAccidentNoticeMsg(bizAccidentInfo, group, title, bizAccidentInfo.getPurpose());
        // todo: 替换新的值班、呼叫方式
        List<String> misIds = getAccidentCallDutyPersonMisIds();
        sendMessageToGroup(bodyText, String.join(",", misIds));
        eveAutoCallAdaptor.autoCall(bizAccidentInfo.getEventId(), misIds, callText);

    }

    @Override
    public void updateResp(BizAccidentInfoRequest bizAccidentInfoRequest) {
        BizAccidentInfo bizAccidentInfo = new BizAccidentInfo();
        BeanUtils.copyProperties(bizAccidentInfoRequest, bizAccidentInfo);
        accidentInfoMapper.update(bizAccidentInfo);
    }

    @Override
    public BizAccidentInfoVO convert(BizAccidentInfo bizAccidentInfo) {
        if (bizAccidentInfo == null) {
            return null;
        }

        BizAccidentInfoVO accidentInfoVO = new BizAccidentInfoVO();
        BeanUtils.copyProperties(bizAccidentInfo, accidentInfoVO);
        if (bizAccidentInfo.getSiteDisposeTime() != null) {
            accidentInfoVO.setSiteDisposeTime(
                    DatetimeUtil.dateFormat(DatetimeUtil.YMDHMS, bizAccidentInfo.getSiteDisposeTime()));
        }
        if (bizAccidentInfo.getRiskEliminationTime() != null) {
            accidentInfoVO.setRiskEliminationTime(
                    DatetimeUtil.dateFormat(DatetimeUtil.YMDHMS, bizAccidentInfo.getRiskEliminationTime()));
        }
        if (bizAccidentInfo.getSecurityGroupResponseTime() != null) {
            accidentInfoVO.setSecurityGroupResponseTime(
                    DatetimeUtil.dateFormat(DatetimeUtil.YMDHMS, bizAccidentInfo.getSecurityGroupResponseTime()));
        }
        Long id = bizAccidentInfo.getId();
        accidentInfoVO.setId(id);
        String vin = bizAccidentInfo.getVin() == null ? "" : bizAccidentInfo.getVin();
        accidentInfoVO.setVin(vin);
        if (bizAccidentInfo.getAffiliation() != null) {
            accidentInfoVO.setSiteBelong(AffiliationEnum.getMsgByCode(bizAccidentInfo.getAffiliation()));
        }
        String vehicleName = vehicleInfoService.getVehicleName(bizAccidentInfo.getVin());
        accidentInfoVO.setVehicleName(vehicleName);
        accidentInfoVO.setVehicleId(vehicleInfoService.getVehicleId(bizAccidentInfo.getVin()));
        if (bizAccidentInfo.getAccidentTime() != null) {
            accidentInfoVO.setAccidentTime(
                    DatetimeUtil.dateFormat(DateTimeFormatterConstant.ymdhms, bizAccidentInfo.getAccidentTime()));
            BizEventDeriveIntervention intervention = getNearestInterventionId(bizAccidentInfo);
            accidentInfoVO.setNearestInterventionId(intervention == null ? "" : intervention.getEventId());
        }
        String locationName = bizAccidentInfo.getLocationName() == null ? "" : bizAccidentInfo.getLocationName();
        accidentInfoVO.setLocationName(locationName);
        String accidentDesc = bizAccidentInfo.getAccidentDesc() == null ? "" : bizAccidentInfo.getAccidentDesc();
        accidentInfoVO.setAccidentDesc(accidentDesc);
        String reporter = bizAccidentInfo.getReporter() == null ? "" : bizAccidentInfo.getReporter();
        accidentInfoVO.setReporter(reporter);
        if (bizAccidentInfo.getCreateTime() != null) {
            accidentInfoVO.setCreateTime(
                    DatetimeUtil.dateFormat(DateTimeFormatterConstant.ymdhms, bizAccidentInfo.getCreateTime()));
        }

        if (bizAccidentInfo.getUpdateTime() != null) {
            accidentInfoVO.setUpdateTime(
                    DatetimeUtil.dateFormat(DateTimeFormatterConstant.ymdhms, bizAccidentInfo.getUpdateTime()));
        }
        String taskIds = bizAccidentInfo.getUploadTaskId();
        log.info("taskIds: {}", bizAccidentInfo.getUploadTaskId());
        Map<String, String> modulesStatusOnviewMap = new HashMap<>();
        List<VehicleUploadRequestResultVO> tasks = new ArrayList<>();
        List<VehicleUploadRequestResultVO> canDataTasks = new ArrayList<>();
        if (taskIds != null && taskIds.contains(CharConstant.CHAR_DD)) {
            String[] ids = taskIds.split(CharConstant.CHAR_DD, -1);
            List<String> uploadedTaskIds = Arrays.asList(ids);
            List<FastRecordFile> allFiles = fastRecordFileMapper.findAllFiles(uploadedTaskIds);
            Map<String, Map<String, Long>> collect = allFiles.stream()
                    .collect(Collectors.groupingBy(FastRecordFile::getUploadTaskId,
                            Collectors.groupingBy(FastRecordFile::getModule, Collectors.counting())));

            List<Long> taskIdList = uploadedTaskIds.stream().map(Long::parseLong).collect(Collectors.toList());
            List<VehicleUploadRequest> uploadRequestList = uploadRequestMapper.getByIds(taskIdList);
            List<Long> completeTaskIdList = uploadRequestList.stream()
                    .filter(item -> FastUploadTaskStatusEnum.FINISHED.getCode() == item.getStatus()
                            || FastUploadTaskStatusEnum.INCOMPLETE_SIDE.getCode() == item.getStatus()
                            || FastUploadTaskStatusEnum.INCOMPLETE_MIDDLE.getCode() == item.getStatus())
                    .map(VehicleUploadRequest::getId).collect(Collectors.toList());
            Map<Long, String> fastIdMapS3Url = queryOsvizVideo(completeTaskIdList);
            for (VehicleUploadRequest uploadRequest : uploadRequestList) {
                VehicleUploadRequestResultVO resultVO = new VehicleUploadRequestResultVO();
                BeanUtils.copyProperties(uploadRequest, resultVO);
                resultVO.setOsvizVideoUrl(fastIdMapS3Url.get(uploadRequest.getId()));
                if (resultVO.getTaskType() == FastUploadTaskTypeEnum.FILE_PATH.getCode()) {
                    canDataTasks.add(resultVO);
                    vehicleRecFileService.extendOtherFileList(resultVO);
                    continue;
                }

                tasks.add(resultVO);
                if (uploadRequest.getStatus() != null
                        && VehicleUploadStatusEnum.SUCCEED.getCode() == uploadRequest.getStatus()) {
                    Map<String, Long> uploadModuleCount = collect.get("" + uploadRequest.getId());
                    final List<String> configModules = Arrays
                            .asList(FastUploadConfig.VEHICLE_UPLOAD_REQUEST_TASK_ACCIDENT_REQUIRED_MODULES
                                    .split(CharConstant.CHAR_DD));
                    List<String> modules = uploadModuleCount == null ? new ArrayList<>()
                            : new ArrayList<>(uploadModuleCount.keySet());
                    // 对模块的状态进行处理
                    List<String> lackModules = new ArrayList<>();
                    lackModules.addAll(configModules);
                    lackModules.removeAll(modules);
                    log.info("lackModules: {}", lackModules);
                    String modelsStatus = String.join(CharConstant.CHAR_DD, lackModules);
                    if (lackModules.size() == configModules.size()) {
                        modulesStatusOnviewMap.put(uploadRequest.getId().toString(), "基础模块全缺失");
                        resultVO.setModuleStatus("数据缺失无法播放");
                        continue;
                    }
                    modulesStatusOnviewMap.put(uploadRequest.getId().toString(), modelsStatus);
                    resultVO.setModuleStatus(modelsStatus);
                }
            }
        }
        accidentInfoVO.setUploadTasks(tasks);
        accidentInfoVO.setCanDataTasks(canDataTasks);
        accidentInfoVO.setModulesStatusOnView(modulesStatusOnviewMap);
        if (id != null) {
            accidentInfoVO.setPictures(fileInfoMapper.queryByRefs(id.toString(), FileSourceEnum.ACCIDENT.getMsg(),
                    FileTypeEnum.IMAGE.getCode()));
            accidentInfoVO.setVideos(fileInfoMapper.queryByRefs(id.toString(), FileSourceEnum.ACCIDENT.getMsg(),
                    FileTypeEnum.VIDEO.getCode()));
            accidentInfoVO.setAccidentReview(accidentReviewMapper.getByAccidentId(id.toString()));
        }
        accidentInfoVO.setReportAccidentTime(
                DatetimeUtil.format(bizAccidentInfo.getReportAccidentTime(), DateTimeFormatterConstant.ymdhms));
        if (DEFAULT_TIME.equals(accidentInfoVO.getSiteDisposeTime())) {
            accidentInfoVO.setSiteDisposeTime("");
        }
        if (DEFAULT_TIME.equals(accidentInfoVO.getRiskEliminationTime())) {
            accidentInfoVO.setRiskEliminationTime("");
        }
        if (DEFAULT_TIME.equals(accidentInfoVO.getSecurityGroupResponseTime())) {
            accidentInfoVO.setSecurityGroupResponseTime("");
        }
        if (bizAccidentInfo.getSpeed().compareTo(INVALID_SPEED) == 0) {
            accidentInfoVO.setSpeed("未知");
        } else {
            accidentInfoVO.setSpeed(String.format("%.2f km/h", bizAccidentInfo.getSpeed()));
        }
        if (bizAccidentInfo.getExpectRepairTime()!=null){
            accidentInfoVO.setExpectRepairTime(DatetimeUtil.dateFormat(DatetimeUtil.YMDHMS, bizAccidentInfo.getExpectRepairTime()));
        }
        if (bizAccidentInfo.getFinishRepairTime()!=null){
            accidentInfoVO.setFinishRepairTime(DatetimeUtil.dateFormat(DatetimeUtil.YMDHMS, bizAccidentInfo.getFinishRepairTime()));
        }
        if (bizAccidentInfo.getAcceptanceTime()!=null){
            accidentInfoVO.setAcceptanceTime(DatetimeUtil.dateFormat(DatetimeUtil.YMDHMS, bizAccidentInfo.getAcceptanceTime()));
        }
        return accidentInfoVO;
    }

    public Map<Long, String> queryOsvizVideo(List<Long> completeTaskIdList) {
        List<BizFastOsvizTask> fastOsvizTaskList = osvizService.queryByFastIds(completeTaskIdList);
        return fastOsvizTaskList.stream()
                .collect(Collectors.toMap(BizFastOsvizTask::getFastId, BizFastOsvizTask::getS3Url));
    }

    private String genAccidentId(Date accidentTime, String vin) {
        String vehicleName = vehicleInfoService.getVehicleName(vin);
        String timeStr = DatetimeUtil.format(accidentTime, DatetimeUtil.yyyyMMddHHmmssSSS);
        return new StringBuilder(timeStr).append(CharConstant.CHAR_XH).append("accident").append(CharConstant.CHAR_XH)
                .append(vehicleName).toString();
    }

    @Override
    public BizRecordRemoteVideo uploadVideo(BizAccidentInfoVO bizAccidentInfoVO, MultipartFile file) throws Exception {
        BizRecordRemoteVideo bizRecordRemoteVideo = new BizRecordRemoteVideo();
        bizRecordRemoteVideo.setTargetId(String.valueOf(bizAccidentInfoVO.getId()));
        bizRecordRemoteVideo.setVin(bizAccidentInfoVO.getVin());
        bizRecordRemoteVideo.setFileName(file.getOriginalFilename());
        bizRecordRemoteVideo.setTargetResource(RecordRemoteVideoResourceEnum.ACCIDENT.getCode());
        bizRecordRemoteVideo.setFileSize(file.getSize());
        String vehicleName = vehicleInfoService.getVehicleName(bizAccidentInfoVO.getVin());
        String fileName = file.getOriginalFilename();
        String s3Key = RECORD_VIDEO + CharConstant.CHAR_XX + vehicleName + CharConstant.CHAR_XX + fileName;
        log.info("Start to upload video, accidentId is [{}], videoName is [{}]", bizAccidentInfoVO.getId(), fileName);
        try {
            s3ClientBeijingInner.putObject(vehicleObject, s3Key, file.getInputStream(), null);
        } catch (Exception e) {
            log.error("Upload local video failed, accidentId is [{}], vehicleName is [{}], videoName is [{}]",
                    bizAccidentInfoVO.getId(), vehicleName, fileName, e);
            throw e;
        }
        log.info("End upload video, accidentId is [{}], vehicleName is [{}]", bizAccidentInfoVO.getId(), fileName);
        bizRecordRemoteVideo.setS3Url(CommonConstant.S3_PREFIX + vehicleObject + CharConstant.CHAR_XX + s3Key);
        log.info("Success to upload accident video, start to insert table, param is [{}]", bizRecordRemoteVideo);
        bizRecordRemoteVideoMapper.insertVideo(bizRecordRemoteVideo);
        return bizRecordRemoteVideo;
    }

    @Override
    public void addRemoteVideo(RemoteVideoRequest remoteVideoRequest) {
        BizRecordRemoteVideo bizRecordRemoteVideo = new BizRecordRemoteVideo();
        BeanUtils.copyProperties(remoteVideoRequest, bizRecordRemoteVideo);
        bizRecordRemoteVideo.setTargetId(String.valueOf(remoteVideoRequest.getId()));
        bizRecordRemoteVideo.setTargetResource(RecordRemoteVideoResourceEnum.ACCIDENT.getCode());
        bizRecordRemoteVideo.setRemoteVideoStartTime(
                DatetimeUtil.covertToDate(remoteVideoRequest.getRemoteVideoStartTime(), DatetimeUtil.YMDHMS));
        bizRecordRemoteVideo.setRemoteVideoEndTime(
                DatetimeUtil.covertToDate(remoteVideoRequest.getRemoteVideoEndTime(), DatetimeUtil.YMDHMS));
        log.info("Start to insert accident remote video time info, param is [{}]", bizRecordRemoteVideo);
        bizRecordRemoteVideoMapper.insertVideo(bizRecordRemoteVideo);
        log.info("End to insert accident remote video time info, parap is [{}]", bizRecordRemoteVideo);
    }

    @Override
    public void deleteLocalVideo(Long id, String fileName) {
        log.info("Start to logic delete local video, accidentId is [{}], fileName is [{}]", id, fileName);
        bizRecordRemoteVideoMapper.logicDeleteByTargetId(String.valueOf(id), fileName);
    }

    @Override
    public void deleteRemoteVideo(RemoteVideoRequest remoteVideoRequest) {
        log.info("Start to logic delete remote video, param is [{}]", remoteVideoRequest);
        Date remoteVideoStartTime = DatetimeUtil.covertToDate(remoteVideoRequest.getRemoteVideoStartTime(),
                DatetimeUtil.YMDHMS);
        Date remoteVideoEndTime = DatetimeUtil.covertToDate(remoteVideoRequest.getRemoteVideoEndTime(),
                DatetimeUtil.YMDHMS);
        bizRecordRemoteVideoMapper.logicDeleteRemoteVideoByTargetId(remoteVideoRequest.getId(), remoteVideoStartTime,
                remoteVideoEndTime);
    }

    @Override
    public void getVideoList(Long accidentId, BizAccidentInfoVO accidentInfoVO) {
        List<BizRecordRemoteVideo> bizRecordRemoteVideoList = bizRecordRemoteVideoMapper.getRecordRemoteVideoByTarget(
                String.valueOf(accidentId), RecordRemoteVideoResourceEnum.ACCIDENT.getCode());
        if (bizRecordRemoteVideoList == null) {
            log.info("This accident not have any video, accidentId is [{}]", accidentId);
            return;
        }
        log.info("This accident have video, query result is [{}]", bizRecordRemoteVideoList);
        List<BizRecordRemoteVideo> localVideo = new ArrayList<>();
        List<BizRecordRemoteVideo> remoteVideo = new ArrayList<>();
        bizRecordRemoteVideoList.forEach(item -> {
            if (StringUtils.isNotBlank(item.getS3Url())) {
                localVideo.add(item);
            } else {
                remoteVideo.add(item);
            }
        });
        accidentInfoVO.setLocalVideo(localVideo);
        accidentInfoVO.setRemoteVideo(remoteVideo);
    }

    @Override
    public Map<String, Object> getVehicleStatus(String vin, Date accidentTime) {
        // 优先从新接口获取
        Map<String, Object> vehicleStatus = getVehicleStatusFromWalle(vin, accidentTime);
        // 新接口没有获取到数据则尝试从旧接口获取
        if (vehicleStatus == null || CollectionUtils.isEmpty(vehicleStatus)) {
            vehicleStatus = getVehicleStatusFromIIIRC(vin, accidentTime);
        }
        return vehicleStatus;
    }

    /**
     * 从Walle获取车辆状态信息（新接口）
     * @param vin
     * @param accidentTime
     * @return
     */
    private Map<String, Object> getVehicleStatusFromWalle(String vin, Date accidentTime) {
        // 换算单位，从ms换算到s
        Long startTimestamp = (accidentTime.getTime() - timeGap.get(0) * CommonConstant.MILLIS_TO_SECOND) / CommonConstant.MS_TO_S;
        Long endTimestamp = (accidentTime.getTime() - timeGap.get(1) * CommonConstant.MILLIS_TO_SECOND) / CommonConstant.MS_TO_S;
        List<VehicleDataInfoVO> data = eveGetVehicleDataAdaptor.getVehicleData(vin, startTimestamp, endTimestamp);
        if (data == null || CollectionUtils.isEmpty(data)) {
            return null;
        }
        VehicleDataInfoVO vehicleDataInfoVO = data.get(0);
        Map<String, Object> returnValue = new HashMap<>();
        // 将速度统一化作m/s，和老接口保持一致，在updateVehicleStatusForAccidentInfo中再统一化为km/h
        returnValue.put("speed", (Optional.ofNullable(vehicleDataInfoVO.getKmph()).orElse(0.0)) / CommonConstant.MPH_TO_KMPH);
        returnValue.put("drive_mode_desc", vehicleDataInfoVO.getDriveStatus());
        returnValue.put("longitude", vehicleDataInfoVO.getLongitude());
        returnValue.put("latitude", vehicleDataInfoVO.getLatitude());
        return returnValue;
    }

    /**
     * 从IIIRC获取车辆状态信息（老接口）
     * @param vin
     * @param accidentTime
     * @return
     */
    private Map<String, Object> getVehicleStatusFromIIIRC(String vin, Date accidentTime) {
        // startTimestamp 表示事故发生前的时刻， endTimestamp 表示事故发生后的时刻
        Long startTimestamp = accidentTime.getTime() - timeGap.get(0) * CommonConstant.MILLIS_TO_SECOND;
        Long endTimestamp = accidentTime.getTime() + timeGap.get(1) * CommonConstant.MILLIS_TO_SECOND;

        String url = String.format("%s/vehicleStatusHistory?vin=%s&startTimestamp=%s&endTimestamp=%s",
                CommonConstant.IIIRC_OPENAPI_URL, vin, startTimestamp, endTimestamp);
        int maxRetryTime = 3;
        String response = "";
        for (int i = 0; i < maxRetryTime; i++) {
            response = CommonUtil.doGet(url);
            if (StringUtils.isNotBlank(response)) {
                break;
            }
        }
        if (StringUtils.isBlank(response)) {
            log.error("Get accident vehicle status failed, url is [{}]", url);
            return null;
        }
        Map<String, Object> responseMap = (Map<String, Object>)JSON.parseObject(response, Map.class);
        log.info("Get accident vehilce status success, url is [{}], response is [{}]", url, responseMap);
        if ((int)responseMap.getOrDefault("ret", 1000) != 0) {
            return null;
        }
        List<Object> dataList = (List<Object>)responseMap.get("data");
        if (dataList == null || dataList.isEmpty()) {
            return null;
        }

        // 从获取的数据列表中获取数据,取最后一帧数据，此时距离发生时刻最近
        Map<String, Object> data = (Map<String, Object>)dataList.get(dataList.size() - 1);
        if (data == null || data.isEmpty()) {
            return null;
        }
        return data;
    }

    private BizEventDeriveIntervention getNearestInterventionId(BizAccidentInfo accidentInfo) {
        BizEventDeriveIntervention intervention = new BizEventDeriveIntervention();
        if (accidentInfo == null || accidentInfo.getAccidentTime() == null) {
            return null;
        }
        String vin = accidentInfo.getVin();
        if (vin != null && !vin.isEmpty()) {
            intervention = interventionMapper.getNearestIntervention(vin,
                    DatetimeUtil.dateFormat(DateTimeFormatterConstant.ymdhms, accidentInfo.getAccidentTime()));
        }
        return intervention;
    }

    private BizEventDeriveIntervention getNearestInterventionTime(BizAccidentInfo accidentInfo) {
        if (accidentInfo == null || accidentInfo.getAccidentTime() == null || accidentInfo.getVin() == null) {
            return null;
        }

        String vin = accidentInfo.getVin();
        Date accidentTime = accidentInfo.getAccidentTime();
        String accidentTimeString = DatetimeUtil.format(accidentTime, DateTimeFormatterConstant.yyyyMMddHHmmss_Str);
        Calendar cd = Calendar.getInstance();
        cd.setTime(accidentTime);
        cd.add(Calendar.MINUTE, -FastUploadConfig.ACCIDENT_TIME_BEFORE);
        Date dateBefore = cd.getTime();
        String dateBeforeStr = DatetimeUtil.format(dateBefore, DateTimeFormatterConstant.yyyyMMddHHmmss_Str);
        log.info("dateBefore: {}", dateBeforeStr);
        BizEventDeriveIntervention intervention = interventionMapper.getNearestInterventionByTimeBefore(vin,
                accidentTimeString, dateBeforeStr);

        if (intervention == null) {
            cd.setTime(accidentTime);
            cd.add(Calendar.MINUTE, FastUploadConfig.ACCIDENT_TIME_AFTER);
            Date dateAfter = cd.getTime();
            String dateAfterStr = DatetimeUtil.format(dateAfter, DateTimeFormatterConstant.yyyyMMddHHmmss_Str);
            log.info("dateAfter: {}", dateAfterStr);

            intervention = interventionMapper.getNearestInterventionByTimeAfter(vin, accidentTimeString, dateAfterStr);
        }
        return intervention;
    }

    private void uploadTaskByIntervention(BizEventDeriveIntervention intervention) throws Exception {
        if (intervention == null || intervention.getEventId() == null) {
            return;
        }
        List<VehicleUploadRequest> tasks = uploadRequestMapper.selectByInterventionId(intervention.getEventId());
        VehicleUploadRequest task = new VehicleUploadRequest();
        if (tasks == null || tasks.isEmpty()) {
            task.setVin(intervention.getVin());
            task.setModule(FastUploadConfig.VEHICLE_UPLOAD_REQUEST_MODULE_LIST);
            task.setTaskType(FastUploadTaskTypeEnum.ACCIDENT.getCode());
            task.setStatus(FastUploadTaskStatusEnum.CREATED.getCode());
            task.setPriority(FastUploadTaskPriorityConstant.NON_OPERATIONAL);
            task.setMeasurementTimestamp(intervention.getInterventionTimestamp());
            task.setStart(DatetimeUtil.covertTimestampToDate(intervention.getInterventionTimeNanoSecond()
                    - TimeConstant.S_TO_NS * FastUploadConfig.VEHICLE_UPLOAD_REQUEST_MEASUREMENT_TIME_LOWER));
            task.setEnd(DatetimeUtil.covertTimestampToDate(intervention.getInterventionTimeNanoSecond()
                    + TimeConstant.S_TO_NS * FastUploadConfig.VEHICLE_UPLOAD_REQUEST_MEASUREMENT_TIME_UPPER));
            task.setInterventionId(intervention.getEventId());
            task.setEventId(intervention.getEventId());
            task.setTaskType(FastUploadTaskTypeEnum.CASE.getCode());
            task.setCreator(FastUploadSystemCreatorEnum.INTERVENTION_ACCIDENT_SYSTEM.getName());

            vehicleUploadRequestService.insertVehicleUploadRequest(task);
        } else {
            task = tasks.get(0);
            if (task.getStatus() == FastUploadTaskStatusEnum.WRONG.getCode()
                    || task.getStatus() == FastUploadTaskStatusEnum.SHUTDOWN.getCode()) {
                task.setStatus(FastUploadTaskStatusEnum.CREATED.getCode());
                task.setPriority(FastUploadTaskPriorityConstant.NON_OPERATIONAL);
                uploadRequestMapper.update(task);
            }
        }
    }

    private void createFastUploadTaskForAccident(BizAccidentInfo accidentInfo, Date taskTime) throws Exception {
        // 默认取事故发生前30s+后10s，可配置步长和频次
        int step = FastUploadConfig.VEHICLE_UPLOAD_REQUEST_TASK_GENERATE_STEP;
        int generationTimes = FastUploadConfig.VEHICLE_UPLOAD_REQUEST_TASK_GENERATE_TIMES;
        String vin = accidentInfo.getVin();
        long occurTime = taskTime.getTime() / TimeConstant.MS_TO_S;
        long reportTime = accidentInfo.getAccidentTime().getTime() / TimeConstant.MS_TO_S;
        long reportUpperTime = reportTime + 2;
        long reportLowerTime = reportTime - 2 * step + 2;
        long interventionTaskTime = occurTime;
        if (occurTime >= (reportLowerTime - step) && occurTime <= reportUpperTime) {
            interventionTaskTime = reportLowerTime - step;
        } else if (occurTime > reportUpperTime && occurTime <= (reportUpperTime + (generationTimes - 1) * step)) {
            interventionTaskTime = reportUpperTime + (generationTimes - 1) * step;
        }
        StringBuilder uploadTaskIds = new StringBuilder();
        // 添加当前磁盘
        String diskId = getCurrentDiskId(vin);
        String module = FastUploadConfig.VEHICLE_UPLOAD_REQUEST_MODULE_LIST + ","
                + FastUploadConfig.ACCIDENT_TASK_EXTRA_MODULES;
        // 添加上报时间对应的对应的任务
        long reportTaskTime = reportTime - step + 2;
        for (int i = 0; i < 2; i++) {
            VehicleUploadRequest taskCommon = createTaskByTimeAndOrder(reportTaskTime, i, vin, module);
            if (taskCommon != null && taskCommon.getId() != null) {
                // 缓存车盘关系
                cacheAccidentDiskRelations(taskCommon.getId(), diskId);
                uploadTaskIds.append(taskCommon.getId() + CharConstant.CHAR_DD);
            }
        }

        // 添加接管时间对应的任务
        for (int i = 0; i < generationTimes; i++) {
            VehicleUploadRequest taskCommon = createTaskByTimeAndOrder(interventionTaskTime, i, vin, module);
            if (taskCommon != null && taskCommon.getId() != null) {
                // 缓存车盘关系
                cacheAccidentDiskRelations(taskCommon.getId(), diskId);
                uploadTaskIds.append(taskCommon.getId()).append(CharConstant.CHAR_DD);
            }
        }
        Date canDataStart = DatetimeUtil.covertTimestampToDate(DatetimeUtil.timestampConversion(occurTime - 2L * step));
        Date canDataEnd = DatetimeUtil.covertTimestampToDate(DatetimeUtil.timestampConversion(occurTime + step));
        FastUploadTaskRequest fastUploadTaskRequest = new FastUploadTaskRequest();
        fastUploadTaskRequest.setVin(vin);
        fastUploadTaskRequest.setStartDate(canDataStart);
        fastUploadTaskRequest.setEndDate(canDataEnd);
        fastUploadTaskRequest.setCreator(FastUploadSystemCreatorEnum.ACCIDENT_SYSTEM.getName());
        fastUploadTaskRequest.setPriority(FastUploadTaskPriorityConstant.ACCIDENT);
        VehicleUploadRequest vehicleUploadRequest = vehicleUploadRequestService
                .createCanDataFastTask(fastUploadTaskRequest);
        Long taskId = vehicleUploadRequest.getId();
        if (taskId != null) {
            uploadTaskIds.append(taskId).append(CharConstant.CHAR_DD);
        }

        if (uploadTaskIds.toString().contains(CharConstant.CHAR_DD)) {
            accidentInfo.setUploadTaskId(uploadTaskIds.substring(0, uploadTaskIds.length() - 1));
        } else {
            accidentInfo.setUploadTaskId(uploadTaskIds.toString());
        }
    }

    private VehicleUploadRequest createTaskByTimeAndOrder(long taskTime, int index, String vin, String module) {
        int step = FastUploadConfig.VEHICLE_UPLOAD_REQUEST_TASK_GENERATE_STEP;
        VehicleUploadRequest request = createFastUploadTaskByTime(vin, module,
                DatetimeUtil.covertTimestampToDate(DatetimeUtil.timestampConversion(taskTime - index * step)),
                DatetimeUtil.covertTimestampToDate(DatetimeUtil.timestampConversion(taskTime - (index - 1) * step)),
                DatetimeUtil.timestampConversion(taskTime - (index - 1) * step));
        if (request != null) {
            request.setExtraInfo(FastUploadConfig.VEHICLE_UPLOAD_ACCIDENT_DATA_EXTRA_INFO);
        }
        vehicleUploadRequestService.insertVehicleUploadRequest(request);
        return request;
    }

    @Override
    public void cacheAccidentDiskRelations(Long fastUploadTaskId, String diskId) {
        String taskId = FastUploadTaskCommonConstant.FAST_UPLOAD_REDIS_PREFIX + fastUploadTaskId;
        String extend = GsonUtil.toJson(FastUploadTaskExtend.builder().fastUploadTaskId(fastUploadTaskId)
                .taskType(FastUploadTaskTypeEnum.ACCIDENT.getCode()).diskId(diskId).build());
        setFastUploadTaskExtend(taskId, extend);
    }

    @Override
    public void createTaskWhenAccidentTimeUpdate(BizAccidentInfo accidentInfo) {
        Long accidentTime = accidentInfo.getAccidentTime().getTime() / TimeConstant.MS_TO_S;
        BizAccidentInfo bizAccidentInfo = accidentInfoMapper.getById(accidentInfo.getId());
        String uploadTaskId = bizAccidentInfo.getUploadTaskId();
        List<VehicleUploadRequest> requestList = new ArrayList<>();
        String module = FastUploadConfig.VEHICLE_UPLOAD_REQUEST_MODULE_LIST + CharConstant.CHAR_DD
                + FastUploadConfig.ACCIDENT_TASK_EXTRA_MODULES;
        VehicleUploadRequest request;
        Date start = DatetimeUtil.covertTimestampToDate(
                DatetimeUtil.timestampConversion(accidentTime - AccidentConfig.update_time_previous_second));
        Date end = DatetimeUtil.covertTimestampToDate(
                DatetimeUtil.timestampConversion(accidentTime + AccidentConfig.update_time_later_second));
        try {
            request = createFastUploadTaskByTime(accidentInfo.getVin(), module, start, end,
                    accidentTime * CommonConstant.NANO_TO_SECOND);
        } catch (Exception e) {
            log.error("Task build failed, id is [{}], accidentTime is [{}]", accidentInfo.getId(),
                    accidentInfo.getAccidentTime(), e);
            return;
        }
        if (request == null) {
            return;
        }
        request.setCreator(accidentInfo.getReporter());
        request.setPriority(FastUploadTaskPriorityConstant.SPEED_UP_ACCIDENT);
        if (StringUtils.isBlank(uploadTaskId) || "0".equals(uploadTaskId)) {
            log.info("no task in this accident [{}], direct create task, params is [{}]", accidentInfo.getId(),
                    request);
            vehicleUploadRequestService.insertVehicleUploadRequest(request);
            if (request.getId() != null) {
                accidentInfo.setUploadTaskId(request.getId().toString());
            }
        } else {
            String[] taskIds = StringUtils.split(uploadTaskId, CharConstant.CHAR_DD);
            for (String taskId : taskIds) {
                requestList.add(uploadRequestMapper.selectById(Long.valueOf(taskId)));
            }
            if (taskIsAlreadyCreated(requestList, start, end)) {
                log.info("tasks time is overlap this accident_time, no need to create other task, param is [{}]",
                        request);
                return;
            }
            log.info("tasks time is not overlap this accident_time, direct create task, params is [{}]", request);
            vehicleUploadRequestService.insertVehicleUploadRequest(request);
            if (request.getId() != null) {
                accidentInfo.setUploadTaskId(uploadTaskId + CharConstant.CHAR_DD + request.getId().toString());
            }
        }
    }

    private boolean taskIsAlreadyCreated(List<VehicleUploadRequest> requestList, Date start, Date end) {
        Collections.sort(requestList);
        List<VehicleUploadRequest> mergeList = new ArrayList<>();
        mergeList.add(requestList.get(0));
        for (int i = 1; i < requestList.size(); i++) {
            if (requestList.get(i).getStart().getTime() <= requestList.get(i - 1).getEnd().getTime()) {
                mergeList.get(mergeList.size() - 1).setEnd(requestList.get(i).getEnd());
            } else {
                mergeList.add(requestList.get(i));
            }
        }
        log.info("origin vehicle_upload_request list is [{}], after merge, request list is [{}]", requestList,
                mergeList);
        for (VehicleUploadRequest request : mergeList) {
            if (request.getStart().getTime() <= start.getTime() && request.getEnd().getTime() >= end.getTime()) {
                return true;
            }
        }
        return false;
    }

    private VehicleUploadRequest createFastUploadTaskByTime(String vin, String module, Date start, Date end,
            long occurTime) {
        if (vin == null || vin.isEmpty()) {
            return null;
        }

        VehicleUploadRequest uploadRequest = new VehicleUploadRequest();
        uploadRequest.setVin(vin);
        uploadRequest.setMeasurementTimestamp(occurTime);
        uploadRequest.setStart(start);
        uploadRequest.setEnd(end);
        uploadRequest.setModule(module);
        uploadRequest.setPriority(FastUploadTaskPriorityConstant.ACCIDENT);
        uploadRequest.setStatus(FastUploadTaskStatusEnum.CREATED.getCode());
        uploadRequest.setOneCase(false);
        uploadRequest.setIsDiscern(OKOrNotEnum.OK.getCode());
        uploadRequest.setTaskType(FastUploadTaskTypeEnum.ACCIDENT.getCode());
        uploadRequest.setCreator(FastUploadSystemCreatorEnum.ACCIDENT_SYSTEM.getName());
        uploadRequest.setExtraInfo(FastUploadConfig.VEHICLE_UPLOAD_ACCIDENT_DATA_EXTRA_INFO);

        return uploadRequest;
    }

    private String makeMsgAndSendToDx(BizAccidentInfo bizAccidentInfo, String purpose) {
        String  stationName = "";
        try {
            List<String> stationNames = walleDeliveryBasicAdaptor.queryBusinessStationListByVin(bizAccidentInfo.getVin());
            if (!CollectionUtils.isEmpty(stationNames)) {
                stationName = stationNames.get(0);
            }
        } catch (Exception e) {
            log.error("Get group id failed, accident id is {}", bizAccidentInfo.getId(), e);
        }
        if (StringUtils.isBlank( stationName)) {
            stationName = "不详";
        }
        String accidentMsg = makeAccidentNoticeMsg(bizAccidentInfo,  stationName, "车辆事故", purpose);
        log.info("makeMsgAndSendToDx, accidentMsg = {}", accidentMsg);
        sendMessageToGroup(accidentMsg, FastUploadConfig.VEHICLE_UPLOAD_REQUEST_TASK_ACCIDENT_MEMBERS);
        return accidentMsg;
    }
    private String makeAccidentNoticeMsg(BizAccidentInfo bizAccidentInfo, String  stationName, String title, String purpose) {
        BizAccidentInfoVO accidentInfoVO = convert(bizAccidentInfo);
        String bodyText = String.format(
                "%s\n" + "车辆：%s / %s\n" + "车辆所属站点：%s\n" + "用车目的：%s\n" + "时间：%s\n" + "地点：%s\n" + "详情描述：%s\n" + "上报人：%s\n"
                        + "[[车辆位置|%s%s]]\t\t" + "[[事故信息提交|%s%s]]\n\n" + "[[运营HMI|%s%s]]\t\t"
                        + "[[事故处理后台|%s?id=%s&mode=drawer]]\n\n" + "[[云分诊-实时视频|%s%s]]",
                title, accidentInfoVO.getVehicleName(), accidentInfoVO.getVehicleId(),  stationName, purpose,
                accidentInfoVO.getAccidentTime(), accidentInfoVO.getLocationName(), accidentInfoVO.getAccidentDesc(),
                accidentInfoVO.getReporter(), SysParamsConfig.vehicle_map_url, accidentInfoVO.getVin(),
                AccidentConfig.accident_info_commit_url_prefix, accidentInfoVO.getId(), SysParamsConfig.hmi_url,
                accidentInfoVO.getVin(), FastUploadConfig.VEHICLE_UPLOAD_REQUEST_ACCIDENT_DETAIL_URL,
                accidentInfoVO.getId(), SysParamsConfig.csm_event_url, accidentInfoVO.getVin());
        return bodyText;
    }

    private void sendMessageToGroup(String bodyText, String members) {
        try {
            AccidentDxUtil.noticeInit();
            log.info("Start send this msg to DX, {}", bodyText);
            log.info("Send msg to mis id, {}", members);
            AccidentDxUtil.notice(members.split(CharConstant.CHAR_DD), bodyText);
        } catch (Exception e) {
            log.error("send message of accident fail，bodyText id is [{}]", bodyText, e);
        }
    }

    @Override
    public BizAccidentInfoVO convertV2(BizAccidentInfo bizAccidentInfo) {
        if (bizAccidentInfo == null) {
            return null;
        }

        BizAccidentInfoVO accidentInfoVO = new BizAccidentInfoVO();
        BeanUtils.copyProperties(bizAccidentInfo, accidentInfoVO);
        Long id = bizAccidentInfo.getId();
        accidentInfoVO.setId(id);
        String vin = bizAccidentInfo.getVin() == null ? "" : bizAccidentInfo.getVin();
        accidentInfoVO.setVin(vin);
        accidentInfoVO.setVehicleName(vehicleInfoService.getVehicleName(bizAccidentInfo.getVin()));
        accidentInfoVO.setVehicleId(vehicleInfoService.getVehicleId(bizAccidentInfo.getVin()));
        if (bizAccidentInfo.getAccidentTime() != null) {
            accidentInfoVO.setAccidentTime(
                    DatetimeUtil.dateFormat(DateTimeFormatterConstant.ymdhms, bizAccidentInfo.getAccidentTime()));
        }
        if (bizAccidentInfo.getCreateTime() != null) {
            accidentInfoVO.setCreateTime(
                    DatetimeUtil.dateFormat(DateTimeFormatterConstant.ymdhms, bizAccidentInfo.getCreateTime()));
        }
        if (bizAccidentInfo.getUpdateTime() != null) {
            accidentInfoVO.setUpdateTime(
                    DatetimeUtil.format(bizAccidentInfo.getUpdateTime(), DateTimeFormatterConstant.ymdhms));
        }
        if (bizAccidentInfo.getSiteDisposeTime() != null) {
            accidentInfoVO
                    .setSiteDisposeTime(DatetimeUtil.format(bizAccidentInfo.getSiteDisposeTime(), DatetimeUtil.YMDHMS));
        }
        if (bizAccidentInfo.getRiskEliminationTime() != null) {
            accidentInfoVO.setRiskEliminationTime(
                    DatetimeUtil.format(bizAccidentInfo.getRiskEliminationTime(), DatetimeUtil.YMDHMS));
        }
        if (bizAccidentInfo.getAffiliation() != null) {
            accidentInfoVO.setSiteBelong(AffiliationEnum.getMsgByCode(bizAccidentInfo.getAffiliation()));
        }
        accidentInfoVO.setReportAccidentTime(
                DatetimeUtil.format(bizAccidentInfo.getReportAccidentTime(), DateTimeFormatterConstant.ymdhms));

        return accidentInfoVO;
    }

    @Override
    public void deleteAccident(Long id) {
        BizAccidentInfo accidentInfo = new BizAccidentInfo();
        BizAccidentInfo accident = getById(id);
        accidentInfo.setId(id);
        accidentInfo.setIsDeleted(1);
        log.info("Start to delete this accident, id is [{}]", id);
        accidentInfoMapper.update(accidentInfo);
        OnCallList onCallList = OnCallList.builder().isDeleted(1).caseId(accident.getEventId()).build();
        List<OnCallList> onCallLists = new ArrayList<>();
        onCallLists.add(onCallList);
        onCallListService.updateOfflineIdBatch(onCallLists);
    }

    @Override
    public String getCurrentDiskId(String vin) {
        String diskId = "";
        List<Map<String, Object>> vehicleStatusList =
                iiircOpenAPIHandler.getVehicleStatusBatch(Collections.singletonList(vin));

        if (!CollectionUtils.isEmpty(vehicleStatusList)) {
            Map<String, Object> vehicleStatus = vehicleStatusList.get(0);
            if (vin.equals(vehicleStatus.get("vin"))) {
                Object diskDataWwn = vehicleStatus.get("disk_data_wwn");
                diskId = Objects.isNull(diskDataWwn) ? "" : diskDataWwn.toString();
            }
            log.info("getCurrentDiskId, vin: {}, diskId: {}", vin, diskId);
        }

        return diskId;
    }

    @Override
    public String getAccidentDiskRelation(String fastUploadTaskId) {
        int i = 0;
        while (i < 3) {
            try {
                return redisClient.hget(STORE_KEY, fastUploadTaskId);
            } catch (Exception e) {
                i++;
                log.error("redis获取值失败: {}, fastUploadTaskId: {}", i, fastUploadTaskId, e);
            }
        }
        return null;
    }

    @Override
    public void setFastUploadTaskExtend(String fastUploadTaskId, String extend) {
        int i = 0;
        while (i < 3) {
            try {
                redisClient.hset(STORE_KEY, fastUploadTaskId, extend);
                return;
            } catch (Exception e) {
                i++;
                log.error("redis添加值失败: {}, fastUploadTaskId: {}, extend: {}", i, fastUploadTaskId, extend, e);
            }
        }
    }

    @Override
    public void noticeResponsibilityDelayAccident() {
        long now = System.currentTimeMillis();
        List<BizAccidentInfo> bizAccidentInfoList = accidentInfoMapper
                .queryAccidentResponsibilityDelay(AccidentConfig.accident_resp_id);
        if (bizAccidentInfoList != null && !bizAccidentInfoList.isEmpty()) {
            for (BizAccidentInfo accidentInfo : bizAccidentInfoList) {
                Date accidentTime = accidentInfo.getAccidentTime();
                if (accidentTime == null) {
                    continue;
                }
                if (!isAccidentNeedToNotice(now, accidentTime.getTime())) {
                    continue;
                }
                String members = AccidentConfig.accident_members;
                String title = String.format("⚠️【提醒️】事故超%d小时未填写\"事故原因\"", (now - accidentTime.getTime()) / HOUR_TO_MS);
                String group = "";
                String headman = "";
                RecordV2 recordV2 = recordV2Mapper.selectByRecordName(accidentInfo.getRecordName());
                String purpose = recordV2 == null ? "" : recordV2.getPurpose();
                GroupQueryResultDTO groupQueryResultDTO = getVehicleGroupInfo(accidentInfo.getVin());
                if (groupQueryResultDTO != null) {
                    group = groupQueryResultDTO.getPurpose();
                    headman = groupQueryResultDTO.getHeadman();
                }
                if (StringUtils.isNotBlank(headman)) {
                    members = AccidentConfig.accident_members + CharConstant.CHAR_DD + headman;
                }
                String bodyText = makeAccidentNoticeMsg(accidentInfo, group, title, purpose);
                sendMessageToGroup(bodyText, members);
            }
        }
    }

    @Override
    public List<Long> queryLatestAccidentTime(String vin, int days) {
        List<Long> result = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, -days);
        String startTime = DatetimeUtil.format(calendar.getTime(), DatetimeUtil.YMDHMS);
        List<BizAccidentInfo> latestAccidentInfo = accidentInfoMapper.queryLatestAccidentInfo(vin, startTime);
        if (latestAccidentInfo == null || latestAccidentInfo.isEmpty()) {
            return result;
        }
        for (BizAccidentInfo bizAccidentInfo : latestAccidentInfo) {
            result.add(bizAccidentInfo.getAccidentTime().getTime());
        }
        return result;
    }

    public boolean isAccidentNeedToNotice(long now, long accidentTime) {
        boolean result = false;
        if ((now - accidentTime) > AccidentConfig.accident_resp_delay_hours * HOUR_TO_MS) {
            if ((now - accidentTime) % (AccidentConfig.accident_resp_delay_hours * HOUR_TO_MS) < HOUR_TO_MS) {
                log.info("Delay hours is {}",
                        (now - accidentTime) % (AccidentConfig.accident_resp_delay_hours * HOUR_TO_MS));
                result = true;
            }
        }
        return result;
    }

    public boolean isUndisposedAccidentNeedToNotice(long now, long accidentTime) {
        long timeoutMinute = AccidentConfig.accident_disposed_delay_minutes;
        long cycleMinute = AccidentConfig.accident_disposed_delay_cycle_minutes;
        return ((now - accidentTime) > timeoutMinute * TimeConstant.MS_TO_MINUTE) && ((now - accidentTime)
                % (timeoutMinute * TimeConstant.MS_TO_MINUTE) <= cycleMinute * TimeConstant.MS_TO_MINUTE);
    }

    public BizAccidentInfoVO queryAccidentInLatestTime(String vin, Date accidentTime, long latestMillis) {
        Date accidentTimeBefore = new Date(accidentTime.getTime() - latestMillis);
        String startTimeBeforeStr = DatetimeUtil.format(accidentTimeBefore, DatetimeUtil.YMDHMS);

        BizAccidentInfo bizAccidentInfo = accidentInfoMapper.queryAccidentInLatestTime(vin, startTimeBeforeStr);
        if (bizAccidentInfo == null) {
            return null;
        }
        BizAccidentInfoVO accidentInfoVO = new BizAccidentInfoVO();
        BeanUtils.copyProperties(bizAccidentInfo, accidentInfoVO);
        accidentInfoVO.setAccidentTime(DatetimeUtil.format(bizAccidentInfo.getAccidentTime(), DatetimeUtil.YMDHMS));
        accidentInfoVO.setReportAccidentTime(
                DatetimeUtil.format(bizAccidentInfo.getReportAccidentTime(), DatetimeUtil.YMDHMS));
        accidentInfoVO.setVehicleName(vehicleInfoService.getVehicleName(bizAccidentInfo.getVin()));

        return accidentInfoVO;
    }

    public Boolean isOccurAccidentInLatestTime(String vin, Date accidentTime, long latestMillis) {
        Date accidentTimeBefore = new Date(accidentTime.getTime() - latestMillis);
        String startTimeBeforeStr = DatetimeUtil.format(accidentTimeBefore, DatetimeUtil.YMDHMS);

        BizAccidentInfo bizAccidentInfo = accidentInfoMapper.queryAccidentInLatestTime(vin, startTimeBeforeStr);
        if (bizAccidentInfo == null) {
            return false;
        }
        return true;
    }

    @Override
    public void resetAccidentUploadTaskReady() {
        // 找具体的事故详细分析
        FastUploadTaskRequest fastUploadTaskRequest = new FastUploadTaskRequest();
        fastUploadTaskRequest.setPriority(FastUploadTaskPriorityConstant.ACCIDENT);
        Calendar target = Calendar.getInstance();
        String start = DatetimeUtil.format(target.getTime(), "yyyy-MM-dd 00:00:00");
        String end = DatetimeUtil.format(target.getTime(), "yyyy-MM-dd 23:59:59");
        fastUploadTaskRequest.setStartTime(start);
        fastUploadTaskRequest.setEndTime(end);
        fastUploadTaskRequest.setStatus(VehicleUploadStatusEnum.FAIL.getCode());
        fastUploadTaskRequest.setTaskType(FastUploadTaskTypeEnum.ACCIDENT.getCode());
        // 查找失败的事故
        List<VehicleUploadRequest> list = vehicleUploadRequestMapper.selectByCondition(fastUploadTaskRequest);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        final List<VehicleUploadRequest> matchList = new ArrayList<>();
        for (VehicleUploadRequest uploadRequest : list) {
            String vin = uploadRequest.getVin();
            if (StringUtils.isBlank(vin)) {
                continue;
            }
            String taskId = FastUploadTaskCommonConstant.FAST_UPLOAD_REDIS_PREFIX + uploadRequest.getId();
            // redis获取车盘关系
            String extendJson = getAccidentDiskRelation(taskId);
            if (extendJson == null) {
                continue;
            }
            Gson gson = new Gson();
            FastUploadTaskExtend fastUploadTaskExtend = gson.fromJson(extendJson, FastUploadTaskExtend.class);
            if (fastUploadTaskExtend == null || fastUploadTaskExtend.getTaskType() == null
                    || fastUploadTaskExtend.getDiskId() == null
                    || FastUploadTaskTypeEnum.ACCIDENT.getCode() != fastUploadTaskExtend.getTaskType()) {
                continue;
            }
            // 获取当前磁盘id
            String currentDiskId = getCurrentDiskId(vin);
            log.info("resetAccidentUploadTaskReady taskId:{}, currentDiskId:{}, oldDiskId:{}", taskId, currentDiskId,
                    fastUploadTaskExtend.getDiskId());
            if (StringUtils.isBlank(currentDiskId)) {
                continue;
            }
            if (currentDiskId.equalsIgnoreCase(fastUploadTaskExtend.getDiskId())) {
                uploadRequest.setStatus(VehicleUploadStatusEnum.READY.getCode());
                matchList.add(uploadRequest);
            }
        }
        if (CollectionUtils.isEmpty(matchList)) {
            return;
        }
        log.info("[BizAccidentInfoServiceImpl]resetAccidentUploadTaskReady, update matchList:{}", matchList);
        vehicleUploadRequestMapper.updateDiscernByIdBatch(matchList);
    }

    @Override
    public void callAndNoticeUncheckedAccident(BizAccidentInfoRequest request) {
        Date eventTime = new Date(request.getEventTimestamp());
        String vehicleName = vehicleInfoService.getVehicleName(request.getVin());
        String vehicleId = vehicleInfoService.getVehicleId(request.getVin());
        String callText = String.format("%s车事故超%s分钟未完成事故核验", vehicleName, request.getUncheckedTime());
        String title = String.format("⚠️ %s", callText);
        String group = "";
        GroupQueryResultDTO groupQueryResultDTO = getVehicleGroupInfo(request.getVin());
        if (groupQueryResultDTO != null) {
            group = groupQueryResultDTO.getPurpose();
        }
        RecordV2 recordV2 = recordV2Mapper.queryLatestRecordByVin(request.getVin());
        BizAccidentInfoVO bizAccidentInfoVO = new BizAccidentInfoVO();
        bizAccidentInfoVO.setVin(request.getVin());
        bizAccidentInfoVO.setGroup(group);
        bizAccidentInfoVO.setPurpose(recordV2.getPurpose());
        bizAccidentInfoVO.setAccidentTime(DatetimeUtil.format(eventTime));
        bizAccidentInfoVO.setVehicleName(vehicleName);
        bizAccidentInfoVO.setVehicleId(vehicleId);
        String bodyText = makeUncheckedAccidentNoticeMsg(title, bizAccidentInfoVO);

        // todo: 替换新的值班方式和外呼方式
        List<String> misIds = getAccidentCallDutyPersonMisIds();
        sendMessageToGroup(bodyText, String.join(",", misIds));

        eveAutoCallAdaptor.autoCall(request.getEventId(), misIds, callText);
    }

    @Override
    public void insertUserPhoneNumber(AutoCallRequest autoCallRequest) {
        UserEncryptPhoneNumber userEncryptPhoneNumber = new UserEncryptPhoneNumber();
        userEncryptPhoneNumber.setMisId(autoCallRequest.getMisId());
        userEncryptPhoneNumber
                .setPhoneNumber(dataCenterAutoCallService.getEncryptNumber(autoCallRequest.getRealNumber()));
        userEncryptPhoneNumberMapper.insertOne(userEncryptPhoneNumber);
    }

    @Override
    public void updateUserPhoneNumber(AutoCallRequest autoCallRequest) {
        String phoneNumber = dataCenterAutoCallService.getEncryptNumber(autoCallRequest.getRealNumber());
        userEncryptPhoneNumberMapper.updatePhoneNumberWithMisId(autoCallRequest.getMisId(), phoneNumber);
    }

    private String makeUncheckedAccidentNoticeMsg(String title, BizAccidentInfoVO accidentInfoVO) {
        String bodyText = String.format(
                "%s\n" + "车辆：%s / %s\n" + "车辆所属组：%s\n" + "用车目的：%s\n" + "时间：%s\n" + "[[车辆位置|%s%s]]\t\t"
                        + "[[运营HMI|%s%s]]\n\n" + "[[云分诊-实时视频|%s%s]]",
                title, accidentInfoVO.getVehicleName(), accidentInfoVO.getVehicleId(), accidentInfoVO.getGroup(),
                accidentInfoVO.getPurpose(), accidentInfoVO.getAccidentTime(), SysParamsConfig.vehicle_map_url,
                accidentInfoVO.getVin(), SysParamsConfig.hmi_url, accidentInfoVO.getVin(),
                SysParamsConfig.csm_event_url, accidentInfoVO.getVin());
        return bodyText;
    }

    private GroupQueryResultDTO getVehicleGroupInfo(String vin) {
        String vehicleGroupUrl = VehicleTagReportConfig.VEHICLE_GROUP_URL + "?vin=" + vin;
        VehicleGroupResponse response = vehicleEventTagReportService.getVehicleGroup(vehicleGroupUrl);
        if (response != null && response.getRet() == 200 && !response.getData().isEmpty()) {
            return response.getData().get(0);
        }
        return null;
    }

    @Override
    public Map<String, String> getCityAndAffiliationFromRecord(String vin) {
        RecordV2 recordV2 = recordV2Mapper.queryLatestRecordByVin(vin);
        Map<String, String> result = new HashMap<>();
        String city = CommonConstant.UNKNOWN;
        String affiliationString = CommonConstant.UNKNOWN;
        if (recordV2 != null) {
            BizMapArea bizMapArea = bizMapAreaMapper.queryByCode(recordV2.getPlace());
            if (bizMapArea != null) {
                city = bizMapArea.getCity();
                affiliationString = AffiliationEnum.getMsgByCode(bizMapArea.getAffiliation());
            }
        }
        result.put("city", city);
        result.put("affiliationString", affiliationString);
        return result;
    }

    public String getRoadTypeOfAccidentVehicle(String recordName, String locationGps) {
        if (StringUtils.isEmpty(recordName) || StringUtils.isEmpty(locationGps)) {
            return AccidentRoadTypeEnum.UNKNOWN.getMsg();
        }
        RecordV2 recordV2 = recordV2Mapper.selectByRecordName(recordName);
        if (StringUtils.isBlank(recordV2.getHdmapVersion())) {
            return AccidentRoadTypeEnum.UNKNOWN.getMsg();
        }
        List<String> locationGpsList = Arrays.asList(locationGps.split(CharConstant.CHAR_DD));
        if (locationGpsList.size() != 2) {
            return AccidentRoadTypeEnum.UNKNOWN.getMsg();
        }
        String url = CommonConstant.HDMAP_BASE_URL + String.format("/api/road/info/gcj02?lon=%s&lat=%s&version=%s",
                locationGpsList.get(0), locationGpsList.get(1), recordV2.getHdmapVersion());
        try {
            String roadInfo = hdmapHandler.getRoadInfo(url);
            if (StringUtils.isBlank(roadInfo)) {
                return AccidentRoadTypeEnum.UNKNOWN.getMsg();
            }
            AccidentRoadTypeEnum roadTypeEnum = AccidentRoadTypeWithHDMapEnum.getAccidentRoadTypeByHDMapMsg(roadInfo);
            return roadTypeEnum.getMsg();
        } catch (Exception e) {
            log.error("getRoadTypeOfAccidentVehicle, getRoadInfo is error", e);
        }
        return AccidentRoadTypeEnum.UNKNOWN.getMsg();
    }

    @Override
    public String wgs84ToGjc02(String wgs84Data) {
        List<String> locationGpsList = Arrays.asList(wgs84Data.split(CharConstant.CHAR_DD));
        if (locationGpsList.size() != 2) {
            return wgs84Data;
        }
        String url = CommonConstant.ADMAP_BASE_URL + String.format("/api/map/lonlat/to/gcj02?lon=%s&lat=%s",
                locationGpsList.get(0), locationGpsList.get(1));
        try {
            String Gjc02Data = hdmapHandler.getGCJ02(url);
            if (StringUtils.isBlank(Gjc02Data)) {
                return wgs84Data;
            }
            return Gjc02Data;
        } catch (Exception e) {
            log.error("wgs84ToGjc02 is error, wgs84Data = {}", wgs84Data, e);
        }
        return wgs84Data;
    }

    /**
     * 该接口有批量限制，拆分批次调用
     *
     * @param dataList
     * @return
     */
    public void reportRealDataBatch(List<DataReportFormatDTO> dataList, String businessKey) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(dataList)) {
            return;
        }
        int batchSize = CommonConstant.DATA_BUS_BATCH_SIZE;
        int totalSize = dataList.size();
        int batchCount = (totalSize + batchSize - 1) / batchSize;

        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min((i + 1) * batchSize, totalSize);

            List<DataReportFormatDTO> batchDataList = dataList.subList(fromIndex, toIndex);
            reportRealDataToDataBus(batchDataList, businessKey);
        }
    }

    /**
     * 上报实时车辆事故状态数据到数据总线
     *
     * @param dataList
     * @return
     */
    private void reportRealDataToDataBus(List<DataReportFormatDTO> dataList, String businessKey) {
        String reportRealDataUrl = AccidentConfig.eveHostName + REPORT_REAL_DATA;
        // 构建上报数据格式
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("business_key", businessKey);
        contentMap.put("come_data", dataList);

        try {
            String contentMapToJson = JacksonUtil.toJson(contentMap);
            log.info("reportRealDataToDataBus# contentMapToJson is : {}", contentMapToJson);
            String reportRealDataResponse = CommonUtil.doPost(reportRealDataUrl, contentMapToJson, null);
            log.info("reportRealDataToDataBus# response is : {}", reportRealDataResponse);
            CommonResponse commonResponse = JSONObject.parseObject(reportRealDataResponse, CommonResponse.class);
            if (Objects.isNull(commonResponse) || Objects.isNull(commonResponse.getCode())
                    || !commonResponse.getCode().equals(CommonConstant.RESPONSE_SUCCESS_CODE)) {
                throw new RemoteErrorException(reportRealDataResponse);
            }
        } catch (RemoteErrorException e) {
            log.error("RemoteErrorException, reportRealDataToDataBus is failed, inputParam is : {}", contentMap, e);
        } catch (Exception e) {
            log.error("SystemException, reportRealDataToDataBus is failed, inputParam is : {}", contentMap, e);
        }
    }

    public String getLocationNameByGps(String locationGps) {
        if (StringUtils.isBlank(locationGps)) {
            return "未知";
        }
        String queryLocationNamePath = CommonConstant.QUERY_LOCATION_NAME_URL
                + String.format("/v1/location/regeo?key=%s&location=%s", accidentLocationNameApiKey, locationGps);

        try {
            String response = CommonUtil.doGet(queryLocationNamePath);
            log.info("getLocationNameByGps，url = {},response = {}", queryLocationNamePath, response);
            if (StringUtils.isNotBlank(response)) {
                // 尽量一次解析json，避免失败
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonNode = objectMapper.readTree(response);
                int status = jsonNode.get("status").asInt();
                if (status == 200) {
                    return jsonNode.get("regeocode").get(0).get("formatted_address").asText();
                }
            }
        } catch (Exception e) {
            log.error("getLocationNameByGps is failed, locationGps = {}", locationGps, e);
        }
        return "未知";
    }

    private Boolean isCheckAndInsertData(BizAccidentInfo bizAccidentInfo) {
        if (!isOccurAccidentInLatestTime(bizAccidentInfo.getVin(), bizAccidentInfo.getAccidentTime(),
                avoidRepeatMillis)) {
            accidentInfoMapper.insert(bizAccidentInfo);
            return true;
        }
        return false;
    }

    /**
     * 发送车辆事故消息到消息队列
     *
     * @param bizAccidentInfo 车辆事故信息
     * @throws Exception 发送消息异常
     */
    public void sendAccidentMsgToMQ(BizAccidentInfo bizAccidentInfo, AccidentStatusEnum statusEnum) throws Exception {
        // 1 构建MQ消息体
        AccidentMessageDTO accidentMessageDTO = new AccidentMessageDTO();
        accidentMessageDTO.setEventId(bizAccidentInfo.getEventId());
        accidentMessageDTO.setVin(bizAccidentInfo.getVin());
        accidentMessageDTO.setTimestamp(new Date().getTime());
        // 设置车辆事故状态
        accidentMessageDTO.setStatus(statusEnum.getCode());
        // 设置扩展字段
        accidentMessageDTO.setContent(AccidentMessageDTO.ContentDTO.builder()
                .operator(bizAccidentInfo.getSecurityGroupDisposer())
                .statusDes(statusEnum.getMsg())
                .reporter(bizAccidentInfo.getReporter()).build());
        // 2 发送MQ消息
        accidentMessageProducer.sendMessage(accidentMessageDTO);
    }

    @Override
    public void updateEventFixStatus(Long id, Integer status) {
        if(Objects.isNull(id) || Objects.isNull(status)){
            return;
        }
        if (AccidentFixStatusEnum.RESTART.getCode() == status) { // 清空
            accidentInfoMapper.clearFixStatus(id);
        } else {
            BizAccidentInfo bizAccidentInfo = new BizAccidentInfo();
            bizAccidentInfo.setId(id);
            bizAccidentInfo.setFixStatus(status);
            accidentInfoMapper.update(bizAccidentInfo);
        }
    }

    /**
     * 获取车辆事故值班人员misId
     * @return
     */
    private List<String> getAccidentCallDutyPersonMisIds() {
        List<String> callDutyPersonMisIds = new ArrayList<>();
        // 获取值班人员
        List<String> oncallMisList = eveTTRgOncallAdaptor.queryRgOnCallUserList(AccidentConfig.accidentRgOnCallRgId);
        if(!CollectionUtils.isEmpty(oncallMisList)){
           callDutyPersonMisIds.addAll(oncallMisList);
        }
        // 获取备用值班人员
        log.info("getAccidentCallDutyPersonMisIds, accidentAutoCallBackupCaller = {}",
                AccidentConfig.accidentAutoCallBackupCaller);
        if(StringUtils.isNotBlank(AccidentConfig.accidentAutoCallBackupCaller)){
            callDutyPersonMisIds.addAll( Arrays.asList(AccidentConfig.accidentAutoCallBackupCaller.split(CharConstant.CHAR_DD)));
        }

        return callDutyPersonMisIds;
    }

    /**
     * 更新事故关联事件状态
     * @return
     */
    private void updateAccidentRelatedEventStatus(BizAccidentInfo bizAccidentInfo) {
        if(Objects.isNull(accidentUpdateRelatedEventStatusSwitch) || !accidentUpdateRelatedEventStatusSwitch){
            return;
        }
        log.info("updateAccidentRelatedEventStatus, bizAccidentInfo = {}", bizAccidentInfo);
        // 未关联异常事件ID的时候不需要进行状态更新
        if(StringUtils.isBlank(bizAccidentInfo.getRelatedEventId())){
            return;
        }
        EventUpdateRequest request = new EventUpdateRequest();
        request.setEventId(bizAccidentInfo.getRelatedEventId());
        request.setStatus(CommonConstant.EVENT_COMPLETED_STATUS);
        request.setOperator(bizAccidentInfo.getReporter());

        try {
            log.info("updateAccidentRelatedEventStatus, request = {}", request);
            // 异步更新
            executorService.submit(() -> cloudTriageAdaptor.updateEventStatusWithRetry(request));
        } catch (Exception e) {
            log.error("updateAccidentRelatedEventStatus error", e);
        }
    }

}
