package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> @Beijing
 * @date 2022/01/21 下午3:02
 * Description:
 * Modified by
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum S3LevelEnum {

    HOT(0, "近半年热数据"),
    COLD_ACCESSED(1, "半年以前冷数据且半年内访问过"),
    COLD_NOT_ACCESSED(2, "半年以前冷数据且半年内未访问过"),
    ;

    private Integer code;
    private String msg;

}
