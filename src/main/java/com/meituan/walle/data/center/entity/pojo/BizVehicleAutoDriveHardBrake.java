package com.meituan.walle.data.center.entity.pojo;

import lombok.Builder;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/07/29
 */
@Builder
@Data
@Table(name = "biz_vehicle_autodrive_hardbrake")
public class BizVehicleAutoDriveHardBrake {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * record_name
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 车架号
     */
    @Column(name = "vin")
    private String vin;

    /**
     * 急刹时间
     */
    @Column(name = "hardbreak_time")
    private Long hardbreakTime;

    /**
     * Utm坐标的x（来源于Localization的x）
     */
    @Column(name = "x")
    private String x;

    /**
     * Utm坐标的y（来源于Localization的y）
     */
    @Column(name = "y")
    private String y;

    /**
     * 加速度
     */
    @Column(name = "acceleration")
    private Double acceleration;

    /**
     * 急刹case_id
     */
    @Column(name = "hard_brake_id")
    private String hardBrakeId;

    /**
     * 0:未处理，1:已处理
     */
    @Column(name = "is_discern")
    private Integer isDiscern;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "sources")
    private String sources;

    @Column(name = "speed")
    private BigDecimal speed;

    @Column(name = "obstacles_info")
    private String obstaclesInfo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRecordName() {
        return recordName;
    }

    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Long getHardbreakTime() {
        return hardbreakTime;
    }

    public void setHardbreakTime(Long hardbreakTime) {
        this.hardbreakTime = hardbreakTime;
    }

    public String getX() {
        return x;
    }

    public void setX(String x) {
        this.x = x;
    }

    public String getY() {
        return y;
    }

    public void setY(String y) {
        this.y = y;
    }

    public Double getAcceleration() {
        return acceleration;
    }

    public void setAcceleration(Double acceleration) {
        this.acceleration = acceleration;
    }

    public String getHardBrakeId() {
        return hardBrakeId;
    }

    public void setHardBrakeId(String hardBrakeId) {
        this.hardBrakeId = hardBrakeId;
    }

    public Integer getIsDiscern() {
        return isDiscern;
    }

    public void setIsDiscern(Integer isDiscern) {
        this.isDiscern = isDiscern;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}

