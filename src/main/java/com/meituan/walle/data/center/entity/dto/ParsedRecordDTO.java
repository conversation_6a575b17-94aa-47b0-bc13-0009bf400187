package com.meituan.walle.data.center.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/09/06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ParsedRecordDTO {
    @JsonProperty(value = "record_name")
    private String recordName;
    @JsonProperty(value = "start_time")
    private String startTime;
    @JsonProperty(value = "end_time")
    private String endTime;
    @JsonProperty(value = "purpose")
    private String purpose;
    @JsonProperty(value = "place")
    private String place;
    @JsonProperty(value = "git_branch")
    private String gitBranch;
    @JsonProperty(value = "is_valid")
    private Boolean isValid;
    @JsonProperty(value = "invalid_desc")
    private String invalidDesc;
    @JsonProperty(value = "send_time")
    private String sendTime;
}