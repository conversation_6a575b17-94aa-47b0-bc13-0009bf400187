package com.meituan.walle.data.center.entity.excel;

import com.meituan.walle.data.center.constant.MiningTaskResultLabelStatusMappingEnum;
import com.meituan.walle.data.center.excel.annotation.ExcelField;
import com.meituan.walle.data.center.excel.entity.BasicExportModel;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/04/11
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MiningTaskResultExcel implements BasicExportModel {

    @ExcelField(fieldTitle = "index", isParent = true, display = false, index = -1)
    private String indexNo;

    @ExcelField(fieldTitle = "case_id", isParent = true, index = 1)
    private String caseId;

    @ExcelField(fieldTitle = "record_name", isParent = true, index = 2)
    private String recordName;

    @ExcelField(fieldTitle = "start_time", isParent = true, index = 3)
    private String startTime;

    @ExcelField(fieldTitle = "end_time", isParent = true, index = 4)
    private String endTime;

    @ExcelField(fieldTitle = "scenario_id", isParent = true, index = 5)
    private String scenarioId;

    @ExcelField(fieldTitle = "scenario_name", isParent = true, index = 6)
    private String scenarioName;

    @ExcelField(fieldTitle = "reserved_field1", isParent = true, index = 7)
    private String reservedField1;

    @ExcelField(fieldTitle = "reserved_field2", isParent = true, index = 8)
    private String reservedField2;

    @ExcelField(fieldTitle = "reserved_field3", isParent = true, index = 9)
    private String reservedField3;

    @ExcelField(fieldTitle = "reserved_field4", isParent = true, index = 10)
    private String reservedField4;

    @ExcelField(fieldTitle = "label_status", isParent = true, index = 11,
            mapping = MiningTaskResultLabelStatusMappingEnum.class)
    private String labelStatus;

    @ExcelField(fieldTitle = "label_result", isParent = true, index = 12)
    private String labelResult;

    @ExcelField(fieldTitle = "label_remark", isParent = true, index = 13)
    private String labelRemark;

    @ExcelField(fieldTitle = "tag", isParent = true, index = 14)
    private String tag;

    @ExcelField(fieldTitle = "task_result_id", isParent = true, index = 15)
    private String taskResultId;

    @Override
    public String getUniqueKey() {
        return taskResultId;
    }
}















