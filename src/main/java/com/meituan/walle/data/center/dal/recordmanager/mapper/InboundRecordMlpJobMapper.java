package com.meituan.walle.data.center.dal.recordmanager.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

public interface InboundRecordMlpJobMapper {
    @Select("<script>" +
            "SELECT DISTINCT record_name FROM inbound_record_mlp_job " +
            "WHERE record_name IN <foreach item='item' collection='recordNameList' open='(' separator=',' close=')'>" +
                "#{item}" +
            "</foreach>" +
            "AND job_status = 2" +
    "</script>")
    List<String> selectSuccessRecordName(@Param("recordNameList") Collection<String> recordNames);
}
