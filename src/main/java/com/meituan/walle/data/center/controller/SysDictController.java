package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.entity.dto.SysDictDTO;
import com.meituan.walle.data.center.entity.vo.SysDictVO;
import com.meituan.walle.data.center.service.SysDictService;
import com.meituan.walle.data.center.util.Trace;
import com.sankuai.walle.wcdp.core.entity.request.RespCodeEnum;
import com.sankuai.walle.wcdp.core.entity.request.RespContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2022/04/20
 */
@InterfaceDoc(
        displayName = "字典相关接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供字典管理的能力。",
        scenarios = "字典处理所有接口"
)
@RestController
@RequestMapping(value = "/dict")
@Slf4j
public class SysDictController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SysDictController.class);

    @Autowired
    private HttpServletResponse response;

    @Autowired
    private SysDictService sysDictService;

    @MethodDoc(
            displayName = "新增字典",
            description = "新增字典",
            parameters = {
                    @ParamDoc(name = "bizIssueRemarkDTO", description = "挖掘结果评论")
            },
            returnValueDescription = "新增结果"
    )
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Object add(@RequestBody SysDictVO sysDictVO) {

        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "add";

        Transaction t = Cat.newTransaction(CatConstant.API, "/dict/" + api);
        LOGGER.info("traceId: {}, received new request for {} , request param is: {}.",
                traceId, api, sysDictVO);

        try {
            response.addHeader("TraceId", traceId);

            SysDictDTO sysDictDTO = new SysDictDTO();
            BeanUtils.copyProperties(sysDictVO, sysDictDTO);
            int result = sysDictService.add(sysDictDTO);

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            if (result > 0) {
                return RespContent.<Object>success("添加成功。");
            } else {
                return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                        String.format("%s request fail, request param is: %s.", api, sysDictVO));
            }

        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param is: {}.", traceId, api, sysDictVO, e);

            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("%s request fail, error info: %s.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }


    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Object update(@RequestBody SysDictVO sysDictVO) {

        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "update";

        Transaction t = Cat.newTransaction(CatConstant.API, "/dict/" + api);
        LOGGER.info("traceId: {}, received new request for {}, request param is: {}.",
                traceId, api, sysDictVO);

        try {
            response.addHeader("TraceId", traceId);
            StringBuilder checkErrorInfo = new StringBuilder();
            if (sysDictVO.getId() == null) {
                checkErrorInfo.append("dict id is not null;");
            }

            if (StringUtils.isBlank(sysDictVO.getDictCode())) {
                checkErrorInfo.append("dict code is not null;");
            }

            if (StringUtils.isBlank(sysDictVO.getCodeIndex())) {
                checkErrorInfo.append("dict code index is not null;");
            }

            if (StringUtils.isBlank(sysDictVO.getIndexNameCn())) {
                checkErrorInfo.append("dict index name cn is not null;");
            }

            String checkErrorInfoStr = checkErrorInfo.toString();
            if (StringUtils.isNotBlank(checkErrorInfoStr)) {
                t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
                t.setSuccessStatus();
                t.complete();
                return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY,
                        String.format("%s request fail, request param is: %s.", api, sysDictVO));
            }
            SysDictDTO dto = new SysDictDTO();
            BeanUtils.copyProperties(sysDictVO, dto);
            int result = sysDictService.update(dto);

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            if (result > 0) {
                return RespContent.<Object>success("操作成功。");
            } else {
                return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                        String.format("%s request fail, request param is: %s.", api, sysDictVO));
            }

        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param is: {}.", traceId, api, sysDictVO, e);

            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("%s fail, error info: %s.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }
}