package com.meituan.walle.data.center.constant;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/18 下午2:32
 * Description:
 * Modified by
 */
public interface KmsConstant {

    String KMS_KEY_ONES_MIS_CLIENTID = "ones.clientId";
    String KMS_KEY_ONES_MIS_SECRET = "ones.secret";

    String KMS_SPIDER_VIDEO_CLIENT_ID = "spider.video.client.id";
    String KMS_SPIDER_VIDEO_SECRET = "spider.video.secret.key";

    String KMS_VRESV_CLIENT_ID = "query.vehicle.group.client.id";
    String KMS_VRESV_CLIENT_SECRET = "query.vehicle.group.secret";

    String S3_RECORD_ACCESS_KEY = "s3.record.accessKey";
    String S3_RECORD_SECRET_KEY = "s3.record.secretKey";

    String S3_HARDWARE_ACCESS_KEY = "s3.hardware.accessKey";
    String S3_HARDWARE_SECRET_KEY = "s3.hardware.secretKey";


    String S3_CALIBRATION_ACCESS_KEY = "s3.calibration.access";
    String S3_CALIBRATION_SECRET_KEY = "s3.calibration.secret";

    String ES_APP_ACCESS_KEY = "es.datacenter.accesskey";

    String CALIBRATION_FILE_CLIENT_ID = "calibration.file.service.client.id";
    String CALIBRATION_FILE_SECRET = "calibration.file.service.secret";

    String KMS_HDMAP_CLIENT_ID = "hdmap.client.id";
    String KMS_HDMAP_CLIENT_SECRET = "hdmap.client.secret";

    String KMS_S3_SERVICE_ACCESS_KEY = "s3plus_service_access_key";
    String KMS_S3_SERVICE_SECRET_KEY = "s3plus_service_secret_key";

    String KMS_CASE_WORKBENCH_SERVICE_CLIENT_ID = "case.workbench.service.client.id";

    String KMS_CASE_WORKBENCH_SERVICE_SECRET = "case.workbench.service.secret";

    String KMS_BA_AUTH_STORAGE_MANAGER_CLIENT_ID = "ba.auth.storage.manager.client.id";

    String KMS_BA_AUTH_STORAGE_MANAGER_CLIENT_SECRET = "ba.auth.storage.manager.client.secret";


}
