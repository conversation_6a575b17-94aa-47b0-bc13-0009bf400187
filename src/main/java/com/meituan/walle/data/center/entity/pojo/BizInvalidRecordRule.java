package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/4/7
 */
@Data
@Table(name = "biz_invalid_record_rule")
public class BizInvalidRecordRule {
    @Id
    @GeneratedValue(generator = "JDBC")
    /**
     * id
     */
    private Long id;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 不可用说明
     */
    private String invalidDesc;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private Boolean isDeleted;

    private Date createTime;

    private Date updateTime;

}
