package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

/**
 * <AUTHOR>
 * @date 2021/09/13
 */
public class AutoAssignPersonConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "electric.fence.assign.person")
    public static volatile String ELECTRIC_FENCE_ASSIGN_PERSON = "mayerui";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "maeb.assign.person")
    public static volatile String MAEB_ASSIGN_PERSON = "wangzhiwei11";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "kappa.assign.person")
    public static volatile String KAPPA_ASSIGN_PERSON = "caona05";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "estop.localization.timeout.assign.person")
    public static volatile String ESTOP_LOCALIZATION_TIMEOUT_ASSIGN_PERSON = "dongjunfeng";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "estop.localization.gnss.assign.person")
    public static volatile String ESTOP_LOCALIZATION_GNSS_ASSIGN_PERSON = "wuyungeng";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "estop.localization.lidar.assign.person")
    public static volatile String ESTOP_LOCALIZATION_LIDAR_ASSIGN_PERSON = "jiangyuan11";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "avoid.hard.brake.assign.person")
    public static volatile String AVOID_HARD_BRAKE_ASSIGN_PERSON = "suchengkai";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "avoid.hard.brake.send.person")
    public static volatile String AVOID_HARD_BRAKE_AREA_SEND_PERSON = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "electric.fence.type.assign.person")
    public static volatile String ELECTRIC_FENCE_TYPE_ASSIGN_PERSON = "{ \"1\": \"lixiao07\", \"2\": \"liqiucheng\", " +
            "\"3\": \"shixinchu\", \"4\": \"caona05\", \"5\": \"lichongchong02\" }";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "parking.assign.person")
    public static volatile String PARKING_ASSIGN_PERSON = "{\"3\":\"\",\"11\":\"wangyawei02\",\"7\":\"wangzhihua04\"," +
            "\"23\":\"wangzhihua04\",\"20\":\"zhaohuipeng\",\"21\":\"zhaohuipeng\",\"31\":\"yuanlingxu\"," +
            "\"32\":\"zhaohuipeng\",\"12\":\"liqiucheng\",\"29\":\"liqiucheng\",\"35\":\"renxinlei02\"," +
            "\"36\":\"wuyungeng\",\"19\":\"jiangyuan11\",\"22\":\"weitongyu\",\"24\":\"lixin125\"," +
            "\"25\":\"yuanlingxu\",\"33\":\"panzuozhou\",\"47\":\"panzuozhou\",\"26\":\"xuzhihao08\"," +
            "\"27\":\"chenhongshuai\",\"28\":\"zhangpeng136\",\"34\":\"dingwenling02\",\"48\":\"lichongchong02\"," +
            "\"46\":\"dingwenling02\"}";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "parking.send.person")
    public static volatile String PARKING_SEND_PERSON = "{\"3\":\"yangziwei03,lidongwei,mayerui\"," +
            "\"11\":\"yangziwei03,wangzhihua04\",\"7\":\"yangziwei03\",\"23\":\"yangziwei03\"," +
            "\"20\":\"yangziwei03\",\"21\":\"yangziwei03\",\"31\":\"yangziwei03\",\"32\":\"yangziwei03\"," +
            "\"12\":\"yangziwei03,dongjunfeng\",\"29\":\"yangziwei03,dongjunfeng\",\"35\":\"yangziwei03\"," +
            "\"36\":\"yangziwei03,dongjunfeng\",\"19\":\"yangziwei03\",\"22\":\"yangziwei03\",\"24\":\"yangziwei03\"," +
            "\"25\":\"yangziwei03,liubocong\",\"33\":\"yangziwei03,shixinchu\",\"47\":\"yangziwei03,shixinchu\"," +
            "\"26\":\"yangziwei03,shixinchu\",\"27\":\"yangziwei03,dingshuguang\",\"28\":\"yangziwei03\"," +
            "\"34\":\"yangziwei03\",\"48\":\"yangziwei03,shixinchu\",\"46\":\"yangziwei03\"}";

    /**
     * 自动分拣领取人配置
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "auto.pick.receive.person")
    public static volatile String AUTO_PICK_RECEIVE_PERSON = "walle";

    /**
     * 自动分拣现象配置 目前只有需要改动的8个，后续全部改为配置化的
     * key: 降级原因枚举number
     * value：现象id
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "parking.reason.appearance.map")
    public static volatile String PARKING_REASON_APPEARANCE_MAP = "{\"48\":\"6859\"," +
            "\"58\":\"280\",\"59\":\"280\",\"60\":\"215\"," +
            "\"61\":\"215\",\"62\":\"6866\",\"63\":\"225\",\"64\":\"6860\"," +
            "\"65\":\"6864\",\"66\":\"286\"}";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "new.maeb.assign.person")
    public static volatile String NEW_MAEB_ASSIGN_PERSON = "yuda.liu";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "electric.fence.auto.assign.types")
    public static volatile String ELECTRIC_FENCE_AUTO_ASSIGN_TYPES = "[]";
}

