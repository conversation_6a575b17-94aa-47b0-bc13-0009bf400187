package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "sim_ads")
public class SimAds {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    private String adsId;
    private String adsType;
    private String recordName;
    private String demandLink;
    private Long startTime;
    private Long endTime;
    private Date createTime;
    private Date updateTime;
}
