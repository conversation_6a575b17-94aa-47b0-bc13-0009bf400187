package com.meituan.walle.data.center.constant;

import com.meituan.walle.data.center.config.mcc.FastUploadConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/12/07
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum WebResponseStatusEnum {
    /**
     * 成功
     */
    SUCCESS(0, "ok"),

    /**
     * failed
     */
    FAILED(1, "failed"),
    /**
     * 系统错误
     */
    SYSTEM_ERROR(10001, "系统错误"),
    /**
     * 服务暂停
     */
    SERVICE_SUSPENSION(10002, "服务暂停"),
    /**
     * IP限制不能请求该资源
     */
    FRAGMENTED(10003, "IP限制不能请求该资源"),
    /**
     * 参数错误
     */
    PARAMETER_ERROR(10004, "参数错误"),
    /**
     * 任务过多，后端限流
     */
    SERVICE_CURRENT_LIMITING(10005, "任务过多，后端限流"),
    /**
     * 任务超时
     */
    TASK_TIMEOUT(10006, "任务超时"),
    /**
     * 非法请求
     */
    ILLEGAL_REQUEST(10007, "非法请求"),
    /**
     * 不合法的用户
     */
    INVALID_USER(10008, "不合法的用户"),
    /**
     * 没有权限
     */
    FORBIDDEN(10009, "没有权限"),
    /**
     * 请求长度超过限制
     */
    REQUEST_BODY_LENGTH_OVER_LIMIT(10010, "请求长度超过限制"),
    /**
     * 接口不存在
     */
    INTERFACE_DOES_NOT_EXIST(10011, "接口不存在"),
    /**
     * 请求的HTTP METHOD不支持，请检查是否选择了正确的POST/GET方式
     */
    HTTP_METHOD_NOT_SUPPORT(10012, "请求的HTTP METHOD不支持，请检查是否选择了正确的POST/GET方式"),
    /**
     * IP请求频次超过上限
     */
    IP_REQUEST_OUT_OF_RATE_LIMIT(10013, "IP请求频次超过上限"),
    /**
     * 用户请求频次超过上限
     */
    USER_REQUEST_OUT_OF_RATE_LIMIT(10014, "用户请求频次超过上限"),

    /**
     * 逻辑异常
     */
    LOGICAL_EXCEPTION(10015, "逻辑异常"),

    DATA_NOT_FOUND(10016, "数据不存在"),

    DATA_IS_ALREADY_IN_DATABASE(10017, "数据重复上传"),

    COLLECTION_IS_EMPTY(10018, "collection is empty"),

    COLLECTION_SIZE_LIMIT_UPPER(10019, "upper collection size limit."),

    ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL(20000, "车端删除文件请求参数异常"),

    DISK_LIFE_CYCLE_MANAGEMENT_QUERY_BUSINESS_VEHICLE_FAIL(20001, "查询运营车辆失败"),

    ONBOARD_FILE_METADATA_REPORT_REQUEST_PARAM_ILLEGAL(40000, "车端文件元数据上报请求参数异常"),

    FAST_UPLOAD_FILE_IS_NULL(50000, "快速上传文件为null"),

    FAST_UPLOAD_FILE_UPLOAD_FAIL(50001, "快速上传文件上传失败"),

    FAST_UPLOAD_CHECK_FILE_IS_UPLOADED_EXCEPTION(50002, "快速上传验证文件是否上传异常"),

    FAST_UPLOAD_CHECK_FILE_IS_UPLOADED_PARAM_ILLEGAL(50003, "快速上传验证文件是否上传的参数无效"),

    FAST_UPLOAD_CHECK_FILE_IS_UPLOADED_VIN_IS_EMPTY(50004, "快速上传验证文件是否上传的车架号为空"),

    FAST_UPLOAD_CHECK_FILE_IS_UPLOADED_UPLOAD_TASK_ID_IS_EMPTY(50005, "快速上传验证文件是否上传的上传任务ID为空"),

    FAST_UPLOAD_CHECK_FILE_IS_UPLOADED_FILE_ABS_PATH_IS_EMPTY(50006, "快速上传验证文件是否上传的文件路径为空"),

    FAST_UPLOAD_CREATE_FAILED(50007, "事故创建失败，请检查参数是否有问题"),

    FAST_UPLOAD_ADD_OSVIZ_TASK_FAILED(50008, String.format("添加任务失败，该任务缺失基础模块[%s]，无法录屏",
            FastUploadConfig.VEHICLE_UPLOAD_REQUEST_TASK_ACCIDENT_REQUIRED_MODULES)),

    FAST_UPLOAD_SPEED_UPLOAD_FAILED_FOR_5G(51000, "车端未开启5G，开启加速上传接口失败"),

    FAST_UPLOAD_SPEED_LIMIT_RELEASE_FAILED(51001, "解除限速失败，加速回收开启失败，可创建RE工单"),

    FAST_UPLOAD_SPEED_UPLOAD_FAILED_FOR_ERROR(51002, "解除限速失败，多线程回收开启失败"),

    FAST_UPLOAD_SPEED_LIMIT_RESTART_FAILED(51003, "恢复限速失败"),

    ACCIDENT_HAS_LATEST_ONE(60001, "该车最近创建过事故了"),
    PREVENT_DUPLICATION_REPORT_ACCIDENT(60002, "短时间内不允许重复上报事故"),

    MVIZ_SUCCESS(10000, "ok"),

    MVIZ_FAILED(20000, "Not found any data"),

    MVIZ_RECORD_INVALID(20001,
            "This record [%s] is invalid, contact DATA-GROUP or ask question in oncall if you need."),

    MVIZ_RECORD_UPLOADING(20002, "This record [%s] is uploading now, please try it again later."),

    MVIZ_RECORD_FILE_S3_IS_DELETED(20003,
            "This record [%s] slice is contain deleted file, " +
                    "contact DATA-GROUP or ask question in oncall if you need."),

    MVIZ_RECORD_FILE_S3_IS_MARK_DELETED(20004,
            "This record [%s] slice is contain file for deletion, " +
                    "contact DATA-GROUP or ask question in oncall if you need."),
    MVIZ_RECORD_PARSING(20005, "This record [%s] is parsing now, please try it again later."),
    MVIZ_S3_DSN_URL_COULD_NOT_GET_CONNECTION_INFO(20006,
            "DSN URL:[%s], could not get connection info, contact DATA-GROUP or ask question in oncall."),
    ;

    private Integer code;
    private String msg;
}