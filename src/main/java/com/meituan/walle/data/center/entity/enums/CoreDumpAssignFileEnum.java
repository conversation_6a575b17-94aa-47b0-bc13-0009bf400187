package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
/**
 * <AUTHOR>
 * @date 2023/2/8 17:26
 * @description
 */
@AllArgsConstructor
@Getter
public enum CoreDumpAssignFileEnum {
    COREDUMP_INFO("coredump_info", "coredump_info\\.txt", 3, 1),
    ONBOARD_MAIN("onboard_main", "onboard_main\\.out", 2, 2),
    DMESG("dmesg", "dmesg\\.log", 7, 1),
    CORE_TIME("core_time","core_time", 3, 1),
    ;
    private String name;
    private String expression;
    // 文件类型 coredump、sys等 OnBoardTypeEnum
    private Integer type;
    // 文件的优先级
    private Integer priority;

    public static CoreDumpAssignFileEnum lookup(String name) {
        return Arrays.stream(CoreDumpAssignFileEnum.values()).filter(e -> e.name.equals(name))
                .findFirst().orElse(null);
    }

    public static CoreDumpAssignFileEnum lookup(String fileName, Integer type) {
        return Arrays.stream(CoreDumpAssignFileEnum.values())
                .filter(e -> fileName.contains(e.name) && e.type.equals(type)
        ).findFirst().orElse(null);
    }
}
