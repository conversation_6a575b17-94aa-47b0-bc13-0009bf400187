package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "group_members")
public class GroupMembers  {


    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * mis号
     */
    private String misId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 所属团队id
     */
    private Integer groupId;

    /**
     * 模块id
     */
    private Integer moduleId;

}