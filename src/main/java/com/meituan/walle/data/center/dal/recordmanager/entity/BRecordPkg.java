package com.meituan.walle.data.center.dal.recordmanager.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2023/05/30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "record_pkg")
public class BRecordPkg {
    private Long id;
    private String recordName;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 文件内容md5
     */
    private String md5;
    /**
     * s3Url
     */
    private String s3Url;
    /**
     * 模块名
     */
    private String module;
    /**
     * 开始录制时间
     */
    private Timestamp startTime;
    /**
     * 结束录制时间
     */
    private Timestamp endTime;
    /**
     * 首条消息时间
     */
    private Timestamp firstMsgTime;
    /**
     * 消息条数
     */
    private Integer msgCount;
    /**
     * Topic信息
     */
    private String topics;
    /**
     * 文件大小
     */
    private Long fileSize;
    /**
     * 录制时长
     */
    private Double duration;
    /**
     * S3中是否物理删除
     */
    private Integer s3IsDeleted;
    /**
     * S3中删除时间
     */
    private Timestamp s3DeleteTime;
    /**
     * S3中是否标记删除，0表示否，1表示是
     */
    private Integer s3IsMarkedForDeletion;
    /**
     * S3中删除时间
     */
    private Timestamp s3IsMarkedForDeletionTime;
    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private Integer isDeleted;
    /**
     * 创建时间
     */
    private Timestamp createTime;
    /**
     * 更新时间
     */
    private Timestamp updateTime;
    /**
     * 所属集群[0:北京备份|1:中卫自动车]
     */
    private Integer cluster;
    /**
     * 压缩格式
     */
    private String compressionFormat;
}