package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/12/25
 */
@Getter
@AllArgsConstructor
public enum TaskErrorCodeEnum implements BaseErrorCodeEnum{

    RECORD_IS_UPLOADING(1001, "record正在上传中"),
    TASK_NON_EXECUTE(1002, "任务未执行"),
    TASK_EXECUTING(1003, "任务执行中"),
    TASK_EXECUTE_SUCCEED(1004, "任务执行成功"),
    TASK_EXECUTE_FAILED(1005, "任务执行失败"),
    TASK_EXECUTE_EXCEPTION(1006, "任务执行异常"),

    TASK_WAIT_CREATE(1007, "任务待创建"),

    ;

    private Integer errorCode;
    private String errorMsg;

    @Override
    public Integer getErrorCode() {
        return errorCode;
    }

    @Override
    public String getErrorMsg() {
        return errorMsg;
    }
}