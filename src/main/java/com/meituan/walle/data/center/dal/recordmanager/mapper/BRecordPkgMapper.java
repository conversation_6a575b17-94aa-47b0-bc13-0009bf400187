package com.meituan.walle.data.center.dal.recordmanager.mapper;

import com.dianping.cat.util.Pair;
import com.meituan.walle.data.center.dal.recordmanager.entity.BRecordPkg;
import com.meituan.walle.data.center.entity.po.RecordPkgPO;
import com.meituan.walle.data.center.entity.pojo.RecordV2;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/05/30
 */
public interface BRecordPkgMapper {
    @Select({
            "<script>" +
                    "    SELECT " +
                    "        t.`module`, " +
                    "        t.`start_time`, " +
                    "        t.`end_time`, " +
                    "        t.`first_msg_time`, " +
                    "        t.`file_name`, " +
                    "        t.`s3_url`, " +
                    "        t.`file_size`, " +
                    "        t.`msg_count`, " +
                    "        t.`duration`," +
                    "        t.`s3_is_marked_for_deletion`, " +
                    "        t.`s3_is_deleted`, " +
                    "        t.`cluster`, " +
                    "        t.`compression_format` " +
                    "     FROM `record_pkg` AS t " +
                    "     WHERE t.`file_size` > 0" +
                    "     <if test='recordName != null and &apos;&apos; != recordName'>" +
                    "          AND t.`record_name` = #{recordName} " +
                    "     </if>" +
                    "     <if test='moduleList != null and moduleList.size() > 0'>" +
                    "          AND t.`module` IN " +
                    "               <foreach item='item' index='index' " +
                    "                                       collection='moduleList' open='(' separator=',' close=')'>" +
                    "                    #{item}" +
                    "               </foreach>" +
                    "     </if>" +
                    "     <if test='startTime != null'>" +
                    "          AND t.`end_time` &gt;= #{startTime}" +
                    "     </if>" +
                    "     <if test='endTime != null'>" +
                    "          AND t.`start_time` &lt;= #{endTime}" +
                    "     </if>" +
                    "</script>"
    })
    @Results(value = {
            @Result(property = "module", column = "module"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "firstMsgTime", column = "first_msg_time"),
            @Result(property = "fileName", column = "file_name"),
            @Result(property = "s3Url", column = "s3_url"),
            @Result(property = "fileSize", column = "file_size"),
            @Result(property = "msgCount", column = "msg_count"),
            @Result(property = "duration", column = "duration"),
            @Result(property = "s3IsMarkedForDeletion", column = "s3_is_marked_for_deletion"),
            @Result(property = "s3IsDeleted", column = "s3_is_deleted"),
            @Result(property = "cluster", column = "cluster"),
            @Result(property = "compressionFormat", column = "compression_format")
    })
    List<BRecordPkg> selectRecordPkg(@Param("recordName") String recordName,
                                     @Param("startTime") String startTime,
                                     @Param("endTime") String endTime,
                                     @Param("moduleList") List<String> moduleList);

    @Select({
        "<script>" +
            "    SELECT " +
            "        t.`module`, " +
            "        t.`record_name`, " +
            "        t.`start_time`, " +
            "        t.`end_time`, " +
            "        t.`first_msg_time`, " +
            "        t.`file_name`, " +
            "        t.`s3_url`, " +
            "        t.`file_size`, " +
            "        t.`msg_count`, " +
            "        t.`duration`," +
            "        t.`s3_is_marked_for_deletion`, " +
            "        t.`s3_is_deleted`, " +
            "        t.`cluster`, " +
            "        t.`compression_format` " +
            "     FROM `record_pkg` AS t " +
            "     WHERE t.`file_size` > 0 AND t.`s3_is_marked_for_deletion` = 0 AND t.`s3_is_deleted` = 0" +
            "     <if test='recordNameList != null and recordNameList.size() > 0'>" +
            "          AND t.`record_name` IN " +
            "               <foreach item='item' index='index' " +
            "                       collection='recordNameList' open='(' separator=',' close=')'>" +
            "                    #{item}" +
            "               </foreach>" +
            "     </if>" +
            "     <if test='moduleList != null and moduleList.size() > 0'>" +
            "          AND t.`module` IN " +
            "               <foreach item='item' index='index' " +
            "                                       collection='moduleList' open='(' separator=',' close=')'>" +
            "                    #{item}" +
            "               </foreach>" +
            "     </if>" +
            "     <if test='startTime != null'>" +
            "          AND t.`end_time` &gt;= #{startTime}" +
            "     </if>" +
            "     <if test='endTime != null'>" +
            "          AND t.`start_time` &lt;= #{endTime}" +
            "     </if>" +
            "</script>"
    })
    @Results(value = {
        @Result(property = "module", column = "module"),
        @Result(property = "recordName", column = "record_name"),
        @Result(property = "startTime", column = "start_time"),
        @Result(property = "endTime", column = "end_time"),
        @Result(property = "firstMsgTime", column = "first_msg_time"),
        @Result(property = "fileName", column = "file_name"),
        @Result(property = "s3Url", column = "s3_url"),
        @Result(property = "fileSize", column = "file_size"),
        @Result(property = "msgCount", column = "msg_count"),
        @Result(property = "duration", column = "duration"),
        @Result(property = "s3IsMarkedForDeletion", column = "s3_is_marked_for_deletion"),
        @Result(property = "s3IsDeleted", column = "s3_is_deleted"),
        @Result(property = "cluster", column = "cluster"),
        @Result(property = "compressionFormat", column = "compression_format")
    })
    List<BRecordPkg> selectRecordsPkg(@Param("recordNameList") List<String> recordNameList,
                                      @Param("startTime") String startTime,
                                      @Param("endTime") String endTime,
                                      @Param("moduleList") List<String> moduleList);

    @Select({
            "<script>" +
                    "    SELECT " +
                    "        t.`module`, " +
                    "        t.`start_time`, " +
                    "        t.`end_time`, " +
                    "        t.`first_msg_time`, " +
                    "        t.`file_name`, " +
                    "        t.`s3_url`, " +
                    "        t.`file_size`, " +
                    "        t.`msg_count`, " +
                    "        t.`duration`, " +
                    "        t.`s3_is_marked_for_deletion`, " +
                    "        t.`s3_is_deleted`, " +
                    "        t.`cluster`, " +
                    "        t.`compression_format` " +
                    "    FROM `record_pkg` AS t " +
                    "    WHERE t.`record_name` = #{recordName} " +
                    "       AND t.`module` = 'Routing'" +
                    "</script>"
    })
    @Results(value = {
            @Result(property = "module", column = "module"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "firstMsgTime", column = "first_msg_time"),
            @Result(property = "fileName", column = "file_name"),
            @Result(property = "s3Url", column = "s3_url"),
            @Result(property = "fileSize", column = "file_size"),
            @Result(property = "msgCount", column = "msg_count"),
            @Result(property = "duration", column = "duration"),
            @Result(property = "s3IsMarkedForDeletion", column = "s3_is_marked_for_deletion"),
            @Result(property = "s3IsDeleted", column = "s3_is_deleted"),
            @Result(property = "cluster", column = "cluster"),
            @Result(property = "compressionFormat", column = "compression_format")
    })
    List<BRecordPkg> selectRoutingRecordPkg(@Param("recordName") String recordName);

    @Update({
            "<script>" +
                    "    UPDATE `record_pkg` SET " +
                    "        <if test='null != s3IsDeleted'>" +
                    "            `s3_is_deleted`=#{s3IsDeleted}," +
                    "        </if> " +
                    "        <if test='null != s3DeleteTime'>" +
                    "            `s3_delete_time`=#{s3DeleteTime}," +
                    "        </if> " +
                    "        <if test='null != s3IsMarkedForDeletion'>" +
                    "            `s3_is_marked_for_deletion`=#{s3IsMarkedForDeletion}," +
                    "        </if> " +
                    "        <if test='null != s3IsMarkedForDeletionTime'>" +
                    "            `s3_is_marked_for_deletion_time`=#{s3IsMarkedForDeletionTime}," +
                    "        </if> " +
                    "        `update_time`=CURRENT_TIMESTAMP " +
                    "    WHERE `s3_url`=#{s3Url} " +
                    "</script>"})
    int update(BRecordPkg recordPkg);

    @Select({
            "select * from `record_pkg` where `s3_url`= #{s3Url}"
    })
    BRecordPkg getRecordPkg(@Param("s3Url") String s3Url);

    // RecordPkgMapper.java 迁移 begin

    List<BRecordPkg> selectRecordPkgListByCondition(@Param("recordName") String recordName,
                                                    @Param("module") String module,
                                                    @Param("startTime") long startTime,
                                                    @Param("endTime") long endTime);

    List<Map<String, Object>> selectRecord2Count(@Param("recordNames") List<String> recordNames);

    @Update({
            "<script>",
            "<foreach item='item' collection='list' separator=';'>",
            "update record_pkg set s3_is_deleted = #{item.s3IsDeleted}, s3_delete_time = #{item.s3DeleteTime} ",
            "where record_name = #{item.recordName}",
            "</foreach>",
            "</script>"
    })
    int updateS3LevelByRecordNameBatch(List<RecordV2> coldRecordLevelPOList);

    @Select({
            "<script>",
            "select record_name, max(end_time) as end_time from record_pkg where record_name in",
            "<foreach item='item' collection='recordNames' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "group by record_name",
            "</script>"
    })
    List<RecordPkgPO> selectMaxEndTime(@Param("recordNames") List<String> recordNames);

    @Select("select record_name,file_name\n" +
            "      from record_pkg \n" +
            "      where create_time>= concat(date_add(curdate(), interval -1 day),' ','00:00')")
    List<String> getRecFileRecord();

    @Select({"<script>",
            "select s3_url from record_pkg where 1=1",
            "<trim>",
            "<if test='recordName != null and &apos;&apos; != recordName'>",
            "and record_name = #{recordName} ",
            "</if>",
            "<if test='moduleList != null and moduleList.size() > 0'>",
            "and module in ",
            "<foreach item='item' collection='moduleList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='timeList != null'>",
            "and",
            "<foreach item='item' collection='timeList' open='(' separator='or' close=')'>",
            "end_time &gt; #{item.key} and start_time &lt; #{item.value}",
            "</foreach>",
            "</if>",
            "</trim>",
            " limit 10000",
            "</script>"
    })
    List<String> queryS3UrlByRecordNameAndModule(@Param("recordName") String recordName,
                                                 @Param("moduleList") List<String> moduleList,
                                                 @Param("timeList") List<Pair<String, String>> timePairList);

    @Select({
            "<script>",
            "select start_time, end_time, duration, s3_url, file_size from record_pkg where 1 = 1",
            "<trim>",
            "<if test='recordNameList != null and recordNameList.size() > 0'>",
            "and record_name in ",
            "<foreach item='item' collection='recordNameList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='moduleList != null and moduleList.size() > 0'>",
            "and module in ",
            "<foreach item='item' collection='moduleList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "<if test='startTime != null'>",
            "and end_time &gt;= #{startTime}",
            "</if>",
            "<if test='endTime != null'>",
            "and start_time &lt;= #{endTime}",
            "</if>",
            "</trim>",
            " order by start_time limit 1000",
            "</script>"
    })
    List<RecordPkgPO> queryRecordPkgFile(@Param("recordNameList") List<String> recordNameList,
                                         @Param("moduleList") List<String> moduleList,
                                         String startTime, String endTime);

    @Select({
            "select * from record_pkg",
            "where create_time between date_sub(#{beforeTime}, interval 24 hour) and #{beforeTime}",
            "and file_name like 'OnboardNoticePublisher%' and record_name > #{eventCompressModuleStartParseDate}"
    })
    List<RecordPkgPO> queryByBeforeTime(@Param("beforeTime") Date beforeTime,
                                        @Param("eventCompressModuleStartParseDate") String eventCompressModuleStartParseDate);

    // RecordPkgMapper.java 迁移 end

    // stream.server迁移 begin

    @Select({
            "<script>",
            "select module, s3_url, start_time, duration, end_time, file_name, file_size from record_pkg",
            "where record_name = #{recordName}",
            "and unix_timestamp(end_time) * 1000 &gt; #{startMillis}",
            "and module = #{module}",
            "order by start_time limit 10",
            "</script>"
    })
    List<BRecordPkg> selectRecordPkgForStreamServer(String recordName, String module, long startMillis);

    @Select({
            "<script>",
            "select module, s3_url, start_time, duration, end_time, file_name, file_size from record_pkg",
            "where record_name = #{recordName}",
            "and module = #{module}",
            "and file_name &gt; #{fileName}",
            "order by start_time limit 10",
            "</script>"
    })
    List<BRecordPkg> selectRecordPkgMoreForStreamServer(String recordName, String module, String fileName);

    // stream.server迁移 end

    // 准实时事件解析flink任务迁移 begin

    @Select({
            "select record_name, file_name, s3_url from record_pkg",
            "where record_name = #{recordName} and file_name like 'OnboardNoticePublisher%rec'"
    })
    List<BRecordPkg> selectOnboardNoticePublisherPkgInfo(String recordName);

    // 准实时事件解析flink任务迁移 end

    @Update({
            "update record_pkg set cluster = 0 where record_name = #{recordName} and cluster = 1"
    })
    Integer updateRecordCluster(@Param("recordName") String recordName);

    @Select({
            "select module from record_pkg",
            "where record_name = #{recordName}",
            "group by module"
    })
    List<String> selectGroupByModule(String recordName);

    @Insert({
            "<script>",
            "insert ignore into record_pkg",
            "<trim prefix='(' suffix=')' suffixOverrides=','>",
            "<if test='list[0].id != null'>",
            "id,",
            "</if>",
            "<if test='list[0].recordName != null'>",
            "record_name,",
            "</if>",
            "<if test='list[0].fileName != null'>",
            "file_name,",
            "</if>",
            "<if test='list[0].md5 != null'>",
            "md5,",
            "</if>",
            "<if test='list[0].s3Url != null'>",
            "s3_url,",
            "</if>",
            "<if test='list[0].module != null'>",
            "module,",
            "</if>",
            "<if test='list[0].startTime != null'>",
            "start_time,",
            "</if>",
            "<if test='list[0].endTime != null'>",
            "end_time,",
            "</if>",
            "<if test='list[0].firstMsgTime != null'>",
            "first_msg_time,",
            "</if>",
            "<if test='list[0].msgCount != null'>",
            "msg_count,",
            "</if>",
            "<if test='list[0].topics != null'>",
            "topics,",
            "</if>",
            "<if test='list[0].fileSize != null'>",
            "file_size,",
            "</if>",
            "<if test='list[0].duration != null'>",
            "duration,",
            "</if>",
            "<if test='list[0].s3IsDeleted != null'>",
            "s3_is_deleted,",
            "</if>",
            "<if test='list[0].s3DeleteTime != null'>",
            "s3_delete_time,",
            "</if>",
            "<if test='list[0].s3IsMarkedForDeletion != null'>",
            "s3_is_marked_for_deletion,",
            "</if>",
            "<if test='list[0].s3IsMarkedForDeletionTime != null'>",
            "s3_is_marked_for_deletion_time,",
            "</if>",
            "<if test='list[0].cluster != null'>",
            "cluster,",
            "</if>",
            "<if test='list[0].compressionFormat != null'>",
            "compression_format,",
            "</if>",
            "</trim>",
            "values",
            "<foreach item='item' collection='list' separator=',' >",
            "<trim prefix='(' suffix=')' suffixOverrides=','>",
            "<if test='item.id != null'>",
            "#{item.id},",
            "</if>",
            "<if test='item.recordName != null'>",
            "#{item.recordName},",
            "</if>",
            "<if test='item.fileName != null'>",
            "#{item.fileName},",
            "</if>",
            "<if test='item.md5 != null'>",
            "#{item.md5},",
            "</if>",
            "<if test='item.s3Url != null'>",
            "#{item.s3Url},",
            "</if>",
            "<if test='item.module != null'>",
            "#{item.module},",
            "</if>",
            "<if test='item.startTime != null'>",
            "#{item.startTime},",
            "</if>",
            "<if test='item.endTime != null'>",
            "#{item.endTime},",
            "</if>",
            "<if test='item.firstMsgTime != null'>",
            "#{item.firstMsgTime},",
            "</if>",
            "<if test='item.msgCount != null'>",
            "#{item.msgCount},",
            "</if>",
            "<if test='item.topics != null'>",
            "#{item.topics},",
            "</if>",
            "<if test='item.fileSize != null'>",
            "#{item.fileSize},",
            "</if>",
            "<if test='item.duration != null'>",
            "#{item.duration},",
            "</if>",
            "<if test='item.s3IsDeleted != null'>",
            "#{item.s3IsDeleted},",
            "</if>",
            "<if test='item.s3DeleteTime != null'>",
            "#{item.s3DeleteTime},",
            "</if>",
            "<if test='item.s3IsMarkedForDeletion != null'>",
            "#{item.s3IsMarkedForDeletion},",
            "</if>",
            "<if test='item.s3IsMarkedForDeletionTime != null'>",
            "#{item.s3IsMarkedForDeletionTime},",
            "</if>",
            "<if test='item.cluster != null'>",
            "#{item.cluster},",
            "</if>",
            "<if test='item.compressionFormat != null'>",
            "#{item.compressionFormat},",
            "</if>",
            "</trim>",
            "</foreach>",
            "</script>"
    })
    int insertBatch(List<BRecordPkg> recordPkgList);

    @Update({
            "update record_pkg set cluster = 1 where s3_url = #{s3Url}"
    })
    Integer updateRecordPkgCluster(@Param("s3Url") String s3Url);

    @Select({
            "select count(*) from record_pkg where record_name = #{recordName} and (s3_is_deleted = 0 or s3_is_marked_for_deletion = 0)"
    })
    Integer selectCountDeletedMark(@Param("recordName") String recordName);

    @Update({
            "update record_pkg set s3_is_deleted = 1, s3_delete_time = #{s3DeleteTime}, ",
            "s3_is_marked_for_deletion = 1, s3_is_marked_for_deletion_time = #{s3DeleteTime}",
            "where record_name = #{recordName}"
    })
    Integer updateRecordPkgDeletedMark(@Param("recordName") String recordName, @Param("s3DeleteTime") Date s3DeleteTime);

    @Update({
            "update record_pkg set s3_is_deleted = 1, s3_delete_time = #{s3DeleteTime}, ",
            "s3_is_marked_for_deletion = 1, s3_is_marked_for_deletion_time = #{s3DeleteTime}",
            "where s3_url = #{s3Url}"
    })
    Integer updateRecordPkgDeletedMarkByS3Url(@Param("s3Url") String s3Url, @Param("s3DeleteTime") Date s3DeleteTime);

    @Select({
            "select * from record_pkg",
            "where record_name >= #{startDate} and record_name < #{endDate}",
            "and s3_is_deleted = 0 and s3_is_marked_for_deletion = 0 and is_deleted = 0",
            "and s3_url is not null and s3_url != '' and file_size > 0 and cluster = 0"
    })
    List<BRecordPkg> selectRecordPkgByRecordDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

}