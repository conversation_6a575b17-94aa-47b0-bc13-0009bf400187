package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.dto.SparkJobInstanceDTO;
import com.meituan.walle.data.center.entity.pojo.SparkJobInstance;
import com.meituan.walle.data.center.entity.pojo.SparkJobPlatform;
import com.meituan.walle.data.center.entity.vo.SparkJobParamVO;
import com.meituan.walle.data.center.mapper.SparkJobInstanceMapper;
import com.meituan.walle.data.center.mapper.SparkJobPlatformMapper;
import com.meituan.walle.data.center.service.SparkJobService;
import com.meituan.walle.data.center.util.SparkUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/spark_job")
@Log4j2
public class SparkJobController {
    @Resource
    private SparkJobInstanceMapper sparkJobInstanceMapper;

    @Resource
    private SparkJobPlatformMapper sparkJobPlatformMapper;

    @Resource
    private SparkJobService sparkJobService;

    @RequestMapping(value = "/restart/spark_job")
    public Map<String, Object> restartFailedJob(String instanceUuid, Boolean rightNow) {
        Map<String, Object> result = new HashMap<>();

        SparkJobInstance sparkJobInstance = sparkJobInstanceMapper.findSparkJobInstanceByUuid(instanceUuid);
        if (sparkJobInstance == null) {
            result.put("code", "404");
            result.put("msg", "任务不存在");
            return result;
        }
        result.put("code", 200);
        result.put("msg", "ok");

        int status = sparkJobInstance.getStatus();
        //立刻执行
        if (rightNow != null && rightNow) {
            //非运行状态，且任务次数大于等于最大执行次数，才能立刻执行，否则还在执行中
            if (status != SparkUtil.STATUS_RUNNING && sparkJobInstance.getRunTimes() >= SparkUtil.MAX_RUN_TIMES) {
                SparkJobPlatform sparkJobPlatform =
                        sparkJobPlatformMapper.findSparkJobByUuid(sparkJobInstance.getJobUuid());

                sparkJobInstance.setRunTimes(sparkJobInstance.getRunTimes() + 1);
                status = SparkUtil.STATUS_RUNNING; //执行中
                boolean submitSuccess = sparkJobService.executeSparkJob(sparkJobInstance, sparkJobPlatform);

                if (!submitSuccess) {
                    //如果没有提交成功，则认为是失败的
                    status = SparkUtil.STATUS_FAILED;
                    result.put("code", 500);
                    result.put("msg", "任务提交到spark平台失败");
                }
                sparkJobInstance.setStatus(status);
                sparkJobInstanceMapper.updateSparkJobInstance(sparkJobInstance);
            } else {
                result.put("code", 400);
                result.put("msg", "任务正在执行，不可提交");
            }
        } else {
            //等待调度执行
            //如果状态是失败或者未知，且已经执行过3次了，亦或者该任务已经成功，则允许重启
            if (((status == SparkUtil.STATUS_FAILED || status == SparkUtil.STATUS_UNKNOWN)
                    && sparkJobInstance.getRunTimes() >= SparkUtil.MAX_RUN_TIMES)
                    || status == SparkUtil.STATUS_SUCCESS) {
                sparkJobInstance.setStatus(SparkUtil.STATUS_WAIT);
                sparkJobInstance.setRunTimes(0);
                sparkJobInstanceMapper.updateSparkJobInstance(sparkJobInstance);
            } else {
                result.put("code", 400);
                result.put("msg", "任务在等待执行或正在执行，不可提交");
            }
        }

        return result;
    }

    @GetMapping("/update_compile_id")
    public Object updateCompileId(Long id, String compileId) {
        String success;
        try {
            if (Objects.isNull(id) || Objects.isNull(compileId)) {
                success = "input param is null.";
            } else {
                sparkJobPlatformMapper.update(id, compileId);
                success = "success";
            }
        } catch (Exception ex) {
            success = ex.getMessage();
            log.error("updateCompileId error: {}, {}", id, compileId, ex);
        }
        return Response.succ(success);
    }

    @RequestMapping(value = "/add")
    public Map<String, Object> startSparkJob(String sparkJobName, String param) {
        SparkJobParamVO sparkJobParamVO = new SparkJobParamVO();
        sparkJobParamVO.setSparkJobName(sparkJobName);
        if (StringUtils.isNotBlank(param)) {
            Map<String, Object> paramMap = new HashMap<>();
            String[] params = param.split(";");
            for (String item : params) {
                String[] arr = item.split("#");
                paramMap.put(arr[0], arr[1]);
            }
            sparkJobParamVO.setOtherParam(paramMap);
        }
        int priority = 150;
        sparkJobParamVO.setPriority(priority);
        SparkJobInstanceDTO instanceDTO = sparkJobService.startSparkJobInstance(sparkJobParamVO);
        Map<String, Object> result = new LinkedHashMap<>();
        if (instanceDTO != null) {
            result.put("code", 200);
            result.put("msg", "ok");
        } else {
            result.put("code", 404);
            result.put("msg", "not found");
        }
        return result;
    }
}
