package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * Created by zhangli110 on 2023/1/31.
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Table(name = "tag_defect")
public class TagDefectPO {

    /**
     * 主键id
     */
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "desc")
    private String desc;

    @Column(name = "module_id")
    private Long moduleId;

    @Column(name = "phenomenon_id")
    private Long phenomenonId;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "group_id")
    private Long groupId;

    @Column(name = "mis_id")
    private String misId;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;


}
