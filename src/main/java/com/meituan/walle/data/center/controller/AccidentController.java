package com.meituan.walle.data.center.controller;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.walle.data.center.component.DxGroupHandler;
import com.meituan.walle.data.center.config.UacFilterConfiguration;
import com.meituan.walle.data.center.config.mcc.AccidentConfig;
import com.meituan.walle.data.center.constant.*;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.enums.*;
import com.meituan.walle.data.center.entity.dto.AccidentInfoDTO;
import com.meituan.walle.data.center.entity.pojo.*;
import com.meituan.walle.data.center.entity.request.AutoCallRequest;
import com.meituan.walle.data.center.entity.request.BizAccidentInfoRequest;
import com.meituan.walle.data.center.entity.request.LatestAccidentRequest;
import com.meituan.walle.data.center.entity.response.PageResponse;
import com.meituan.walle.data.center.entity.vo.BizAccidentInfoVO;
import com.meituan.walle.data.center.entity.request.RemoteVideoRequest;
import com.meituan.walle.data.center.service.*;
import com.meituan.walle.data.center.util.CallRejectConstant;
import com.meituan.walle.data.center.util.CommonUtil;
import com.meituan.walle.data.center.util.DatetimeUtil;
import com.meituan.walle.data.center.util.Trace;
import com.meituan.walle.mad.logger.client.annotation.MadLog;
import com.sankuai.carosscan.eve_common_output.sdk.service.RedisGLLock;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.service.http.conf.SimpleConfigSource;
import com.sankuai.meituan.uac.sdk.entity.menu.UserMenu;
import com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/05
 */
@InterfaceDoc(
        displayName = "事故相关接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供事故管理的能力。",
        scenarios = "事故处理所有接口"
)
@RestController
@RequestMapping(value = "/accident")
@Slf4j
public class AccidentController {

    @Resource
    private BizAccidentInfoService accidentInfoService;

    @Resource
    private BizFileInfoService fileInfoService;

    @Resource
    private VresvRecordHistoryService vresvRecordHistoryService;

    @Resource
    private VehicleInfoService vehicleInfoService;

    @Resource
    private UacAuthRemoteService uacAuthRemoteService;

    @Resource
    private DxGroupHandler dxGroupHandler;

    @Resource
    private DataCenterAutoCallService dataCenterAutoCallService;

    @Value("${uac.host}")
    private String uacHost;

    private static final String API_PREFIX = "/accident";

    /**
     * 数据库Date类型字段默认时间
     */
    private static final String DB_DEFAULT_TIME  = "1970-01-01 08:00:01";

    @MethodDoc(
            displayName = "获取事故列表",
            description = "根据条件获取事故列表",
            parameters = {
                    @ParamDoc(name = "BizAccidentInfoRequest", description = "事故请求参数")
            },
            returnValueDescription = "事故列表",
            restExampleResponseData = "{\n" +
                    "\t\"ret\": \"0\",\n" +
                    "\t\"msg\": \"ok\",\n" +
                    "\t\"data\": {\n" +
                    "\t\t\"page\": 1,\n" +
                    "\t\t\"size\": 10,\n" +
                    "\t\t\"total\": 1,\n" +
                    "\t\t\"result\": [{\n" +
                    "\t\t\t\"id\": 1,\n" +
                    "\t\t\t\"vin\": \"LTESTLVIN123\",\n" +
                    "\t\t\t\"vehicleName\": null,\n" +
                    "\t\t\t\"accidentTime\": 1636442137978,\n" +
                    "\t\t\t\"locationName\": \"King & Queen Garden\",\n" +
                    "\t\t\t\"locationGps\": \"(116.33,40.02)\",\n" +
                    "\t\t\t\"reporter\": \"wu.wang\",\n" +
                    "\t\t\t\"siteMisId\": \"si.lili\",\n" +
                    "\t\t\t\"remoteMisId\": \"san.zhangsan\",\n" +
                    "\t\t\t\"uploadTasks\": [{\n" +
                    "\t\t\t\t\"id\": 789,\n" +
                    "\t\t\t\t\"vin\": \"3LN6L5SU9KR631259\",\n" +
                    "\t\t\t\t\"start\": *************,\n" +
                    "\t\t\t\t\"end\": *************,\n" +
                    "\t\t\t\t\"measurementTimestamp\": 1620971761559782189,\n" +
                    "\t\t\t\t\"module\": \"Prediction,Canbus,Planning,Localization,Routing,TrafficLight,Control\",\n" +
                    "\t\t\t\t\"status\": 100,\n" +
                    "\t\t\t\t\"priority\": 150,\n" +
                    "\t\t\t\t\"oneCase\": null,\n" +
                    "\t\t\t\t\"isDiscern\": 1,\n" +
                    "\t\t\t\t\"uuid\": \"66764565D2DA5647C7F3090AE95AB10A\",\n" +
                    "\t\t\t\t\"createTime\": *************,\n" +
                    "\t\t\t\t\"updateTime\": *************,\n" +
                    "\t\t\t\t\"recordName\": \"20210514_112841_mkz-06\",\n" +
                    "\t\t\t\t\"interventionId\": \"\",\n" +
                    "\t\t\t\t\"eventId\": \"\",\n" +
                    "\t\t\t\t\"taskType\": 1,\n" +
                    "\t\t\t\t\"isDeleted\": \"0\"\n" +
                    "\t\t\t}],\n" +
                    "\t\t\t\"pictures\": [{\n" +
                    "\t\t\t\t\"id\": 2,\n" +
                    "\t\t\t\t\"fileName\": \"test.jpg\",\n" +
                    "\t\t\t\t\"fileType\": 2,\n" +
                    "\t\t\t\t\"fileSize\": 20,\n" +
                    "\t\t\t\t\"fileUrl\": \"s3://vehicle-data/common-file/image/test.jpg\",\n" +
                    "\t\t\t\t\"refId\": \"1\",\n" +
                    "\t\t\t\t\"source\": \"accident\",\n" +
                    "\t\t\t\t\"memo\": \"\",\n" +
                    "\t\t\t\t\"isDeleted\": 0,\n" +
                    "\t\t\t\t\"createTime\": 1636471929000,\n" +
                    "\t\t\t\t\"updateTime\": 1636471929000\n" +
                    "\t\t\t}],\n" +
                    "\t\t\t\"videos\": [{\n" +
                    "\t\t\t\t\"id\": 1,\n" +
                    "\t\t\t\t\"fileName\": \"test.mp4\",\n" +
                    "\t\t\t\t\"fileType\": 3,\n" +
                    "\t\t\t\t\"fileSize\": 0,\n" +
                    "\t\t\t\t\"fileUrl\": \"s3://vehicle-data/common-file/vedio/test.mp4\",\n" +
                    "\t\t\t\t\"refId\": \"1\",\n" +
                    "\t\t\t\t\"source\": \"accident\",\n" +
                    "\t\t\t\t\"memo\": \"test\",\n" +
                    "\t\t\t\t\"isDeleted\": 0,\n" +
                    "\t\t\t\t\"createTime\": 1636449650000,\n" +
                    "\t\t\t\t\"updateTime\": 1636463414000\n" +
                    "\t\t\t}],\n" +
                    "\t\t\t\"accidentReview\": {\n" +
                    "\t\t\t\t\"id\": 2,\n" +
                    "\t\t\t\t\"accidentId\": \"1\",\n" +
                    "\t\t\t\t\"level\": \"blue\",\n" +
                    "\t\t\t\t\"cause\": \"cause\",\n" +
                    "\t\t\t\t\"damage\": \"damage\",\n" +
                    "\t\t\t\t\"compensation\": \"comp\",\n" +
                    "\t\t\t\t\"impact\": \"impact\",\n" +
                    "\t\t\t\t\"confirmResponsibility\": \"cfr\",\n" +
                    "\t\t\t\t\"rewardPunishment\": \"rp\",\n" +
                    "\t\t\t\t\"suggestion\": \"s\",\n" +
                    "\t\t\t\t\"isDeleted\": 0,\n" +
                    "\t\t\t\t\"createTime\": 1636447023000,\n" +
                    "\t\t\t\t\"updateTime\": 1636447023000\n" +
                    "\t\t\t},\n" +
                    "\t\t\t\"accidentDesc\": \"test,test\",\n" +
                    "\t\t\t\"status\": 11,\n" +
                    "\t\t\t\"isDeleted\": 0,\n" +
                    "\t\t\t\"createTime\": 1636440152000,\n" +
                    "\t\t\t\"updateTime\": 1636442138000\n" +
                    "\t\t}]\n" +
                    "\t}\n" +
                    "}"
    )
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public Response query(@RequestBody BizAccidentInfoRequest accidentInfoRequest) {
        Transaction t = Cat.newTransaction(CatConstant.ACCIDENT_INFO, "accident.info.list");
        long start = System.currentTimeMillis();
        if (accidentInfoRequest == null) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param is illegal");
        }
        // 非逻辑删除的记录
        accidentInfoRequest.setIsDeleted("0");
        if (accidentInfoRequest.getSiteBelongList() != null && !accidentInfoRequest.getSiteBelongList().isEmpty()) {
            accidentInfoRequest.setAffiliationList(
                    AffiliationEnum.getCodeByMsg(accidentInfoRequest.getSiteBelongList()));
        }
        PageResponse queryDTO = new PageResponse();
        try {
            List<BizAccidentInfo> tmpList = accidentInfoService.queryByPage(accidentInfoRequest);
            if (tmpList == null) {
                t.setDurationInMillis(System.currentTimeMillis() - start);
                t.setSuccessStatus();
                t.complete();
                return Response.fail("1", "get accident list fail");
            }
            List<BizAccidentInfoVO> accidentInfoList = tmpList.stream()
                    .map(item -> accidentInfoService.convertV2(item))
                    .collect(Collectors.toList());
            queryDTO.setPage(accidentInfoRequest.getPage());
            queryDTO.setSize(accidentInfoRequest.getSize());
            queryDTO.setTotal(accidentInfoService.countByPage(accidentInfoRequest));
            queryDTO.setResult(accidentInfoList);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(queryDTO);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Get accident list fail, request param is {}", accidentInfoRequest, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "get accident list fail");
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "获取事故详情",
            description = "根据ID获取事故详情",
            parameters = {
                    @ParamDoc(name = "id", description = "事故ID")
            },
            returnValueDescription = "事故详细信息",
            restExampleResponseData = "{\n" +
                    "\t\"ret\": \"0\",\n" +
                    "\t\"msg\": \"ok\",\n" +
                    "\t\"data\": {\n" +
                    "\t\t\"id\": 1,\n" +
                    "\t\t\"vin\": \"LTESTL123VIN123345\",\n" +
                    "\t\t\"vehicleName\": 'ST',\n" +
                    "\t\t\"accidentTime\": 1636442137978,\n" +
                    "\t\t\"locationName\": \"King & Queen Garden\",\n" +
                    "\t\t\"locationGps\": \"(116.33,40.02)\",\n" +
                    "\t\t\"reporter\": \"wu.wang\",\n" +
                    "\t\t\"siteMisId\": \"si.lili\",\n" +
                    "\t\t\"remoteMisId\": \"san.zhangsan\",\n" +
                    "\t\t\"uploadTasks\": [{\n" +
                    "\t\t\t\"id\": 789,\n" +
                    "\t\t\t\"vin\": \"3LN6L5SU9KR631259\",\n" +
                    "\t\t\t\"start\": *************,\n" +
                    "\t\t\t\"end\": *************,\n" +
                    "\t\t\t\"measurementTimestamp\": 1620971761559782189,\n" +
                    "\t\t\t\"module\": \"Prediction,Canbus,Planning,Localization,TrafficLight,Control\",\n" +
                    "\t\t\t\"status\": 100,\n" +
                    "\t\t\t\"priority\": 150,\n" +
                    "\t\t\t\"isDiscern\": 1,\n" +
                    "\t\t\t\"createTime\": *************,\n" +
                    "\t\t\t\"updateTime\": *************,\n" +
                    "\t\t\t\"recordName\": \"20210514_112841_mkz-06\",\n" +
                    "\t\t\t\"eventId\": \"\",\n" +
                    "\t\t\t\"taskType\": 1,\n" +
                    "\t\t\t\"isDeleted\": \"0\"\n" +
                    "\t\t}],\n" +
                    "\t\t\"pictures\": [{\n" +
                    "\t\t\t\"id\": 2,\n" +
                    "\t\t\t\"fileName\": \"test.jpg\",\n" +
                    "\t\t\t\"fileType\": 2,\n" +
                    "\t\t\t\"fileSize\": 20,\n" +
                    "\t\t\t\"fileUrl\": \"s3://vehicle-data/common-file/image/test.jpg\",\n" +
                    "\t\t\t\"refId\": \"1\",\n" +
                    "\t\t\t\"source\": \"accident\",\n" +
                    "\t\t\t\"memo\": \"\",\n" +
                    "\t\t\t\"isDeleted\": 0,\n" +
                    "\t\t\t\"createTime\": 1636471929000,\n" +
                    "\t\t\t\"updateTime\": 1636471929000\n" +
                    "\t\t}],\n" +
                    "\t\t\"videos\": [{\n" +
                    "\t\t\t\"id\": 1,\n" +
                    "\t\t\t\"fileName\": \"test.mp4\",\n" +
                    "\t\t\t\"fileType\": 3,\n" +
                    "\t\t\t\"fileSize\": 0,\n" +
                    "\t\t\t\"fileUrl\": \"s3://vehicle-data/common-file/vedio/test.mp4\",\n" +
                    "\t\t\t\"refId\": \"1\",\n" +
                    "\t\t\t\"source\": \"accident\",\n" +
                    "\t\t\t\"memo\": \"test\",\n" +
                    "\t\t\t\"isDeleted\": 0,\n" +
                    "\t\t\t\"createTime\": 1636449650000,\n" +
                    "\t\t\t\"updateTime\": 1636463414000\n" +
                    "\t\t}],\n" +
                    "\t\t\"accidentReview\": {\n" +
                    "\t\t\t\"id\": 2,\n" +
                    "\t\t\t\"accidentId\": \"1\",\n" +
                    "\t\t\t\"level\": \"blue\",\n" +
                    "\t\t\t\"cause\": \"cause\",\n" +
                    "\t\t\t\"damage\": \"damage\",\n" +
                    "\t\t\t\"compensation\": \"comp\",\n" +
                    "\t\t\t\"impact\": \"impact\",\n" +
                    "\t\t\t\"confirmResponsibility\": \"cfr\",\n" +
                    "\t\t\t\"rewardPunishment\": \"rp\",\n" +
                    "\t\t\t\"suggestion\": \"s\",\n" +
                    "\t\t\t\"isDeleted\": 0,\n" +
                    "\t\t\t\"createTime\": 1636447023000,\n" +
                    "\t\t\t\"updateTime\": 1636447023000\n" +
                    "\t\t},\n" +
                    "\t\t\"accidentDesc\": \"test,test\",\n" +
                    "\t\t\"status\": 11,\n" +
                    "\t\t\"isDeleted\": 0,\n" +
                    "\t\t\"createTime\": 1636440152000,\n" +
                    "\t\t\"updateTime\": 1636442138000\n" +
                    "\t}\n" +
                    "}"
    )
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public Response detail(@RequestParam Long id) {
        Transaction t = Cat.newTransaction(CatConstant.ACCIDENT_INFO, "accident.info.detail");
        long start = System.currentTimeMillis();
        if (id == null) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param is illegal");
        }
        try {
            BizAccidentInfoVO accidentInfo = accidentInfoService.convert(accidentInfoService.getById(id));
            accidentInfoService.getVideoList(id, accidentInfo);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(accidentInfo);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Get accident by id fail, request param is {}", id, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "get accident by id fail");
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "新增事故",
            description = "新增事故",
            parameters = {
                    @ParamDoc(name = "accidentInfo", description = "事故对象")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )

    @MadLog
    @Deprecated
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Response add(@RequestBody BizAccidentInfoRequest accidentInfo) {
        log.error("AccidentController add : {}", JSON.toJSON(accidentInfo));
        Transaction t = Cat.newTransaction(CatConstant.ACCIDENT_INFO, "accident.info.add");
        long start = System.currentTimeMillis();
        if (accidentInfo == null || accidentInfo.getVin() == null) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();

            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.ADD_ACCIDENT_REJECT,
                    "pram is null or vin is empty", accidentInfo);
            return Response.fail("1", "Param is illegal");
        }

        try {
            String vin = accidentInfo.getVin();
            Date accidentTime = DatetimeUtil.covertToDate(accidentInfo.getAccidentTime(), DatetimeUtil.YMDHMS);

            if (accidentInfo.getCheckLatestAccident()) {
                BizAccidentInfoVO bizAccidentInfoVO = accidentInfoService.queryAccidentInLatestTime(vin, accidentTime,
                        AccidentConfig.accident_avoid_repeat_millis);
                //如果能查到，表示在近几个小时内，能查到事故，告诉
                if (bizAccidentInfoVO != null ) {
                    WebResponseStatusEnum statusEnum = WebResponseStatusEnum.ACCIDENT_HAS_LATEST_ONE;
                    return Response.builder()
                            .ret(statusEnum.getCode() + "")
                            .msg(statusEnum.getMsg())
                            .data(bizAccidentInfoVO).build();
                }
            }

            BizAccidentInfo accident = new BizAccidentInfo();
            BeanUtils.copyProperties(accidentInfo, accident);
            if (accidentInfo.getAccidentTime() != null) {
                accident.setAccidentTime(accidentTime);
            }
            if (StringUtils.isNotBlank(accidentInfo.getSiteDisposeTime())) {
                accident.setSiteDisposeTime(
                        DatetimeUtil.covertToDate(accidentInfo.getSiteDisposeTime(), DatetimeUtil.YMDHMS));
            }
            AccidentInfoDTO accidentInfoDTO = accidentInfoService.add(accident);
            if(AccidentConfig.isOpenAccidentGroupCreate){
                log.info("Start to create group and send msg, param is [{}]", accidentInfoDTO);
                dxGroupHandler.createGroupAndSendMsg(accidentInfoDTO);
            }
            accidentInfoService.getAccidentRemoteVideo(accident);
            log.info("Start to call person, accident is [{}]", accident);
            accidentInfoService.autoCallForAccident(accident);
            log.info("Start to update accident extended info, accident_id is [{}]", accident.getId());
            accidentInfoService.updateAccidentExtendedInfo(accident);
            List<String> files = accidentInfo.getFiles();
            if (files != null && !files.isEmpty()) {
                String refId = accident.getId() == null ? "" : accident.getId().toString();
                for (String fileName : files) {
                    fileInfoService.updateAccidentIdByFileName(refId, fileName);
                }
            }
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(accident);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Add accident fail, request param is {}", accidentInfo, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "add accident fail");
        } finally {
            t.complete();
        }
    }

    @MadLog
    @RequestMapping(value = "/add/v2", method = RequestMethod.POST)
    public Response addV2(@RequestBody BizAccidentInfoRequest accidentInfo) {
        if (accidentInfo == null || accidentInfo.getVin() == null) {
            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.ADD_ACCIDENT_REJECT,
                    "pram is null or vin is empty", accidentInfo);
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.toString(),
                    WebResponseStatusEnum.PARAMETER_ERROR.getMsg());
        }

        try {
            User user = UserUtils.getUser();
            String misId = user.getLogin();
            if (StringUtils.isBlank(misId)) {
                return Response.fail(WebResponseStatusEnum.INVALID_USER.getCode().toString(),
                        WebResponseStatusEnum.INVALID_USER.getMsg());
            }
            accidentInfo.setReporter(misId);
            accidentInfo.setVin(accidentInfo.getVin().toUpperCase());
            log.info("Accident add v2: {}, ", JSON.toJSON(accidentInfo));
            String vin = accidentInfo.getVin();
            Date accidentTime = DatetimeUtil.covertToDate(accidentInfo.getAccidentTime(), DatetimeUtil.YMDHMS);

            if (accidentInfo.getCheckLatestAccident()) {
                BizAccidentInfoVO bizAccidentInfoVO = accidentInfoService.queryAccidentInLatestTime(vin, accidentTime,
                        AccidentConfig.accident_avoid_repeat_millis);
                //如果能查到，表示在近几个小时内，能查到事故，告诉
                if (bizAccidentInfoVO != null ) {
                    log.info("report accident is Duplicated! accidentInfo = {}", accidentInfo);
                    WebResponseStatusEnum statusEnum = WebResponseStatusEnum.ACCIDENT_HAS_LATEST_ONE;
                    return Response.builder()
                            .ret(statusEnum.getCode() + "")
                            .msg(statusEnum.getMsg())
                            .data(bizAccidentInfoVO).build();
                }
            }

            BizAccidentInfo accident = new BizAccidentInfo();
            BeanUtils.copyProperties(accidentInfo, accident);
            if (accidentInfo.getAccidentTime() != null) {
                accident.setAccidentTime(accidentTime);
            }
            if (StringUtils.isNotBlank(accidentInfo.getSiteDisposeTime())) {
                accident.setSiteDisposeTime(
                        DatetimeUtil.covertToDate(accidentInfo.getSiteDisposeTime(), DatetimeUtil.YMDHMS));
            }
            AccidentInfoDTO accidentInfoDTO = accidentInfoService.add(accident);

            //add 函数中有两种情况会返回 null, 排出过滤的那种
            if(accidentInfoDTO == null && !AccidentConfig.accident_test_vin.equalsIgnoreCase(accident.getVin())) {
                log.info("insert data failed, accidentInfo = {}", accidentInfo);
                WebResponseStatusEnum statusEnum = WebResponseStatusEnum.PREVENT_DUPLICATION_REPORT_ACCIDENT;
                return Response.builder()
                        .ret(statusEnum.getCode() + "")
                        .msg(statusEnum.getMsg())
                        .build();
            }

            List<String> files = accidentInfo.getFiles();
            if (files != null && !files.isEmpty()) {
                String refId = accident.getId() == null ? "" : accident.getId().toString();
                for (String fileName : files) {
                    fileInfoService.updateAccidentIdByFileName(refId, fileName);
                }
            }
            if (accidentInfoDTO != null) {
                if(AccidentConfig.isOpenAccidentGroupCreate){
                    log.info("Start to create group and send msg, param is [{}]", accidentInfoDTO);
                    dxGroupHandler.createGroupAndSendMsg(accidentInfoDTO);
                }
                accidentInfoService.getAccidentRemoteVideo(accident);
                log.info("Start to call person, accident is [{}]", accident);
                accidentInfoService.autoCallForAccident(accident);
                log.info("Start to update accident extended info, accident_id is [{}]", accident.getId());
                accidentInfoService.updateAccidentExtendedInfo(accident);
            }
            return Response.succ(accident);
        } catch (Exception e) {
            log.error("Add accident fail, request param is {}", accidentInfo, e);
            return Response.fail(WebResponseStatusEnum.FAILED.toString(), "add accident fail");
        }
    }

    @MadLog
    @RequestMapping(value = "/add/v3", method = RequestMethod.POST)
    public Response addV3(@RequestBody BizAccidentInfoRequest accidentInfo) {
        if (accidentInfo == null || accidentInfo.getVin() == null) {
            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.ADD_ACCIDENT_REJECT,
                    "pram is null or vin is empty", accidentInfo);
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.toString(),
                    WebResponseStatusEnum.PARAMETER_ERROR.getMsg());
        }

        try {
            if(accidentInfo.getReporter() == null){
                accidentInfo.setReporter("system");
            }
            //处理经纬度信息，wgs84_to_gjc02
            if(accidentInfo.getLocationGps() != null){
               accidentInfo.setLocationGps(accidentInfoService.wgs84ToGjc02(accidentInfo.getLocationGps()));
            }
            accidentInfo.setVin(accidentInfo.getVin().toUpperCase());
            log.info("Accident add V3: {}, ", JSON.toJSON(accidentInfo));
            String vin = accidentInfo.getVin();
            Date accidentTime = DatetimeUtil.covertToDate(accidentInfo.getAccidentTime(), DatetimeUtil.YMDHMS);

            if (accidentInfo.getCheckLatestAccident()) {
                BizAccidentInfoVO bizAccidentInfoVO = accidentInfoService.queryAccidentInLatestTime(vin, accidentTime,
                        AccidentConfig.accident_avoid_repeat_millis);
                //如果能查到，表示在近几个小时内，能查到事故，告诉
                if (bizAccidentInfoVO != null ) {
                    WebResponseStatusEnum statusEnum = WebResponseStatusEnum.ACCIDENT_HAS_LATEST_ONE;
                    return Response.builder()
                            .ret(statusEnum.getCode() + "")
                            .msg(statusEnum.getMsg())
                            .data(bizAccidentInfoVO).build();
                }
            }

            BizAccidentInfo accident = new BizAccidentInfo();
            BeanUtils.copyProperties(accidentInfo, accident);
            if (accidentInfo.getAccidentTime() != null) {
                accident.setAccidentTime(accidentTime);
            }
            if (StringUtils.isNotBlank(accidentInfo.getSiteDisposeTime())) {
                accident.setSiteDisposeTime(
                        DatetimeUtil.covertToDate(accidentInfo.getSiteDisposeTime(), DatetimeUtil.YMDHMS));
            }
            AccidentInfoDTO accidentInfoDTO = accidentInfoService.add(accident);
            List<String> files = accidentInfo.getFiles();
            if (files != null && !files.isEmpty()) {
                String refId = accident.getId() == null ? "" : accident.getId().toString();
                for (String fileName : files) {
                    fileInfoService.updateAccidentIdByFileName(refId, fileName);
                }
            }
            if (accidentInfoDTO != null) {
                if(AccidentConfig.isOpenAccidentGroupCreate){
                    log.info("Start to create group and send msg, param is [{}]", accidentInfoDTO);
                    dxGroupHandler.createGroupAndSendMsg(accidentInfoDTO);
                }
                accidentInfoService.getAccidentRemoteVideo(accident);
                log.info("Start to call person, accident is [{}]", accident);
                accidentInfoService.autoCallForAccident(accident);
                log.info("Start to update accident extended info, accident_id is [{}]", accident.getId());
                accidentInfoService.updateAccidentExtendedInfo(accident);
            }
            return Response.succ(accident);
        } catch (Exception e) {
            log.error("Add accident fail, request param is {}", accidentInfo, e);
            return Response.fail(WebResponseStatusEnum.FAILED.toString(), "add accident fail");
        }
    }

    @MethodDoc(
            displayName = "检查最近事故",
            description = "检查最近若干秒内的事故",
            parameters = {
                    @ParamDoc(name = "LatestAccidentRequest", description = "最近事故对象")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"60001\",\n" +
                    "\"msg\": \"该车最近创建过事故了\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )
    @GetMapping(value = "/check/latest")
    public Response getLatestAccident(LatestAccidentRequest request) {
        String vin = request.getVin();
        Date accidentTime = request.getAccidentTime() == null ? new Date() : request.getAccidentTime();
        long latestMillis = request.getLatestSeconds() == null ? AccidentConfig.accident_avoid_repeat_millis :
                request.getLatestSeconds() * CommonConstant.MILLIS_TO_SECOND;
        BizAccidentInfoVO bizAccidentInfoVO = accidentInfoService.queryAccidentInLatestTime(vin, accidentTime,
                latestMillis);
        if (bizAccidentInfoVO == null) {
            return Response.fail(WebResponseStatusEnum.DATA_NOT_FOUND.getCode() + "",
                    WebResponseStatusEnum.DATA_NOT_FOUND.getMsg());
        }

        WebResponseStatusEnum statusEnum = WebResponseStatusEnum.ACCIDENT_HAS_LATEST_ONE;
        return Response.builder()
                .ret(statusEnum.getCode() + "")
                .msg(statusEnum.getMsg())
                .data(bizAccidentInfoVO).build();
    }

    @MethodDoc(
            displayName = "更新事故",
            description = "更新事故",
            parameters = {
                    @ParamDoc(name = "accidentInfo", description = "事故对象")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )
    @MadLog
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Response update(@RequestBody BizAccidentInfoRequest bizAccidentInfoRequest) {
        Transaction t = Cat.newTransaction(CatConstant.ACCIDENT_INFO, "accident.info.update");
        long start = System.currentTimeMillis();
        if (bizAccidentInfoRequest == null || bizAccidentInfoRequest.getId() == null) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.UPDATE_ACCIDENT_REJECT,
                    "param is null or id is null", bizAccidentInfoRequest);
            return Response.fail("1", "Param is illegal");
        }
        User user = UserUtils.getUser();
        if (user == null) {
            log.warn("update accident info lack misId info, id={}", bizAccidentInfoRequest.getId());
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(), "缺少用户信息");
        }
        String misId = user.getLogin();
        try {
            log.info("request reporter={}, actual reporter={}, bizAccidentInfoRequest = {}",
                    bizAccidentInfoRequest.getReporter(), misId, bizAccidentInfoRequest);
            bizAccidentInfoRequest.setReporter(misId);
            BizAccidentInfo bizAccidentInfo = accidentInfoService.getById(bizAccidentInfoRequest.getId());
            BizAccidentInfo accident = new BizAccidentInfo();
            BeanUtils.copyProperties(bizAccidentInfoRequest, accident);
            accident.setVin(bizAccidentInfo.getVin());
            if (bizAccidentInfoRequest.getSiteDisposeTime() != null) {
                accident.setSiteDisposeTime(DatetimeUtil.covertToDate(
                        bizAccidentInfoRequest.getSiteDisposeTime(), DatetimeUtil.YMDHMS));
            }
            if (bizAccidentInfoRequest.getRiskEliminationTime() != null) {
                accident.setRiskEliminationTime(DatetimeUtil.covertToDate(
                        bizAccidentInfoRequest.getRiskEliminationTime(), DatetimeUtil.YMDHMS));

                // 只有第一次点击解除风险时作为事故工单结束信号，防止修改车辆历史事故的风险解除时间时对新事故工单产生影响
                if(Objects.equals(DatetimeUtil.format(bizAccidentInfo.getRiskEliminationTime()), DB_DEFAULT_TIME)){

                    // 将事故状态设置为已完成
                    accident.setStatus(AccidentStatusEnum.FINISHED.getCode());
                    // 当接收到风险解除消息时同步事故完成信号到MQ，风险解除等于事故工单完结
                    accidentInfoService.sendAccidentMsgToMQ(bizAccidentInfo, AccidentStatusEnum.FINISHED);
                }

            }
            if (bizAccidentInfoRequest.getAccidentTime() != null) {
                Date accidentTime = DatetimeUtil.covertToDate(bizAccidentInfoRequest.getAccidentTime(),
                        DatetimeUtil.YMDHMS);
                accident.setAccidentTime(accidentTime);
                accidentInfoService.updateVehicleStatusForAccidentInfo(accident);
                log.info("Start to create task, because accident time update, param is [{}]", accident);
                accidentInfoService.createTaskWhenAccidentTimeUpdate(accident);
            }
            if (bizAccidentInfoRequest.getSecurityGroupResponseTime() != null) {
                accident.setSecurityGroupResponseTime(
                        DatetimeUtil.covertToDate(
                                bizAccidentInfoRequest.getSecurityGroupResponseTime(), DatetimeUtil.YMDHMS));
            }
            if (bizAccidentInfoRequest.getExpectRepairTime()!=null){
                accident.setExpectRepairTime(DatetimeUtil.covertToDate(bizAccidentInfoRequest.getExpectRepairTime(), DatetimeUtil.YMDHMS));
            }
            if (bizAccidentInfoRequest.getFinishRepairTime()!=null){
                accident.setFinishRepairTime(DatetimeUtil.covertToDate(bizAccidentInfoRequest.getFinishRepairTime(), DatetimeUtil.YMDHMS));
            }
            if (bizAccidentInfoRequest.getAcceptanceTime() != null) {
                accident.setAcceptanceTime(DatetimeUtil.covertToDate(bizAccidentInfoRequest.getAcceptanceTime(), DatetimeUtil.YMDHMS));
            }
            accidentInfoService.update(accident);
            List<String> files = bizAccidentInfoRequest.getFiles();
            if (files != null && !files.isEmpty()) {
                String refId = accident.getId() == null ? "" : accident.getId().toString();
                for (String fileName : files) {
                    fileInfoService.updateAccidentIdByFileName(refId, fileName);
                }
            }
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ("");
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Update accident fail, request param is {}", bizAccidentInfoRequest, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "update accident fail");
        } finally {
            t.complete();
        }
    }

    // 加一个接口，以及对应的事故状态表，用于处理状态
    @PostMapping("/update_event_fix_status")
    public Response updateEventStatus(@RequestParam Long id,@RequestParam Integer status) {
        Transaction t = Cat.newTransaction(CatConstant.ACCIDENT_INFO, "accident.info.update");
        long start = System.currentTimeMillis();
        if (id == null || status == null) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param is illegal");
        }
        log.info("updateEventStatus, request params:id {} . status {}",id,status);
        try {
            accidentInfoService.updateEventFixStatus(id, status);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ("");
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Update accident fail, request param is {}", id, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "update accident fail");
        }
    }

    @MethodDoc(
            displayName = "更新事故责任",
            description = "更新事故责任",
            parameters = {
                    @ParamDoc(name = "accidentRespInfo", description = "事故责任对象")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )
    @RequestMapping(value = "/update_accident_resp", method = RequestMethod.POST)
    public Response updateAccidentResp(@RequestBody BizAccidentInfoRequest accidentRespInfo) {
        Transaction t = Cat.newTransaction(CatConstant.ACCIDENT_RESP_UPDATE, "accident.resp.update");
        long start = System.currentTimeMillis();
        if (accidentRespInfo == null || accidentRespInfo.getId() == null) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param is illegal");
        }
        try {
            log.info("Accident responsibility update, id: {}, accidentResp: {}, accidentRespDesc: {}",
                    accidentRespInfo.getId(), accidentRespInfo.getAccidentResponsibility(),
                    accidentRespInfo.getAccidentResponsibilityDesc());
            accidentInfoService.updateResp(accidentRespInfo);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ("");
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Update accident responsibility failed, request param is {}", accidentRespInfo, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "update accident responsibility failed");
        } finally {
            t.complete();
        }
    }

    @PostMapping("/upload_video")
    @MadLog
    public Response uploadVideo(BizAccidentInfoVO bizAccidentInfoVO,
                                @RequestParam("data") MultipartFile file) {
        String api = "/upload_vehicle";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        if (file.isEmpty() || file.getSize() <= 0L) {
            log.warn("Size of file {} is zero, accidentId is {}",
                    file.getOriginalFilename(), bizAccidentInfoVO.getId());
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.UPLOAD_VIDEO_REJECT,
                    "upload file is empty", bizAccidentInfoVO);
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), "upload file is empty");
        }
        if (StringUtils.isBlank(bizAccidentInfoVO.getVin())) {
            log.warn("Bad request, vin is null, accidentId is [{}], fileName is [{}]",
                    bizAccidentInfoVO.getId(), file.getOriginalFilename());
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.UPLOAD_VIDEO_REJECT,
                    "vin is empty", bizAccidentInfoVO);
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(), "lack vin param");
        }
        String fileName = file.getOriginalFilename();
        log.info("traceId: {}, Begin to upload video: {}, accidentId is {}",
                traceId, fileName, bizAccidentInfoVO.getId());
        try {
            BizRecordRemoteVideo bizRecordRemoteVideo = accidentInfoService.uploadVideo(bizAccidentInfoVO, file);
            log.info("traceId: {}, Success to upload video: {}, accidentId is {}",
                    traceId, fileName, bizAccidentInfoVO.getId());
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(bizRecordRemoteVideo);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, Upload video failed, accidentId is [{}], fileName is [{}]",
                    traceId, bizAccidentInfoVO.getId(), file.getOriginalFilename(), e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping("/query_map_area")
    public Response queryMapArea() {
        String api = "/query_map_area";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            List<BizMapArea> result = accidentInfoService.queryMapArea();
            log.info("traceId: {}, Success to query map area, result: {} ", traceId, result);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, query map area failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @MadLog
    @PostMapping("/add_remote_video")
    public Response addRemoteVideo(@RequestBody RemoteVideoRequest remoteVideoRequest) {

        String api = "/add_remote_video";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        if (remoteVideoRequest == null || remoteVideoRequest.getId() == null ||
                remoteVideoRequest.getVin() == null || remoteVideoRequest.getRemoteVideoStartTime() == null ||
                remoteVideoRequest.getRemoteVideoEndTime() == null) {
            log.warn("Bad add remote video request, request param is [{}]", remoteVideoRequest);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.ADD_REMOTE_VIDEO_REJECT,
                    "param or id or vin or video_start_time or video_end_time is empty", remoteVideoRequest);
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(), "lack some parameter");
        }
        try {
            log.info("traceId: {}, start to add remote video, request param is {}", traceId, remoteVideoRequest);
            accidentInfoService.addRemoteVideo(remoteVideoRequest);
            log.info("traceId: {}, success add remote video", traceId);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, Add remote video failed, request param is [{}]", traceId, remoteVideoRequest, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @PostMapping("/delete_local_video")
    public Response deleteLocalVideo(@RequestBody BizRecordRemoteVideo bizRecordRemoteVideo) {
        String api = "/delete_local_video";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        if (bizRecordRemoteVideo == null || bizRecordRemoteVideo.getId() == null ||
                bizRecordRemoteVideo.getFileName() == null) {
            log.warn("Bad delete local video request, request param is [{}]", bizRecordRemoteVideo);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), "lack some parameter");
        }
        try {
            log.info("traceId: {}, start to delete local video, request param is [{}]", traceId, bizRecordRemoteVideo);
            accidentInfoService.deleteLocalVideo(bizRecordRemoteVideo.getId(), bizRecordRemoteVideo.getFileName());
            log.info("traceId: {}, success delete local video", traceId);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, logic delete local video failed, request param is [{}]",
                    traceId, bizRecordRemoteVideo, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @PostMapping("/delete")
    public Response delete(@RequestBody BizAccidentInfoRequest request) {
        String api = "/delete";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        long start = System.currentTimeMillis();
        if (request == null || request.getId() == null) {
            log.warn("Bad delete accident request, request param is [{}]", request);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), "lack id parameter");
        }
        User user = UserUtils.getUser();
        if (user == null) {
            log.warn("delete accident lack misId info, id={}", request.getId());
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(), "缺少用户信息");
        }
        String misId = user.getLogin();
        try {
            log.info("start to delete accident, misId={}, accident id={}", misId, request.getId());
            accidentInfoService.deleteAccident(request.getId());
            log.info("success delete accident, id is [{}]", request.getId());
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("logic delete accident failed, request param is [{}]", request, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @PostMapping("/delete_remote_video")
    public Response deleteRemoteVideo(@RequestBody RemoteVideoRequest remoteVideoRequest) {
        String api = "/delete_remote_video";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        if (remoteVideoRequest == null || remoteVideoRequest.getId() == null ||
                remoteVideoRequest.getRemoteVideoStartTime() == null ||
                remoteVideoRequest.getRemoteVideoEndTime() == null) {
            log.warn("Bad delete remote video request, request param is [{}]", remoteVideoRequest);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), "lack some parameter");
        }
        try {
            log.info("traceId: {}, start to delete remote video, request param is [{}]", traceId, remoteVideoRequest);
            accidentInfoService.deleteRemoteVideo(remoteVideoRequest);
            log.info("traceId: {}, success delete remote video", traceId);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, logic delete remote video failed, request param is [{}]",
                    traceId, remoteVideoRequest, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping("/get_resp_enum")
    public Response getRespEnum() {
        String api = "/get_resp_enum";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            log.info("traceId: {}, start to get accident resp enum", traceId);
            List<Object> result = AccidentRespEnum.getCodeMsgMap();
            log.info("traceId: {}, end get accident resp enum, result: [{}]", traceId, result);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, get accident resp enum failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping("/get_road_info_enum")
    public Response getRoadInfoEnum() {
        String api = "/get_road_info_enum";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            log.info("traceId: {}, start to get accident road info enum", traceId);
            List<Object> result = AccidentRoadTypeEnum.getCodeMsgMap();
            log.info("traceId: {}, end get accident road info enum, result: [{}]", traceId, result);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, get accident road info enum failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping("/get_fix_verify_result_enum")
    public Response getFixVerifyResultEnum() {
        String api = "/get_fix_verify_result_enum";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            log.info("traceId: {}, start to get accident verify result enum", traceId);
            List<Object> result = AccidentFixVerifyResultEnum.getCodeMsgMap();
            log.info("traceId: {}, end get accident verify result enum, result: [{}]", traceId, result);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, get accident verify result enum failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping("/get_vehicle_status_enum")
    public Response getVehicleStatusEnum() {
        String api = "/get_vehicle_status_enum";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            log.info("traceId: {}, start to get accident vehicle status enum", traceId);
            List<Object> result = AccidentVehicleStatusEnum.getCodeMsgMap();
            log.info("traceId: {}, end get accident vehicle status enum, result: [{}]", traceId, result);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, get accident vehicle status enum failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping("/get_two_sides_form_enum")
    public Response getTwoSidesFormEnum() {
        String api = "/get_two_sides_form_enum";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            log.info("traceId: {}, start to get accident two sides form enum", traceId);
            List<Object> result = AccidentTwoSidesFormEnum.getCodeMsgMap();
            log.info("traceId: {}, end get accident two sides form enum, result: [{}]", traceId, result);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, get accident two sides form enum failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping("/get_violation_station_enum")
    public Response getViolationStationEnum() {
        String api = "/get_violation_station_enum";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            log.info("traceId: {}, start to get accident violation station enum", traceId);
            List<Object> result = AccidentViolationStationEnum.getCodeMsgMap();
            log.info("traceId: {}, end get accident violation station enum, result: [{}]", traceId, result);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, get accident violation station enum failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping("/get_rank_enum")
    public Response getRankEnum() {
        String api = "/get_rank_enum";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            log.info("traceId: {}, start to get accident rank enum", traceId);
            Map<Integer, String> result = AccidentLevelEnum.getCodeMsgMap();
            log.info("traceId: {}, end get accident rank enum, result: [{}]", traceId, result);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, get accident rank enum failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping("/get_resp_ratio_enum")
    public Response getRespRatioEnum() {
        String api = "/get_resp_ratio_enum";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            log.info("traceId: {}, start to get accident resp ratio enum", traceId);
            Map<Integer, String> result = AccidentRespRatioEnum.getCodeMsgMap();
            log.info("traceId: {}, end get accident resp ratio enum, result: [{}]", traceId, result);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, get accident resp ratio enum failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping("/get_type_enum")
    public Response getTypeEnum() {
        String api = "/get_type_enum";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            log.info("traceId: {}, start to get accident type enum", traceId);
            Map<Integer, String> result = AccidentTypeEnum.getCodeMsgMap();
            log.info("traceId: {}, end get accident type enum, result: [{}]", traceId, result);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, get accident type enum failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping("/get_auth_menu")
    public Response getAuthMenu() {
        String api = "/get_auth_menu";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            User user = UserUtils.getUser();
            log.info("traceId: {}, start to get auth menu, misId is [{}], userId is [{}]",
                    traceId, user.getLogin(), user.getId());
            Map<String, String> config = Maps.newHashMap();
            config.put("uac", uacHost);
            uacAuthRemoteService.setConfigSource(new SimpleConfigSource(config,
                    UacFilterConfiguration.getAppKey(), UacFilterConfiguration.getAppSecret()));
            UserMenu result = uacAuthRemoteService.getUserMenus(String.valueOf(user.getId()));
            log.info("traceId: {}, end get auth menu, result: [{}]", traceId, result);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, get auth menu failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @PostMapping("/auto_call/insert")
    public Response insertAutoCall(@RequestBody AutoCallRequest autoCallRequest) {
        String api = "/auto_call/insert";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            log.info("traceId: {}, start to insert auto call rule, request is [{}]", traceId, autoCallRequest);
            dataCenterAutoCallService.insertAccidentCallNumber(autoCallRequest);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, insert auto call rule failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @PostMapping("/auto_call/update")
    public Response updateAutoCall(@RequestBody AutoCallRequest autoCallRequest) {
        String api = "/auto_call/update";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            log.info("traceId: {}, start to update auto call rule, request is [{}]", traceId, autoCallRequest);
            dataCenterAutoCallService.updateAccidentCallNumber(autoCallRequest);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, update auto call rule failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @PostMapping("/add/user_phone")
    public Response addUserPhone(@RequestBody AutoCallRequest autoCallRequest) {
        String traceId = Trace.generateId();
        try {
            log.info("traceId: {}, start to add user phone, request is [{}]", traceId, autoCallRequest);
            accidentInfoService.insertUserPhoneNumber(autoCallRequest);
            return Response.succ();
        } catch (Exception e) {
            log.error("traceId: {}, add user phone failed, request is [{}]", traceId, autoCallRequest, e);
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @PostMapping("/update/user_phone")
    public Response updateUserPhone(@RequestBody AutoCallRequest autoCallRequest) {
        String traceId = Trace.generateId();
        try {
            log.info("traceId: {}, start to update user phone, request is [{}]", traceId, autoCallRequest);
            accidentInfoService.updateUserPhoneNumber(autoCallRequest);
            return Response.succ();
        } catch (Exception e) {
            log.error("traceId: {}, add update phone failed, request is [{}]", traceId, autoCallRequest, e);
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping("/remove_dx_group")
    public Response removeDxGroup(String groupIds) {
        String api = "/remove_dx_group";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        try {
            log.info("traceId: {}, start to remove dx group, request is [{}]", traceId, groupIds);
            String result = dxGroupHandler.removeDxGroups(groupIds);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, remove dx group failed", traceId, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @PostMapping("/unchecked/call_and_notice")
    public Response callAndNotice(@RequestBody BizAccidentInfoRequest request) {
        String traceId = Trace.generateId();
        if (StringUtils.isBlank(request.getVin()) || request.getUncheckedTime() == null ||
                request.getEventTimestamp() == null) {
            Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    WebResponseStatusEnum.PARAMETER_ERROR.getMsg());
        }
        try {
            accidentInfoService.callAndNoticeUncheckedAccident(request);
            return Response.succ();
        } catch (Exception e) {
            log.error("traceId: {}, call and notice unchecked accident failed, request param is [{}]",
                    traceId, request, e);
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }
    
    @GetMapping(value = "/getGroupMember")
    public Response getGroupMember(String vin , String recordName, Long accidentTime) {
        log.info("accident/getGroupMember, vin = {}. recordName = {}, accidentTime = {}", vin, recordName, accidentTime);
        try{
            List<String> res = dxGroupHandler.getGroupMember(vin, recordName, new Date(accidentTime));
            return Response.succ(WebResponseStatusEnum.SUCCESS.getCode().toString(), "ok", res);
        }
        catch (Exception e){
            log.error("getGroupMember is failed");
        }
        return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), "failed");
    }

    @GetMapping(value = "/getCityByVin")
    public Response getCityAndAffiliationFromRecord(String vin){
        try{
            return Response.succ(WebResponseStatusEnum.SUCCESS.getCode().toString(),"ok",accidentInfoService.getCityAndAffiliationFromRecord(vin));
        }
        catch (Exception e){
            log.error("getCity is failed, vin = {}", vin);
        }
        return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(),"failed");
    }

    @GetMapping(value = "/getRoadType")
    public Response getRoadTypeOfAccidentVehicle(String recordName, String locationGps){
        return Response.succ(WebResponseStatusEnum.SUCCESS.getCode().toString(),
                "ok",
                accidentInfoService.getRoadTypeOfAccidentVehicle(recordName, locationGps));
    }
}
