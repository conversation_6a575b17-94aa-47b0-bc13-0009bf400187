package com.meituan.walle.data.center.entity.po;

import com.meituan.walle.data.center.entity.pojo.RealUploadProgress;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;


@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class RealUploadProgressPO implements Serializable {
    private RealUploadProgress uploadProgress = new RealUploadProgress(); // 总上传流程进度

    private RealUploadProgress hdfsUploadProgress = new RealUploadProgress(); // hdfs上传流程进度

    private RealUploadProgress s3UploadProgress = new RealUploadProgress(); // s3上传流程进度

    private Map<String, RealUploadProgress> hdfsUploadProgressMap = new HashMap<>(); // hdfs record上传流程进度

    private Map<String, RealUploadProgress> s3UploadProgressMap = new HashMap<>(); // s3 record上传流程进度

    private AtomicLong uploadTime = new AtomicLong(0L); // 文件上传的总耗时，毫秒

    private String currentUploadFile; // 当前上传文件

    private Integer s3TrashFileTotal = 0;

    private Set<String> s3TrashSet = new HashSet<>(20); // 写入逻辑限定，只存20个

    private Integer diskTrashFileTotal = new Integer(0);

    private Set<String> diskTrashSet = new HashSet<>(20); // 写入逻辑限定，只存20个

    private String batchId;

    public RealUploadProgress getHdfsUploadProgress() {
        hdfsUploadProgress.setFileTotal(0);
        hdfsUploadProgress.setFileUploaded(0);
        hdfsUploadProgress.setSizeTotal(0L);
        hdfsUploadProgress.setSizeUploaded(0L);
        if (hdfsUploadProgressMap != null) {
            for (RealUploadProgress value : hdfsUploadProgressMap.values()) {
                hdfsUploadProgress.setFileTotal(hdfsUploadProgress.getFileTotal() + value.getFileTotal());
                hdfsUploadProgress.setFileUploaded(hdfsUploadProgress.getFileUploaded() + value.getFileUploaded());
                hdfsUploadProgress.setSizeTotal(hdfsUploadProgress.getSizeTotal() + value.getSizeTotal());
                hdfsUploadProgress.setSizeUploaded(hdfsUploadProgress.getSizeUploaded() + value.getSizeUploaded());
            }
        }
        return hdfsUploadProgress;
    }

    public RealUploadProgress getS3UploadProgress() {
        s3UploadProgress.setFileTotal(0);
        s3UploadProgress.setFileUploaded(0);
        s3UploadProgress.setSizeTotal(0L);
        s3UploadProgress.setSizeUploaded(0L);
        if (s3UploadProgressMap != null) {
            for (RealUploadProgress value : s3UploadProgressMap.values()) {
                s3UploadProgress.setFileTotal(s3UploadProgress.getFileTotal() + value.getFileTotal());
                s3UploadProgress.setFileUploaded(s3UploadProgress.getFileUploaded() + value.getFileUploaded());
                s3UploadProgress.setSizeTotal(s3UploadProgress.getSizeTotal() + value.getSizeTotal());
                s3UploadProgress.setSizeUploaded(s3UploadProgress.getSizeUploaded() + value.getSizeUploaded());
            }
        }
        return s3UploadProgress;
    }

    public RealUploadProgress getUploadProgress() {
        RealUploadProgress hdfsUploadProgress = getHdfsUploadProgress();
        RealUploadProgress s3UploadProgress = getS3UploadProgress();
        uploadProgress.setFileTotal(
                hdfsUploadProgress.getFileTotal() + s3UploadProgress.getFileTotal());
        uploadProgress.setFileUploaded(
                hdfsUploadProgress.getFileUploaded() + s3UploadProgress.getFileUploaded());
        uploadProgress.setSizeTotal(
                hdfsUploadProgress.getSizeTotal() + s3UploadProgress.getSizeTotal());
        uploadProgress.setSizeUploaded(
                hdfsUploadProgress.getSizeUploaded() + s3UploadProgress.getSizeUploaded());
        return uploadProgress;
    }
}
