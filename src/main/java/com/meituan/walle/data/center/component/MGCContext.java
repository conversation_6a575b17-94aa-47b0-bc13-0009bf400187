package com.meituan.walle.data.center.component;

import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.walle.data.center.entity.vo.OriginDatasourceConfigVO;
import com.meituan.walle.data.center.handle.impl.MafkaConnector;
import com.meituan.walle.data.center.handle.impl.OriginDataExtractor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import static com.meituan.walle.data.center.constant.CommonConstant.GENERAL_CASE_TOPIC_DATASOURCE_LOCK_PREV;

/**
 * <AUTHOR>
 * @date 2022/12/23 16:44
 * @description mgc公共参数组件
 */
public class MGCContext {
    // originDatasourceName -> datasourceConfig
    private static final Map<String, OriginDatasourceConfigVO> datasourceConfigs = new ConcurrentHashMap<>();
    // originDatasourceName -> mafkaConnector
    private static final Map<String, MafkaConnector> datasourceConnectors = new ConcurrentHashMap<>();
    // originDatasourceName -> dataExtractor
    private static final Map<String, OriginDataExtractor> dataExtractors = new ConcurrentHashMap<>();
    // topicName -> consumer
    private static final Map<String, IConsumerProcessor> consumers = new ConcurrentHashMap<>();
    // topicName -> originDatasourceName
    private static final Map<String, Set<String>> topicNameOriginDatasourceNames = new ConcurrentHashMap<>();

    public static OriginDatasourceConfigVO getConfig(String originDatasourceName) {
        return datasourceConfigs.get(originDatasourceName);
    }

    public static OriginDataExtractor getDataExtractor(String originDatasourceName) {
        return dataExtractors.get(originDatasourceName);
    }


    public static void setConfig(String originDatasourceName, OriginDatasourceConfigVO configVO) {
        datasourceConfigs.put(originDatasourceName, configVO);
    }

    public static void setDatasourceConnector(String originDatasourceName, MafkaConnector connector) {
        datasourceConnectors.put(originDatasourceName, connector);
    }

    public static void setDataExtractor(String originDatasourceName, OriginDataExtractor extractor) {
        dataExtractors.put(originDatasourceName, extractor);
    }

    public static IConsumerProcessor getConsumer(String topicName) {
        return consumers.get(topicName);
    }

    public static void setConsumer(String topicName, IConsumerProcessor consumer) {
        consumers.put(topicName, consumer);
    }

    public static Set<String> getTopicOriginDatasourceNames(String topicName) {
        return topicNameOriginDatasourceNames.get(topicName);
    }

    public static void addTopicOriginDatasourceName(String topicName, String originDatasourceName) {
        if (StringUtils.isEmpty(topicName) || StringUtils.isEmpty(originDatasourceName)) {
            return;
        }
        synchronized (GENERAL_CASE_TOPIC_DATASOURCE_LOCK_PREV + topicName) {
            Set<String> set = topicNameOriginDatasourceNames.get(topicName);
            if (CollectionUtils.isEmpty(set)) {
                set = new HashSet<>();
            }
            set.add(originDatasourceName);
            topicNameOriginDatasourceNames.put(topicName, set);
        }
    }

    public static void removeTopicOriginDatasourceName(String topicName, String originDatasourceName) {
        if (StringUtils.isEmpty(topicName) || StringUtils.isEmpty(originDatasourceName)) {
            return;
        }
        synchronized (GENERAL_CASE_TOPIC_DATASOURCE_LOCK_PREV + topicName) {
            Set<String> set = topicNameOriginDatasourceNames.get(topicName);
            if (CollectionUtils.isEmpty(set)) {
                set = new HashSet<>();
            }
            set.remove(originDatasourceName);
            topicNameOriginDatasourceNames.put(topicName, set);
        }
    }

}
