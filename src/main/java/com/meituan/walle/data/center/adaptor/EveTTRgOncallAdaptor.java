package com.meituan.walle.data.center.adaptor;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.walle.data.center.exception.RemoteErrorException;
import com.sankuai.carosscan.request.RgOnCallQueryRequest;
import com.sankuai.carosscan.response.RgOnCallInfoVO;
import com.sankuai.carosscan.service.IThriftTTRgOnCallService;
import com.sankuai.walleeve.thrift.response.EveThriftResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EveTTRgOncallAdaptor {

    @MdpThriftClient(remoteAppKey = "com.sankuai.carosscan.common.output", timeout = 2000)
    private IThriftTTRgOnCallService iThriftTTRgOnCallService;

    /**
     * 查询RG值班人员列表
     *
     * @param rgId
     */
    public List<String> queryRgOnCallUserList(Long rgId) {
        if (Objects.isNull(rgId)) {
            log.info("EveTTRgOncallAdaptor#queryRgOnCallUserList rgId is null");
            return new ArrayList<>();
        }
        RgOnCallQueryRequest request = RgOnCallQueryRequest.builder().rgId(rgId).build();
        log.info("EveTTRgOncallAdaptor#queryRgOnCallUserList, RgOnCallQueryRequest:{}", request);
        try {
            EveThriftResponse<RgOnCallInfoVO> response = iThriftTTRgOnCallService.queryRgOnCallUserList(request);
            log.info("EveTTRgOncallAdaptor#queryRgOnCallUserList, response:{}", response);
            if (Objects.isNull(response) || response.getCode() != 0 || Objects.isNull(response.getData())) {
                throw new RemoteErrorException("EveTTRgOncallAdaptor#queryRgOnCallUserList error");
            }
            return response.getData().getOnCallUserList();
        } catch (Exception e) {
            log.error("EveTTRgOncallAdaptor#queryRgOnCallUserList error", e);
        }

        return new ArrayList<>();
    }

}
