package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "vehicle_imu_hardbrake")
@Data
public class VehicleHardBrake {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 表record的record_name外键
     */
    private String recordName;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    private Date handBrakeTime;


    private String x;

    private String y;

    private String handBrakeInfo;

    private Integer recordDate;

    private Date createTime;

    private Date updateTime;

}