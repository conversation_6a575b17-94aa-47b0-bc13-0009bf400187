package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

public class RecordConfig {
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "record.pkg.index.bucket")
    public static volatile String recordPkgIndexBucket = "record-pkg-index";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "record.purpose.not.created.spark.task")
    public static volatile String RECORD_PURPOSE_NOT_CREATED_SPARK_TASK = "VEHICLE_SIMULATION";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "judge.record.spark.task.count")
    public static volatile Integer judgeRecordTaskCount = 50;
}
