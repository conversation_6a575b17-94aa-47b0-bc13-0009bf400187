package com.meituan.walle.data.center.config;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.walle.data.center.handle.impl.S3GovernanceHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2022/12/02
 */
@Slf4j
@Configuration
public class GovernanceConfig {
    private static final String NAMESPACE = "waimai";
    private static final String TOPIC_S3_GOVERNANCE_EVENT = "walle.data.center.s3.governance.event";

    private static final String TOPIC_S3_GOVERNANCE_COLD_RECORD_FILE_DTS_EVENT
            = "s3.governance.cold.record.file.dts.event";

    @Value("${appkey}")
    private String appkey;

    @Autowired
    private S3GovernanceHandler s3GovernanceHandler;

    @Bean(destroyMethod = "close")
    public IConsumerProcessor s3GovernanceEventConsumer() throws Exception {
        return buildS3GovernanceEventConsumer();
    }

    @Lazy
    @Bean(value = "s3GovernanceEventProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> s3GovernanceEventProducer() throws Exception {
        return buildProducer(TOPIC_S3_GOVERNANCE_EVENT, appkey);
    }

    private IProducerProcessor<String, String> buildProducer(String topic, String appKey) throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appKey);
        return MafkaClient.buildProduceFactory(properties, topic);
    }

    private IConsumerProcessor buildS3GovernanceEventConsumer() throws Exception {
        Properties properties = getProperties(appkey);
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, TOPIC_S3_GOVERNANCE_EVENT);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {
            String msg = (String) message.getBody();
            try {
                s3GovernanceHandler.s3GovernanceEventConsumer(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer s3 governance event, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        });
        return consumer;
    }


    @Bean(destroyMethod = "close")
    public IConsumerProcessor s3GovernanceColdRecordFileDtsEventConsumer() throws Exception {
        return buildS3GovernanceColdRecordFileDtsEventConsumer();
    }

    private IConsumerProcessor buildS3GovernanceColdRecordFileDtsEventConsumer() throws Exception {
        Properties properties = getProperties(appkey);
        IConsumerProcessor consumer = MafkaClient.buildCommonConsumerFactory(properties,
                TOPIC_S3_GOVERNANCE_COLD_RECORD_FILE_DTS_EVENT);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {
            String msg = (String) message.getBody();
            try {
                s3GovernanceHandler.s3GovernanceColdRecordFileDtsEventConsumer(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer s3 governance cold record file dts event, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        });
        return consumer;
    }

    private Properties getProperties(String appKey) {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appKey);
        properties.setProperty(ConsumerConstants.SubscribeGroup, appKey);
        properties.setProperty("fetch.message.max.bytes", "5242880");
        return properties;
    }
}