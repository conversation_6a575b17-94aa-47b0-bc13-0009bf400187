package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "sim_task_file")
public class SimTaskFile {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    private String taskId;
    private String adsId;
    private Integer loopIndex;
    private String fileName;
    private String filePath;
    private String s3Url;
    private Integer fileType;
    private Long fileSize;
    private Date createTime;
    private Date updateTime;
}
