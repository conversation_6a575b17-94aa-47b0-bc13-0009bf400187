package com.meituan.walle.data.center.config.mcc;

import com.dianping.lion.client.Lion;
import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/22
 */
@Component
public class FastUploadConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.module.list")
    public static volatile String VEHICLE_UPLOAD_REQUEST_MODULE_LIST =
            "Prediction,Canbus,Planning,Localization,Routing,ReferenceLine";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.status")
    public static volatile int VEHICLE_UPLOAD_REQUEST_STATUS = 5;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.operation.intervention.vin.list")
    public static volatile String VEHICLE_UPLOAD_OPERATION_VIN_LIST = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.measurement.time.lower")
    public static volatile int VEHICLE_UPLOAD_REQUEST_MEASUREMENT_TIME_LOWER = 8;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.measurement.time.upper")
    public static volatile int VEHICLE_UPLOAD_REQUEST_MEASUREMENT_TIME_UPPER = 2;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.operation.data.time.lower")
    public static volatile int VEHICLE_UPLOAD_REQUEST_OPERATION_DATA_TIME_LOWERE = 8;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.operation.data.time.upper")
    public static volatile int VEHICLE_UPLOAD_REQUEST_OPERATION_DATA_TIME_UPPER = 7;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.task.generate.step")
    public static volatile int VEHICLE_UPLOAD_REQUEST_TASK_GENERATE_STEP = 10;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.mstat.upload.task.start.padding")
    public static volatile int VEHICLE_MSTAT_UPLOAD_TASK_START_PADDING = 15;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.mstat.upload.task.end.padding")
    public static volatile int VEHICLE_MSTAT_UPLOAD_TASK_END_PADDING = 5;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.task.generate.times")
    public static volatile int VEHICLE_UPLOAD_REQUEST_TASK_GENERATE_TIMES = 3;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.task.accident.members")
    public static volatile String VEHICLE_UPLOAD_REQUEST_TASK_ACCIDENT_MEMBERS = "xushibiao,fuyuanbo,liuqichun";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.accident.detail.url")
    public static volatile String VEHICLE_UPLOAD_REQUEST_ACCIDENT_DETAIL_URL
            = "https://walledata.mad.test.sankuai.com/accident";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.task.accident.required.modules")
    public static volatile String VEHICLE_UPLOAD_REQUEST_TASK_ACCIDENT_REQUIRED_MODULES
            = "Prediction,Canbus,Planning,Localization,Routing,Control,Perception";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.map.data.extra.info")
    public static volatile String VEHICLE_UPLOAD_MAP_DATA_EXTRA_INFO = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.max.concurrent.num")
    public static volatile int MAX_CONCURRENT_NUM = 30;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.accident.data.extra.info")
    public static volatile String VEHICLE_UPLOAD_ACCIDENT_DATA_EXTRA_INFO = "{\"max_concurrent_num\":1}";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.case.data.extra.info")
    public static volatile String VEHICLE_UPLOAD_CASE_DATA_EXTRA_INFO = "{\"max_concurrent_num\":1}";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.time.after")
    public static volatile int ACCIDENT_TIME_AFTER = 5;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.time.before")
    public static volatile int ACCIDENT_TIME_BEFORE = 10;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.task.extra.modules")
    public static volatile String ACCIDENT_TASK_EXTRA_MODULES = "GnssDriver";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "event.task.extra.modules")
    public static volatile String EVENT_TASK_EXTRA_MODULES = "GnssDriver";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.pnc.modules")
    public static volatile String VEHICLE_UPLOAD_PNC_MODULES = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.pnc.time.lower")
    public static volatile int VEHICLE_UPLOAD_PNC_LOWER_TIME = 120;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.pnc.time.upper")
    public static volatile int VEHICLE_UPLOAD_PNC_UPPER_TIME = 0;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.pnc.vin")
    public static volatile String VEHICLE_UPLOAD_PNC_VIN = "LMTZSV028NC000930";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.base.extra.info")
    public static volatile String BASE_TASK_EXTRA_INFO = "";

    /**
     * 14day
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "base.data.upload.task.expiration.period")
    public static volatile long BASE_DATA_UPLOAD_TASK_EXPIRATION_PERIOD = 1_209_600_000;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "base.data.record.end.time.from.current.time.period")
    public static volatile long BASE_DATA_RECORD_END_TIME_FROM_CURRENT_TIME_PERIOD = 7200_000L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "base.data.rec.file.recovery.rate")
    public static volatile double BASE_DATA_REC_FILE_RECOVERY_RATE = 0.995d;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "base.data.integrity.check.threshold.nano")
    public static volatile long BASE_DATA_INTEGRITY_CHECK_THRESHOLD_NANO = 500_000_000L;

    public static volatile Map<String, Long> VEHICLE_UPLOAD_REQUEST_VALIDATE_CONTINUITY =
            Lion.getMap(MccConstant.MCC_CLIENT_ID, "vehicle.upload.request.validate.continuity", Long.class);

    public static volatile Map<String, Long> VEHICLE_UPLOAD_REQUEST_VALIDATE_COMPLETENESS =
            Lion.getMap(MccConstant.MCC_CLIENT_ID, "vehicle.upload.request.validate.completeness", Long.class);

    public static volatile Boolean VEHICLE_UPLOAD_REQUEST_VALIDATE_MODE_WHITELIST =
            Lion.getBoolean(MccConstant.MCC_CLIENT_ID, "vehicle.upload.request.validate.mode.whitelist", true);

    public static volatile List<String> VEHICLE_UPLOAD_REQUEST_VALIDATE_BLACKLIST =
            Lion.getList(MccConstant.MCC_CLIENT_ID, "vehicle.upload.request.validate.blacklist", String.class);

    public static volatile List<String> VEHICLE_UPLOAD_REQUEST_VALIDATE_WHITELIST =
            Lion.getList(MccConstant.MCC_CLIENT_ID, "vehicle.upload.request.validate.whitelist", String.class);

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.special.record.names")
    public static volatile String SPECIAL_RECORD_NAMES = "20220828_074048_s20-288";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.request.maeb.extra.modules")
    public static volatile String MAEB_EXTRA_MODULES = "PerceptionCamera,RadarTransform";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "fast.upload.management.page.url")
    public static volatile String FAST_UPLOAD_URL = "https://walle.sankuai.com/app/recycle/upload?creator=";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "fast.upload.re.notice.members")
    public static volatile String RE_MEMBERS = "zhangxin180,liuyang422,dingfukang,huangjinzhong,shennanyang02,niekexin";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "fast.upload.launch.failed.prev.second")
    public static volatile Integer LAUNCH_FAILED_PREV_SECOND = 5;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "fast.upload.launch.failed.post.second")
    public static volatile Integer LAUNCH_FAILED_POST_SECOND = 5;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "fast.upload.collision.prev.second")
    public static volatile Integer COLLISION_PREV_SECOND = 10;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "fast.upload.collision.post.second")
    public static volatile Integer COLLISION_POST_SECOND = 10;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "fast.upload.task.max.duration.minutes")
    public static volatile String TASK_MAX_DURATION_MINUTES_MAP = "{4: 10, 8: 480}";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "maeb.to.case.event")
    public static volatile String MAEB_TO_CASE_EVENT = "START_BRAKE,CONTROL_SAFETY_BRAKE_WITH_MAEB";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "fast.upload.online.display.url")
    public static volatile String ONLINE_DISPLAY_URL = "https://spider-mad.sankuai.com/gstatus/static/staff/html/"
        + "dataRecycle.html#/?vin=";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.v2.vin.whitelist")
    public static volatile String V2_VIN_WHITELIST = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.hdmap_exception.switch")
    public static volatile Boolean HD_MAP_EXCEPTION_SWITCH = true;


    public static final String MAX_CONCURRENT_NUM_FIELD = "max_concurrent_num";

    public static final String SPEED_LIMIT_SYSTEM_ID = "auto_data_platfrom";

    // 下发任务的延迟, endTime + DELAY_MILLISECONDS < currentTime
    public static final Long DELAY_MILLISECONDS = 5000L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.eu.latency.modules")
    public static volatile String EU_LATENCY_MODULES = "ScheduleNode";

}
