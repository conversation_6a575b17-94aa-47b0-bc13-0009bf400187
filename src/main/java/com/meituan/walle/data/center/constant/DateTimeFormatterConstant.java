package com.meituan.walle.data.center.constant;

import java.time.format.DateTimeFormatter;

public interface DateTimeFormatterConstant {
    DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");
    DateTimeFormatter yyyyMMddHHmmssSSS = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    DateTimeFormatter yyyyMMddHHmmssDotSSS = DateTimeFormatter.ofPattern("yyyyMMddHHmmss.SSS");
    String ymdhms = "yyyy-MM-dd HH:mm:ss";
    String ymd = "yyyyMMdd";
    String y_m_d = "yyyy-MM-dd";
    String yyyyMMddHHmmssSSS_Str = "yyyyMMddHHmmssSSS";
    String yyyyMMddHHmmss_Str = "yyyyMMddHHmmss";
    String YMDHMSDotSSS_Str = "yyyy-MM-dd HH:mm:ss.SSS";
    String md = "MMdd";
    String hm = "HHmm";
    DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    DateTimeFormatter DATE_DAY_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

}
