package com.meituan.walle.data.center.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.vo.VehicleDiskMountInfoVO;
import com.meituan.walle.data.center.service.VehicleDiskMountInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@InterfaceDoc(
        displayName = "磁盘挂载信息接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供磁盘挂载信息的新增、状态修改",
        scenarios = "对磁盘挂载信息表进行操作，数据新增、状态修改"
)
@Slf4j
@RestController
@RequestMapping("/vehicle_disk_mount_info")
public class VehicleDiskMountInfoController {

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private VehicleDiskMountInfoService vehicleDiskMountInfoService;

    @MethodDoc(
            requestMethods = {HttpMethod.POST},
            displayName = "新增磁盘挂载信息",
            description = "磁盘挂载信息入库",
            parameters = {
                    @ParamDoc(name = "vehicleDiskMountInfoVO", description = "磁盘挂载信息对象",
                            requiredness = Requiredness.REQUIRED)
            },
            returnValueDescription = "调用成功与否的Response对象，包含code、message等信息"
    )
    @PostMapping("/add")
    public Object add(VehicleDiskMountInfoVO vo) {
        String success;
        try {
            Map<String, Object> ret = vehicleDiskMountInfoService.insert(vo);
            boolean exeSuccess = (boolean) ret.get("success");
            String exeMsg = ret.get("msg").toString();
            success = exeSuccess ? "success" : exeMsg;
        } catch (Exception ex) {
            log.error("insert exception", ex);
            success = ex.getMessage();
        }
        return Response.succ(success);
    }

    @MethodDoc(
            requestMethods = {HttpMethod.POST},
            displayName = "根据磁盘ID更新be_send状态",
            description = "根据磁盘ID更新表中的be_send状态，从0更新为1",
            parameters = {
                    @ParamDoc(name = "jsonStr", description = "json字符串参数", requiredness = Requiredness.REQUIRED)
            },
            restExamplePostData = "{\"diskId\": \"55cd2e415367aaf0\"}",
            returnValueDescription = "调用成功与否的Response对象，包含code、message等信息"
    )
    @PostMapping("/be_send_update")
    public Object beSendUpdate(@RequestBody String jsonStr) {
        String success;
        try {
            String diskId = objectMapper.readTree(jsonStr).get("diskId").asText();
            Map<String, Object> ret = vehicleDiskMountInfoService.beSendUpdate(diskId);
            String exeMsg = ret.get("msg").toString();
            boolean exeSuccess = (boolean) ret.get("success");
            success = exeSuccess ? "success" : exeMsg;
        } catch (Exception ex) {
            log.error("beSendUpdate exception", ex);
            success = ex.getMessage();
        }
        return Response.succ(success);
    }

    @MethodDoc(
            requestMethods = {HttpMethod.POST},
            displayName = "根据磁盘ID更新批次ID",
            description = "根据磁盘ID更新表中的批次ID",
            parameters = {
                    @ParamDoc(name = "jsonStr", description = "json字符串参数", requiredness = Requiredness.REQUIRED)
            },
            restExamplePostData = "{\"bid\": \"6cb1231ba1a945f8b349a6f0c11340b6\", \"diskId\": \"55cd2e415367aaf0\"}",
            returnValueDescription = "调用成功与否的Response对象，包含code、message等信息"
    )
    @PostMapping("/bid_update")
    public Object bidUpdate(@RequestBody String jsonStr) {
        String success;
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            String bid = jsonNode.get("bid").asText();
            String diskId = jsonNode.get("diskId").asText();
            Map<String, Object> ret = vehicleDiskMountInfoService.bidUpdate(bid, diskId);
            String exeMsg = ret.get("msg").toString();
            boolean exeSuccess = (boolean) ret.get("success");
            success = exeSuccess ? "success" : exeMsg;
        } catch (Exception ex) {
            log.error("bidUpdate exception", ex);
            success = ex.getMessage();
        }
        return Response.succ(success);
    }

}