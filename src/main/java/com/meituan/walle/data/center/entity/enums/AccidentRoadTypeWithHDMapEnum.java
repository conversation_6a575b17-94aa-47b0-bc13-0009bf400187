package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/01/04
 */

@Getter
@AllArgsConstructor
public enum AccidentRoadTypeWithHDMapEnum {

    PUBLIC_MAIN_ROAD("公开道路主路", AccidentRoadTypeEnum.PUBLIC_MAIN_ROAD),
    PUBLIC_SIDE_ROAD("公开道路辅路", AccidentRoadTypeEnum.PUBLIC_SIDE_ROAD),
    CROSSROAD_WITH_LIGHT("公开道路交叉口-有灯", AccidentRoadTypeEnum.CROSSROAD_WITH_LIGHT),
    CROSSROAD_WITHOUT_LIGHT("公开道路交叉口-无灯", AccidentRoadTypeEnum.CROSSROAD_WITHOUT_LIGHT),
    PUBLIC_PARKING("公开道路停车区域", AccidentRoadTypeEnum.PUBLIC_PARKING),
    SCHOOL_ROAD("校园道路", AccidentRoadTypeEnum.INTERNAL_ROAD),
    SCHOOL_CROSSROAD("校园道路交叉口", AccidentRoadTypeEnum.INTERNAL_CROSSROAD),
    SCHOOL_PARKING("校园道路停车区域", AccidentRoadTypeEnum.INTERNAL_PARKING),
    ;
    private String HDMapMsg;
    private AccidentRoadTypeEnum roadTypeEnum;

    public static AccidentRoadTypeEnum getAccidentRoadTypeByHDMapMsg(String msg) {
        for (AccidentRoadTypeWithHDMapEnum roadTypeWithHDMapEnum : AccidentRoadTypeWithHDMapEnum.values()) {
            if (roadTypeWithHDMapEnum.getHDMapMsg().equals(msg)) {
                return roadTypeWithHDMapEnum.getRoadTypeEnum();
            }
        }
        return AccidentRoadTypeEnum.OTHER;
    }
}
