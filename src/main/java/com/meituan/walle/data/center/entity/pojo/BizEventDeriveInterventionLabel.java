package com.meituan.walle.data.center.entity.pojo;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/08/13
 */
@Table(name = "biz_event_derive_intervention_label")
public class BizEventDeriveInterventionLabel {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 接管时间
     */
    @Column(name = "intervention_time")
    private Date interventionTime;

    /**
     * 根据record_name + intervention_time生成的caseId
     */
    @Column(name = "event_id")
    private String eventId;

    /**
     * 标签类型
     * 1 tag，2 系统接管，3 终点前接管，4 电子围栏接管， 5 进场接管，6 远遥接管
     */
    @Column(name = "label_type")
    private Byte labelType;

    /**
     * 标签名称
     */
    @Column(name = "label_name")
    private String labelName;

    /**
     * 区分标签是自动打的还是人工打的 0 自动，1 人工
     */
    private Byte source;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    public BizEventDeriveInterventionLabel() {
        // default constructor
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRecordName() {
        return recordName;
    }

    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    public Date getInterventionTime() {
        return interventionTime;
    }

    public void setInterventionTime(Date interventionTime) {
        this.interventionTime = interventionTime;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public Byte getLabelType() {
        return labelType;
    }

    public void setLabelType(Byte labelType) {
        this.labelType = labelType;
    }

    public String getLabelName() {
        return labelName;
    }

    public void setLabelName(String labelName) {
        this.labelName = labelName;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BizEventDeriveInterventionLabel that = (BizEventDeriveInterventionLabel) o;
        return eventId.equals(that.eventId) &&
                labelType.equals(that.labelType) &&
                labelName.equals(that.labelName) &&
                source.equals(that.source);
    }

    @Override
    public int hashCode() {
        return Objects.hash(eventId, labelType, labelName, source);
    }

    public interface BizEventDeriveInterventionLabelBuilder {
        BizEventDeriveInterventionLabelBuilder recordName(String value);
        BizEventDeriveInterventionLabelBuilder interventionTime(Date value);
        BizEventDeriveInterventionLabelBuilder eventId(String value);
        BizEventDeriveInterventionLabelBuilder labelType(Byte value);
        BizEventDeriveInterventionLabelBuilder labelName(String value);
        BizEventDeriveInterventionLabelBuilder source(Byte value);
        BizEventDeriveInterventionLabel build();
    }

    public static final class DefaultBizEventDeriveInterventionLabelBuilder
            implements BizEventDeriveInterventionLabelBuilder {

        private BizEventDeriveInterventionLabel bizEventDeriveInterventionLabel =
                new BizEventDeriveInterventionLabel();

        @Override
        public BizEventDeriveInterventionLabelBuilder recordName(String value) {
            bizEventDeriveInterventionLabel.setRecordName(value);
            return this;
        }

        @Override
        public BizEventDeriveInterventionLabelBuilder interventionTime(Date value) {
            bizEventDeriveInterventionLabel.setInterventionTime(value);
            return this;
        }

        @Override
        public BizEventDeriveInterventionLabelBuilder eventId(String value) {
            bizEventDeriveInterventionLabel.setEventId(value);
            return this;
        }

        @Override
        public BizEventDeriveInterventionLabelBuilder labelType(Byte value) {
            bizEventDeriveInterventionLabel.setLabelType(value);
            return this;
        }

        @Override
        public BizEventDeriveInterventionLabelBuilder labelName(String value) {
            bizEventDeriveInterventionLabel.setLabelName(value);
            return this;
        }

        @Override
        public BizEventDeriveInterventionLabelBuilder source(Byte value) {
            bizEventDeriveInterventionLabel.setSource(value);
            return this;
        }

        @Override
        public BizEventDeriveInterventionLabel build() {
            return bizEventDeriveInterventionLabel;
        }
    }
}
