package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/14 下午3:21
 * Description:
 * Modified by
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CasesPO {

    private Long id;
    private String caseId;
    private String sourceId;
    private String source;
    private Date caseTime;
    private String recordName;
    private String description;
    private String responsible;
    private String vin;
    private String vehicleName;
    private Integer vehicleType;
    private String longitude;
    private String latitude;
    private String x;
    private String y;
    private String onesProjectId;
    private String onesIssueId;
    private Integer dateKey;
    private Integer status;
    private String simVerifiedState;
    private String level;
    private String simVerifiedDetail;
    private Integer channel;
    private String uploadTaskId;
    private Integer playStatus;
    private Timestamp createTime;
    private Timestamp updateTime;

}
