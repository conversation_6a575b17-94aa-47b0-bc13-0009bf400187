package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "biz_remote_video")
@Data
public class BizRemoteVideo {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 远遥视频任务唯一id
     */
    private String subTaskId;

    /**
     * 指定获取的视频类型
     */
    private String stream;

    /**
     * 视频文件名
     */
    private String fileName;

    /**
     * 视频s3地址
     */
    private String s3Url;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
