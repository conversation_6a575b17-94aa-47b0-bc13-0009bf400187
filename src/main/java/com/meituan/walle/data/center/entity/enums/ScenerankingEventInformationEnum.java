package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/07/03
 */
@Getter
@AllArgsConstructor
public enum ScenerankingEventInformationEnum {

    ACCIDENT_DETECTION(1001, "accident-detection", "事故检测", 5001),
    FAULT_DETECTION(1002, "fault-detection", "故障", 5002),
    TRAFFIC_JAM(1003, "traffic-jam", "多车扎堆开始", 5003),
    TRAFFIC_JAM_END(1004, "traffic-jam-end", "多车扎堆结束", 5004),
    ACCIDENT_STRATIFY(1005, "accident-stratify", "事故检测分层", 5005),
    ADC_STAGNANT_RECALL(1006, "adc-stagnant-recall", "停滞不前开始", 5006),
    ADC_STAGNANT_LIFTED(1007, "adc-stagnant-lifted", "停滞不前结束", 5007),
    ;

    private int code;
    private String name;
    private String desc;
    private int eventCenterCode;

    public static ScenerankingEventInformationEnum byCode(int code) {
        for (ScenerankingEventInformationEnum en : ScenerankingEventInformationEnum.values()) {
            if (en.code == code) {
                return en;
            }
        }
        return null;
    }

    public static ScenerankingEventInformationEnum byName(String name) {
        for (ScenerankingEventInformationEnum en : ScenerankingEventInformationEnum.values()) {
            if (en.name.equals(name)) {
                return en;
            }
        }
        return null;
    }

}
