package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Passage {
    /**
     * 自增主键
     */
    @Id
    @Column(name = "primary_id")
    private Long primaryId;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 表road_segment中id的外键
     */
    @Column(name = "road_segment_id")
    private String roadSegmentId;

    /**
     * 代码生成，用于关联lane_segment，不对外展示
     */
    @GeneratedValue(generator = "JDBC")
    private transient String id;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 获取自增主键
     *
     * @return primary_id - 自增主键
     */
    public Long getPrimaryId() {
        return primaryId;
    }

    /**
     * 设置自增主键
     *
     * @param primaryId 自增主键
     */
    public void setPrimaryId(Long primaryId) {
        this.primaryId = primaryId;
    }

    /**
     * 获取表record的record_name外键
     *
     * @return record_name - 表record的record_name外键
     */
    public String getRecordName() {
        return recordName;
    }

    /**
     * 设置表record的record_name外键
     *
     * @param recordName 表record的record_name外键
     */
    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    /**
     * 获取表road_segment中id的外键
     *
     * @return road_segment_id - 表road_segment中id的外键
     */
    public String getRoadSegmentId() {
        return roadSegmentId;
    }

    /**
     * 设置表road_segment中id的外键
     *
     * @param roadSegmentId 表road_segment中id的外键
     */
    public void setRoadSegmentId(String roadSegmentId) {
        this.roadSegmentId = roadSegmentId;
    }

    /**
     * 获取代码生成，用于关联lane_segment，不对外展示
     *
     * @return id - 代码生成，用于关联lane_segment，不对外展示
     */
    public String getId() {
        return id;
    }

    /**
     * 设置代码生成，用于关联lane_segment，不对外展示
     *
     * @param id 代码生成，用于关联lane_segment，不对外展示
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}