package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/11/5
 */
@Table(name = "biz_file_info")
@Data
public class BizFileInfo {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 文件名称
     */
    @Column(name = "file_name")
    private String fileName;

    /**
     * 文件类型:-1unknown|0txt|1rec|2img|3video|4bin
     */
    @Column(name = "file_type")
    private int fileType;

    /**
     * 文件大小
     */
    @Column(name = "file_size")
    private Long fileSize;

    /**
     * 文件存储路径
     */
    @Column(name = "file_url")
    private String fileUrl;

    /**
     * 关联id
     */
    @Column(name = "ref_id")
    private String refId;

    /**
     * 来源
     */
    @Column(name = "source")
    private String source;

    /**
     * 备注
     */
    @Column(name = "memo")
    private String memo;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
