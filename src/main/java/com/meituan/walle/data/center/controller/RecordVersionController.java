package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.entity.po.RecordVersionPO;
import com.meituan.walle.data.center.entity.vo.RecordVersionFileListVO;
import com.meituan.walle.data.center.entity.vo.RecordVersionVO;
import com.meituan.walle.data.center.service.RecordVersionService;
import com.meituan.walle.data.center.util.Trace;
import com.sankuai.walle.wcdp.core.entity.request.RespCodeEnum;
import com.sankuai.walle.wcdp.core.entity.request.RespContent;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/record_version")
public class RecordVersionController {

    @Autowired
    private RecordVersionService recordVersionService;

    @Autowired
    private HttpServletResponse response;

    @MethodDoc(
            displayName = "生成record版本信息",
            description = "生成record版本信息并入库，返回record版本信息，详细定义见 https://km.sankuai.com/page/1286672303",
            parameters = {
                    @ParamDoc(name = "recordVersionVOList", description = "生成record版本信息的原始数据，可批量传参")
            },
            returnValueDescription = "record版本信息"
    )
    @PostMapping("/insert_batch")
    public Object insertBatch(@RequestBody List<RecordVersionVO> recordVersionVOList) {
        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();
        String api = "insert_batch";
        Transaction t = Cat.newTransaction(CatConstant.API, "/record_version/" + api);
        log.info("traceId: 【{}】, received new request for 【{}】 , request param is: 【{}】.",
                traceId, api, recordVersionVOList);
        response.addHeader("TraceId", traceId);

        if (recordVersionVOList == null || recordVersionVOList.isEmpty()) {
            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setSuccessStatus();
            t.complete();
            log.info("traceId: 【{}】, complete 【{}】 request, overall cost 【{}】 ms.", traceId, api, durationTime);
            return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY,
                    String.format("【%s】 request fail, request param is: 【%s】.", api, recordVersionVOList));
        }
        if (recordVersionVOList.size() > 100) {
            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setSuccessStatus();
            t.complete();
            log.info("traceId: 【{}】, complete 【{}】 request, overall cost 【{}】 ms.", traceId, api, durationTime);
            return RespContent.<Object>success(RespCodeEnum.PARAMS_PROBLEM,
                    String.format("【%s】 request fail, param list size is too large, the max size limit is 100, " +
                            "request param is: 【%s】.", api, recordVersionVOList));
        }

        Map<String, List<RecordVersionVO>> resultMap;
        try {
            resultMap = recordVersionService.insertRecordVersionBatch(recordVersionVOList);

            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setSuccessStatus();
            log.info("traceId: 【{}】, complete 【{}】 request, overall cost 【{}】 ms.", traceId, api, durationTime);
            return RespContent.<Object>success(resultMap);
        } catch (Exception e) {
            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setStatus(e);
            Cat.logError(e);
            log.error("traceId: 【{}】, 【{}】 request fail, request param is: 【{}】.",
                    traceId, api, recordVersionVOList, e);
            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("【%s】 request fail, error info: 【%s】.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "查询record版本信息",
            description = "根据versionId查询record版本信息，详细定义见 https://km.sankuai.com/page/1286672303",
            parameters = {
                    @ParamDoc(name = "versionIdList", description = "待查询的versionId列表，可批量传参")
            },
            returnValueDescription = "record版本信息"
    )
    @PostMapping("/status_batch")
    public Object statusBatch(@RequestBody List<String> versionIdList) {
        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();
        String api = "status_batch";
        Transaction t = Cat.newTransaction(CatConstant.API, "/record_version/" + api);
        log.info("traceId: 【{}】, received new request for 【{}】 , request param is: 【{}】.",
                traceId, api, versionIdList);
        response.addHeader("TraceId", traceId);

        if (versionIdList == null || versionIdList.isEmpty()) {
            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setSuccessStatus();
            t.complete();
            log.info("traceId: 【{}】, complete 【{}】 request, overall cost 【{}】 ms.", traceId, api, durationTime);
            return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY,
                    String.format("【%s】 request fail, request param is: 【%s】.", api, versionIdList));
        }
        if (versionIdList.size() > 100) {
            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setSuccessStatus();
            t.complete();
            log.info("traceId: 【{}】, complete 【{}】 request, overall cost 【{}】 ms.", traceId, api, durationTime);
            return RespContent.<Object>success(RespCodeEnum.PARAMS_PROBLEM,
                    String.format("【%s】 request fail, param list size is too large, the max size limit is 100, " +
                            "request param is: 【%s】.", api, versionIdList));
        }

        List<RecordVersionPO> resultList;
        try {
            resultList = recordVersionService.listRecordVersionByVersionId(versionIdList);

            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setSuccessStatus();
            log.info("traceId: 【{}】, complete 【{}】 request, overall cost 【{}】 ms.", traceId, api, durationTime);
            return RespContent.<Object>success(resultList);
        } catch (Exception e) {
            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setStatus(e);
            Cat.logError(e);
            log.error("traceId: 【{}】, 【{}】 request fail, request param is: 【{}】.",
                    traceId, api, versionIdList, e);
            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("【%s】 request fail, error info: 【%s】.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "获取record版本对应的文件列表",
            description = "根据versionId与moduleList，获取对应的record版本文件列表，" +
                    "详细定义见 https://km.sankuai.com/page/1286672303",
            parameters = {
                    @ParamDoc(name = "recordVersionVOList", description = "需要获取对应文件的versionId列表，可批量传参")
            },
            returnValueDescription = "record版本文件列表"
    )
    @PostMapping("/file_list_batch")
    public Object fileListBatch(@RequestBody List<RecordVersionVO> recordVersionVOList) {
        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();
        String api = "file_list_batch";
        Transaction t = Cat.newTransaction(CatConstant.API, "/record_version/" + api);
        log.info("traceId: 【{}】, received new request for 【{}】 , request param is: 【{}】.",
                traceId, api, recordVersionVOList);
        response.addHeader("TraceId", traceId);

        if (recordVersionVOList == null || recordVersionVOList.isEmpty()) {
            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setSuccessStatus();
            t.complete();
            log.info("traceId: 【{}】, complete 【{}】 request, overall cost 【{}】 ms.", traceId, api, durationTime);
            return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY,
                    String.format("【%s】 request fail, request param is: 【%s】.", api, recordVersionVOList));
        }
        if (recordVersionVOList.size() > 100) {
            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setSuccessStatus();
            t.complete();
            log.info("traceId: 【{}】, complete 【{}】 request, overall cost 【{}】 ms.", traceId, api, durationTime);
            return RespContent.<Object>success(RespCodeEnum.PARAMS_PROBLEM,
                    String.format("【%s】 request fail, param list size is too large, the max size limit is 100, " +
                            "request param is: 【%s】.", api, recordVersionVOList));
        }
        for (RecordVersionVO recordVersionVO : recordVersionVOList) {
            if (recordVersionVO.getVersionId() == null || recordVersionVO.getVersionId().isEmpty()) {
                long durationTime = System.currentTimeMillis() - startTime.getMillis();
                t.setDurationInMillis(durationTime);
                t.setSuccessStatus();
                t.complete();
                log.info("traceId: 【{}】, complete 【{}】 request, overall cost 【{}】 ms.", traceId, api, durationTime);
                return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY,
                        String.format("【%s】 request fail, param versionId cannot be empty, request param is: 【%s】.",
                                api, recordVersionVOList));
            }
        }

        List<RecordVersionFileListVO> resultList;
        try {
            resultList = recordVersionService.fileListBatch(recordVersionVOList);

            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setSuccessStatus();
            log.info("traceId: 【{}】, complete 【{}】 request, overall cost 【{}】 ms.", traceId, api, durationTime);
            return RespContent.<Object>success(resultList);
        } catch (Exception e) {
            long durationTime = System.currentTimeMillis() - startTime.getMillis();
            t.setDurationInMillis(durationTime);
            t.setStatus(e);
            Cat.logError(e);
            log.error("traceId: 【{}】, 【{}】 request fail, request param is: 【{}】.",
                    traceId, api, recordVersionVOList, e);
            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("【%s】 request fail, error info: 【%s】.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }

}