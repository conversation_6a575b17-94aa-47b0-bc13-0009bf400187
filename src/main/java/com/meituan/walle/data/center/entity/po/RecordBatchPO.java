package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/10 上午11:33
 * Description:
 * Modified by
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RecordBatchPO {

    private Long id;
    private String bid;
    private String did;
    private Date recordFinishTime;
    private Date checkinTime;
    private Date procDoneTime;
    private Long used;
    private Long dataSize;
    private Long capacity;
    private Integer recordCount;
    private Integer status;
    private Timestamp createTime;
    private Timestamp updateTime;

}
