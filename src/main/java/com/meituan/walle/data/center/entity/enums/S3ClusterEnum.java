package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum S3ClusterEnum {

    BJ_TEST(-2, "bj_test", "北京测试环境"),
    MIGRATE(-1, "migrate", "迁移阶段双写"),
    BJ_BAK(0, "bj_bak", "北京备份"),
    ZW_MAD(1, "zw_mad", "中卫自动车"),
    ;

    private int code;
    private String name;
    private String desc;

    public static S3ClusterEnum byCode(int code) {
        for (S3ClusterEnum en : S3ClusterEnum.values()) {
            if (en.code == code) {
                return en;
            }
        }
        return null;
    }

    public static S3ClusterEnum byName(String name) {
        for (S3ClusterEnum en : S3ClusterEnum.values()) {
            if (en.name.equals(name)) {
                return en;
            }
        }
        return null;
    }

}
