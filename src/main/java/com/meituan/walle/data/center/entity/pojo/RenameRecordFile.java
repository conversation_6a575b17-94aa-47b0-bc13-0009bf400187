package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Table(name = "rename_record_file")
public class RenameRecordFile {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 原文件名
     */
    @Column(name = "source_file")
    private String sourceFile;

    /**
     * rename文件名
     */
    @Column(name = "dest_file")
    private String destFile;

    /**
     * 0.初始|10.补全
     */
    private Short status;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取表record的record_name外键
     *
     * @return record_name - 表record的record_name外键
     */
    public String getRecordName() {
        return recordName;
    }

    /**
     * 设置表record的record_name外键
     *
     * @param recordName 表record的record_name外键
     */
    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    /**
     * 获取原文件名
     *
     * @return source_file - 原文件名
     */
    public String getSourceFile() {
        return sourceFile;
    }

    /**
     * 设置原文件名
     *
     * @param sourceFile 原文件名
     */
    public void setSourceFile(String sourceFile) {
        this.sourceFile = sourceFile;
    }

    /**
     * 获取rename文件名
     *
     * @return dest_file - rename文件名
     */
    public String getDestFile() {
        return destFile;
    }

    /**
     * 设置rename文件名
     *
     * @param destFile rename文件名
     */
    public void setDestFile(String destFile) {
        this.destFile = destFile;
    }

    /**
     * 获取0.初始|10.补全
     *
     * @return status - 0.初始|10.补全
     */
    public Short getStatus() {
        return status;
    }

    /**
     * 设置0.初始|10.补全
     *
     * @param status 0.初始|10.补全
     */
    public void setStatus(Short status) {
        this.status = status;
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}