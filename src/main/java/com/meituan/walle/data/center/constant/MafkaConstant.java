package com.meituan.walle.data.center.constant;

/**
 * <AUTHOR>
 * @date 2023/3/31 19:45
 */
public class MafkaConstant {
    public static final int MAX_DELAY_MILLIS = 10000_000;

    public static final String NAMESPACE = "waimai";

    public static final String TOPIC_TO_GENERAL_CASE = "com.sankuai.walledata.data.center.to.general.case";

    public static final String TOPIC_RECORD_HDFS_SYNC_NOTICE = "walle.data.center.record.hdfs.sync.notice";

    public static final String TOPIC_EVENT_SPECIFIC_EXPORT_TO_HIVE = "event.specific.export.to.hive";

    public static final String TOPIC_ONBOARD_FILE_RERUN = "flink.etl.onboard.event.rerun";

    public static final String ETL_APP_KEY = "com.sankuai.mad.walle.wcdpetl";

    public static final String TOPIC_DISK_STATUS = "walle.data.center.disk.status";

    public static final String TOPIC_DISK_STATUS_CONSUMER = "com.sankuai.walledata.data.center.consumer";

    public static final String TOPIC_MVIZ_RECORDER_TASK_COLLECTOR = "mviz_recorder_task_collector";

    public static final String TOPIC_SNAPHSOT = "walle.data.center.snapshot";

    public static final String TOPIC_RECORD = "walle.data.center.record";

    public static final String TOPIC_RECORD_FILE = "walle.data.center.record_file";

    public static final String TOPIC_RECORD_CIRCULATION = "walle.data.center.record.circulation";

    public static final String TOPIC_ONBOARD_FILE_METADATA = "walle.data.center.onboard.file.metadata";

    public static final String TOPIC_REALTIME_RECORD = "walle.data.center.realtime.record";

    public static final String TOPIC_ONBOARD_EVENT = "walle.data.center.onboard.event";

    public static final String TOPIC_USER_FAST_TASK_NOTICE = "walle.data.center.fast.task.notice";

    public static final String TOPIC_INTERVENTION_EVENT_MATCH_RESULT = "walle.data.intervention_event_match_result";

    public static final String TOPIC_DELAY_PULL_REMOTE_VIDEO_EVENT = "walle.data.center.delay.pull.remote.video.event";

    public static final String TOPIC_INTERVENTION_MILEAGE_DELAY = "walle.data.center.intervention.mileage.delay";

    public static final String TOPIC_JUPITER_CALLBACK = "walle.data.center.jupiter.callback";

    public static final String TOPIC_AUK_ONBOARD_EVENT = "walle.data.auk.onboard.event";

    public static final String TOPIC_CASE_INFO_AGGREGATION = " walle.data.center.case.info.aggregation";

    public static final String TOPIC_JUDGE_PERSON_INTERVENTION_MSG = "walle.data.center.judge.person.intervention.msg";

    public static final String TOPIC_SAFEGUARD_SYSTEM_EVENT_NOTICE = "walle.sceneranking.event.information";

    public static final String TOPIC_RECORD_FOR_APP = "walle.data.record.for.app";

    public static final String TOPIC_SNAPSHOT_NOTICE = "walle.disk.server.snapshot";

    public static final String TOPIC_UPLOADED_RECORD = "walle.data.uploaded.record";

    public static final String TOPIC_AUTODRIVE_EVENT = "walle.data.autodrive.event";

    public static final String TOPIC_INTERVENTION_LOG = "walle.data.event.intervention.log";

    public static final String TOPIC_SNAPSHOT_NOTICE_CONSUMER = "com.sankuai.walledata.data.center.consumer";

    public static final String RECORD_FOR_APP_CASE_SORTABLE_CONSUMER_GROUP
            = "record_for_app_case_sortable_consumer_group";

    public static final String RECORD_FOR_APP_GEN_CORE_DUMP_CASE_CONSUMER_GROUP
            = "record_for_app_gen_core_dump_case_consumer_group";

    public static final String RECORD_INFO_EXTRACTOR_CONSUMER_GROUP = "record_info_extractor_consumer_group";

    public static final String TOPIC_SCENERANKING_EVENT_INFORMATION = "walle.sceneranking.event.information";

    public static final String TOPIC_SCENERANKING_EVENT_INFORMATION_CONSUMER = "com.sankuai.walledata.data.center.consumer";

    /**
     * 输出事故消息
     */
    public static final String TOPIC_OUTPUT_ACCIDENT_MESSAGE = "data.center.output.accident.message";

    public static final String TOPIC_S3_CLEANUP_FILE = "walle.data.s3.cleanup.file";

    public static final String CASTLE_DAOJIA_NAMESPACE = "com.sankuai.mafka.castle.daojiacommon";

    public static final String TOPIC_INBOUND_RECORD_SNAPSHOT = "walle.data.inbound.record.snapshot";

    public static final String TOPIC_INBOUND_RECORD_FILE_METADATA = "walle.data.inbound.record.file.metadata";

    public static final String TOPIC_INBOUND_RECORD_INITIALIZED = "walle.data.inbound.record.initialized";

    public static final String TOPIC_INBOUND_RECORD_UPLOADED = "walle.data.inbound.record.uploaded";

    public static final String TOPIC_INBOUND_RECORD_PARSED = "walle.data.inbound.record.parsed";

    public static final String TOPIC_INBOUND_RECORD_COMPLETED = "walle.data.inbound.record.completed";

    /**
     * consumer group
     */
    // 入库快照解析阶段topic - compatible consumer
    public static final String CONSUMER_INBOUND_RECORD_SNAPSHOT_COMPATIBLE = "inbound.record.snapshot.compatible.consumer";
    // 入库快照解析阶段topic - offline consumer
    public static final String CONSUMER_INBOUND_RECORD_SNAPSHOT_OFFLINE = "inbound.record.snapshot.offline.consumer";
    // 入库快照解析阶段topic - initialize consumer
    public static final String CONSUMER_INBOUND_RECORD_INITIALIZE = "inbound.record.snapshot.initialize.consumer";

    public static final String CONSUMER_DISK_COLLECT_RECORD_UPLOAD = "disk.collect.record.upload.consumer";

    public static final String CONSUMER_DISK_COLLECT_RECORD_PARSE = "disk.collect.record.parse.consumer";

    public static final String CONSUMER_RECORD_SPARK_JOB_CREATE = "record.spark.job.create.consumer";

}
