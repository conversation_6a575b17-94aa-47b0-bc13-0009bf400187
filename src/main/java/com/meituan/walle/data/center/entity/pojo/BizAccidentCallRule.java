package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/09/14
 */
@Table(name = "biz_accident_call_rule")
@Data
public class BizAccidentCallRule implements Serializable {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 场地归属
     */
    private Integer affiliation;

    /**
     * 所属市级行政区
     */
    private String city;

    /**
     * 用车目的
     */
    private String purpose;

    /**
     * 第一电话人misId
     */
    private String firstPersonMisId;

    /**
     * 第一电话人加密电话号码
     */
    private String firstPersonNumber;

    /**
     * 第二电话人misId
     */
    private String secondPersonMisId;

    /**
     * 第二电话人加密电话号码
     */
    private String secondPersonNumber;

    /**
     * 是否逻辑删除
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
