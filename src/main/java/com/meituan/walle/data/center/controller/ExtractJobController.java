package com.meituan.walle.data.center.controller;

import com.alibaba.fastjson.JSONObject;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.walle.data.center.constant.CommonConstant;
import com.meituan.walle.data.center.constant.ResponseConstant;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.service.ExtractJobService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/extract_job")
public class ExtractJobController {
    @Resource
    private ExtractJobService extractJobService;

    @Resource
    @Qualifier("redisClientBeanFactory")
    private RedisStoreClient redisClient;

    @PostMapping("/createInputFile2S3")
    public Object createInputFile2S3(@RequestBody List<String> recordNames) {
        try {
            for (String recordName : recordNames) {
                extractJobService.createInputFile2S3(recordName);
            }
        } catch (Exception e) {
            throw e;
        }
        return Response.builder().ret(String.valueOf(0)).msg("success").build();
    }

    @PostMapping("/del_cached_record_parsing_process")
    public Object delCachedRecordParsingProcess(@RequestBody List<String> recordNames) {
        final Map<String, String> result = new HashMap<>();
        try {
            if (CollectionUtils.isEmpty(recordNames)) {
                result.put(ResponseConstant.RESULT_RET_NAME,
                        WebResponseStatusEnum.COLLECTION_IS_EMPTY.getCode().toString());
                result.put(ResponseConstant.RESULT_MSG_NAME, "RecordName list is empty.");
            } else {
                Long l = extractJobService.delCachedRecordParsingProcess(recordNames);
                result.put(ResponseConstant.RESULT_RET_NAME, WebResponseStatusEnum.SUCCESS.getCode().toString());
                result.put(ResponseConstant.RESULT_MSG_NAME, WebResponseStatusEnum.SUCCESS.getMsg());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("removeCount", l);
                return Response.result(result, jsonObject);
            }
        } catch (Exception e) {
            result.put(ResponseConstant.RESULT_RET_NAME, WebResponseStatusEnum.FAILED.getCode().toString());
            result.put(ResponseConstant.RESULT_MSG_NAME, e.getMessage());
        }
        return Response.result(result);
    }

    @PostMapping("/put_cached_record_parsing_process")
    public Object putCachedRecordParsingProcess(@RequestBody List<String> recordNames) {
        try {
            if (!CollectionUtils.isEmpty(recordNames)) {
                Map<String, Double> map = recordNames
                        .stream()
                        .collect(Collectors.toMap(recordName -> recordName,
                                recordName -> Double.valueOf(System.currentTimeMillis())));
                redisClient.zadd(new StoreKey(CommonConstant.WALLE_DATA_CENTER_CATEGORY,
                        CommonConstant.RECORD_TASK_EXECUTE_SET_KEY), map);
            }
        } catch (Exception e) {
            throw e;
        }
        return Response.builder().ret(String.valueOf(0)).msg("success").build();
    }
}