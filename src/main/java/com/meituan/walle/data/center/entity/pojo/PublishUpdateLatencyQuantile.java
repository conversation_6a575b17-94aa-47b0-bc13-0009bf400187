package com.meituan.walle.data.center.entity.pojo;

import com.meituan.walle.data.center.constant.IndicatorConstant;
import lombok.Builder;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@Table(name = "publish_update_latency_quantile")
public class PublishUpdateLatencyQuantile {
    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    @Column(name = "dim_number")
    private int dimNumber;

    @Column(name = "dim_pub_sub")
    private String dimPubSub;

    @Column(name = "dim_topic")
    private String dimTopic;

    @Column(name = "dim_record_name")
    private String dimRecordName;

    @Column(name = "min")
    private BigDecimal min;

    @Column(name = "avg")
    private BigDecimal avg;

    @Column(name = "p50")
    private BigDecimal p50;

    @Column(name = "p75")
    private BigDecimal p75;

    @Column(name = "p90")
    private BigDecimal p90;

    @Column(name = "p95")
    private BigDecimal p95;

    @Column(name = "p99")
    private BigDecimal p99;

    @Column(name = "p999")
    private BigDecimal p999;

    @Column(name = "p9999")
    private BigDecimal p9999;

    @Column(name = "p99999")
    private BigDecimal p99999;

    @Column(name = "p999999")
    private BigDecimal p999999;

    @Column(name = "max")
    private BigDecimal max;

    @Column(name = "message_count")
    private Integer messageCount;

    @Column(name = "lost_frame_quantile")
    private double lostFrameQuantile;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "is_discern")
    private int isDiscern;

    public BigDecimal getValueByIndicator(String indicator) {
        switch (indicator) {
            case IndicatorConstant.AVG: return getAvg();
            case IndicatorConstant.MAX: return getMax();
            case IndicatorConstant.MIN: return getMin();
            case IndicatorConstant.P50: return getP50();
            case IndicatorConstant.P75: return getP75();
            case IndicatorConstant.P90: return getP90();
            case IndicatorConstant.P95: return getP95();
            case IndicatorConstant.P99: return getP99();
            case IndicatorConstant.P999: return getP999();
            case IndicatorConstant.P9999: return getP9999();
            case IndicatorConstant.P99999: return getP99999();
            case IndicatorConstant.P999999: return getP999999();
            default: return null;
        }
    }
}
