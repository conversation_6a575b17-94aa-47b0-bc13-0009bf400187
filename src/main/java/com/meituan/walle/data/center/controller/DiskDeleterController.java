package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.vo.VehicleServiceFailVO;
import com.meituan.walle.data.center.service.DiskDeleterService;
import com.meituan.walle.data.center.util.CallRejectConstant;
import com.meituan.walle.data.center.util.CommonUtil;
import com.meituan.walle.mad.logger.client.annotation.MadLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/03/17
 */
@InterfaceDoc(
        displayName = "车端落盘数据删除相关接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供对车端删除数据服务监控的能力。",
        scenarios = "用于监控车端删除数据服务可用性、可靠性相关接口"
)
@RestController
@RequestMapping(value = "/disk_deleter")
@Slf4j
public class DiskDeleterController {

    @Resource
    private DiskDeleterService diskDeleterService;

    @MethodDoc(
            displayName = "车端磁盘删除文件的元数据",
            description = "车端磁盘删除文件的元数据")
    @MadLog
    @PostMapping(value = "/disk_delete_files")
    public Response diskDeleteFiles(@RequestBody List<String> param) {
        if (param == null) {
            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.DELETE_DISK_FILES_REPORT_REJECT,
                    "param is null", param);
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "param is null !");
        }
        Transaction t = Cat.newTransaction(CatConstant.ONBOARD_DISK_DELETE_FILES_INFO, "disk.delete.files.info");
        long start = System.currentTimeMillis();
        try {
            diskDeleterService.updateOnboardFileStatusIfDeleted(param);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            log.error("Post disk delete files info failed, request param is {}", param, e);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                "Post disk delete files info failed !");
    }

    @MethodDoc(
            displayName = "车端磁盘删除文件失败报警",
            description = "车端磁盘删除文件失败报警")
    @PostMapping(value = "/delete_fail")
    public Response deleteFail(@RequestBody VehicleServiceFailVO param) {
        if (param == null) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "param is null !");
        }
        if (StringUtils.isBlank(param.getVin())) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "lack vin parameter");
        }
        Transaction t = Cat.newTransaction(CatConstant.ONBOARD_DISK_DELETE_FAIL, "disk.delete.files.info");
        long start = System.currentTimeMillis();
        try {
            diskDeleterService.sendDeleteFailedMsgToDx(param);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            log.error("Failed to report disk delete fail event, vin is {}, diskUsage is {}",
                    param.getVin(), param.getDiskUsage(), e);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                "Failed to report disk delete fail event!");
    }
}
