package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "record_pkg_index")
public class RecordPkgIndex {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 模块名
     */
    private String module;

    /**
     * s3url
     */
    @Column(name = "s3_url")
    private String s3Url;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 文件大小(B)
     */
    @Column(name = "file_size")
    private Long fileSize;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取表record的record_name外键
     *
     * @return record_name - 表record的record_name外键
     */
    public String getRecordName() {
        return recordName;
    }

    /**
     * 设置表record的record_name外键
     *
     * @param recordName 表record的record_name外键
     */
    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    /**
     * 获取模块名
     *
     * @return module - 模块名
     */
    public String getModule() {
        return module;
    }

    /**
     * 设置模块名
     *
     * @param module 模块名
     */
    public void setModule(String module) {
        this.module = module;
    }

    /**
     * 获取s3url
     *
     * @return s3_url - s3url
     */
    public String getS3Url() {
        return s3Url;
    }

    /**
     * 设置s3url
     *
     * @param s3Url s3url
     */
    public void setS3Url(String s3Url) {
        this.s3Url = s3Url;
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取文件大小(B)
     *
     * @return file_size - 文件大小(B)
     */
    public Long getFileSize() {
        return fileSize;
    }

    /**
     * 设置文件大小(B)
     *
     * @param fileSize 文件大小(B)
     */
    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }
}