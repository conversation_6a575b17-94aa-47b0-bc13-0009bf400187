package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/05/31
 */
@Getter
@AllArgsConstructor
public enum EventPncParkingRelatedEnum {

    UNKNOWN(0, "unknown"),
    PARKING(1, "parking"),
    PARKING_COMPLETED(2, "parking_completed"),
    UTURN(3, "uturn"),
    UTURN_COMPLETED(4, "uturn_completed"),
    ROADBLOCK(5, "road_block"),
    ROADBLOCK_COMPLETED(6, "roadblock_completed"),
    STARTING(7, "starting"),
    STARTING_COMPLETED(8, "starting_completed");

    private int code;
    private String msg;

}
