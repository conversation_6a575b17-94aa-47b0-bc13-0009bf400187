package com.meituan.walle.data.center.config;

import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.call.sdk.service.AutoCallService;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Date 2022/08/19
 */

@Configuration
public class PigeonConfig {
    @MdpPigeonClient(timeout = 1000L, url = "autoCallServiceV2")
    private AutoCallService autoCallService;
}
