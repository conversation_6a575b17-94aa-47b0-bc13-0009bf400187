package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Date;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class FastRecordFilePO {
    private Long id;
    private String vin;
    private String uploadTaskId;
    private String recordName;
    private String fileName;
    private String filePath;
    private String s3Url;
    private String s3DsnUrl;
    private Integer fileType;
    private Long fileSize;
    private Long originFileSize;
    private String module;
    private Timestamp startTime;
    private Timestamp endTime;
    private Long startNano;
    private Long endNano;
    private Date createTime;
    private Date updateTime;
    private Date uploadStartTime;
    private String isDeleted;
    private Integer cluster;
    private String compressionFormat;
}