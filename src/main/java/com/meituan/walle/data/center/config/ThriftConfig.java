package com.meituan.walle.data.center.config;

import com.meituan.service.inf.kms.client.KmsAuthDataSource;
import com.meituan.service.mobile.mtthrift.auth.DefaultAuthHandler;
import com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher;
import com.meituan.walle.data.center.constant.MccConstant;
import com.meituan.walle.data.center.service.impl.DataCenterServiceImpl;
import com.sankuai.caros.gstatus.thrift.service.RemoteOperatorThriftService;
import com.sankuai.walle.rmanage.assign.group.thrift.service.GroupStatisticsThriftService;
import com.sankuai.walle.wcdp.data.center.iface.DataCenterThriftService;
import com.sankuai.xm.ems.open.EmsGroupOpenThriftService;
import com.sankuai.xm.ginfo.thrift.GinfoOpenServiceI;
import com.sankuai.xm.ginfo.thrift.service.GinfoOpenThriftClientService;
import com.sankuai.xm.openplatform.api.service.open.XmOpenGroupServiceI;
import com.sankuai.xm.openplatform.api.service.open.XmOpenMessageServiceI;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import com.sankuai.xm.udb.thrift.UdbOpenThriftClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;

@Configuration
public class ThriftConfig {
    @Lazy
    @Autowired
    private DataCenterServiceImpl dataCenterService;

    @Resource
    private MTThriftPoolConfig mtThriftPoolConfig;

    private static final String OPEN_XM_APPKEY = "com.sankuai.dxenterprise.open.gateway";

    @Bean(initMethod = "publish", destroyMethod = "destroy")
    public ThriftServerPublisher dataCenterThriftServerPublisher() throws Exception {
        ThriftServerPublisher publisher = new ThriftServerPublisher();
        publisher.setAppKey(MccConstant.MCC_APPKEY);
        publisher.setServiceInterface(DataCenterThriftService.class);
        publisher.setServiceImpl(dataCenterService);
        publisher.setPort(8412);
        KmsAuthDataSource kmsAuthDataSource = new KmsAuthDataSource();
        kmsAuthDataSource.setAppkey(MccConstant.MCC_APPKEY);
        DefaultAuthHandler defaultAuthHandler = new DefaultAuthHandler();
        defaultAuthHandler.setAuthDataSource(kmsAuthDataSource);
        try {
            defaultAuthHandler.afterPropertiesSet();
        } catch (Exception exp) {
            throw exp;
        }
        publisher.setAuthHandler(defaultAuthHandler);
        return publisher;
    }

    @Bean
    public MTThriftPoolConfig mtThriftPoolConfig() {
        MTThriftPoolConfig poolConfig = new MTThriftPoolConfig();
        poolConfig.setMaxActive(100);
        poolConfig.setMaxIdle(20);
        poolConfig.setMinIdle(1);
        poolConfig.setMaxWait(3000);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(false);
        poolConfig.setTestWhileIdle(false);
        return poolConfig;
    }

    @Bean(destroyMethod = "destroy")
    public ThriftClientProxy thriftClientProxy() {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setMtThriftPoolConfig(mtThriftPoolConfig);
        proxy.setServiceInterface(GinfoOpenServiceI.class);
        proxy.setTimeout(3000);
        proxy.setRemoteAppkey("com.sankuai.xm.account.ginfo.open");
        return proxy;
    }

    @Bean
    public GinfoOpenThriftClientService ginfoOpenThriftClientService() {
        return new GinfoOpenThriftClientService();
    }

    @Bean
    public UdbOpenThriftClient udbOpenThriftClient() {
        return new UdbOpenThriftClient();
    }

    @Bean
    public EmsGroupOpenThriftService.Iface emsGroupOpenThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setMtThriftPoolConfig(mtThriftPoolConfig);
        proxy.setServiceInterface(EmsGroupOpenThriftService.class);
        proxy.setTimeout(3000);
        proxy.setFilterByServiceName(true);
        proxy.setRemoteAppkey("com.sankuai.dxenterprise.ems.group");
        proxy.afterPropertiesSet();
        return (EmsGroupOpenThriftService.Iface) proxy.getObject();
    }

    @Bean
    public RemoteOperatorThriftService.Iface remoteOperatorThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setMtThriftPoolConfig(mtThriftPoolConfig);
        proxy.setServiceInterface(RemoteOperatorThriftService.class);
        proxy.setTimeout(3000);
        proxy.setFilterByServiceName(true);
        proxy.setRemoteAppkey("com.sankuai.caros.gstatus");
        proxy.afterPropertiesSet();
        return (RemoteOperatorThriftService.Iface) proxy.getObject();
    }


    @Bean
    public GroupStatisticsThriftService.Iface activeVehicleService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setMtThriftPoolConfig(mtThriftPoolConfig);
        proxy.setServiceInterface(GroupStatisticsThriftService.class);
        proxy.setTimeout(3000);
        proxy.setFilterByServiceName(true);
        proxy.setRemoteAppkey("com.sankuai.caros.rmanage.assign");
        proxy.afterPropertiesSet();
        return (GroupStatisticsThriftService.Iface) proxy.getObject();
    }

    @Bean
    public XmOpenGroupServiceI.Iface openGroupThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey(OPEN_XM_APPKEY);
        proxy.setTimeout(5000);
        proxy.setServiceInterface(XmOpenGroupServiceI.class);//目标接口类
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();
        return (XmOpenGroupServiceI.Iface) proxy.getObject();
    }

    @Bean
    public XmAuthServiceI.Iface openAuthThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey(OPEN_XM_APPKEY);
        proxy.setTimeout(5000);
        proxy.setServiceInterface(XmAuthServiceI.class);//目标接口类
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();
        return (XmAuthServiceI.Iface) proxy.getObject();
    }

    @Bean
    public XmOpenMessageServiceI.Iface openMessageThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey(OPEN_XM_APPKEY);
        proxy.setTimeout(5000);
        proxy.setServiceInterface(XmOpenMessageServiceI.class);//目标接口类
        proxy.setFilterByServiceName(true);
        proxy.afterPropertiesSet();
        return (XmOpenMessageServiceI.Iface) proxy.getObject();
    }

}
