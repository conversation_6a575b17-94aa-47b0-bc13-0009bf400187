package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/9/2 15:55
 */
@AllArgsConstructor
public enum HardBreakSourceTypeEnum {
    UNKNOWN("未知"),
    CHASSIS("底盘"),
    CONTROL("控制"),
    OPTIMIZER("规划"),
    NUDGE("NUDGE"),
    CROSSING("横穿"),
    MEETING("逆行/会车"),
    STATIONARY("静态障碍物"),
    PEDESTRIAN("行人"),
    PARALLEL("并排/切入"),
    START("起步"),
    BACKUP("倒车"),
    TRAFFIC_LIGHT("信号灯"),
    DESTINATION("终点"),
    REFERENCE_END("参考线终点"),
    PARKING_GATE("过杆/门禁"),
    CURB("路沿"),
    PATH_CURVETURE("路径曲率"),
    LANE("车道"),
    ROAD_ELEMENT("道路元素"),
    OCCLUSION("遮挡/盲区"),
    LATERAL("横向"),
    ARBITRATION("仲裁"),
    FREESPACE("FREESPACE"),
    ;
    private String desc;
}
