package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/07/05
 */
@Table(name = "biz_accident_group_rule")
@Data
public class BizAccidentGroupRule {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 场地归属
     */
    private Integer affiliation;

    /**
     * 所属市级行政区
     */
    private String city;

    /**
     * 用车目的
     */
    private String purpose;

    /**
     * 人员名单
     */
    private String members;

    /**
     * 是否逻辑删除
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
