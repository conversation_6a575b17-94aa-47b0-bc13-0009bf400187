package com.meituan.walle.data.center.config;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.walle.data.center.entity.po.VehicleChassisStatusPO;
import com.meituan.walle.data.center.entity.po.VehicleRealtimeStatusChassisPO;
import com.meituan.walle.data.center.service.VehicleChassisStatusService;
import com.meituan.walle.data.center.service.impl.DefaultVehicleChassisStatusBuilder;
import com.meituan.walle.data.center.util.DatetimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import walle.slab.chassis.SlabVehicleStatus;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2022/10/14
 */
@Slf4j
@Configuration
public class ElectricalConfig {
    private static final String NAMESPACE = "waimai";
    private static final String TOPIC_MAD_VEHICLE_REAL_STATUS_CHASSIS = "mad-vehicle.real.status.chassis.cloud";

    private static final String TOPIC_VEHICLE_REAL_STATUS_CHASSIS = "walle.data.center.vehicle.real.status.chassis";

    private static final String TOPIC_VEHICLE_REALTIME_STATUS_CHASSIS
            = "walle.data.center.vehicle.realtime.status.chassis";

    @Value("${appkey}")
    private String appkey;

    @Autowired
    private VehicleChassisStatusService vehicleChassisStatusService;

    @Bean(destroyMethod = "close")
    public IConsumerProcessor electricalVehicleRealStatusConsumer() throws Exception {
        return buildElectricalVehicleRealStatusConsumer();
    }

    @Lazy
    @Bean(value = "vehicleRealStatusChassisProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> vehicleRealStatusChassisProducer() throws Exception {
        return buildProducer(TOPIC_VEHICLE_REAL_STATUS_CHASSIS, appkey);
    }

    @Lazy
    @Bean(value = "vehicleRealtimeStatusChassisProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> vehicleRealtimeStatusChassisProducer() throws Exception {
        return buildProducer(TOPIC_VEHICLE_REALTIME_STATUS_CHASSIS, appkey);
    }

    private IProducerProcessor<String, String> buildProducer(String topic, String appKey) throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appKey);
        return MafkaClient.buildProduceFactory(properties, topic);
    }

    private IConsumerProcessor buildElectricalVehicleRealStatusConsumer() throws Exception {
        Properties properties = getProperties(appkey);
        IConsumerProcessor consumer =
                MafkaClient.buildConsumerFactory(properties, TOPIC_MAD_VEHICLE_REAL_STATUS_CHASSIS);
        consumer.recvMessageWithParallel(byte[].class, (MafkaMessage message, MessagetContext context) -> {

            final byte[] buf = (byte[]) message.getBody();
            String msg = "empty";
            try {
                walle.slab.chassis.SlabVehicleStatus.VehicleChassisStatus res =
                        walle.slab.chassis.SlabVehicleStatus.VehicleChassisStatus.parseFrom(buf);
                msg = JsonFormat.printer().omittingInsignificantWhitespace().print(res);

                final String vin = res.getVehicleStatus().getVehicleInfo().getVin();
                VehicleRealtimeStatusChassisPO po = VehicleRealtimeStatusChassisPO.builder()
                        .vin(vin)
                        .timestamp(res.getTimestamp())
                        .chassisStatus(toJson(res))
                        .createTime(DatetimeUtil.getCurrentTime())
                        .updateTime(DatetimeUtil.getCurrentTime())
                        .eventTime(DatetimeUtil.format(DatetimeUtil.toMicroDate(res.getTimestamp()), DatetimeUtil.YMDHMS))
                        .build();
                vehicleChassisStatusService.sendMessage(po);
                sendVehicleRealStatus(res, vin);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer electrical vehicle realtime status, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    @Deprecated
    private void sendVehicleRealStatus(SlabVehicleStatus.VehicleChassisStatus res, String vin) throws Exception {
        VehicleChassisStatusPO po = new DefaultVehicleChassisStatusBuilder()
                .vin(vin)
                .timestamp(res.getTimestamp())
                .wsuSdcdc(toJson(res.getWsuSdcdc()))
                .spduStatus(toJson(res.getSpduStatus().toBuilder().build()))
                .bcuStatus(toJson(res.getBcuStatus().toBuilder().build()))
                .upuStatus(toJson(res.getUpuStatus()))
                .mcuStatus(toJson(res.getMcuStatus()))
                .canStatus(toJson(res.getCanStatus()))
                .vcuStatus(toJson(res.getVcuStatus()))
                .devicesVersion(toJson(res.getDevicesVersion()))
                .vehicleExceptInfo(toJson(res.getVehicleExceptInfo()))
                .epsStatus(toJson(res.getEpsStatus()))
                .vehicleStatus(toJson(res.getVehicleStatus().toBuilder().build()))
                .tpmsStatus(toJson(res.getTpmsStatus()))
                .avasStatus(toJson(res.getAvasStatus()))
                .ehbStatus(toJson(res.getEhbStatus()))
                .escStatus(toJson(res.getEscStatus()))
                .elcAddStatus(toJson(res.getElcAddStatus()))
                .sdcdcStatus(toJson(res.getSdcdcStatus()))
                .fcuStatus(toJson(res.getFcuStatus()))
                .slabAddStatus(toJson(res.getSlabAddStatus()))
                .plgfStatus(toJson(res.getPlgfStatus()))
                .plfrStatus(toJson(res.getPlfrStatus()))
                .hmiStatus(toJson(res.getHmiStatus()))
                .testerFunctional(toJson(res.getTesterFunctional()))
                .slabBcmStatus(toJson(res.getSlabBcmStatus()))
                .otherStatus(toJson(res.getOtherStatus()))
                .powerStatus(toJson(res.getPowerStatus()))
                .mcuTemperature(toJson(res.getMcuTemperature()))
                .createTime(DatetimeUtil.getCurrentTime())
                .updateTime(DatetimeUtil.getCurrentTime())
                .eventTime(DatetimeUtil.format(DatetimeUtil.toMicroDate(res.getTimestamp()), DatetimeUtil.YMDHMS))
                .build();
        vehicleChassisStatusService.sendMessageDeprecated(po);
    }

    private String toJson(MessageOrBuilder message) throws InvalidProtocolBufferException {
        final String result = JsonFormat.printer().includingDefaultValueFields().print(message);
        return result.replaceAll("[\t\n\000\001]", "");
    }

    private Properties getProperties(String appKey) {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appKey);
        properties.setProperty(ConsumerConstants.SubscribeGroup, appKey);
        properties.setProperty("fetch.message.max.bytes", "5242880");
        return properties;
    }
}