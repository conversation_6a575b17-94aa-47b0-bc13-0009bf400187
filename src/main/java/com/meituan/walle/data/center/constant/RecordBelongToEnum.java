package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RecordBelongToEnum {
    DISK_COLLECT("DISK_COLLECT", "硬盘数据回收"),
    SENSOR_SIMULATION("SENSOR_SIMULATION", "传感器仿真"),
    FAST_COLLECT("FAST_COLLECT", "车端快速回传"),
    SIM_REWRITE("SIM_REWRITE", "仿真二次落盘数据"),
    WORKFLOW_REFRESH_RECORD("WORKFLOW_REFRESH_RECORD", "workflow刷新二次落盘数据"),
    HIL_REWRITE("HIL_REWRITE", "HIL仿真二次落盘数据"),
    ;

    private String code;
    private String desc;


    public static RecordBelongToEnum byCode(String code) {
        for (RecordBelongToEnum en : RecordBelongToEnum.values()) {
            if (en.code.equalsIgnoreCase(code)) {
                return en;
            }
        }
        return null;
    }
}