package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/1/13 19:23
 * @description
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class WorkstationCaseIndexPO implements Serializable {

    private String caseId;

    private Long operateTimestamp;

    /**
     * 此刻在今天所在的第几秒
     */
    private Integer operateHour;

    private String offlineId;

    private String realTimeId;

    private String vin;

    private String dataSource;

    private String priority;

    private String manualLevel;

    private String finalLevel;

    /**
     * 接收人mis
     */
    private String receiver;

    private String operationStatus;

    private Long receiveTimestamp;

    private String assign;

    private String state;

    private String title;

    private String appearance;

    private String module;

    /**
     * 逗号分隔的id
     */
    private String classify;

    /**
     * TagDefect表中的name，逗号分割的名称
     */
    private String classifyName;

    private String categoryText;

    private String send;

    private String tag;

    private String operatorId;

    private String originCaseId;

    private String labelType;

    private String labelName;

    private String purpose;

    private String place;

    private String gitBranch;

    private String gitCommit;

    private String packageVersion;

    private String hdmapVersion;

    private String channel;

    private Double hardbrakeSpeed;

    private Double hardbrakeAcceleration;

    private String hardbrakeId;

    private Integer demotionType;

    private Integer demotionReason;

    private String caseHistoryDesc;

    private String caseHistoryType;

    private Integer lowSpeedIsIntervened;

    private Double lowSpeedDuration;

    private String lowSpeedAlertType;

    private String lowSpeedType;

    /**
     * yyyy-MM-dd HH:mm:ss.SSS格式字符串
     */
    private String lowSpeedStartTime;

    /**
     * yyyy-MM-dd HH:mm:ss.SSS格式字符串
     */
    private String lowSpeedEndTime;

    private String lowSpeedSource;

    /**
     * 13位时间戳，毫秒
     */
    private Long sendTimestamp;

    private String vehicleName;

    /**
     * 区域名
     */
    private String areaName;

    /**
     * 场地所属
     */
    private Integer affiliation;

    /**
     * 现象名
     */
    private String appearanceName;

    /**
     * 模块名
     */
    private String moduleName;

    private Long takeOverDuration;

    private Double takeOverMileage;

    private Integer isDeleted;

    private Long createTimestamp;

    private String position;

    private String recordName;

    /**
     * 通用case（case3.0）动态字段信息
     */
    private String column1;

    private String column2;

    private String column3;

    private String column4;

    private String column5;

    private String column6;

    private String column7;

    private String column8;

    private String column9;

    private String column10;
}
