package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/06/10
 */
@Data
@Table(name = "biz_map_area")
public class BizMapArea {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 区域编号
     */
    private String code;

    /**
     * 中文名称
     */
    private String name;

    /**
     * 所属省级行政区
     */
    private String province;

    /**
     * 所属市级行政区
     */
    private String city;

    /**
     * 所属县级行政区
     */
    private String district;

    /**
     * 场地所属
     */
    private Integer affiliation;

    private Boolean isDeleted;

    private Date createTime;

    private Date updateTime;
}
