package com.meituan.walle.data.center.constant;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.nio.charset.StandardCharsets;

/**
 * Created by <PERSON><PERSON><PERSON> on 2021/3/29.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CommonConstant {

    public static final Integer RESPONSE_SUCCESS_CODE = 200;

    public static final int PARTITION_SIZE = 100;

    public static final long NANO_TO_MILLIS = 1000_000L;

    public static final long MILLIS_TO_SECOND = 1000L;

    public static final long DEFAULT_TIMESTAMP_THRESH = 1000L;

    public static final long SECOND_TO_MINUTE = 60L;

    public static final long NANO_TO_SECOND = 1000_000_000L;

    /**
     * 用于逻辑判断，无业务含义
     */
    public static final int LOGICAL_VALUE = -1;

    public static final String S3_PREFIX = "s3://";

    public static final String S3_PUBLIC_DOWNLOAD_URL =
            "https://s3plus.meituan.net/v1/mss_914377ab071e4a8b8b50cc10a00ec5af/";

    public static final String S3_LABEL_DOWNLOAD_URL = "https://s3plus.vip.sankuai.com/";

    public static final String PNG_SUFFIX = ".png";

    public static final String HDMAP_NAME_SUFFIX = "_admap";

    public static final String BIN_SUFFIX = ".bin";

    public static final String TMP_PATH = "/tmp";

    public static final String IMAGE_TOPIC = "/walle/image_data";

    public static final String IMAGE_HORIZON_TOPIC = "/walle/image_data_horizon";

    public static final String HEVC_TOPIC = "/walle/hevc_data";

    public static final String HEVC_HORIZON_TOPIC = "/walle/hevc_data_horizon";

    //python3的路径，需要安装opencv-python模块
    public static final String PY_INTERPRETER = "/opt/meituan/walle-data-center/.venv/bin/python3";

    public static final String VEHICLE_DATA_BUCKET = "vehicle-data";

    public static final String RECORD_TIME_FORMAT = "yyyyMMdd_HHmmss";

    public static final Long REC_FILE_BREAKPOINT_NANO = 500_000_000L;

    public static final String DEFAULT_CREATOR = "system";

    public static final int K_BYTES = 1024;

    public static final double NINETY_NINE_PERCENT = 0.99D;

    public static final double SMALL_NUMBER = 0.01D;

    public static final String UNKNOWN = "未知";

    public static final String DEFAULT_RECEIVER = "walle";

    public static final String WALLE_DATA_CENTER_CATEGORY = "walle_data_center";

    public static final String RECORD_TASK_EXECUTE_KEY = "executing_record_list";

    public static final String RECORD_TASK_EXECUTE_SET_KEY = "executing_record_set";

    public static final String ONBOARD_EVENT_MODULE = "OnboardNoticePublisher";

    public static final String ZIP_SUFFIX = ".zip";

    public static final String TXT_SUFFIX = ".txt";

    public static final int MAX_LIMIT = 100;

    public static final String ONCALL_LIST_MAX_ID_KEY = "oncall_list_handled_max_id";

    public static final long TWO_THOUSAND_YEAR_MILLIS = 946684800000L;

    public static final long TWO_DAY_MILLIS = 2 * 24 * 60 * 60 * 1000L;

    public static final String OSVIZ_APP_ID = "render";

    public static final String AUK_EVENT_MSG_TOPIC_SUFFIX = "/iot/data";

    public static final SerializeConfig SERIALIZE_CONFIG = new SerializeConfig();

    public static final String CAN_DATA_FILE_SUFFIX = ".CRF";

    public static final String MSTAT_PREFIX = "mstat";

    public static final String MSTAT_MASTER_PREFIX = "mstat-master";

    public static final String MSTAT_SLAVE0_PREFIX = "mstat-slave0";

    public static final String MSTAT_IPC_PREFIX = "mstat-ipc";

    public static final String EU_LATENCY_PREFIX = "eu_latency_item";

    public static final String PRIORITY_MANAGER_PREFIX = "priority_manager";

    public static final String DOWN_SAMPLE_PARAM = "downSampleParam";

    static {
        SERIALIZE_CONFIG.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;
    }

    public static final String TIME_FORMAT_DATE = "yyyy-MM-dd";

    public static final String RISK_ALERT_ARBITRATION_ALERT_NAME = "REQUEST_TAKEOVER_IN_GENERAL";

    public static final String IIIRC_OPENAPI_URL = "https://walle.sankuai.com/iiirc/openapi";

    public static final double MPH_TO_KMPH = 3.6;

    public static final int MS_TO_S = 1000; // x ms / 1000 = y s

    public static final String HDMAP_BASE_URL = "https://hdmap.sankuai.com";

    public static final String ADMAP_BASE_URL = "https://admap.sankuai.com";

    public static final String GENERAL_CASE_CONSUMER_LOCK_PREV = "general_case_consumer_lock_";

    public static final String GENERAL_CASE_TOPIC_DATASOURCE_LOCK_PREV = "general_case_topic_datasource_lock_";

    public static final String CASE_SEARCH_INDEX_PREV = "case_search_index_";

    public static final String WORKSTATION_CASE_ES_INDEX = "workstation_case_search_index_alias";

    public static final int WORKSTATION_CASE_CACHE_EXPIRE_S = 300;

    public static final int WORKSTATION_CASE_SEARCH_DEFAULT_LIMIT = 10;

    public static final String WORKSTATION_CASE_SYNC_CRAN_DEFAULT_PARAM = "default";

    public static final String DESTINATION_BASE_EVENT_SET_KEY = "destination_base_event_set";

    public static final Long DESTINATION_BASE_EVENT_MERGE_MILLIS = 10000L;

    public static final int ONE = 1;

    public static final int MB_TO_B = 1024 * 1024;

    public static final int APPEARANCE_TREE_HIGH = 50;

    public static final String HADOOP_WCDP_HDFS_PATH = "viewfs://hadoop-meituan/user/hadoop-wcdp/onboard-record-data/";

    public static final String HADOOP_WCDP_HDFS_PATH_AD = "/user/hadoop-wcdp/onboard-record-data/";
    public static final int ZERO = 0;

    public static final String DTS_INSERT = "insert";

    public static final String DTS_UPDATE = "update";
    public static final String S3_UPLOADED_EFFECTIVE_DATE = "20230410";

    public static final String RECORD_DATA_READY_EFFECTIVE_DATE = "20231018";

    public static final String S3_DELETED_FILE_COPY_EVENT = "copy";
    public static final String S3_DELETED_FILE_DELETE_EVENT = "delete";

    public static final String ENCODE_UTF_8 = "utf-8";

    public static final String ENVIRONMENT_PROD = "prod";

    public static final String ENVIRONMENT_TEST = "test";

    public static final String BID_DRB_PREFIX = "drb";

    public static final int NO_ROWS_INFLUENCED = 0;

    public static final String DOWNLOAD_STORAGE_TYPE_S3 = "s3";

    public static final String DOWNLOAD_STORAGE_TYPE_HDFS = "hdfs";
    /**
     * 验证record解析任务是否全部执行完成：解析任务配置化上线日期
     */
    public static final String RECORD_PARSING_TASK_CONFIG_ONLINE_DATE = "20231101";

    public static final String QUERY_LOCATION_NAME_URL = "https://lbsapi.sankuai.com";

    public static final String ACCIDENT_ORDER_NAME = "accidentOrder";

    public static final String accidentRedisLockCategory = "accident_redis_lock";

    /**
     * 与数据总线约定的业务唯一键，用于上报事故数据
     */
    public static final String BUSINESS_KEY = "accident_order";

    /**
     * 数据总线上报接口的数据限制大小
     */
    public static final Integer DATA_BUS_BATCH_SIZE = 100;

    /**
     * 事故工单风险解除时间的默认值
     */
    public static final String RISK_ELIMINATION_TIME_DEFAULT = "1970-01-01 08:00:01";

    public static final String GET_DATA_SOURCE_API_STORAGE_MANAGER = "/data_source/get_data_source/ba";

    // 常量定义
    public static final String SHA_256 = "SHA-256";
    public static final String UTF_8 = StandardCharsets.UTF_8.name();

    /**
     * 事故更新关联异常事件状态线程池
     */
    public static final String ACCIDENT_UPDATE_EVENT_STATUS_THREAD_POOL = "data.center.update.event.status";

    /**
     * 云分诊事件完成状态
     */
    public static final Integer EVENT_COMPLETED_STATUS = 2;

    /**
     * 事件中心系统作为快速回传任务的creator前缀
     */
    public static final String EVENT_SYSTEM_FAST_UPLOAD_CREATOR_PREFIX = "event_system";

    /**
     * 判断record是否解析完成的分类
     */
    public static final String RECORD_WITH_REC = "record_with_rec";
    public static final String RECORD_WITHOUT_REC = "record_without_rec";
}
