package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RecordGroupNameEnum {
    WALLE_DATA("walle-data", "数据组"),
    ;

    private String code;
    private String desc;


    public static RecordGroupNameEnum byCode(String code) {
        for (RecordGroupNameEnum en : RecordGroupNameEnum.values()) {
            if (en.code.equalsIgnoreCase(code)) {
                return en;
            }
        }
        return null;
    }
}
