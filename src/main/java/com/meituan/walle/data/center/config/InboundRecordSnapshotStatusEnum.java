package com.meituan.walle.data.center.config;

/**
 * <AUTHOR>
 * @date 2024/10/30
 */
public enum InboundRecordSnapshotStatusEnum {
    FAIL(Byte.valueOf("-1"), "入库检查失败"),
    INITIAL(Byte.valueOf("0"), "入库检查初始化"),
    SUCCEED(Byte.valueOf("1"), "入库检查成功"),

    RUNNING(Byte.valueOf("10"), "入库检查进行中"),
    ;

    InboundRecordSnapshotStatusEnum(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Byte code;
    private String desc;

    public Byte getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
