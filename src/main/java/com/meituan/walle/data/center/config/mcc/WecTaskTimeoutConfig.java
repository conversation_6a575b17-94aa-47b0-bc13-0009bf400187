package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

/**
 * <AUTHOR>
 * @data 2019/4/19 11:12
 */
public class WecTaskTimeoutConfig {
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "wec.task.cancel.timeout.second")
    public static volatile int wecTaskCancelTimeoutSeconde = 3600 * 2;
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "wec.task.done.timeout.second")
    public static volatile int wecTaskDoneTimeoutSeconde = 3600 * 2;
}
