package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/01
 */
@AllArgsConstructor
@Getter
public enum RecordDataStatusEnum {

    PRODUCE((short) 0, "record产生"),
    NORMAL_FINISH((short) 1, "正常结束"),
    START_UPLOADING((short) 2, "数据文件开始上传"),
    FINISH_UPLOADING((short) 3, "数据文件结束上传"),
    START_PARSING((short) 4, "开始解析"),
    FINISH_PARSING((short) 5, "解析结束"),
    DATA_READY((short) 6, "数据Ready");

    private Short code;
    private String msg;

}
