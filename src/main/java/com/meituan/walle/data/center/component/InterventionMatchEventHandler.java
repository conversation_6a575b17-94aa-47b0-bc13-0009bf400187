package com.meituan.walle.data.center.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meituan.walle.data.center.config.mcc.FastUploadConfig;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.CharConstant;
import com.meituan.walle.data.center.constant.CommonConstant;
import com.meituan.walle.data.center.entity.dto.InterventionMatchEventDTO;
import com.meituan.walle.data.center.entity.dto.OnboardEventBriefDTO;
import com.meituan.walle.data.center.entity.enums.OnCallListAutoAssignedEnum;
import com.meituan.walle.data.center.entity.enums.VehicleEventTypeEnum;
import com.meituan.walle.data.center.entity.pojo.OnCallList;
import com.meituan.walle.data.center.entity.vo.VehicleUploadRequestVO;
import com.meituan.walle.data.center.service.OnCallListService;
import com.meituan.walle.data.center.service.VehicleEventService;
import com.meituan.walle.data.center.service.VehicleInfoService;
import com.meituan.walle.data.center.service.VehicleUploadRequestService;
import com.meituan.walle.data.center.util.DatetimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/25
 */
@Component
@Slf4j
public class InterventionMatchEventHandler {
    @Resource
    private VehicleInfoService vehicleInfoService;

    @Resource
    private VehicleUploadRequestService vehicleUploadRequestService;

    @Resource
    private OnCallListService onCallListService;

    @Resource
    private VehicleEventService vehicleEventService;

    private static final int EVENT_TIME_LEN = 14;

    public void handleMatchMsg(String msg) {
        InterventionMatchEventDTO interventionMatchEventDTO = JSON.parseObject(msg, InterventionMatchEventDTO.class);
        String interventionId = interventionMatchEventDTO.getInterventionId();

        if (StringUtils.isBlank(interventionId)) {
            log.error("handleMatchMsg from mafka(flink) interventionId is empty");
            return;
        }
        String[] interventionIdParts = interventionId.split(CharConstant.CHAR_XH);
        String vin = vehicleInfoService.getVin(interventionIdParts[interventionIdParts.length - 1]);
        if (StringUtils.isBlank(vin)) {
            log.error("handleMatchMsg from mafka(flink) vin is empty");
            return;
        }

        OnCallList onCallList = OnCallList.builder()
                .caseId(interventionId)
                .isAutoAssigned(OnCallListAutoAssignedEnum.MATCHED.getCode())
                .build();
        List<OnboardEventBriefDTO> events = interventionMatchEventDTO.getEvents();
        if (events != null && !events.isEmpty()) {
            Date operateTime = null;

            for (OnboardEventBriefDTO event : events) {
                Integer eventType = event.getEventType();

                if (!Integer.valueOf(VehicleEventTypeEnum.PARKING.getCode()).equals(eventType)) {
                    continue;
                }
                String eventId = event.getEventId();
                // 创建降级的快速回传任务
                String eventTime = eventId.substring(0, EVENT_TIME_LEN);
                operateTime = DatetimeUtil.covertToDate(eventTime, DatetimeUtil.yyMMddHHmmss);

                if (operateTime != null) {
                    long timestamp = operateTime.getTime() * CommonConstant.NANO_TO_MILLIS;
                    VehicleUploadRequestVO vehicleUploadRequestVO = new VehicleUploadRequestVO();
                    vehicleUploadRequestVO.setVin(vin);
                    vehicleUploadRequestVO.setMeasurementTimestamp(timestamp);
                    vehicleUploadRequestVO.setInterventionId(interventionId);
                    vehicleUploadRequestVO.setExtraInfo(FastUploadConfig.VEHICLE_UPLOAD_CASE_DATA_EXTRA_INFO);
                    try {
                        List<String> demotionTypes = Lists.newArrayList(
                                SysParamsConfig.demotion_types.split(CharConstant.CHAR_DD));
                        Integer configDemotionType = vehicleEventService.checkDemotionEvent(eventId, demotionTypes);
                        // 如果是在配置里的那些降级case，则直接创建快传任务，否则按白名单的配置来
                        boolean isEnableWhiteList = configDemotionType == 0;
                        vehicleUploadRequestService.addRequestFromEventIntervention(vehicleUploadRequestVO,
                                isEnableWhiteList, configDemotionType);
                    } catch (Exception e) {
                        log.error("handleMatchMsg add fast task error, interventionId: {}, parking eventId: {}",
                                interventionId, eventId, e);
                    }
                }

            }

            onCallList.setOperateTime(operateTime);
        }

        onCallListService.updateByCaseId(onCallList);
    }

}
