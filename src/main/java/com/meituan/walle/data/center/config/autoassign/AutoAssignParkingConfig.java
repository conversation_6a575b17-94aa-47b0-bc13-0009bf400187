package com.meituan.walle.data.center.config.autoassign;

import com.meituan.walle.data.center.constant.WorkTableTypeEnum;

/**
 * <AUTHOR>
 * @date 2021/10/13
 */
public class AutoAssignParkingConfig {

    public static final String OPERATOR_ID = "auto";
    public static final String TITLE = "系统接管";
    public static final String AVOID_CRASH = "(防碰撞)";
    public static final String AVOID_HARD_BRAKE = "(防急刹)";
    public static final String DATA_SOURCE = "system_intervention";

    /**
     * 系统问题
     */
    public static final String VEHICLE_SYSTEM_ABNORMAL_APPEARANCE = "32";
    public static final String VEHICLE_SYSTEM_ABNORMAL_PRIORITY = "P1";

    /**
     * 遮挡红绿灯识别问题
     */
    public static final String TRAFFIC_LIGHT_OCCLUDED_APPEARANCE = "124";
    public static final String TRAFFIC_LIGHT_OCCLUDED_PRIORITY = "P1";
    public static final int TRAFFIC_LIGHT_OCCLUDED_TABLE_TYPE = WorkTableTypeEnum.AUTO_CAR.getCode();

    /**
     * 其他
     */
    public static final String TELEOPERATION_UNAVAILABLE_APPEARANCE = "209";
    public static final String TELEOPERATION_UNAVAILABLE_PRIORITY = "P1";

    /**
     * 其他
     */
    public static final String TELEOPERATION_LATENCY_TOO_HIGHT_APPEARANCE = "209";
    public static final String TELEOPERATION_LATENCY_TOO_HIGHT_PRIORITY = "P1";

    /**
     * 非交互问题-无障碍物-路由问题
     */
    public static final String REFERENCE_LINES_NOT_MATCH_ROUTING_APPEARANCE = "364";
    public static final String REFERENCE_LINES_NOT_MATCH_ROUTING_PRIORITY = "P1";
    public static final int REFERENCE_LINES_NOT_MATCH_ROUTING_TABLE_TYPE = WorkTableTypeEnum.AUTO_CAR.getCode();

    /**
     * 模块延时高
     */
    public static final String PREDICTION_LATENCY_TOO_HIGH_APPEARANCE = "88";
    public static final String PREDICTION_LATENCY_TOO_HIGH_PRIORITY = "P0";

    /**
     * 模块延时高
     */
    public static final String PLANNING_LATENCY_TOO_HIGH_APPEARANCE = "88";
    public static final String PLANNING_LATENCY_TOO_HIGH_PRIORITY = "P0";

    /**
     * 模块延时高
     */
    public static final String PERCEPTION_LATENCY_TOO_HIGH_APPEARANCE = "88";
    public static final String PERCEPTION_LATENCY_TOO_HIGH_PRIORITY = "P0";

    /**
     * 系统问题
     */
    public static final String MEMORY_USAGE_PERCENT_TOO_HIGH_APPEARANCE = "32";
    public static final String MEMORY_USAGE_PERCENT_TOO_HIGH_PRIORITY = "P0";

    /**
     * 定位不准
     */
    public static final String LOCALIZATION_UNAVAILABLE_APPEARANCE = "131";
    public static final String LOCALIZATION_UNAVAILABLE_PRIORITY = "P0";

    /**
     * 模块延时高
     */
    public static final String LOCALIZATION_LATENCY_TOO_HIGH_APPEARANCE = "88";
    public static final String LOCALIZATION_LATENCY_TOO_HIGH_PRIORITY = "P0";

    /**
     * 设备问题
     */
    public static final String LIDAR_UNAVAILABLE_APPEARANCE = "31";
    public static final String LIDAR_UNAVAILABLE_PRIORITY = "P0";

    /**
     * 设备问题
     */
    public static final String LIDAR_FREQUENCY_ABNORMAL_APPEARANCE = "31";
    public static final String LIDAR_FREQUENCY_ABNORMAL_PRIORITY = "P0";

    /**
     * 硬件故障key
     */
    public static final String HARDWARE_ERROR_KEY = "-1";

    /**
     * 系统问题
     */
    public static final String GPU_USAGE_PERCENT_TOO_HIGH_APPEARANCE = "32";
    public static final String GPU_USAGE_PERCENT_TOO_HIGH_PRIORITY = "P0";

    /**
     * 设备问题
     */
    public static final String GPU_TEMPERATURE_TOO_HIGH_APPEARANCE = "31";
    public static final String GPU_TEMPERATURE_TOO_HIGH_PRIORITY = "P0";

    /**
     * 设备问题
     */
    public static final String GNSS_UNAVAILABLE_APPEARANCE = "31";
    public static final String GNSS_UNAVAILABLE_PRIORITY = "P0";

    /**
     * 行驶问题
     */
    public static final String DEVIATE_ROUTING_APPEARANCE = "14";
    public static final String DEVIATE_ROUTING_PRIORITY = "P1";
    public static final int DEVIATE_ROUTING_TABLE_TYPE = WorkTableTypeEnum.AUTO_CAR.getCode();

    /**
     * 系统问题
     */
    public static final String CPU_USAGE_PERCENT_TOO_HIGH_APPEARANCE = "32";
    public static final String CPU_USAGE_PERCENT_TOO_HIGH_PRIORITY = "P0";

    /**
     * 系统问题
     */
    public static final String BCM_DOOR_UNLOCKED_APPEARANCE = "32";
    public static final String BCM_DOOR_UNLOCKED_PRIORITY = "P1";

    /**
     * 系统问题
     */
    public static final String MEMORY_STATUS_ABNORMAL_APPEARANCE = "32";
    public static final String MEMORY_STATUS_ABNORMAL_PRIORITY = "P1";

    /**
     * 设备问题
     */
    public static final String LIDAR_MAIN_STATUS_ABNORMAL_APPEARANCE = "31";
    public static final String LIDAR_MAIN_STATUS_ABNORMAL_PRIORITY = "P0";

    /**
     * 系统问题
     */
    public static final String SATA_LINK_SPEED_STATUS_ABNORMAL_APPEARANCE = "32";
    public static final String SATA_LINK_SPEED_STATUS_ABNORMAL_PRIORITY = "P1";

    /**
     * 系统问题
     */
    public static final String DISK_WRITE_STATUS_ABNORMAL_APPEARANCE = "32";
    public static final String DISK_WRITE_STATUS_ABNORMAL_PRIORITY = "P1";

    /**
     * 系统问题
     */
    public static final String DATA_DISK_USAGE_PERCENT_TOO_HIGH_APPEARANCE = "32";
    public static final String DATA_DISK_USAGE_PERCENT_TOO_HIGH_PRIORITY = "P1";

    /**
     * 模块延时高
     */
    public static final String CONTROL_LATENCY_TOO_HIGH_APPEARANCE = "88";
    public static final String CONTROL_LATENCY_TOO_HIGH_PRIORITY = "P0";

    /**
     * 模块延时高
     */
    public static final String CHASSIS_LATENCY_TOO_HIGH_APPEARANCE = "88";
    public static final String CHASSIS_LATENCY_TOO_HIGH_PRIORITY = "P0";

    /**
     * 交互问题
     */
    public static final String APPROACHING_OCCLUSION_AREA_APPEARANCE = "44";
    public static final String APPROACHING_OCCLUSION_AREA_PRIORITY = "P1";
    public static final int APPROACHING_OCCLUSION_AREA_TABLE_TYPE = WorkTableTypeEnum.AUTO_CAR.getCode();

    /**
     * 行驶问题
     */
    public static final String AVOID_HARD_BRAKE_APPEARANCE = "14";
    public static final String AVOID_HARD_BRAKE_PRIORITY = "P2";
    public static final int AVOID_HARD_BRAKE_TABLE_TYPE = WorkTableTypeEnum.AUTO_CAR.getCode();

    /**
     * 移动红绿灯识别
     */
    public static final String TRAFFIC_LIGHT_BROKEN_APPEARANCE = "50";
    public static final String TRAFFIC_LIGHT_BROKEN_PRIORITY = "P1";
    public static final int TRAFFIC_LIGHT_BROKEN_TABLE_TYPE = WorkTableTypeEnum.AUTO_CAR.getCode();

    /**
     * 其他
     */
    public static final String OBSTACLE_COLLISION_RISK_APPEARANCE = "209";
    public static final String OBSTACLE_COLLISION_RISK_PRIORITY = "P1";

    /**
     * 模块延时高
     */
    public static final String ARBITRATION_MONITOR_ABNORMAL_APPEARANCE = "88";
    public static final String ARBITRATION_MONITOR_ABNORMAL_PRIORITY = "P1";

    /**
     * 设备问题
     */
    public static final String RADAR_ABNORMAL_APPEARANCE = "31";
    public static final String RADAR_ABNORMAL_PRIORITY = "P0";

    /**
     * 标定问题
     */
    public static final String LIDAR_CALIBRATION_ABNORMAL_APPEARANCE = "174";
    public static final String LIDAR_CALIBRATION_ABNORMAL_PRIORITY = "P1";

    /**
     * 标定问题
     */
    public static final String IMU_CALIBRATION_ABNORMAL_APPEARANCE = "174";
    public static final String IMU_CALIBRATION_ABNORMAL_PRIORITY = "P1";

    /**
     * 其他
     */
    public static final String CONTROL_INVAILD_APPEARANCE = "209";
    public static final String CONTROL_INVAILD_PRIORITY = "P1";

    /**
     * 设备问题
     */
    public static final String CAMERA_ABNORMAL_APPEARANCE = "31";
    public static final String CAMERA_ABNORMAL_PRIORITY = "P1";

    /**
     * 模块延时高
     */
    public static final String SCHEDULING_ABNORMAL_APPEARANCE = "88";
    public static final String SCHEDULING_ABNORMAL_PRIORITY = "P0";

    /**
     * 定位不准
     */
    public static final String LOCALIZATION_RELIABLE_APPEARANCE = "131";
    public static final String LOCALIZATION_RELIABLE_PRIORITY = "P0";

    /**
     * 其他
     */
    public static final String CONTROL_INVALID_APPEARANCE = "209";
    public static final String CONTROL_INVALID_PRIORITY = "P0";

    /**
     * 设备问题
     */
    public static final String LIDAR_ABNORMAL_APPEARANCE = "31";
    public static final String LIDAR_ABNORMAL_PRIORITY = "P0";

    /**
     * 地面异物
     */
    @Deprecated
    public static final String BLOCKED_BY_STATIC_OBSTACLE_APPEARANCE = "285";
    public static final String BLOCKED_BY_STATIC_OBSTACLE_PRIORITY = "P2";
    public static final int BLOCKED_BY_STATIC_OBSTACLE_TABLE_TYPE = WorkTableTypeEnum.AUTO_CAR.getCode();

    /**
     * 盲区障碍物
     */
    @Deprecated
    public static final String OBSTACLE_APPEAR_FROM_OCCLUSION_APPEARANCE = "236";
    public static final String OBSTACLE_APPEAR_FROM_OCCLUSION_PRIORITY = "P2";
    public static final int OBSTACLE_APPEAR_FROM_OCCLUSION_TABLE_TYPE = WorkTableTypeEnum.AUTO_CAR.getCode();

    /**
     * 开车门
     */
    @Deprecated
    public static final String OPENING_VEHICLE_DOOR_INTO_TRAFFIC_APPEARANCE = "286";
    public static final String OPENING_VEHICLE_DOOR_INTO_TRAFFIC_PRIORITY = "P2";
    public static final int OPENING_VEHICLE_DOOR_INTO_TRAFFIC_TABLE_TYPE = WorkTableTypeEnum.AUTO_CAR.getCode();

    /**
     * 加载地图失败
     */
    public static final String MAP_LOAD_ERROR_APPEARANCE = "186";
    public static final String MAP_LOAD_ERROR_PRIORITY = "P1";

    /**
     * 加载地图失败
     */
    public static final String MAP_ACCESS_OUT_OF_RANGE_APPEARANCE = "186";
    public static final String MAP_ACCESS_OUT_OF_RANGE_PRIORITY = "P1";

    /**
     * 加载地图失败
     */
    public static final String MAP_LOAD_TIMEOUT_APPEARANCE = "186";
    public static final String MAP_LOAD_TIMEOUT_PRIORITY = "P1";

    /**
     * 设备问题
     */
    public static final String BATTERY_IS_SWITCHING_APPEARANCE = "31";
    public static final String BATTERY_IS_SWITCHING_PRIORITY = "P1";

    /**
     * 设备问题
     */
    public static final String WSU_STATE_ABNORMAL_APPEARANCE = "31";
    public static final String WSU_STATE_ABNORMAL_PRIORITY = "P1";

    /**
     * 设备问题
     */
    public static final String LIDAR_POINT_CLOUD_QUALITY_ABNORMAL_APPEARANCE = "31";
    public static final String LIDAR_POINT_CLOUD_QUALITY_ABNORMAL_PRIORITY = "P1";

    /**
     * 地图问题/曲率超限
     */
    public static final String CURVATURE_OVER_LIMIT_APPEARANCE = "6470";
    public static final String CURVATURE_OVER_LIMIT_PRIORITY = "P2";

    /**
     * 地图问题/曲率超限
     */
    public static final String REF_LINE_CURVATURE_OVER_LIMIT_APPEARANCE = "166";
    public static final String REF_LINE_CURVATURE_OVER_LIMIT_PRIORITY = "P1";

    /**
     * 非交互问题-无障碍物-路由问题
     */
    public static final String ROUTING_ERROR_STATUS_APPEARANCE = "353";
    public static final String ROUTING_ERROR_STATUS_PRIORITY = "P3";

    /**
     * 非交互问题-有障碍物-轨迹异常
     */
    public static final String TRAJECTORY_INVALID_APPEARANCE = "364";
    public static final String TRAJECTORY_INVALID_PRIORITY = "P0";

    /**
     * 应急系统-降级策略
     */
    public static final String BOUNDARY_COLLISION_DETECTTED_APPEARANCE = "238";
    public static final String BOUNDARY_COLLISION_DETECTTED_PRIORITY = "P1";

    /**
     * 应急系统-降级策略
     */
    public static final String TELEOP_COLLISION_RISK_APPEARANCE = "238";
    public static final String TELEOP_COLLISION_RISK_PRIORITY = "P1";

    /**
     * 应急系统-降级策略
     */
    public static final String MAEB_COLLISION_RISK_MODULE = "137";
    public static final String MAEB_COLLISION_RISK_PRIORITY = "P1";

    /**
     * 非交互问题-有障碍物-轨迹异常
     */
    public static final String PLANNING_FAR_FROM_LOCALIZATION_APPEARANCE = "364";
    public static final String PLANNING_FAR_FROM_LOCALIZATION_PRIORITY = "P0";

    /**
     * 地图元素高风险异常
     */
    public static final String HDMAP_HIGH_EMERGENCY_ABNORMAL_APPEARANCE = "154";
    public static final String HDMAP_HIGH_EMERGENCY_ABNORMAL_PRIORITY = "P0";

    /**
     * 设备问题
     */
    public static final String BATTERY_REQUEST_SWITCH_APPEARANCE = "8144";
    public static final String BATTERY_REQUEST_SWITCH_PRIORITY = "P2";

}

