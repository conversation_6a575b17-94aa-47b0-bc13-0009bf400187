package com.meituan.walle.data.center.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/02/10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadProgressDTO {
    @JsonProperty(value = "idc_port")
    private String idcPort;
    @JsonProperty(value = "disk_name")
    private String diskName;
    @JsonProperty(value = "record_name")
    private String recordName;
    @JsonProperty(value = "upload_stage")
    private String uploadStage;
    @JsonProperty(value = "upload_complete_unixtime_s")
    private Long uploadCompleteUnixtimeS;
    @JsonProperty(value = "belong_to")
    private String belongTo;
}