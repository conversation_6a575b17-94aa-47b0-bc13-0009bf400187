package com.meituan.walle.data.center.entity.map;

import com.meituan.walle.data.center.constant.HdmapConstant;
import lombok.Getter;

@Getter
public class LineSegment {
    private Point2d start;
    private Point2d end;
    private double length;
    private Point2d unitDirection;

    public LineSegment(Point2d start, Point2d end) {
        this.start = start;
        this.end = end;
        double dx = end.getX() - start.getX();
        double dy = end.getY() - start.getY();
        this.length = Math.hypot(dx, dy);
        if (length <= HdmapConstant.MATH_EPSILON) {
            this.unitDirection = Point2d.builder().x(0).y(0).build();
        } else {
            this.unitDirection = Point2d.builder().x(dx / this.length).y(dy / this.length).build();
        }
    }
}
