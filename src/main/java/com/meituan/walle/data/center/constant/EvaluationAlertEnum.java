package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/05/30
 */
@Getter
@AllArgsConstructor
public enum EvaluationAlertEnum {

    DEFAULT(0, "DEFAULT"),

    BATTERY_REQUEST_SWITCH(120, "BATTERY_REQUEST_SWITCH");

    private Integer code;
    private String desc;

    public static EvaluationAlertEnum get(Integer code) {
        for (EvaluationAlertEnum en : EvaluationAlertEnum.values()) {
            if (Objects.equals(en.code, code)) {
                return en;
            }
        }
        return null;
    }
}