package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/04/08
 */
@Component
public class MiningConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "second.before.case.begin.time")
    public static volatile long SECOND_BEFORE_CASE_BEGIN_TIME = 10_000L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "second.after.case.end.time")
    public static volatile long SECOND_AFTER_CASE_END_TIME = 10_000L;
}