package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "biz_remote_video_request")
@Data
public class BizRemoteVideoRequest {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 视频起始时间
     */
    private Date startTime;

    /**
     * 视频终止时间
     */
    private Date endTime;

    /**
     * 指定获取的视频类型
     */
    private String stream;

    /**
     * 远遥视频任务唯一id
     */
    private String subTaskId;

    /**
     * 任务状态, 5:开始获取;100:获取完成;404:获取失败
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
