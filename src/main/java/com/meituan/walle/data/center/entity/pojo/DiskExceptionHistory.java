package com.meituan.walle.data.center.entity.pojo;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/07/26
 */
@Builder
@Getter
@Setter
@Table(name = "disk_exception_history")
@AllArgsConstructor
@NoArgsConstructor
public class DiskExceptionHistory {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 硬盘WWN编码
     */
    @Column(name = "disk_id")
    private String diskId;

    /**
     * 硬盘名称
     */
    @Column(name = "disk_name")
    private String diskName;

    /**
     * IDC主机端口号
     */
    @Column(name = "idc_port")
    private String idcPort;

    /**
     * 异常类型: 1-挂载异常|2-加解密异常
     */
    @Column(name = "exception_type")
    private Integer exceptionType;

    /**
     * 异常编码: 101-错误文件系统类型|102-未知文件系统类型
     */
    @Column(name = "exception_code")
    private Integer exceptionCode;

    /**
     * 执行命令的返回码
     */
    @Column(name = "shell_code")
    private Integer shellCode;

    /**
     * 执行命令的返回消息
     */
    @Column(name = "shell_message")
    private String shellMessage;

    /**
     * 批次id
     */
    @Column(name = "batch_id")
    private String batchId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否逻辑删除: 0-否|1-是
     */
    @Column(name = "is_deleted")
    private Integer idDeleted;

    /**
     * 逻辑删除时间
     */
    @Column(name = "delete_time")
    private Date deleteTime;
}

