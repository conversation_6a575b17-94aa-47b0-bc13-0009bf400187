package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.net.HttpHeaders;
import com.meituan.walle.data.center.constant.CharConstant;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.params.RgsDataListParam;
import com.meituan.walle.data.center.entity.pojo.RecordV2;
import com.meituan.walle.data.center.entity.pojo.VehicleUploadRequest;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.entity.response.HttpResponse;
import com.meituan.walle.data.center.entity.vo.MvizDataListVO;
import com.meituan.walle.data.center.entity.vo.RecordFileResultVO;
import com.meituan.walle.data.center.handle.impl.RecordDownloadValidatorHandler;
import com.meituan.walle.data.center.handle.impl.S3DsnUrlHandler;
import com.meituan.walle.data.center.service.RecordV2Service;
import com.meituan.walle.data.center.service.RgsService;
import com.meituan.walle.data.center.service.VehicleRecFileService;
import com.meituan.walle.data.center.util.BaAuthUtil;
import com.meituan.walle.data.center.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping("/rgs")
public class RgsController {

    @Resource
    private RgsService rgsService;

    @Resource
    private S3DsnUrlHandler s3DsnUrlHandler;

    @Resource
    private RecordDownloadValidatorHandler downloadValidatorHandler;

    @Resource
    private VehicleRecFileService vehicleRecFileService;

    @Resource
    private RecordV2Service recordV2Service;

    @GetMapping("/data_list")
    public CommonResponse rgsDataList(RgsDataListParam param, HttpServletRequest httpServletRequest) {
        String requestAuth = httpServletRequest.getHeader(HttpHeaders.AUTHORIZATION);
        String clientId = BaAuthUtil.getBasicAuthClientIdByRequestAuth(requestAuth);
        Cat.logMetricForCount("data_list_request", ImmutableMap.of("type", "rgs", "userClientId", param.getUserClientId()));
        try {
            String modules = param.getModules();
            String noModules = param.getNoModules();
            if (StringUtils.isNotBlank(modules) && StringUtils.isNotBlank(noModules)) {
                Set<String> moduleSet = new HashSet<>(Splitter.on(CharConstant.CHAR_DD).splitToList(modules));
                Set<String> noModuleSet = new HashSet<>(Splitter.on(CharConstant.CHAR_DD).splitToList(noModules));
                moduleSet.removeAll(noModuleSet);
                param.setModules(String.join(CharConstant.CHAR_DD, moduleSet));
            }

            // 根据fastId下载数据，对应/mviz/fast_task_file的逻辑
            String recordNameTagPrefix = ":fid-";
            if (param.getRecordName().contains(recordNameTagPrefix)) {
                String fastId = param.getRecordName().substring(param.getRecordName().indexOf(recordNameTagPrefix) + recordNameTagPrefix.length());
                if (org.springframework.util.StringUtils.isEmpty(fastId)) {
                    throw new RuntimeException("parse tag of recordName exception! recordName: " + param.getRecordName());
                }
                VehicleUploadRequest vehicleUploadRequest = vehicleRecFileService.getById(Long.valueOf(fastId));
                if (vehicleUploadRequest == null) {
                    Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "rgs", "userClientId", param.getUserClientId(), "status", "failed", "reason", "record_not_exist"));
                    return CommonResponse.fail(WebResponseStatusEnum.DATA_NOT_FOUND.getCode(),
                            "the vehicleUploadRequest does not exist in the record table! recordName: " + param.getRecordName());
                }
                RecordV2 recordV2Info = null;
                if ((param.getForceDownload() == null || !param.getForceDownload()) && StringUtils.isNotBlank(vehicleUploadRequest.getRecordName())) {
                    recordV2Info = recordV2Service.selectByRecordName(vehicleUploadRequest.getRecordName());
                    if (recordV2Info != null) {
                        HttpResponse httpResponse = downloadValidatorHandler.checkRecordBeDownload(recordV2Info,
                                param.getMisId(),
                                param.getUserClientId(),
                                false,
                                new ArrayList<>());
                        if (httpResponse != null) {
                            Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "rgs", "userClientId", param.getUserClientId(), "status", "failed", "reason", "record_not_download"));
                            return CommonResponse.fail(httpResponse.getCode(), httpResponse.getMsg());
                        }
                    }
                }
                // 核心逻辑
                RecordFileResultVO resultVO = rgsService.listFastTaskFileById(param, vehicleUploadRequest, recordV2Info, param.getUserClientId());
                if (resultVO == null) {
                    Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "rgs", "userClientId", param.getUserClientId(), "status", "failed", "reason", "file_list_empty"));
                    return CommonResponse.fail(WebResponseStatusEnum.DATA_NOT_FOUND.getCode(), "File list is empty! ");
                }
                resultVO.setIsFastUpload(Boolean.TRUE);
                Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "rgs", "userClientId", param.getUserClientId(), "status", "success"));
                return CommonResponse.success(resultVO);
            }

            // 常规下载数据，对应/mviz/data_list的逻辑
            RecordV2 record = rgsService.getRecordByRequest(param.getRecordName(), param.getCarName(), param.getStartTime());
            if (record == null) {
                Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "rgs", "userClientId", param.getUserClientId(), "status", "failed", "reason", "record_not_found"));
                return CommonResponse.fail(WebResponseStatusEnum.DATA_NOT_FOUND.getCode(), "The record does not exist in the record table! ");
            }

            MvizDataListVO mvizDataListVO;
            if (param.getDownloadFromOffline() != null && param.getDownloadFromOffline()) {
                mvizDataListVO = rgsService.getRecordDataListFromOfflineOnly(param, record, clientId);
            } else if (param.getDownloadFromOnline() != null && param.getDownloadFromOnline()) {
                mvizDataListVO = rgsService.getRecordDataListFromOnlineOnly(param, record, clientId);
            } else {
                mvizDataListVO = rgsService.getDataList(param, record, clientId);
            }

            RecordFileResultVO resultVO = mvizDataListVO.getRecordFileResultVO();
            if (resultVO == null) {
                Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "rgs", "userClientId", param.getUserClientId(), "status", "failed", "reason", "file_list_empty"));
                return CommonResponse.fail(WebResponseStatusEnum.DATA_NOT_FOUND.getCode(), "File list is empty! ");
            }

            if ((param.getForceDownload() == null || !param.getForceDownload())) {
                HttpResponse httpResponse;
                if (resultVO.getIsFastUpload()) {
                    httpResponse = downloadValidatorHandler.checkRecordBeDownload(record, param.getMisId(), clientId, false, new ArrayList<>());
                } else {
                    httpResponse = downloadValidatorHandler.checkRecordBeDownload(record, param.getMisId(), clientId, true, mvizDataListVO.getRecordPkgCheckList());
                }
                if (httpResponse != null) {
                    String reason = String.format("WebResponseStatusEnum[%d]", httpResponse.getCode());
                    Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "rgs", "userClientId", param.getUserClientId(), "status", "failed", "reason", reason));
                    return CommonResponse.fail(httpResponse.getCode(), httpResponse.getMsg());
                }
            }

            return CommonResponse.success(resultVO);
        } catch (Exception e) {
            log.error("[/rgs/data_list] exception: {}, param: {}", e.getMessage(), JacksonUtil.serialize(param), e);
            Cat.logMetricForCount("data_list_response", ImmutableMap.of("type", "rgs", "userClientId", param.getUserClientId(), "status", "failed", "reason", "exception"));
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

}
