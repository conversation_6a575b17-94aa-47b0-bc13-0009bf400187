package com.meituan.walle.data.center.entity.pojo;

import lombok.Builder;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * table name:  mining_task_result
 * @author: liuqichun
 * @date: 2022/03/23
 */
@Builder
@Data
@Table(name = "mining_task_result")
public class MiningTaskResult {

	/**
	 * 主键id
	 */
	@Id
	@GeneratedValue(generator = "JDBC")
	private Long id;

	/**
	 * 挖掘任务结果id，对md5({task_id}+{record_name}+{result_begin_time}+{result_end_time})取值保证数据的幂等
	 */
	@Column(name = "task_result_id")
	private String taskResultId;

	/**
	 * mining_task表中的task_id
	 */
	@Column(name = "task_id")
	private String taskId;

	/**
	 * record_name
	 */
	@Column(name = "record_name")
	private String recordName;

	/**
	 * 该结果数据段的开始时间
	 */
	@Column(name = "result_begin_time")
	private Date resultBeginTime;

	/**
	 * 该结果数据段的结束时间
	 */
	@Column(name = "result_end_time")
	private Date resultEndTime;

	/**
	 * case id：如果有的话
	 */
	@Column(name = "case_id")
	private String caseId;

	/**
	 * 人工标注状态。0：未标注，1：标注
	 */
	@Column(name = "label_status")
	private Integer labelStatus;

	/**
	 * 标注结果
	 */
	@Column(name = "label_result")
	private String labelResult;

	/**
	 * 标注补充信息
	 */
	@Column(name = "label_remark")
	private String labelRemark;

	/**
	 * 人工打的标签，破坏范式，存放多个标签
	 */
	@Column(name = "tag")
	private String tag;

	/**
	 * 结果的可视化任务的状态（mviz视频生成的状态）
	 */
	@Column(name = "result_visual_status")
	private Integer resultVisualStatus;

	/**
	 * 结果的可视化任务的id（mviz视频生成的任务id）
	 */
	@Column(name = "result_visual_id")
	private String resultVisualId;

	/**
	 * 结果的可视化的url地址
	 */
	@Column(name = "result_visual_s3_url")
	private String resultVisualS3Url;

	/**
	 * 结果可视化是否有用，0：默认，-1：没有帮助，1：有帮助
	 */
	@Column(name = "result_visual_helpful")
	private Integer resultVisualHelpful;

	/**
	 * 选填，场景id：如果有的话
	 */
	@Column(name = "scenario_id")
	private String scenarioId;

	/**
	 * 选填，场景名称：如果有的话
	 */
	@Column(name = "scenario_name")
	private String scenarioName;

	/**
	 * 预留字段1
	 */
	@Column(name = "reserved_field1")
	private String reservedField1;

	/**
	 * 预留字段2
	 */
	@Column(name = "reserved_field2")
	private String reservedField2;

	/**
	 * 预留字段3
	 */
	@Column(name = "reserved_field3")
	private String reservedField3;

	/**
	 * 预留字段4
	 */
	@Column(name = "reserved_field4")
	private String reservedField4;

	/**
	 * 是否逻辑删除，0表示否，1表示是
	 */
	@Column(name = "is_deleted")
	private Integer isDeleted;

	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@Column(name = "update_time")
	private Date updateTime;

	private Long sequence;

}

