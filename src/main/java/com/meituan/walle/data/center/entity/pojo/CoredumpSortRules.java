package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/9/9 16:16
 * @descriotion
 */
@Data
@Table(name = "coredump_sort_rules")
public class CoredumpSortRules {
    /**
     * 自增ID
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车架号（车辆设备id）
     */
    private String expression;

    /**
     * misId
     */
    private String misId;

    /**
     * type
     */
    private String type;

    /**
     * 添加时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}