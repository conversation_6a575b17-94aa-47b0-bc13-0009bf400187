package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.params.PageParams;
import com.meituan.walle.data.center.service.RecordDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/5 下午2:47
 * Description:
 * Modified by
 */
@RestController
@Validated
@RequestMapping("/recordDetail")
public class RecordDetailController {

    @Autowired
    RecordDetailService recordDetailService;

    @GetMapping("/records")
    public Object getRecords(@Validated PageParams params,
                             @RequestParam(value = "recordName", required = false) String recordName,
                             @RequestParam(value = "vehicleName", required = false) String vehicleName,
                             @RequestParam(value = "startTime", required = false) Long startTime,
                             @RequestParam(value = "endTime", required = false) Long endTime) {
        return Response.succ(recordDetailService.getRecords(
                params.getPageNum(), params.getPageSize(), recordName, vehicleName, startTime, endTime));
    }

    @GetMapping("/recordInfo")
    public Object getRecordDetail(@NotBlank(message = "recordName cannot be null") String recordName) {
        return Response.succ(recordDetailService.getRecordDetail(recordName));
    }

    @GetMapping("/recordFiles")
    public Object getRecordFilesInfo(@NotBlank(message = "recordName cannot be null") String recordName) {
        return Response.succ(recordDetailService.getRecordFilesInfo(recordName));
    }

    @GetMapping("/cases")
    public Object getCasesOfRecord(@NotBlank(message = "recordName cannot be null") String recordName) {
        return Response.succ(recordDetailService.getCasesByRecordName(recordName));
    }
}
