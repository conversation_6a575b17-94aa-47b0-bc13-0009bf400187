package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/10/28
 */
@Getter
@AllArgsConstructor
public enum GetTagTypeEnum {
    /**
     * 标识没有标签的事件
     */
    NO_TAG_EVENT(1, "Identify no tag events"),
    /**
     * 标识打了标签但未完成描述的事件
     */
    NO_DESCRIPTION_EVENT(2, "Identify get no description events"),
    /**
     * 标识既打了标签也已完成描述的事件
     */
    TAG_AND_DESCRIPTION(3, "Identify events with tag and description");

    private int code;
    private String message;
}
