package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Table(name = "oncall_list")
public class OnCallList {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    @Column(name = "vin")
    private String vin;

    @Column(name = "state")
    private String state;

    @Column(name = "comment")
    private String comment;

    @Column(name = "operator_id")
    private String operatorId;

    @Column(name = "operate_time")
    private Date operateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "module")
    private String module;

    @Column(name = "relate_case")
    private String relateCase;

    @Column(name = "case_id")
    private String caseId;

    @Column(name = "priority")
    private String priority;

    @Column(name = "data_source")
    private String dataSource;

    @Column(name = "real_time_id")
    private String realTimeId;

    @Column(name = "offline_id")
    private String offlineId;

    @Column(name = "appearance")
    private String appearance;
    /**
     * 指派人
     */
    @Column(name = "assign")
    private String assign;

    @Column(name = "operation_status")
    private String operationStatus;

    @Column(name = "title")
    private String title;

    @Column(name = "classify")
    private String classify;

    @Column(name = "send")
    private String send;

    /**
     * 是否逻辑删除，1：是，0：否
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 标签
     */
    @Column(name = "tag")
    private String tag;

    /**
     * 自动化分拣类型
     */
    @Column(name = "auto_sort_type")
    private Integer autoSortType;

    /**
     * 工作台字段
     */
    @Column(name = "work_table_type")
    private Integer workTableType;

    /**
     * 接收人mis
     */
    @Column(name = "receiver")
    private String receiver;

    /**
     * 接收时间
     */
    @Column(name = "receive_time")
    private Date receiveTime;

    /**
     * 快速回传数据是否准备好，主要用于实时case自动化分拣，标识快速上传任务是否完成
     */
    @Column(name = "is_auto_assigned")
    private Integer isAutoAssigned;

    /**
     * 复制出来的case关联的源caseId
     */
    @Column(name = "origin_case_id")
    private String originCaseId;

    /**
     * 是否可分拣字段，record数据是否已完成，展示给运营看
     */
    @Column(name = "sortable")
    private Integer sortable;

    /**
     * record_name字段
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * s3_data_ready字段
     */
    @Column(name = "s3_data_ready")
    private Integer s3DataReady;

    @Column(name = "category")
    private String category;

    @Column(name = "manual_level")
    private String manualLevel;

    @Column(name = "final_level")
    private String finalLevel;

    @Column(name = "position")
    private String position;

    /**
     * 备注信息，目前coredump类型case会放匹配到的规则信息
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "weather")
    private String weather;

    @Column(name = "image_quality")
    private String imageQuality;
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OnCallList that = (OnCallList) o;
        return caseId.equals(that.caseId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(caseId);
    }
}