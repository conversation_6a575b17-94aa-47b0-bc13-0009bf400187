package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Table(name = "spark_job_platform")
@Entity
@Data
public class SparkJobPlatform {
    @Id
    @GeneratedValue(strategy= GenerationType.IDENTITY)
    private int id;
    private String jobUuid;
    private String sparkJobName;
    private String compileId;
    private String regularParam;
    private String user;
    private String notify;
    private Integer enabled;
    private Integer channel;
    @Column(name = "update_time", insertable = false, updatable = false)
    private Date updateTime;
    @Column(name = "create_time", insertable = false, updatable = false)
    private Date createTime;
}
