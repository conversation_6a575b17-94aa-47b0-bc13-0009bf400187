package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> @Beijing
 * @date 2022/03/21
 * Description:
 * Modified by
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class RecordVersionSparkJobInfo implements Serializable {
    private String versionId;
    private String commitId;
    private String moduleList;
    private String recordName;
    private Long startTime;
    private Long endTime;
    private String outputHdfsPath;
    private Integer status;
}
