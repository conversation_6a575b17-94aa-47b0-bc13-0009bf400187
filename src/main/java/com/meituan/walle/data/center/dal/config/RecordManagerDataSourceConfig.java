package com.meituan.walle.data.center.dal.config;

import com.dianping.zebra.group.jdbc.GroupDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * <AUTHOR>
 * @date 2023/05/30
 */
@Configuration
@MapperScan(basePackages = "com.meituan.walle.data.center.dal.recordmanager.mapper",
        sqlSessionFactoryRef = "recordmanagerSqlSessionFactory")
public class RecordManagerDataSourceConfig {
    @Value("${blade.recordmanager.jdbcref}")
    private String jdbcref;
    @Value("${blade.recordmanager.poolType}")
    private String poolType;
    @Value("${blade.recordmanager.initialPoolSize}")
    private int initialPoolSize;
    @Value("${blade.recordmanager.minPoolSize}")
    private int minPoolSize;
    @Value("${blade.recordmanager.maxPoolSize}")
    private int maxPoolSize;
    @Value("${blade.recordmanager.checkoutTimeout}")
    private int checkoutTimeout;

    @Bean(name = "recordmanagerDataSource", initMethod = "init", destroyMethod = "close")
    public GroupDataSource zebraDataSource() {
        GroupDataSource dataSource = new GroupDataSource();
        dataSource.setJdbcRef(jdbcref);
        dataSource.setPoolType(poolType);
        dataSource.setInitialPoolSize(initialPoolSize);
        dataSource.setMinPoolSize(minPoolSize);
        dataSource.setMaxPoolSize(maxPoolSize);
        dataSource.setCheckoutTimeout(checkoutTimeout);
        // https://km.sankuai.com/page/1209645483
        dataSource.setExtraJdbcUrlParams("sessionVariables=blade_txn_timeout%3D300,blade_stmt_timeout%3D240");
        return dataSource;
    }

    @Bean(name = "recordmanagerSqlSessionFactory")
    public SqlSessionFactory setSqlSessionFactory(
            @Qualifier(value = "recordmanagerDataSource") GroupDataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(
                new PathMatchingResourcePatternResolver()
                        .getResources("classpath*:mapper/blade/*Mapper.xml"));
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        bean.setConfiguration(configuration);
        bean.setTypeAliasesPackage("com.meituan.walle.data.center.dal.recordmanager.entity");
        return bean.getObject();
    }

    @Bean
    @Primary
    public DataSourceTransactionManager dataSourceTransactionManager(
            @Qualifier(value = "recordmanagerDataSource") GroupDataSource dataSource) {
        DataSourceTransactionManager dataSourceTransactionManager = new DataSourceTransactionManager();
        dataSourceTransactionManager.setDataSource(dataSource);
        return dataSourceTransactionManager;
    }

    @Bean("recordmanagerTransactionTemplate")
    @Primary
    public TransactionTemplate recordmanagerTransactionTemplate(
            @Qualifier(value = "recordmanagerDataSource") GroupDataSource dataSource) {
        return new TransactionTemplate(dataSourceTransactionManager(dataSource));
    }
}