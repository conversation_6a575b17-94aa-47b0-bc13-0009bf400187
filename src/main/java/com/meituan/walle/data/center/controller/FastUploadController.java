package com.meituan.walle.data.center.controller;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.walle.data.center.config.mcc.FastUploadConfig;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.*;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.dto.VehicleUploadTaskModuleUploadTimeListResultDTO;
import com.meituan.walle.data.center.entity.pojo.BizAccidentInfo;
import com.meituan.walle.data.center.entity.pojo.BizOnboardPreUploadFile;
import com.meituan.walle.data.center.entity.pojo.VehicleUploadRequest;
import com.meituan.walle.data.center.entity.request.FastUploadFileRequest;
import com.meituan.walle.data.center.entity.request.FastUploadTaskRequest;
import com.meituan.walle.data.center.entity.request.FinishTaskRequest;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.entity.response.PageResponse;
import com.meituan.walle.data.center.entity.vo.*;
import com.meituan.walle.data.center.handle.impl.VehicleUploadVersionAdapter;
import com.meituan.walle.data.center.service.*;
import com.meituan.walle.data.center.service.impl.EventCoredumpStrategy;
import com.meituan.walle.data.center.util.DatetimeUtil;
import com.meituan.walle.data.center.util.JacksonUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/11/8
 */
@InterfaceDoc(
        displayName = "快速上传",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供快速上传任务管理的能力。",
        scenarios = "快速上传所有接口"
)
@Slf4j
@RestController
@RequestMapping("/fast_upload")
public class FastUploadController {

    @Resource
    private VehicleRecFileService vehicleRecFileService;

    @Autowired
    private VehicleUploadRequestService vehicleUploadRequestService;

    @Resource
    private VehicleInfoService vehicleInfoService;

    @Resource
    private BizAccidentInfoService accidentInfoService;

    @Resource
    private VehicleUploadMonitorService monitorService;

    @Autowired
    private BizOnboardPreUploadFileService onboardPreUploadFileService;

    @Resource
    private EventCoredumpStrategy coredumpStrategy;

    @Resource
    private OsvizService osvizService;

    @Resource
    private FastUploadService fastUploadService;

    // 任务服务
    @Resource
    private VehicleUploadTaskService vehicleUploadTaskService;

    @Resource
    private VehicleUploadVersionAdapter vehicleUploadVersionAdapter;

    @MethodDoc(
            displayName = "根据VIN获取上传任务",
            description = "根据VIN获取上传任务",
            parameters = {@ParamDoc(name = "vin", description = "车架号")},
            returnValueDescription = "上传任务",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": []\n" +
                    "}"
    )
    @GetMapping("/get_task_by_vin")
    public Response getDataParam(@RequestParam("vin") String vin) {
        if (StringUtils.isBlank(vin)) {
            log.info("Get task by vin failed, for lack of vin");
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(), "lack of vin");
        }
        if (vehicleUploadVersionAdapter.isVehicleUploadV2(vin)) {
            // v2版本
            // v1版本状态机转换到v2版本时，不需要特别设置
            log.info("[FastUploadController#get_task_by_vin] {} isVehicleUploadV2", vin);
            return getTaskByVinV2(vin);
        }
        // 从v2版本状态机存在v1状态机没有的一些状态，在切换时，需要适配
        // UPLOADING, PAUSED 的任务需要修改状态为 5(CREATED)
        // 11.17: 避免超时，由定时任务进行修改/手动触发
        // vehicleUploadVersionAdapter.changeStatusToV1(vin);

        Map<String, Object> result = new HashMap<>(2);
        try {
            if (StringUtils.isNotBlank(vehicleInfoService.getVehicleName(vin))) {
                monitorService.report(vin);
            }
            List<VehicleUploadRequest> requests = vehicleRecFileService.selectByVinAndStatus(vin,
                    FastUploadTaskStatusEnum.CREATED.getCode());
            String vehicleType = vehicleInfoService.getVehicleType(vin);
            final List<FastUploadTaskVO> fastUploadTasks = new ArrayList<>();
            for (VehicleUploadRequest vehicleUploadRequest : requests) {
                FastUploadTaskVO vo = new FastUploadTaskVO();
                vo.setVehicleType(vehicleType);
                if (SysParamsConfig.vehicle_resume_task_types.contains(vehicleUploadRequest.getTaskType())) {
                    VehicleUploadTaskModuleUploadTimeListResultDTO vehicleUploadTaskModuleUploadTimeListResultDTO =
                            vehicleRecFileService.getFastTaskModuleUploadTimeList(vehicleUploadRequest);
                    Map<String, List<FastUploadModuleTimeVO>> fastTaskModuleTimeMap =
                            vehicleUploadTaskModuleUploadTimeListResultDTO.getFastTaskModuleTimeMap();
                    List<String> recordNameList = vehicleUploadTaskModuleUploadTimeListResultDTO.getRecordNameList();
                    vo.setModuleUploadTimeList(fastTaskModuleTimeMap);
                    boolean ifDuplicate =
                            vehicleRecFileService.isDuplicateTask(vehicleUploadRequest, fastTaskModuleTimeMap);
                    /*
                    当前任务上传中，被打断，另一个任务上传了完全相同的模块，再次拉取当前任务时，需要把当前任务的状态更新为已完成
                    否则会造成死锁。
                     */
                    if (ifDuplicate) {
                        // 仅当任务重复+需要更新任务状态的时候，更新任务状态并发送mq消息
                        if (CollectionUtils.isEmpty(recordNameList)) {
                            log.error("[FastUploadController#get_task_by_vin] recordNameList is empty, " +
                                    "vehicleUploadRequest: {}", JacksonUtil.serialize(vehicleUploadRequest));
                            return Response.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode().toString(),
                                    "[FastUploadController#get_task_by_vin] recordNameList is empty");
                        }
                        log.info("[FastUploadController#get_task_by_vin] " +
                                        "duplicate task! call method [succeedUploadFile], vehicleUploadRequest: {}",
                                JacksonUtil.serialize(vehicleUploadRequest));
                        vehicleRecFileService.
                                succeedUploadFile(vin, vehicleUploadRequest.getId(), recordNameList.get(0));
                        vehicleRecFileService.updateDuplicateTaskStatusByTaskId(
                                vehicleUploadRequest,
                                vehicleUploadTaskModuleUploadTimeListResultDTO.getTaskIdListStr());

                        // 对于完全重复的任务都不下发
                        log.info("[FastUploadController#get_task_by_vin] " +
                                "duplicate task, skip, vehicleUploadRequest: {}", JacksonUtil.serialize(vehicleUploadRequest));
                        return Response.succ(WebResponseStatusEnum.DATA_IS_ALREADY_IN_DATABASE.getCode().toString(),
                                WebResponseStatusEnum.DATA_IS_ALREADY_IN_DATABASE.getMsg(), null);
                    }

                }
                BeanUtils.copyProperties(vehicleUploadRequest, vo);
                FastUploadTaskTypeEnum fastUploadTaskTypeEnum = FastUploadTaskTypeEnum.byOrdinal(vehicleUploadRequest.getTaskType());
                switch (Objects.requireNonNull(fastUploadTaskTypeEnum)) {
                    case BASE:
                        BizOnboardPreUploadFile preUploadFile =
                                onboardPreUploadFileService.selectByUploadTaskId(vehicleUploadRequest.getId());
                        if (preUploadFile != null) {
                            vo.setFileType(preUploadFile.getFileType());
                            vo.setFileAbsolutePath(preUploadFile.getFileAbsolutePath());
                            vo.setRecordName(preUploadFile.getRecordName());
                            vo.setFilePath(preUploadFile.getFilePath());
                        } else {
                            log.error("[FastUploadController#get_task_by_vin] " +
                                    "get BizOnboardPreUploadFile by upload_task_id:{} failed.", vehicleUploadRequest.getId());
                        }
                        break;
                    case MAP_COLLECT_DOWN_SAMPLING:
                        // 从extraInfo中提取降采样参数
                        String downSampleParamStr = "";
                        JsonNode extraInfoJson = JacksonUtil.fromJson(vehicleUploadRequest.getExtraInfo(), JsonNode.class);
                        if (extraInfoJson != null) {
                            JsonNode downSampleParamJson = extraInfoJson.get(CommonConstant.DOWN_SAMPLE_PARAM);
                            if (downSampleParamJson != null) {
                                downSampleParamStr = JacksonUtil.getMapper().writeValueAsString(downSampleParamJson);
                            }
                        }
                        if (StringUtils.isEmpty(downSampleParamStr)) {
                            downSampleParamStr =
                                    JacksonUtil.getMapper().writeValueAsString(SysParamsConfig.map_collect_down_sampling_param);
                        }
                        vo.setDownSampleParam(downSampleParamStr);
                        break;
                    case COREDUMP:
                        vo.setRecordName(coredumpStrategy.getRecordNameByEventId(vehicleUploadRequest.getEventId()));
                        break;
                    case CASE:
                    default:
                        break;
                }
                fastUploadTasks.add(vo);
            }
            log.info("[FastUploadController#get_task_by_vin] " +
                    "get task succeed, fastUploadTasks:{}", JacksonUtil.serialize(fastUploadTasks));
            return Response.succ(fastUploadTasks);
        } catch (Exception e) {
            result.put("success", false);
            result.put("msg", e.getMessage());
            log.error("getDataParam failed", e);

        }
        return Response.fail("-1", (String) result.get("msg"));
    }

    @MethodDoc(
            displayName = "根据VIN获取目前未完成的上传任务列表",
            description = "根据VIN获取目前未完成的上传任务列表",
            parameters = {@ParamDoc(name = "vin", description = "车架号")},
            returnValueDescription = "当前车辆未完成的快速上传任务列表",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": []\n" +
                    "}"
    )
    @GetMapping("/get_incomplete_tasks_by_vin")
    public Response getIncompleteTasks(@RequestParam("vin") String vin) {
        final String logPrefix = "[/fast_upload/get_incomplete_tasks_by_vin]";
        if (StringUtils.isBlank(vin)) {
            log.info("{} Get incomplete task by vin failed, for lack of vin", logPrefix);
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(), "lack of vin");
        }
        Map<String, Object> result = new HashMap<>(2);
        try {
            //获取Status==CREATED的任务列表
            List<VehicleUploadRequest> vehicleUploadRequestList = vehicleRecFileService.selectTaskListByVinAndStatus(vin,
                    FastUploadTaskStatusEnum.CREATED.getCode());
            List<VehicleUploadRequestBaseInfoVo> voList = new ArrayList<>();
            for (VehicleUploadRequest task : vehicleUploadRequestList) {
                VehicleUploadRequestBaseInfoVo vo = new VehicleUploadRequestBaseInfoVo();
                BeanUtils.copyProperties(task, vo);
                voList.add(vo);
            }
            return Response.succ(voList);
        } catch (Exception e) {
            result.put("success", false);
            result.put("msg", e.getMessage());
            log.error("{} exception: {}, vin: {}", logPrefix, e.getMessage(), vin, e);
        }
        return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), (String) result.get("msg"));
    }

    @GetMapping("/booster")
    public Response uploadFile(@RequestParam("fastId") Long fastId,
                               @RequestParam("isSpeedUp") Boolean isSpeedUp) {
        try {
            User user = UserUtils.getUser();
            String misId = user.getLogin();
            log.info("start to speed up, fastId is {}, isSpeedUp is {}, misId is {}", fastId, isSpeedUp, misId);
            WebResponseStatusEnum result;
            if (isSpeedUp) {
                result = fastUploadService.speedUpWithFastId(fastId);
            } else {
                result = fastUploadService.closeSpeedUpWithFastId(fastId);
            }
            if (result == WebResponseStatusEnum.SUCCESS) {
                return Response.succ();
            }
            return Response.fail(result.getCode().toString(), result.getMsg());
        } catch (Exception e) {
            log.error("fast upload speed up failed, fastId is {}, isSpeedUp is {}", fastId, isSpeedUp, e);
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "service error");
    }

    @MethodDoc(
            displayName = "获取快速上传任务列表",
            description = "根据条件获取快速上传任务列表",
            parameters = {
                    @ParamDoc(name = "BizAccidentInfoRequest", description = "快速上传任务请求参数")
            },
            returnValueDescription = "快速上传任务列表",
            restExampleResponseData = "{\n" +
                    "\t\"ret\": \"0\",\n" +
                    "\t\"msg\": \"ok\",\n" +
                    "\t\"data\": {\n" +
                    "\t\t\"page\": 1,\n" +
                    "\t\t\"size\": 2,\n" +
                    "\t\t\"total\": 7223,\n" +
                    "\t\t\"result\": [{\n" +
                    "\t\t\t\"id\": 5,\n" +
                    "\t\t\t\"vin\": \"3LN6L5SU1JR622134\",\n" +
                    "\t\t\t\"start\": 1617954180000,\n" +
                    "\t\t\t\"end\": 1617954240000,\n" +
                    "\t\t\t\"measurementTimestamp\": 0,\n" +
                    "\t\t\t\"module\": \"Canbus,Control,Localization,Perception,Planning,Prediction\",\n" +
                    "\t\t\t\"status\": 100,\n" +
                    "\t\t\t\"priority\": 10,\n" +
                    "\t\t\t\"isDiscern\": 0,\n" +
                    "\t\t\t\"uuid\": \"\",\n" +
                    "\t\t\t\"createTime\": 1617964142000,\n" +
                    "\t\t\t\"updateTime\": 1617964859000,\n" +
                    "\t\t\t\"recordName\": \"20210409_154251_mkz-00\",\n" +
                    "\t\t\t\"eventId\": \"\",\n" +
                    "\t\t\t\"taskType\": 1,\n" +
                    "\t\t\t\"isDeleted\": \"0\"\n" +
                    "\t\t}, {\n" +
                    "\t\t\t\"id\": 9,\n" +
                    "\t\t\t\"vin\": \"LMTZSV018LC000012\",\n" +
                    "\t\t\t\"start\": 1618289400000,\n" +
                    "\t\t\t\"end\": 1618289430000,\n" +
                    "\t\t\t\"measurementTimestamp\": 0,\n" +
                    "\t\t\t\"module\": \"Canbus,Control,Localization,Perception,Planning,CameraThumbnail\",\n" +
                    "\t\t\t\"status\": -1,\n" +
                    "\t\t\t\"priority\": 120,\n" +
                    "\t\t\t\"isDiscern\": 0,\n" +
                    "\t\t\t\"uuid\": \"\",\n" +
                    "\t\t\t\"createTime\": 1618290496000,\n" +
                    "\t\t\t\"updateTime\": 1618896664000,\n" +
                    "\t\t\t\"recordName\": \"\",\n" +
                    "\t\t\t\"eventId\": \"\",\n" +
                    "\t\t\t\"taskType\": 1,\n" +
                    "\t\t\t\"isDeleted\": \"0\"\n" +
                    "\t\t}]\n" +
                    "\t}\n" +
                    "}"
    )
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Response query(FastUploadTaskRequest fastUploadTaskRequest) {
        Transaction t = Cat.newTransaction(CatConstant.VehicleUploadRequest, "fast.upload.task.list");
        long start = System.currentTimeMillis();
        if (fastUploadTaskRequest == null) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param is illegal");
        }
        // 非逻辑删除的记录
        fastUploadTaskRequest.setIsDeleted("0");
        PageResponse queryDTO = new PageResponse();
        try {
            List<VehicleUploadRequestResultVO> resultVOList = vehicleRecFileService.selectByPage(fastUploadTaskRequest);
            queryDTO.setPage(fastUploadTaskRequest.getPage());
            queryDTO.setSize(fastUploadTaskRequest.getSize());
            queryDTO.setTotal(vehicleRecFileService.countByPage(fastUploadTaskRequest));
            queryDTO.setResult(resultVOList);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(queryDTO);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Get fast upload task list fail, request param is {}", fastUploadTaskRequest, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "get fast upload task list fail");
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "批量获取快速上传任务详细信息",
            description = "批量获取快速上传任务详细信息"
    )
    @RequestMapping(value = "/tasks_detail", method = RequestMethod.GET)
    public Response tasksDetail(FastUploadTaskRequest fastUploadTaskRequest) {
        if (StringUtils.isBlank(fastUploadTaskRequest.getFastIds())) {
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.toString(), "lack params");
        }
        String fastIds = fastUploadTaskRequest.getFastIds();
        try {
            List<FastUploadTaskDetailVO> resultVOList =
                    vehicleRecFileService.getTasksDetails(fastUploadTaskRequest.getFastIds());
            return Response.succ(resultVOList);
        } catch (Exception e) {
            log.error("Query tasks detail fail, taskIds is {}", fastIds, e);
        }
        return Response.fail(WebResponseStatusEnum.SYSTEM_ERROR.toString(), "service error");
    }

    // 任务创建不影响状态机，暂不做灰度
    @MethodDoc(
            displayName = "新增快速上传任务",
            description = "新增快速上传任务",
            parameters = {
                    @ParamDoc(name = "fastUploadTask", description = "快速上传任务对象")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Response add(@RequestBody FastUploadTaskRequest fastUploadTask) {
        log.info("fastUploadTask: {}", fastUploadTask);
        Response response = checkFastUploadParam(fastUploadTask);
        if (response != null) {
            return response;
        }

        Integer taskType = fastUploadTask.getTaskType();
        String vin = fastUploadTask.getVin();
        Long timestamp = fastUploadTask.getMeasurementTimestamp();
        VehicleUploadRequest vehicleUploadRequest;
        try {
            if (taskType == FastUploadTaskTypeEnum.CASE.getCode()) {
                if (timestamp == null || timestamp == 0) {
                    return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.toString(),
                            "measurement timestamp is illegal");
                }
                VehicleUploadRequestVO vehicleUploadRequestVO = new VehicleUploadRequestVO();
                vehicleUploadRequestVO.setVin(vin);
                vehicleUploadRequestVO.setMeasurementTimestamp(timestamp);
                vehicleUploadRequestVO.setCreator(fastUploadTask.getCreator());
                vehicleUploadRequestVO.setPriority(FastUploadTaskPriorityConstant.NON_OPERATIONAL);
                vehicleUploadRequestVO.setExtraInfo(FastUploadConfig.VEHICLE_UPLOAD_CASE_DATA_EXTRA_INFO);
                vehicleUploadRequest = vehicleUploadRequestService.addRequestFromEventIntervention(vehicleUploadRequestVO,
                        false, null);
                if (vehicleUploadRequest == null) {
                    return Response.fail(WebResponseStatusEnum.FAST_UPLOAD_CREATE_FAILED.getCode().toString(),
                            WebResponseStatusEnum.FAST_UPLOAD_CREATE_FAILED.getMsg() +
                                    ", task type: " + FastUploadTaskTypeEnum.CASE.getCode());
                }
            } else if (taskType == FastUploadTaskTypeEnum.FILE_PATH.getCode()) {
                Date startDate = DatetimeUtil.covertToDate(fastUploadTask.getStart(), DatetimeUtil.YMDHMS);
                Date endDate = DatetimeUtil.covertToDate(fastUploadTask.getEnd(), DatetimeUtil.YMDHMS);
                fastUploadTask.setStartDate(startDate);
                fastUploadTask.setEndDate(endDate);
                if(Objects.equals(fastUploadTask.getModule(), CommonConstant.MSTAT_PREFIX)){
                    vehicleUploadRequest = vehicleUploadRequestService.createMstatFastTask(fastUploadTask);
                } else if (Objects.equals(fastUploadTask.getModule(), CommonConstant.EU_LATENCY_PREFIX)) {
                    vehicleUploadRequest = vehicleUploadRequestService.createFilePathFastTask(fastUploadTask, false);
                } else {
                    vehicleUploadRequest = vehicleUploadRequestService.createCanDataFastTask(fastUploadTask);
                }
            } else {
                vehicleUploadRequest = new VehicleUploadRequest();
                BeanUtils.copyProperties(fastUploadTask, vehicleUploadRequest);
                vehicleUploadRequestService.setTaskInfoForNotCase(fastUploadTask, vehicleUploadRequest);
                int result = vehicleUploadRequestService.add(vehicleUploadRequest);
                if (result < 1) {
                    return Response.fail(WebResponseStatusEnum.FAST_UPLOAD_CREATE_FAILED.getCode().toString(),
                            "Add fast upload task fail, an identical task already exists.");
                }
                // 同下发逻辑
                VehicleUploadTaskModuleUploadTimeListResultDTO vehicleUploadTaskModuleUploadTimeListResultDTO =
                        vehicleRecFileService.getFastTaskModuleUploadTimeList(vehicleUploadRequest);
                Map<String, List<FastUploadModuleTimeVO>> fastTaskModuleTimeMap =
                        vehicleUploadTaskModuleUploadTimeListResultDTO.getFastTaskModuleTimeMap();
                List<String> recordNameList = vehicleUploadTaskModuleUploadTimeListResultDTO.getRecordNameList();
                boolean ifDuplicate =
                        vehicleRecFileService.isDuplicateTask(vehicleUploadRequest, fastTaskModuleTimeMap);
                if (ifDuplicate) {
                    // 仅当任务重复 更新任务状态并发送mq消息
                    if (!CollectionUtils.isEmpty(recordNameList)) {
                        log.info("[FastUploadController#add] duplicate task! call method [succeedUploadFile], " +
                                "vehicleUploadRequest: {}", JacksonUtil.serialize(vehicleUploadRequest));
                        vehicleRecFileService.
                                succeedUploadFile(vin, vehicleUploadRequest.getId(), recordNameList.get(0));
                        vehicleRecFileService.updateDuplicateTaskStatusByTaskId(
                                vehicleUploadRequest,
                                vehicleUploadTaskModuleUploadTimeListResultDTO.getTaskIdListStr());
                    }
                }
            }
            VehicleUploadRequestResultVO vo = new VehicleUploadRequestResultVO();
            BeanUtils.copyProperties(vehicleUploadRequest, vo);
            return Response.succ(vo);
        } catch (Exception e) {
            log.error("Add fast upload task fail, request param is {}", fastUploadTask, e);
        }
        return Response.fail("1", "add fast upload task fail");
    }

    private Response checkFastUploadParam(FastUploadTaskRequest fastUploadTask) {
        if (fastUploadTask == null) {
            log.warn("check fast_upload task create parameter failed, fastUploadTask is null");
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.toString(), "Param is illegal");
        }
        String misId;
        // 优先使用传入的creator字段
        if (StringUtils.isNotBlank(fastUploadTask.getCreator())) {
            misId = fastUploadTask.getCreator();
        } else {
            User user = UserUtils.getUser();
            misId = user != null ? user.getLogin() : null;
        }
        if (StringUtils.isBlank(misId)) {
            log.warn("lack misId parameter when create fast_upload task");
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    "lack creator parameter");
        }
        fastUploadTask.setCreator(misId);
        Integer taskType = fastUploadTask.getTaskType();
        if (taskType == null || taskType == 0) {
            log.warn("taskType={}, is not expected", taskType);
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.toString(), "Task Type is illegal");
        }

        if (StringUtils.isBlank(fastUploadTask.getModule())) {
            log.warn("module is empty, is not expected");
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.toString(), "module is empty");
        }

        if (fastUploadTask.getTaskType() != FastUploadTaskTypeEnum.CASE.getCode()) {
            if (StringUtils.isBlank(fastUploadTask.getStart()) || StringUtils.isBlank(fastUploadTask.getEnd())) {
                log.warn("parameter start and end is null, is not expected");
                return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.toString(),
                        "start_time and end_time not be null");
            }
            Long startTimestamp = DatetimeUtil.covertToDate(fastUploadTask.getStart(), DatetimeUtil.YMDHMS).getTime();
            Long endTimestamp = DatetimeUtil.covertToDate(fastUploadTask.getEnd(), DatetimeUtil.YMDHMS).getTime();

            JSONObject maxDurationMinutesMap = JSONObject.parseObject(FastUploadConfig.TASK_MAX_DURATION_MINUTES_MAP);
            int maxDurationMinute = (int) maxDurationMinutesMap.getOrDefault(taskType, -1);
            if (maxDurationMinute > 0) {
                int maxDurationMillis = maxDurationMinute * TimeConstant.S_TO_MINUTE * TimeConstant.MS_TO_S;
                if ((endTimestamp - startTimestamp) > maxDurationMillis) {
                    log.warn("taskType={}, duration should not larger than {} minutes", taskType, maxDurationMinute);
                    return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                            String.format("时间跨度不可超过%s分钟", maxDurationMinute));
                }
            }
        }

        String vin = fastUploadTask.getVin();
        String vehicleName = fastUploadTask.getVehicleName();
        if (StringUtils.isBlank(vin) && StringUtils.isNotBlank(vehicleName)) {
            vin = vehicleInfoService.getVin(vehicleName);
            fastUploadTask.setVin(vin);
        }
        if (StringUtils.isBlank(vin)) {
            log.warn("request param lack vin field, is not expected");
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.toString(), "vin is illegal");
        }
        Boolean isRecording = fastUploadTask.getIsRecording();
        if (isRecording != null && isRecording) {
            String[] baseModuleArr = FastUploadConfig.VEHICLE_UPLOAD_REQUEST_TASK_ACCIDENT_REQUIRED_MODULES
                    .split(CharConstant.CHAR_DD);
            for (String baseModule : baseModuleArr) {
                if (!fastUploadTask.getModule().contains(baseModule)) {
                    return Response.fail(WebResponseStatusEnum.FAST_UPLOAD_ADD_OSVIZ_TASK_FAILED.getCode().toString(),
                            WebResponseStatusEnum.FAST_UPLOAD_ADD_OSVIZ_TASK_FAILED.getMsg());
                }
            }
        }
        return null;
    }

    @MethodDoc(
            displayName = "新增事故快速上传任务",
            description = "新增事故快速上传任务",
            parameters = {
                    @ParamDoc(name = "fastUploadTask", description = "快速上传任务对象"),
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )
    @RequestMapping(value = "/add/accident", method = RequestMethod.POST)
    public Response addAccidentTask(@RequestBody FastUploadTaskRequest fastUploadTask) {
        Transaction t = Cat.newTransaction(CatConstant.VehicleUploadRequest, "fast.upload.task.add.accident");
        long startMillis = System.currentTimeMillis();
        if (fastUploadTask == null || fastUploadTask.getVin() == null || fastUploadTask.getAccidentId() == null) {
            t.setDurationInMillis(System.currentTimeMillis() - startMillis);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param is illegal");
        }
        Long taskId;
        try {
            User user = UserUtils.getUser();
            String misId = user.getLogin();
            Date start = DatetimeUtil.covertToDate(fastUploadTask.getStart(), DatetimeUtil.YMDHMS);
            Date end = DatetimeUtil.covertToDate(fastUploadTask.getEnd(), DatetimeUtil.YMDHMS);
            fastUploadTask.setPriority(FastUploadTaskPriorityConstant.ACCIDENT);
            fastUploadTask.setCreator(misId);
            fastUploadTask.setStartDate(start);
            fastUploadTask.setEndDate(end);
            if (fastUploadTask.getTaskType() != null &&
                    fastUploadTask.getTaskType().equals(FastUploadTaskTypeEnum.FILE_PATH.getCode())) {
                // 事故增加8的数据，表示上传candata
                VehicleUploadRequest request = vehicleUploadRequestService.createCanDataFastTask(fastUploadTask);
                taskId = request.getId();
            } else {
                VehicleUploadRequest task = new VehicleUploadRequest();
                BeanUtils.copyProperties(fastUploadTask, task);
                task.setPriority(FastUploadTaskPriorityConstant.SPEED_UP_ACCIDENT);
                task.setTaskType(FastUploadTaskTypeEnum.ACCIDENT.getCode());
                task.setCreator(misId);
                if (fastUploadTask.getStart() != null) {
                    task.setStart(start);
                }
                if (fastUploadTask.getEnd() != null) {
                    task.setEnd(end);
                }
                if (fastUploadTask.getIsCase() != null) {
                    task.setOneCase(fastUploadTask.getIsCase() == 1);
                }
                task.setExtraInfo(FastUploadConfig.VEHICLE_UPLOAD_ACCIDENT_DATA_EXTRA_INFO);

                task.setModule(FastUploadConfig.VEHICLE_UPLOAD_REQUEST_MODULE_LIST +
                        "," + FastUploadConfig.ACCIDENT_TASK_EXTRA_MODULES);
                vehicleUploadRequestService.add(task);
                taskId = task.getId();
            }
            // 添加当前磁盘
            String diskId = accidentInfoService.getCurrentDiskId(fastUploadTask.getVin());
            if (taskId != null && taskId > 0 && fastUploadTask.getAccidentId() > 0) {
                BizAccidentInfo accidentInfo = accidentInfoService.getById(fastUploadTask.getAccidentId());
                if (accidentInfo != null) {
                    String taskIds = accidentInfo.getUploadTaskId();
                    if (taskIds == null || taskIds.isEmpty()) {
                        taskIds = String.valueOf(taskId);
                    } else {
                        taskIds = taskIds + CharConstant.CHAR_DD + taskId;
                    }
                    // 缓存车盘关系
                    accidentInfoService.cacheAccidentDiskRelations(taskId, diskId);
                    accidentInfo.setUploadTaskId(taskIds);
                    accidentInfoService.update(accidentInfo);
                }
            }
            t.setDurationInMillis(System.currentTimeMillis() - startMillis);
            t.setSuccessStatus();
            return Response.succ("");
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startMillis);
            log.error("Add fast upload task fail, request param is {}", fastUploadTask, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "add fast upload task fail");
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "更新任务",
            description = "更新任务",
            parameters = {
                    @ParamDoc(name = "fastUploadTask", description = "快速上传任务")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Response update(@RequestBody FastUploadTaskRequest fastUploadTask) {
        Transaction t = Cat.newTransaction(CatConstant.VehicleUploadRequest, "fast.upload.task.update");
        long start = System.currentTimeMillis();
        if (fastUploadTask == null) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param is illegal");
        }
        VehicleUploadRequest task = new VehicleUploadRequest();
        BeanUtils.copyProperties(fastUploadTask, task);
        if (fastUploadTask.getStart() != null) {
            task.setStart(DatetimeUtil.covertToDate(fastUploadTask.getStart(), DatetimeUtil.YMDHMS));
        }
        if (fastUploadTask.getEnd() != null) {
            task.setEnd(DatetimeUtil.covertToDate(fastUploadTask.getEnd(), DatetimeUtil.YMDHMS));
        }
        if (fastUploadTask.getIsCase() != null) {
            task.setOneCase(fastUploadTask.getIsCase() == 1);
        }
        try {
            VehicleUploadRequest taskInDb = vehicleRecFileService.getById(task.getId());
            if (taskInDb == null) {
                return Response.fail(WebResponseStatusEnum.DATA_NOT_FOUND.getCode().toString(),
                        WebResponseStatusEnum.DATA_NOT_FOUND.getMsg());
            }
            if (fastUploadTask.getIsRecording() != null && fastUploadTask.getIsRecording()) {
                String[] baseModuleArr = FastUploadConfig.VEHICLE_UPLOAD_REQUEST_TASK_ACCIDENT_REQUIRED_MODULES
                        .split(CharConstant.CHAR_DD);
                for (String baseModule : baseModuleArr) {
                    if (!taskInDb.getModule().contains(baseModule)) {
                        return Response.fail(
                                WebResponseStatusEnum.FAST_UPLOAD_ADD_OSVIZ_TASK_FAILED.getCode().toString(),
                                WebResponseStatusEnum.FAST_UPLOAD_ADD_OSVIZ_TASK_FAILED.getMsg());
                    }
                }
                // 录屏可能是任务上传完成后点击的，此时需要单独调用录屏方法。
                taskInDb.setIsRecording(true);
                osvizService.createOsMvizTaskForFastTask(taskInDb);
            }
            vehicleRecFileService.update(task);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ("");
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Update fast upload task fail, request param is {}", fastUploadTask, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "update fast upload task fail");
        } finally {
            t.complete();
        }
    }

    @PostMapping("/fast_upload_file")
    public Response fastUploadFile(FastUploadFileRequest fileInfo,
                                   @RequestParam("data") MultipartFile file) {
        Transaction t = Cat.newTransaction(CatConstant.VehicleUploadRequest, "fast.upload.file");
        long start = System.currentTimeMillis();
        try {
            if (file == null || file.getSize() == 0) {
                log.warn("upload {} file equals null, in task {}", fileInfo.getOriginFileAbsPath(),
                        fileInfo.getUploadTaskId());
                return Response.fail(WebResponseStatusEnum.FAST_UPLOAD_FILE_IS_NULL.getCode().toString(),
                        "file is null");
            }
            log.info("begin to upload file: {}, in task {}", fileInfo.getOriginFileAbsPath(),
                    fileInfo.getUploadTaskId());
            boolean succeed = vehicleUploadVersionAdapter.fastUploadFile(fileInfo, file);

            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            if (succeed) {
                return Response.succ();
            }
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            try {
                log.error("fast upload file fail, request param is {}", JacksonUtil.toJson(fileInfo), e);
            } catch (JsonProcessingException jsonProcessingException) {
                log.error("JacksonUtil to Json fail", e);
            }
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.FAST_UPLOAD_FILE_UPLOAD_FAIL.getCode().toString(),
                "upload fail!");
    }

    @MethodDoc(
            displayName = "删除任务",
            description = "删除任务",
            parameters = {
                    @ParamDoc(name = "fastUploadTask", description = "快速上传任务")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public Response delete(@RequestParam String id) {
        Transaction t = Cat.newTransaction(CatConstant.VehicleUploadRequest, "fast.upload.task.delete");
        long start = System.currentTimeMillis();
        if (id == null) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param is illegal");
        }
        try {
            long uploadId = Long.parseLong(id);
            VehicleUploadRequest uploadRequest = vehicleRecFileService.getById(uploadId);
            String userLogin = UserUtils.getUser().getLogin();
            if (!SysParamsConfig.tag_report_white_list.contains(userLogin) && !userLogin.equals(uploadRequest.getCreator())) {
                log.info("delete task failed, user: {}, task creator: {}, white list: {}",
                        userLogin, uploadRequest.getCreator(), SysParamsConfig.tag_report_white_list);
                return Response.fail(WebResponseStatusEnum.FORBIDDEN.getCode().toString(),
                        WebResponseStatusEnum.FORBIDDEN.getMsg());
            }
            log.info("start to delete fast_upload task, id={}, misId={}", id, userLogin);
            vehicleRecFileService.deleteTaskById(uploadId);
            vehicleRecFileService.deleteFileByTaskId(id);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ("");
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("delete fast {} upload task fail, request param is {}", id, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "delete fast upload task fail");
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "创建者列表",
            description = "创建者列表",
            parameters = {
                    @ParamDoc(name = "queryGroupMembersByMisId", description = "查询创建者列表")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )
    @GetMapping(value = "/members")
    public CommonResponse queryGroupMembersByMisId(@RequestParam(required = false) String misId) {
        Long start = System.currentTimeMillis();
        Transaction t = Cat.newTransaction(CatConstant.VehicleUploadRequest, "fast.upload.task.members");
        try {
            CommonResponse response = CommonResponse
                    .success(vehicleUploadRequestService.queryGroupMembersByMisId(misId));
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return response;
        } catch (Exception e) {
            log.error("queryGroupMembersByMisId error: {}", misId, e);
            Cat.logError(e);
            t.setStatus(e);
        }
        return CommonResponse.fail();
    }

    /**
     * 不是车端调用的接口，和车端快速回传无关，调用方不明
     * https://km.sankuai.com/collabpage/1554455097
     */
    @GetMapping(value = "/vehicle_detail")
    public Response vehicleDetail(String vin) {
        if (StringUtils.isBlank(vin)) {
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    WebResponseStatusEnum.PARAMETER_ERROR.getMsg());
        }
        try {
            FastUploadTaskVO result = vehicleRecFileService.queryUploadDetailByVin(vin);
            return Response.succ(result);
        } catch (Exception e) {
            log.error("get vehicle upload detail failed, vin={}", vin, e);
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(),
                WebResponseStatusEnum.LOGICAL_EXCEPTION.getMsg());
    }

    @GetMapping("/shared_files_status")
    public Response checkSharedFilesUploadStatus(@RequestParam String recordName) {
        try {
            if (StringUtils.isBlank(recordName)) {
                return Response.fail(ResponseCodeEnum.LACK_PARAMETER.getCode(), "Record name cannot be empty.");
            }
            Map<String, Boolean> sharedFileStatus = vehicleRecFileService.getSharedFileStatus(recordName);
            log.info("[/fast_upload/shared_files_status] recordName: {}, sharedFileStatus: {}",
                    recordName, JacksonUtil.serialize(sharedFileStatus));
            return Response.succ(sharedFileStatus);
        } catch (Exception e) {
            log.error("[/fast_upload/shared_files_status] exception: {}, recordName: {}", e.getMessage(), recordName, e);
            return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), e.getMessage());
        }
    }

    /**
     * ------------------------------------快速回传迭代2.0接口------------------------------------------
     * -------------------------------------车端调用接口-----------------------------------------------
     * <a href="https://km.sankuai.com/collabpage/2526918783">车云接口变更文档</a>
     */


    @GetMapping("/vehicle/get_task_by_vin")
    public Response getTaskByVinV2(@RequestParam("vin") String vin) {
        if (StringUtils.isBlank(vin)) {
            log.info("Get task by vin failed, for lack of vin");
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(), "lack of vin");
        }
        if (vehicleUploadVersionAdapter.isVehicleUploadV1(vin)) {
            // v1 版本
            log.info("[FastUploadControllerV2#/vehicle/get_task_by_vin] {} isVehicleUploadV1", vin);
            return getDataParam(vin);
        }
        log.info("[FastUploadControllerV2#/vehicle/get_task_by_vin] {} isVehicleUploadV2", vin);
        if (StringUtils.isNotBlank(vehicleInfoService.getVehicleName(vin))) {
            monitorService.report(vin);
        }
        try {
            final List<FastUploadTaskVO> fastUploadTaskVOList = vehicleUploadTaskService.pullVehicleUploadTask(vin);
            log.info("[FastUploadControllerV2#/vehicle/get_task_by_vin] {} fastUploadTaskVOList: {}",
                    vin, JacksonUtil.serialize(fastUploadTaskVOList));
            return Response.succ(fastUploadTaskVOList);
        } catch (Exception e) {
            log.error("[FastUploadControllerV2#/vehicle/get_task_by_vin] get_task_by_vin error", e);
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), e.getMessage());
        }
    }

    @GetMapping("/vehicle/finish_task")
    public Response finishTaskV2(FinishTaskRequest finishTaskRequest) {
        try {
            vehicleUploadVersionAdapter.finishTask(finishTaskRequest);
            return Response.succ();
        } catch (Exception e) {
            log.error("[FastUploadControllerV2#/vehicle/get_task_by_vin] finishTaskV2 error", e);
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), e.getMessage());
        }
    }

    @GetMapping("/vehicle/get_meta_list")
    public Response getMetaList(@RequestParam("id") String id) {
        try {
            VehicleUploadRequest vehicleUploadTask = vehicleRecFileService.getById(Long.valueOf(id));
            List<String> alreadyUploadMetaModuleList =
                    vehicleRecFileService.getAlreadyUploadMetaModuleList(vehicleUploadTask);
            Map<String, List<String>> result = new HashMap<>();
            result.put("alreadyMetaModuleList", alreadyUploadMetaModuleList);
            return Response.succ(result);
        } catch (Exception e){
            log.error("[FastUploadControllerV2#/vehicle/get_meta_list] get_meta_list error", e);
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), e.getMessage());
        }
    }

    /*
    上传文件，暂时不做灰度
     */
    @GetMapping("/vehicle/upload_file")
    public Response uploadFileV2(FastUploadFileRequest fileInfo,
                                 @RequestParam("data") MultipartFile file) {
        return null;
    }


    /**
     * -------------------------------------云端调用接口-----------------------------------------------
     */

    @GetMapping("/cancel_task")
    public Response cancelTask(@RequestParam("vin") String vin,
                               @RequestParam("taskId") Long taskId) {
        try {
            boolean res = vehicleUploadVersionAdapter.cancelTask(vin, taskId);
            return Response.succ(WebResponseStatusEnum.SUCCESS.getCode().toString(), res ? "cancel success" :
                    "cancel fail", res);
        } catch (Exception e) {
            log.error("[FastUploadControllerV2#/cancel_task] cancel_task error", e);
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), e.getMessage());
        }
    }

    /**
     * for 研发：用于提升优先级和修改状态，不做鉴权
     */
    @PostMapping("/update_task/V2")
    public Response updateTask(@RequestBody VehicleUploadRequest vehicleUploadTask) {
        try {
            vehicleUploadVersionAdapter.updateTask(vehicleUploadTask);
            log.info("[/fast_upload/update_task/V2] updateTask: {}", JacksonUtil.serialize(vehicleUploadTask));
            return Response.succ();
        } catch (Exception e) {
            log.error("[FastUploadControllerV2#/update_task] update_task error", e);
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), e.getMessage());
        }
    }

}
