package com.meituan.walle.data.center.entity.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/12/02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ColdRecordMessage extends AbstractEventMessage {
    @JsonProperty(value = "id")
    private Long id;
    @JsonProperty(value = "record_name")
    private String recordName;
    @JsonProperty(value = "s3_is_deleted")
    private Byte s3IsDeleted;
    @JsonProperty(value = "s3_delete_time")
    private Date s3DeleteTime;
}