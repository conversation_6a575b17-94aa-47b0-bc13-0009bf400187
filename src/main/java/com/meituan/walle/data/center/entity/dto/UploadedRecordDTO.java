package com.meituan.walle.data.center.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class UploadedRecordDTO {

    private String recordName;
    private String uploadStage;
    private Long uploadCompleteUnixtimeS;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UploadedRecordDTO that = (UploadedRecordDTO) o;
        return Objects.equals(recordName, that.recordName) && Objects.equals(uploadStage, that.uploadStage);
    }

    @Override
    public int hashCode() {
        return Objects.hash(recordName, uploadStage);
    }
}
