package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/09/24
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "big_demotion_offline")
public class BigDemotionOffline {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * demotion id
     */
    @Column(name = "demotion_id")
    private String demotionId;

    /**
     * demotion reason
     */
    @Column(name = "demotion_type")
    private Integer demotionType;

    /**
     * demotion reason
     */
    @Column(name = "demotion_reason")
    private Integer demotionReason;

    /**
     * demotion开始的时间戳
     */
    @Column(name = "begin_timestamp")
    private Long beginTimestamp;

    /**
     * demotion结束的时间戳
     */
    @Column(name = "end_timestamp")
    private Long endTimestamp;

    /**
     * 车架号
     */
    @Column(name = "vin")
    private String vin;

    /**
     * record name
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * data_source
     */
    @Column(name = "data_source")
    private String dataSource;

    /**
     * 是否处理过, 0:未处理|1:处理过
     */
    @Column(name = "is_discern")
    private Integer isDiscern;

    /**
     * 是否逻辑删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDemotionId() {
        return demotionId;
    }

    public void setDemotionId(String demotionId) {
        this.demotionId = demotionId;
    }

    public Integer getDemotionType() {
        return demotionType;
    }

    public void setDemotionType(Integer demotionType) {
        this.demotionType = demotionType;
    }

    public Integer getDemotionReason() {
        return demotionReason;
    }

    public void setDemotionReason(Integer demotionReason) {
        this.demotionReason = demotionReason;
    }

    public Long getBeginTimestamp() {
        return beginTimestamp;
    }

    public void setBeginTimestamp(Long beginTimestamp) {
        this.beginTimestamp = beginTimestamp;
    }

    public Long getEndTimestamp() {
        return endTimestamp;
    }

    public void setEndTimestamp(Long endTimestamp) {
        this.endTimestamp = endTimestamp;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getRecordName() {
        return recordName;
    }

    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Integer getIsDiscern() {
        return isDiscern;
    }

    public void setIsDiscern(Integer isDiscern) {
        this.isDiscern = isDiscern;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}

