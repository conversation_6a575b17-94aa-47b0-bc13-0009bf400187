package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/14
 */

@Getter
@AllArgsConstructor
public enum AccidentRoadTypeEnum {

    UNKNOWN(-1, "未知"),
    PUBLIC_MAIN_ROAD(1, "公开道路主路"),
    CROSSROAD_WITH_LIGHT(2, "公开道路交叉口-有灯"),
    PUBLIC_PARKING(3, "公开道路停车区域"),
    INTERNAL_ROAD(4, "园区道路"),
    INTERNAL_CROSSROAD(5, "园区道路交叉路口"),
    INTERNAL_PARKING(6, "园区道路停车区域"),
    WAREHOUSE(7, "基地库房"),
    PUBLIC_SIDE_ROAD(8, "公开道路辅路"),
    CROSSROAD_WITHOUT_LIGHT(9, "公开道路交叉口-无灯"),
    OTHER(100, "其他"),
    ;

    private int code;
    private String msg;

    public static List<Object> getCodeMsgMap() {
        List<Object> result = new ArrayList<>();
        for (AccidentRoadTypeEnum accidentRoadInfoEnum : AccidentRoadTypeEnum.values()) {
            List<Object> tmpResult = new ArrayList<>();
            tmpResult.add(accidentRoadInfoEnum.getCode());
            tmpResult.add(accidentRoadInfoEnum.getMsg());
            result.add(tmpResult);
        }
        return result;
    }
}
