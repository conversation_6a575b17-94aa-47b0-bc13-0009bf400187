package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "fusion_vehicle_file")
public class FusionVehicleFile {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    private String remoteName;
    private String remoteRecordName;
    private String vin;
    private String vehicleName;
    private Integer recordDate;
    private String hostIp;
    private String filename;
    private Byte fileType;
    private Long fileSize;
    private Date fileModifyTime;
    private Date fileChangeTime;
    private String discDir;
    private String s3Url;
    private Date createTime;
    private Date updateTime;
}
