package com.meituan.walle.data.center.entity.pojo;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2021/10/13
 */
@Table(name = "hdmap_admap_relation")
public class HdmapAdmapRelation {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 高精地图版本
     */
    @Column(name = "hdmap_version")
    private String hdmapVersion;

    /**
     * 高精地图版本
     */
    @Column(name = "admap_version")
    private String admapVersion;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getHdmapVersion() {
        return hdmapVersion;
    }

    public void setHdmapVersion(String hdmapVersion) {
        this.hdmapVersion = hdmapVersion;
    }

    public String getAdmapVersion() {
        return admapVersion;
    }

    public void setAdmapVersion(String admapVersion) {
        this.admapVersion = admapVersion;
    }
}