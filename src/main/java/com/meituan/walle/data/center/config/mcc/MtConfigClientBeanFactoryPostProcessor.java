package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.MtConfigClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @data 2019/4/19 11:03
 * <p>
 * https://km.sankuai.com/page/28344955
 */
@Slf4j
@Component
public class MtConfigClientBeanFactoryPostProcessor implements BeanFactoryPostProcessor {
    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        MtConfigClient client = new MtConfigClient();
        client.setModel("v2");
        client.setAppkey(MccConstant.MCC_APPKEY);
        client.setId(MccConstant.MCC_CLIENT_ID);
        client.setScanBasePackage(MccConstant.SCAN_BASE_PACKAGE);
        client.init();
        beanFactory.registerSingleton("mtConfigClient", client);
        log.info("MtConfigClient (defaultClient) init");
    }
}
