package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/14
 */

@Getter
@AllArgsConstructor
public enum AccidentFixVerifyResultEnum {

    UNKNOWN(-1, "未知"),
    PASS(1, "验证通过"),
    NOT_PASS(2, "验证不通过"),
    UNABLE_VERIFY(3, "无法验证"),
    OTHER(4, "其他")
    ;

    private int code;
    private String msg;

    public static List<Object> getCodeMsgMap() {
        List<Object> result = new ArrayList<>();
        for (AccidentFixVerifyResultEnum accidentFixVerifyResultEnum : AccidentFixVerifyResultEnum.values()) {
            List<Object> tmpResult = new ArrayList<>();
            tmpResult.add(accidentFixVerifyResultEnum.getCode());
            tmpResult.add(accidentFixVerifyResultEnum.getMsg());
            result.add(tmpResult);
        }
        return result;
    }
}
