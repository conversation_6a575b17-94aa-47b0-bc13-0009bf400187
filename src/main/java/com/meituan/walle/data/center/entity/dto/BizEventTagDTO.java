package com.meituan.walle.data.center.entity.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/08/20
 */
@Setter
@Getter
public class BizEventTagDTO {
    private Long id;
    private String vin;
    private String recordName;
    private Date interventionTime;
    private Long interventionTimestamp;
    private String eventId;
    private Integer eventType;
    private String tagName;
    private String misid;
    private String vehicleName;
    private String description;
    private List<String> pictures;
    private Boolean sendToOncall;
    private Integer status;
    private Boolean isUpload;
    private String startTime;
    private String endTime;
    private Set<String> interventionTypeSet;
}
