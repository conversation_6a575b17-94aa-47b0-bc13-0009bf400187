package com.meituan.walle.data.center.entity.enums;


import com.meituan.walle.data.center.constant.IpcEventTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum VehicleEventTypeEnum {
    CHECK_START(0, -1),
    ROUTING_REQUEST(1, 1),
    ROUTING_RESPONSE(2, 1),
    DRIVING_MODE_CHANGE(3, 1),
    INTERVENTION(13, 0),
    HARDBRAKE(4, 1),
    ESTOP(5, 1),
    PARKING(6, 1),
    MAEB(7, 1),
    DESTINATION_BASE(8, 1),
    DESTINATION(9, 1),
    ELECTRIC_FENCE(10, 1),
    RECORD_INFO(11, 1),
    SPEED_LIMIT(12, 1),
    SCENARIO_SWITCH(14, 1),
    RECORD_END(15, 1),
    HDMAP_EXCEPTION(16, 1),
    BUMPER_TRIGGER(17, 1),
    // 18 ~ 37 归并pnc_parking
    PNC_PARKING(18, 1),
    PNC_PARKING_COMPLETE(19, 1),
    PNC_UTURN(20, 1),
    PNC_UTURN_COMPLETE(21, 1),
    PNC_ROADBLOCK(22, 1),
    PNC_ROADBLOCK_COMPLETE(23, 1),
    PNC_STARTING(24, 1),
    PNC_STARTING_COMPLETE(25, 1),
    FREESPACE_COMPLETE(26, 1),
    REVERSE_TRIGGER(27, 1),
    REVERSE_COMPLETE(28, 1),
    REVERSE_STARTING_COMPLETE(29, 1),
    PNC_PARKING_EXIT(30, 1),
    PARKING_CONFLICT(31, 1),
    UTURN_CONFLICT(32, 1),
    ROADBLOCK_CONFLICT(33, 1),
    FREESPACE_QUEUE(34, 1),
    FREESPACE_QUEUE_COMPLETE(35, 1),
    MEETING(36, 1),
    MEETING_COMPLETE(37, 1),
    // strategy
    SUSPEND(38, 1),
    RESUME(39, 1),
    // 100 ~ 109 归并ipc
    IPC(100, 2),
    // 需要特殊解析的事件
    COREDUMP(110, 2),
    // 来自ipc
    AUTOCAR_RELEASE(111, 2),
    // 执行通用策略
    SPECIFIC(200, -1), //总类
    ONBOARD_OOM(205, 2),
    CALIBRATION_WARNING(302, 1),
    CALIBRATION_ABNORMAL(303, 1),
    SURROUNDING_OBSTACLE_CHANGE(306, 1),
    ONBOARD_MENDER_EVENT(319, 1),
    RISK_ALERT(320, 1),
    COLLISION_DETECTION(322, 1),
    AUTOCAR_LAUNCH_FAILED(327, 1),
    SIDE_BY_SIDE_START(398, 1),
    SIDE_BY_SIDE_END(399, 1),
    ;

    private int code;
    private int datasource; // AutodriveEventDataSourceEnum

    VehicleEventTypeEnum(int code) {
        this.code = code;
    }


    public static VehicleEventTypeEnum getInstance(int code) {
        // 100 ~ 109
        if (code >= IPC.getCode() && code < IpcEventTypeEnum.COREDUMP.getCode()) {
            return VehicleEventTypeEnum.IPC;
        }
        // > 200
        if (code > SPECIFIC.getCode()) {
            return VehicleEventTypeEnum.SPECIFIC;
        }
        // 18 ~ 37
        if (code >= PNC_PARKING.getCode() && code <= MEETING_COMPLETE.getCode()) {
            return VehicleEventTypeEnum.PNC_PARKING;
        }
        VehicleEventTypeEnum[] values = VehicleEventTypeEnum.values();
        for (VehicleEventTypeEnum eventEnum: values) {
            if (eventEnum.getCode() == code) {
                return eventEnum;
            }
        }
        return null;
    }

    public static VehicleEventTypeEnum getPncParkingType(int code) {
        for (VehicleEventTypeEnum eventTypeEnum: VehicleEventTypeEnum.values()) {
            if (eventTypeEnum.getCode() == code) {
                return eventTypeEnum;
            }
        }
        return VehicleEventTypeEnum.PNC_PARKING;
    }

    public static VehicleEventTypeEnum byCode(int code) {
        for (VehicleEventTypeEnum en : VehicleEventTypeEnum.values()) {
            if (en.code == code) {
                return en;
            }
        }
        return null;
    }

}
