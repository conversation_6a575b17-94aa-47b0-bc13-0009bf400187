package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "biz_recommended_routing")
public class BizRecommendedRouting {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 路由ID
     */
    @Column(name = "routing_id")
    private String routingId;

    /**
     * 路线名称
     */
    @Column(name = "routing_name")
    private String routingName;

    /**
     * 路由完成状态[0-未完成|1-已完成
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 车架号
     */
    @Column(name = "vin")
    private String vin;

    /**
     * 车辆名称
     */
    @Column(name = "vehicle_name")
    private String vehicleName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 路由任务uuid
     */
    @Column(name = "routing_task_id")
    private String routingTaskId;

    /**
     * 领取人
     */
    @Column(name = "operator")
    private String operator;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 推荐来源[1-随机推荐 | 0-a*
     */
    @Column(name = "recommend_origin")
    private Integer recommendOrigin;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRoutingId() {
        return routingId;
    }

    public void setRoutingId(String routingId) {
        this.routingId = routingId;
    }

    public String getRoutingName() {
        return routingName;
    }

    public void setRoutingName(String routingName) {
        this.routingName = routingName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }

    public String getRoutingTaskId() {
        return routingTaskId;
    }

    public void setRoutingTaskId(String routingTaskId) {
        this.routingTaskId = routingTaskId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getRecommendOrigin() {
        return recommendOrigin;
    }

    public void setRecommendOrigin(Integer recommendOrigin) {
        this.recommendOrigin = recommendOrigin;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
