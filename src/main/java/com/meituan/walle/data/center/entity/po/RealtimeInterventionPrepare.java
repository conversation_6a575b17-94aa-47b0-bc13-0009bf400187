package com.meituan.walle.data.center.entity.po;

import com.meituan.walle.data.center.entity.pojo.LiveIssueTag;
import com.meituan.walle.data.center.entity.pojo.VehicleUploadRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/28
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class RealtimeInterventionPrepare {

    private List<VehicleUploadRequest> listVehicleUploadRequest;

    private List<LiveIssueTag> listLiveIssueTags;
}