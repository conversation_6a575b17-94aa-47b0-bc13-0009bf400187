package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Table(name = "core_dump")
public class CoreDump {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 触发时间
     */
    @Column(name = "trigger_time")
    private Date triggerTime;

    /**
     * 文件名(名称中含TMP，是原文件名乱码时，trigger_time是处理时间)
     */
    @Column(name = "file_name")
    private String fileName;

    /**
     * 0.初始化|1.补充stack信息|2.分拣coredump|3.已创建case
     */
    private Byte status;

    /**
     * coredump的栈信息
     */
    private String stack;

    /**
     * cases表case_id外键
     */
    private String caseId;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取车架号（车辆设备id）
     *
     * @return vin - 车架号（车辆设备id）
     */
    public String getVin() {
        return vin;
    }

    /**
     * 设置车架号（车辆设备id）
     *
     * @param vin 车架号（车辆设备id）
     */
    public void setVin(String vin) {
        this.vin = vin;
    }

    /**
     * 获取表record的record_name外键
     *
     * @return record_name - 表record的record_name外键
     */
    public String getRecordName() {
        return recordName;
    }

    /**
     * 设置表record的record_name外键
     *
     * @param recordName 表record的record_name外键
     */
    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    /**
     * 获取触发时间
     *
     * @return trigger_time - 触发时间
     */
    public Date getTriggerTime() {
        return triggerTime;
    }

    /**
     * 设置触发时间
     *
     * @param triggerTime 触发时间
     */
    public void setTriggerTime(Date triggerTime) {
        this.triggerTime = triggerTime;
    }

    /**
     * 获取文件名(名称中含TMP，是原文件名乱码时，trigger_time是处理时间)
     *
     * @return file_name - 文件名(名称中含TMP，是原文件名乱码时，trigger_time是处理时间)
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * 设置文件名(名称中含TMP，是原文件名乱码时，trigger_time是处理时间)
     *
     * @param fileName 文件名(名称中含TMP，是原文件名乱码时，trigger_time是处理时间)
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * 获取0.初始化|1.补充stack信息|2.已创建case
     *
     * @return status - 0.初始化|1.补充stack信息|2.已创建case
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置0.初始化|1.补充stack信息|2.已创建case
     *
     * @param status 0.初始化|1.补充stack信息|2.已创建case
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取coredump的栈信息
     *
     * @return stack - coredump的栈信息
     */
    public String getStack() {
        return stack;
    }

    /**
     * 设置coredump的栈信息
     *
     * @param stack coredump的栈信息
     */
    public void setStack(String stack) {
        this.stack = stack;
    }

    public String getCaseId() {
        return caseId;
    }

    public void setCaseId(String caseId) {
        this.caseId = caseId;
    }
}