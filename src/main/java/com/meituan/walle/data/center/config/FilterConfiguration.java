package com.meituan.walle.data.center.config;

import com.meituan.service.inf.kms.client.Kms;
import com.meituan.walle.data.center.config.mcc.BasicAuthConfig;
import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.it.sso.sdk.spring.FilterFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;

import static javax.servlet.DispatcherType.REQUEST;

@Slf4j
@Configuration
public class FilterConfiguration {

    private static final String KMS_SSO_CLIENT_ID = "data.center.sso.client.id";
    private static final String KMS_SSO_SECRET = "data.center.sso.secret";

    @Bean
    public FilterRegistrationBean mtFilter(@Qualifier("mtFilterBean") Filter mtFilterBean) {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(mtFilterBean);
        registration.addUrlPatterns("/*");
        registration.setDispatcherTypes(REQUEST);
        registration.setName("mtFilter");
        registration.setOrder(2);
        return registration;
    }

    @Bean
    public FilterFactoryBean mtFilterBean() {
        FilterFactoryBean filterFactoryBean = new FilterFactoryBean();
        String ssoClientId = null;
        String ssoSecret = null;
        try {
            ssoClientId = Kms.getByName(MccConstant.MCC_APPKEY, KMS_SSO_CLIENT_ID);
            ssoSecret = Kms.getByName(MccConstant.MCC_APPKEY, KMS_SSO_SECRET);
        } catch (Exception e) {
            log.error("get ssox secret failed.", e);
            throw new RuntimeException("get ssox secret failed.", e);
        }
        filterFactoryBean.setClientId(ssoClientId);
        filterFactoryBean.setSecret(ssoSecret);

        /**
         * 不需要 SSO 检查的 Url 配置, 多个以逗号分隔，允许换行
         * 单独配 includedUriList，includedUriList 以外的链接都不检查sso登录
         * 单独配 excludedUriList，excludedUriList 以外的链接都会检查sso登录
         * includedUriList，excludedUriList 都有的时候，includedUriList 优先级更高
         **/
        //---------------以下配置请业务方系统阅读其使用方法再行接入----------------
        //表示需要经过SSO过滤的uri，多个uri以','分割。
        //两者都配置的情况下，includedUriList优先级更高
        filterFactoryBean.setIncludedUriList(BasicAuthConfig.SSO_INCLUDED_URI_LIST);

        /**
         * 根据实际情况指定，如果nginx配置Location时进行了rewrite抹除，
         * 				请在这里填写该location，SDK2.0会在跳转时拼接回正确的url。否则不用填写。
         *         例如：/locationA/uriB经nginx转发后，到后端被重写成/uriB，则/locationA就是rewriteLocation，
         * 				参考nginx配置：
         *         location /locationA/ {
         *             rewrite /locationA/?(.*)$ /$1 break;
         *         }
         * */
        filterFactoryBean.setRewriteLocation(BasicAuthConfig.SSO_REWRITE_LOCATION);

        /**
         * 根据实际情况指定接入SSO线下(取值test)或线上(取值prod)环境。
         * ##请不要接入SSO的dev和staging环境，不提供稳定服务##
         * 默认不需要配置，SDK将根据客户端环境自动对齐，
         * 即dev、test对齐到SSO线下环境，staging和prod对齐到SSO线上环境
         */
//        filterFactoryBean.setAccessEnv("test");//test或prod

        //可不配置，默认预留/sso/logout作为登出地址，业务方可以直接使用该uri
        filterFactoryBean.setLogoutUri("/logout");

        return filterFactoryBean;
    }
}
