package com.meituan.walle.data.center.entity.algorithm.AutoGenRouting;

import java.util.List;
import java.util.Vector;

public class AutoGenRoutingGraph {

    public int n; // number of vertices
    public int[] degree; // degrees of vertices
    public int[] neg; // unbalances vertices with negative degrees
    public int[] pos; // unbalances vertices with positive degrees
    public int[][] path;
    public int[][] edges;
    public int[][] cheapestEdge;
    public int[][] f;
    public boolean[][] defined;
    public Vector[][] label; // names of edges
    public double[][] c;
    public boolean initialised = false;

    public AutoGenRoutingGraph(int vertices) {
        n = vertices;
        degree = new int[n];
        defined = new boolean[n][n];
        label = new Vector[n][n];
        c = new double[n][n];
        f = new int[n][n];
        this.edges = new int[n][n];
        cheapestEdge = new int[n][n];
        path = new int[n][n];
        initialised = true;
    }

    public AutoGenRoutingGraph(int vertices, List<AutoGenRoutingEdge> edges) {
        this.n = vertices;
        degree = new int[n];
        defined = new boolean[n][n];
        label = new Vector[n][n];
        c = new double[n][n];
        f = new int[n][n];
        this.edges = new int[n][n];
        cheapestEdge = new int[n][n];
        path = new int[n][n];
        initialised = true;

        int i = 0;
        for (AutoGenRoutingEdge edge: edges) {
            addEdge(edge.edgeLabel, edge.startVertex, edge.endVertex, edge.cost);
            i = i + 1;
        }
    }

    public void addEdge (String lab, int u, int v, double cost) {
        if (!defined[u][v]) {
            label[u][v] = new Vector();
        }
        label[u][v].addElement(lab);
        if (!defined[u][v] || c[u][v] > cost) {
            c[u][v] = cost;
            cheapestEdge[u][v] = edges[u][v];
            defined[u][v] = true;
            path[u][v] = v;
        }

        edges[u][v]++;
        degree[u]++;
        degree[v]--;
    }
}
