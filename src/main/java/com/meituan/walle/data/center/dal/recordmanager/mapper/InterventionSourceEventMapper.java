package com.meituan.walle.data.center.dal.recordmanager.mapper;

import com.meituan.walle.data.center.entity.po.InterventionSourceEventPO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface InterventionSourceEventMapper {

    @Insert({
            "<script>",
            "insert ignore into intervention_source_event",
            "<trim prefix='(' suffix=')' suffixOverrides=','>",
            "<if test='eventId != null'>event_id,</if>",
            "<if test='eventCode != null'>event_code,</if>",
            "<if test='eventName != null'>event_name,</if>",
            "<if test='eventTimestamp != null'>event_timestamp,</if>",
            "<if test='senderTimestamp != null'>sender_timestamp,</if>",
            "<if test='receiverTimestamp != null'>receiver_timestamp,</if>",
            "<if test='vin != null'>vin,</if>",
            "<if test='vehicleId != null'>vehicle_id,</if>",
            "<if test='vehicleName != null'>vehicle_name,</if>",
            "<if test='recordName != null'>record_name,</if>",
            "<if test='utmZone != null'>utm_zone,</if>",
            "<if test='utmX != null'>utm_x,</if>",
            "<if test='utmY != null'>utm_y,</if>",
            "<if test='datasource != null'>datasource,</if>",
            "<if test='content != null'>content,</if>",
            "</trim>",
            "values",
            "<trim prefix='(' suffix=')' suffixOverrides=','>",
            "<if test='eventId != null'>#{eventId},</if>",
            "<if test='eventCode != null'>#{eventCode},</if>",
            "<if test='eventName != null'>#{eventName},</if>",
            "<if test='eventTimestamp != null'>#{eventTimestamp},</if>",
            "<if test='senderTimestamp != null'>#{senderTimestamp},</if>",
            "<if test='receiverTimestamp != null'>#{receiverTimestamp},</if>",
            "<if test='vin != null'>#{vin},</if>",
            "<if test='vehicleId != null'>#{vehicleId},</if>",
            "<if test='vehicleName != null'>#{vehicleName},</if>",
            "<if test='recordName != null'>#{recordName},</if>",
            "<if test='utmZone != null'>#{utmZone},</if>",
            "<if test='utmX != null'>#{utmX},</if>",
            "<if test='utmY != null'>#{utmY},</if>",
            "<if test='datasource != null'>#{datasource},</if>",
            "<if test='content != null'>#{content},</if>",
            "</trim>",
            "</script>"
    })
    int insert(InterventionSourceEventPO interventionSourceEventPO);

    @Select({
            "<script>",
            "SELECT * FROM intervention_source_event",
            "WHERE vin = #{vin}",
            "AND event_timestamp &gt;= #{startTime}",
            "AND event_timestamp &lt; #{endTime}",
            "ORDER BY event_timestamp ASC",
            "</script>"
    })
    List<InterventionSourceEventPO> selectByTimeRange(@Param("vin") String vin,
                                                      @Param("startTime") String startTime,
                                                      @Param("endTime") String endTime);

}
