package com.meituan.walle.data.center.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.pojo.LabelSyncJob;
import com.meituan.walle.data.center.service.LabelService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@InterfaceDoc(
        displayName = "标签同步任务接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供标签同步任务状态的入库",
        scenarios = "根据同步任务对象状态提交任务"
)
@RestController
@RequestMapping("label")
public class LabelController {
    @Resource
    private LabelService labelService;

    @MethodDoc(
            requestMethods = {HttpMethod.POST},
            displayName = "提交标签同步任务",
            description = "标签同步任务状态入库",
            parameters = {
                    @ParamDoc(name = "labelSyncJob", description = "标签同步任务对象",
                            requiredness = Requiredness.REQUIRED)
            },
            returnValueDescription = "调用成功与否的Response对象，包含code、message等信息"
    )
    @PostMapping("submitSyncJob")
    public Response submitSyncJob(@RequestBody LabelSyncJob labelSyncJob) {
        labelService.saveSyncJob(labelSyncJob);
        return Response.succ();
    }
}