package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * [快速回传2.0] 车辆上传任务状态枚举
 *
 * <AUTHOR>
 * @version v2.0
 */
@Getter
@AllArgsConstructor
public enum VehicleUploadTaskStatusEnum {
    INIT(0, "任务初始化"),   // 为代码编写方便设置INIT状态，在参数校验通过后转为CREATED
    CREATED(5, "任务创建"),
    UPLOADING(50, "任务进行中"),
    PAUSED(49, "暂停"),
    CANCELED(51, "任务已取消"),
    FINISHED(55, "任务结束"),
    FINISHED_SUCCEED(100, "任务结束-数据完整"),
    FINISHED_DATA_MISS(101, "任务结束-数据首尾缺失或不连续"),
    FINISHED_MODULE_MISS(103, "任务结束-重要模块缺失"),
    FINISHED_FAILED(404, "任务结束-失败"),
    ;
    private final Integer code;
    private final String message;

    public static VehicleUploadTaskStatusEnum fromCode(Integer code) {
        for (VehicleUploadTaskStatusEnum status : VehicleUploadTaskStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid VehicleUploadTaskStatusEnum code: " + code);
    }
}
