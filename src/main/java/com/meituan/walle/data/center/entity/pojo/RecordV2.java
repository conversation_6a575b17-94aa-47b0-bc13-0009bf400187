package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import java.util.Date;
import javax.persistence.*;

@Table(name = "record")
@Data
public class RecordV2 {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 表record_batch的bid外键
     */
    private String bid;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * purpose
     */
    private String purpose;

    /**
     * 结束录制时间
     */
    @Column(name = "begin_time")
    private Date beginTime;

    /**
     * 开始录制时间
     */
    @Column(name = "end_time")
    private Date endTime;

    /**
     * 地点
     */
    private String place;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 车辆名称
     */
    @Column(name = "vehicle_name")
    private String vehicleName;

    /**
     * 车辆类型
     */
    @Column(name = "vehicle_type")
    private Byte vehicleType;

    /**
     * 车辆批次
     */
    @Column(name = "vehicle_batch")
    private String vehicleBatch;

    /**
     * 数据大小（单位：byte）
     */
    @Column(name = "data_size")
    private Long dataSize;

    /**
     * 文件数量
     */
    @Column(name = "file_count")
    private Integer fileCount;

    /**
     * 代码打包的版本
     */
    @Column(name = "package_version")
    private String packageVersion;

    /**
     * git branch
     */
    @Column(name = "git_branch")
    private String gitBranch;

    /**
     * git commit
     */
    @Column(name = "git_commit")
    private String gitCommit;

    /**
     * 高精地图版本
     */
    @Column(name = "hdmap_version")
    private String hdmapVersion;

    /**
     * 0.启动trace|1.正常结束|2.数据文件开始上传|3.数据文件结束上传|4.开始解析|5.解析结束|6.异常结束
     */
    private Short status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 高精地图名字
     */
    @Column(name = "hdmap_name")
    private String hdmapName;

    /**
     * git tag
     */
    @Column(name = "git_tag")
    private String gitTag;

    /**
     * driver mis id
     */
    @Column(name = "driver_mis_id")
    private String driverMisId;

    /**
     * 高精地图名字
     */
    @Column(name = "admap_name")
    private String admapName;

    /**
     * 高精地图版本
     */
    @Column(name = "admap_version")
    private String admapVersion;

    /**
     * 信息提取
     */
    @Column(name = "extracted")
    private Integer extracted;

    /**
     * 碎片化
     */
    @Column(name = "fragmented")
    private Integer fragmented;

    /**
     * record在S3中的存储分级
     */
    @Column(name = "s3_level")
    private Integer s3Level;

    /**
     * S3中是否物理删除
     */
    @Column(name = "s3_is_deleted")
    private Integer s3IsDeleted;

    /**
     * S3中删除时间
     */
    @Column(name = "s3_delete_time")
    private Date s3DeleteTime;

    /**
     * S3上传完成[0:否|1:是]
     */
    @Column(name = "s3_is_upload_completed")
    private Integer s3IsUploadCompleted;

    /**
     * HDFS上传完成[0:否|1:是]
     */
    @Column(name = "hdfs_is_upload_completed")
    private Integer hdfsIsUploadCompleted;

    @Column(name = "is_valid")
    private Boolean isValid;

    @Column(name = "invalid_desc")
    private String invalidDesc;

    @Column(name = "is_deleted")
    private Boolean isDeleted;

    @Column(name = "resv_id")
    private String resvId;

    @Column(name = "belong_to")
    private String belongTo;

    @Column(name = "inbound_status")
    private String inboundStatus;

    private String owner;

    @Column(name = "group_name")
    private String groupName;

    @Column(name = "localization_mode")
    private String localizationMode;

    /**
     * 是否是异常record
     */
    @Column(name = " ")
    private String abnormalType;

}