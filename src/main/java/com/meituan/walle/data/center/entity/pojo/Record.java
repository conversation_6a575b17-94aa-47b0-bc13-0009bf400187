package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import java.util.Date;
import javax.persistence.*;

@Data
public class Record {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 表record_batch的bid外键
     */
    private String bid;

    /**
     * 表record的record_name外键
     */
    private String recordName;

    private String purpose;

    /**
     * 结束录制时间
     */
    private Date beginTime;

    /**
     * 开始录制时间
     */
    private Date endTime;

    /**
     * 地点
     */
    private String place;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 车辆类型
     */
    private Byte vehicleType;

    /**
     * 数据大小（单位：byte）
     */
    private Long dataSize;

    /**
     * 文件数量
     */
    private Integer fileCount;

    private String packageVersion;

    /**
     * git branch
     */
    private String gitBranch;

    /**
     * git commit
     */
    private String gitCommit;

    /**
     * 高精地图版本
     */
    private String hdmapVersion;

    /**
     * 0.启动trace|1.正常结束|2.数据文件开始上传|3.数据文件结束上传|4.开始解析|5.解析结束|6.异常结束
     */
    private Short status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    private String hdmapName;

    private String gitTag;

    private String admapName;

    private String admapVersion;

    private Boolean isValid;

    private String invalidDesc;

    private String resvId;
}