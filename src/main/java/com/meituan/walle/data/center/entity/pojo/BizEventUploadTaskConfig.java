package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "biz_event_upload_task_config")
@Data
public class BizEventUploadTaskConfig {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 事件描述
     */
    @Column(name = "event_desc")
    private String eventDesc;

    /**
     * 车辆vin白名单
     */
    @Column(name = "vehicle_white_list")
    private String vehicleWhiteList;

    /**
     * 每日每车使用该配置项创建的快速回收任务最大数量
     */
    @Column(name = "limit_num_per_vehicle_per_day")
    private Integer limitNumPerVehiclePerDay;

    /**
     * 事件发生前时间，单位：秒
     */
    @Column(name = "event_time_before")
    private Integer eventTimeBefore;

    /**
     * 事件发生后时间，单位：秒
     */
    @Column(name = "event_time_later")
    private Integer eventTimeLater;

    /**
     * 模块列表
     */
    @Column(name = "module_list")
    private String moduleList;

    /**
     * 任务的优先级
     */
    @Column(name = "priority")
    private Integer priority ;

    /**
     * 配置注册人misId
     */
    @Column(name = "owner")
    private String owner;

    /**
     * 是否审核通过
     */
    @Column(name = "is_permit")
    private Boolean isPermit;

    /**
     * 是否生效
     */
    @Column(name = "is_effect")
    private Boolean isEffect;

    /**
     * 是否逻辑删除
     */
    @Column(name = "is_deleted")
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否所有车辆都可创建快速回收任务
     */
    @Column(name = "is_all_vehicles_allowed")
    private Boolean isAllVehiclesAllowed;
}
