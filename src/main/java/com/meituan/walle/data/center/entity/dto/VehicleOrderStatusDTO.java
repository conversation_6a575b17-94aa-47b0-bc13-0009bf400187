package com.meituan.walle.data.center.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleOrderStatusDTO {
    /**
     * 是否存在工单
     */
    @JsonProperty("has_order")
    private Boolean hasOrder;

    /**
     * 工单名称
     * 说明: 工单名称【"accidentOrder"-事故工单|"rescueOrder"-救援工单|"transactionOrder"-维保工单】
     */
    @JsonProperty("order_name")
    private String orderName;

    /**
     * 工单状态
     */
    @JsonProperty("order_status")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer orderStatus;

    /**
     * 工单状态描述
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("order_status_desc")
    private String orderStatusDesc;

    /**
     * 数据更新时间
     */
    @JsonProperty("update_time")
    private Long updateTime;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private Long createTime;
}
