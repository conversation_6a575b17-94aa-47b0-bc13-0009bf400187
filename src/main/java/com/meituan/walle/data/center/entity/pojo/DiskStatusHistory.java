package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/08/17
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "disk_status_history")
public class DiskStatusHistory {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 硬盘WWN编码
     */
    @Column(name = "disk_id")
    private String diskId;

    /**
     * 大象用户登录名
     */
    @Column(name = "login_name")
    private String loginName;

    /**
     * 大象用户姓名
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * IDC主机端口号
     */
    @Column(name = "idc_port")
    private String idcPort;

    /**
     * 0.初始化|100.已回收|200.已上车|300.已记录|400.已下车|500.已盘点|650.已识别|
     * 670.自动检查|700.开始上传|800.已完成|850.已卸载|900.已拨盘'
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 车辆名称
     */
    @Column(name = "vehicle_name")
    private String vehicleName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 硬盘名称
     */
    @Column(name = "disk_name")
    private String diskName;

    /**
     * 批次id
     */
    @Column(name = "batch_id")
    private String batchId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDiskId() {
        return diskId;
    }

    public void setDiskId(String diskId) {
        this.diskId = diskId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getIdcPort() {
        return idcPort;
    }

    public void setIdcPort(String idcPort) {
        this.idcPort = idcPort;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDiskName() {
        return diskName;
    }

    public void setDiskName(String diskName) {
        this.diskName = diskName;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }
}

