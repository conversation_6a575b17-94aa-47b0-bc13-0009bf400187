package com.meituan.walle.data.center.config;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.MafkaConstant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2023/3/31
 */
@Configuration
public class MafkaProducerConfig {
    @Value("${appkey}")
    private String appkey;

    @Value("${mafka.producer.mvizAppKey}")
    private String mvizAppKey;

    @Lazy
    @Bean(value = "snapshotProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> snapshotProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_SNAPHSOT, appkey);
    }

    @Lazy
    @Bean(value = "recordProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> recordProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_RECORD, appkey);
    }

    @Lazy
    @Bean(value = "recordFileProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> recordFileProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_RECORD_FILE, appkey);
    }

    @Lazy
    @Bean(value = "recordCirculationProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> recordCirculationProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_RECORD_CIRCULATION, appkey);
    }

    @Lazy
    @Bean(value = "onboardFileMetadataProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> onboardFileMetadataProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_ONBOARD_FILE_METADATA, appkey);
    }

    @Lazy
    @Bean(value = "onboardEventProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> onboardEventProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_ONBOARD_EVENT, appkey);
    }

    @Lazy
    @Bean(value = "realtimeRecordProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> realtimeRecordProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_REALTIME_RECORD, appkey);
    }

    @Lazy
    @Bean(value = "eventSpecificExportProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> eventSpecificExportProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_EVENT_SPECIFIC_EXPORT_TO_HIVE, appkey);
    }

    @Lazy
    @Bean(value = "interventionMileageProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> interventionMileageProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_INTERVENTION_MILEAGE_DELAY, appkey);
    }

    @Lazy
    @Bean(value = "mvizRecorderTaskCollectorProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> mvizRecorderTaskCollectorProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_MVIZ_RECORDER_TASK_COLLECTOR, mvizAppKey);
    }

    @Lazy
    @Bean(value = "toGeneralCaseProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> toGeneralCaseProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_TO_GENERAL_CASE, appkey);
    }

    @Lazy
    @Bean(value = "diskStatusProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> diskStatusProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_DISK_STATUS, appkey);
    }

    private IProducerProcessor<String, String> buildProducer(String topic, String appKey) throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, MafkaConstant.NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appKey);

        if (MafkaConstant.TOPIC_ONBOARD_EVENT.equals(topic)) {
            properties.setProperty("partitioner.class",
                    SysParamsConfig.intervention_mafka_partitioner_class);
        }

        return MafkaClient.buildProduceFactory(properties, topic);
    }

    private IProducerProcessor<String, String> buildDelayProducer(String topic, String appKey) throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, MafkaConstant.NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appKey);
        return MafkaClient.buildDelayProduceFactory(properties, topic);
    }

    @Lazy
    @Bean(value = "fastTaskNoticeProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> fastTaskNoticeProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_USER_FAST_TASK_NOTICE, appkey);
    }

    @Lazy
    @Bean(value = "delayPullRemoteVideoEventProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> delayPullRemoteVideoEventProducer() throws Exception {
        return buildDelayProducer(MafkaConstant.TOPIC_DELAY_PULL_REMOTE_VIDEO_EVENT, appkey);
    }

    @Lazy
    @Bean(value = "onboardEventFileRerunProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> onboardEventFileRerunProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_ONBOARD_FILE_RERUN, MafkaConstant.ETL_APP_KEY);
    }

    @Lazy
    @Bean(value = "recordHdfsSyncNoticeProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> recordHdfsSyncNoticeProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_RECORD_HDFS_SYNC_NOTICE, appkey);
    }

    @Lazy
    @Bean(value = "judgePersonInterventionProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> judgePersonInterventionProducer() throws Exception {
        return buildDelayProducer(MafkaConstant.TOPIC_JUDGE_PERSON_INTERVENTION_MSG, appkey);
    }

    @Lazy
    @Bean(value = "recordForAppProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> recordForAppProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_RECORD_FOR_APP, appkey);
    }

    @Lazy
    @Bean(value = "uploadedRecordProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> uploadedRecordProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_UPLOADED_RECORD, appkey);
    }

    @Lazy
    @Bean(value = "autodriveEventProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> autodriveEventProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_AUTODRIVE_EVENT, appkey);
    }

    @Lazy
    @Bean(value = "outputAccidentMsgProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> outputAccidentMsgProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_OUTPUT_ACCIDENT_MESSAGE, appkey);
    }

    @Lazy
    @Bean(value = "interventionProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> interventionProducer() throws Exception {
        return buildProducer(MafkaConstant.TOPIC_INTERVENTION_LOG, appkey);
    }

    @Lazy
    @Bean(value = "s3CleanupFileProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> s3CleanupFileProducer() throws Exception {
        return buildCastleDaojiaProducer(MafkaConstant.TOPIC_S3_CLEANUP_FILE, appkey);
    }

    @Lazy
    @Bean(value = "inboundRecordSnapshotProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> inboundRecordSnapshotProducer() throws Exception {
        return buildCastleDaojiaProducer(MafkaConstant.TOPIC_INBOUND_RECORD_SNAPSHOT, appkey);
    }

    @Lazy
    @Bean(value = "inboundRecordFileMetadataProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> inboundRecordFileMetadataProducer() throws Exception {
        return buildCastleDaojiaProducer(MafkaConstant.TOPIC_INBOUND_RECORD_FILE_METADATA, appkey);
    }

    @Lazy
    @Bean(value = "inboundRecordInitializedProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> inboundRecordInitializedProducer() throws Exception {
        return buildCastleDaojiaProducer(MafkaConstant.TOPIC_INBOUND_RECORD_INITIALIZED, appkey);
    }

    @Lazy
    @Bean(value = "inboundRecordUploadedProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> inboundRecordUploadedProducer() throws Exception {
        return buildCastleDaojiaProducer(MafkaConstant.TOPIC_INBOUND_RECORD_UPLOADED, appkey);
    }

    @Lazy
    @Bean(value = "inboundRecordParsedProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> inboundRecordParsedProducer() throws Exception {
        return buildCastleDaojiaProducer(MafkaConstant.TOPIC_INBOUND_RECORD_PARSED, appkey);
    }

    @Lazy
    @Bean(value = "inboundRecordCompletedProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> inboundRecordCompletedProducer() throws Exception {
        return buildCastleDaojiaProducer(MafkaConstant.TOPIC_INBOUND_RECORD_COMPLETED, appkey);
    }

    private IProducerProcessor<String, String> buildCastleDaojiaProducer(String topic, String appKey) throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, MafkaConstant.CASTLE_DAOJIA_NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appKey);
        return MafkaClient.buildProduceFactory(properties, topic);
    }

}
