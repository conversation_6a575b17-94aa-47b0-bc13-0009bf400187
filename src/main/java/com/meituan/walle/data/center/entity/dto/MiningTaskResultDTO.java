package com.meituan.walle.data.center.entity.dto;

import com.sankuai.walle.wcdp.core.entity.page.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/03/22
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MiningTaskResultDTO extends Page implements Serializable {
    private String taskResultId;
    private String taskId;
    private Integer labelStatus;
    private String labelResult;
    private Integer resultVisualStatus;
    private String resultVisualId;
    private String resultVisualS3Url;
    private Integer resultVisualHelpful;
    private String recordName;
    private Date resultBeginTime;
    private Date resultEndTime;
    private String caseId;
    private String scenarioId;
    private String scenarioName;
    private String reservedField1;
    private String reservedField2;
    private String reservedField3;
    private String reservedField4;
    private String vin;
    private Integer dataRecoveryType;
    private String tag;
    private String excludeTag;
}
