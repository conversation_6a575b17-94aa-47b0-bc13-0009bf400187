package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Beijing
 * @date 2020/02/19 下午2:35
 * Description:
 * Modified by
 */
@Getter
@AllArgsConstructor
public enum CasesSourceEnum {

    /**
     * 其他
     */
    OTHER(0, "other"),
    /**
     * intervention
     */
    INTERVENTION(1, "intervention"),
    /**
     * coredump
     */
    CORE_DUMP(2, "coredump"),
    /**
     * custom ta
     */
    CUSTOM_TAG(3, "custom_tag"),
    /**
     * active report
     */
    ACTIVE_REPORT(4, "active_report"),
    /**
     * hard brake
     */
    HARD_BREAK(5, "hardbrake"),
    /**
     * message latency
     */
    @Deprecated
    MESSAGE_LATENCY(6, "message_latency"),
    /**
     * performance latency
     */
    @Deprecated
    PERFORMANCE_LATENCY(7, "performance_latency"),
    /**
     * 远程遥控
     */
    REMOTE(8, "remote_intervention"),
    /**
     * scene intervention
     */
    SCENE(9, "scene_intervention"),
    /**
     * parking
     */
    PARKING(10, "parking"),
    /**
     * speed limit
     */
    @Deprecated
    SPEED_LIMIT(11, "speed-limit"),
    /**
     * electrice fence
     */
    ELECTRIC_FENCE(12, "electric-fence"),
    /**
     * estop
     */
    ESTOP(13, "estop"),
    /**
     * 后面4个是前端直接从event转化的case datasource
     */
    DRIVE_MODE_CHANGE(14, "drive-mode-change"),
    IPC(15, "ipc"),
    //测车助手
    BREAKDOWN(16, "breakdown"),
    DESTINATION(17, "destination"),

    ACCIDENT(18, "accident"),

    MAEB(19, "MAEB"),

    RISK_ALERT(20, "risk-alert"),

    START_AUTO_FAILED(21, "launch_failed")
    ;

    private int code;
    private String msg;

}