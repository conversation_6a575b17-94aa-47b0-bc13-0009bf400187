package com.meituan.walle.data.center.entity.params;

import com.sankuai.walle.wcdp.core.entity.page.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SnapshotContentParam extends Page {
    private String recordName;
    private String fileNameLike;
    private String filePath;
    private List<Integer> fileTypeList;

}
