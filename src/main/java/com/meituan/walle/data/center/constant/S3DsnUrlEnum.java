package com.meituan.walle.data.center.constant;

/**
 *
 * <AUTHOR>
 * @date 2024/6/4
 */
public enum S3DsnUrlEnum {
    // 北京测试环境
    ONBOARD_RECORD_DATA_BJ_TEST("s3_onboard-record-data_bj-test"),
    VEHICLE_UPLOAD_BJ_TEST("s3_vehicle-upload_bj-test"),
    VEHICLE_DATA_ORIGIN_BJ_TEST("s3_vehicle-data-origin_bj-test"),
    // 北京备份集群
    ONBOARD_RECORD_DATA_BJ_BACKUP("s3_onboard-record-data_bj-backup"),
    VEHICLE_UPLOAD_BJ_BACKUP("s3_vehicle-upload_bj-backup"),
    VEHICLE_DATA_ORIGIN_BJ_BACKUP("s3_vehicle-data-origin_bj-backup"),
    // 中卫自动车集群
    ONBOARD_RECORD_DATA_ZW_MAD("s3_onboard-record-data_zw-mad"),
    VEHICLE_UPLOAD_ZW_MAD("s3_vehicle-upload_zw-mad"),
    VEHICLE_DATA_ORIGIN_ZW_MAD("s3_vehicle-data-origin_zw-mad"),
    // 北京02集群
    SIMCLUSTER_SYNC_DATA_BJ02("s3_simcluster-sync-data_bj02"),
    ;

    private final String id;

    S3DsnUrlEnum(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }
}
