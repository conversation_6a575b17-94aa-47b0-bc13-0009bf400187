package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2021/7/8 8:23 下午
 */
@Getter
@AllArgsConstructor
public enum RecommendRoutingStatusEnum {
    UNKNOWN(-1, "未知"),
    START(0, "未完成"),
    END(1, "已完成");

    private int code;
    private String msg;

    public static RecommendRoutingStatusEnum getByMsg(String msg) {
        for (RecommendRoutingStatusEnum type : values()) {
            if (type.getMsg().equals(msg)) {
                return type;
            }
        }
        return UNKNOWN;
    }
}
