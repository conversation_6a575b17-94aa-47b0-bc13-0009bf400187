package com.meituan.walle.data.center.entity.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimulationTaskV2 {
    @JSONField(name = "job_id")
    private String jobId;
    @JSONField(name = "task_id")
    private String taskId;
    private Integer status;
    private String timestamp;
    @JSONField(name = "version_id")
    private String versionId;
}
