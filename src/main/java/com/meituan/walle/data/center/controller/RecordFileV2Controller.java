package com.meituan.walle.data.center.controller;

import com.google.common.net.HttpHeaders;
import com.meituan.walle.data.center.constant.OnBoardTypeEnum;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.dal.recordmanager.entity.BRecordFile;
import com.meituan.walle.data.center.dal.recordmanager.entity.BRecordPkg;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.params.CompressionAddParam;
import com.meituan.walle.data.center.entity.request.QueryRecordFileByPageRequest;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.entity.response.QueryRecordFileByPageResponse;
import com.meituan.walle.data.center.service.RecordFileV2Service;
import com.meituan.walle.data.center.service.RecordPkgService;
import com.meituan.walle.data.center.util.BaAuthUtil;
import com.meituan.walle.data.center.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/record_file")
@Slf4j
public class RecordFileV2Controller {

    @Resource
    private RecordFileV2Service recordFileV2Service;

    @Resource
    private RecordPkgService recordPkgService;

    @PostMapping("/sync_s3")
    public Response syncS3File(@RequestBody List<String> recordNames) {
        if (!CollectionUtils.isEmpty(recordNames)) {
            recordNames.stream().forEach(recordName -> recordFileV2Service.updateRecordInfoFromS3(recordName));
        }
        return Response.succ();
    }

    @PostMapping("/query_by_page")
    public QueryRecordFileByPageResponse queryByPage(@Validated @RequestBody QueryRecordFileByPageRequest request,
                                                     HttpServletRequest httpServletRequest) {
        String requestAuth = httpServletRequest.getHeader(HttpHeaders.AUTHORIZATION);
        String clientId = BaAuthUtil.getBasicAuthClientIdByRequestAuth(requestAuth);
        return recordFileV2Service.queryRecordFileByPage(request, clientId);
    }

    @GetMapping(value = "/streamServer/getAllValidS3Url")
    public CommonResponse getAllValidS3UrlForStreamServer(@RequestParam String recordName) {
        log.info("[RecordFileV2Controller#getAllValidS3UrlForStreamServer] " +
                        "call /record_file/streamServer/getAllValidS3Url api, recordName: {}", recordName);
        if (StringUtils.isBlank(recordName)) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "参数不能为空");
        }
        try {
            List<BRecordFile> recordFileList = recordFileV2Service.getAllValidS3UrlForStreamServer(recordName);
            return CommonResponse.success(recordFileList);
        } catch (Exception exception) {
            log.error("[RecordFileV2Controller#getAllValidS3UrlForStreamServer] " +
                            "api /record_file/streamServer/getAllValidS3Url exception, {}",
                    exception.getMessage(), exception);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), exception.getMessage());
        }
    }

    @GetMapping(value = "/streamServer/checkRecordReady")
    public CommonResponse checkRecordReadyForStreamServer(@RequestParam String recordName) {
        log.info("[RecordFileV2Controller#checkRecordReadyForStreamServer] " +
                        "call /record_file/streamServer/checkRecordReady api, recordName: {}", recordName);
        if (StringUtils.isBlank(recordName)) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "参数不能为空");
        }
        try {
            List<Integer> resultList = recordFileV2Service.checkRecordReadyForStreamServer(recordName);
            return CommonResponse.success(resultList);
        } catch (Exception exception) {
            log.error("[RecordFileV2Controller#checkRecordReadyForStreamServer] " +
                            "api /record_file/streamServer/checkRecordReady exception, {}",
                    exception.getMessage(), exception);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), exception.getMessage());
        }
    }

    @GetMapping(value = "/streamServer/getRecordFile")
    public CommonResponse getRecordFileForStreamServer(@RequestParam String recordName) {
        log.info("[RecordFileV2Controller#getRecordFileForStreamServer] " +
                        "call /record_file/streamServer/getRecordFile api, recordName: {}", recordName);
        if (StringUtils.isBlank(recordName)) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "参数不能为空");
        }
        try {
            List<BRecordFile> recordPkgList = recordFileV2Service.getRecordFileForStreamServer(recordName);
            return CommonResponse.success(recordPkgList);
        } catch (Exception exception) {
            log.error("[RecordFileV2Controller#getRecordFileForStreamServer] " +
                            "api /record_file/streamServer/getRecordFile exception, {}",
                    exception.getMessage(), exception);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), exception.getMessage());
        }
    }

    @PostMapping("/compression/add_batch")
    public CommonResponse compressionAddBatch(@RequestBody List<CompressionAddParam> paramFileList) {
        if (CollectionUtils.isEmpty(paramFileList)) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "The param cannot be null or empty");
        }
        if (paramFileList.size() > 100) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "The number of parameters cannot exceed 100! ");
        }
        try {
            List<BRecordFile> recordFileList = paramFileList.stream()
                    .map(file -> BRecordFile.builder()
                            .id(0L)
                            .recordName(file.getRecordName())
                            .recordDate(file.getRecordDate())
                            .fileName(file.getFileName())
                            .filePath(file.getFilePath())
                            .s3Url(file.getS3Url())
                            .fileType(file.getFileType())
                            .fileSize(file.getFileSize())
                            .fileSizeS3(file.getFileSizeS3())
                            .datekey(file.getDatekey())
                            .lastModified(file.getLastModified())
                            .uploadTime(file.getUploadTime())
                            .cluster(file.getCluster())
                            .compressionFormat(file.getCompressionFormat())
                            .build())
                    .collect(Collectors.toList());
            Integer recordFileRows = recordFileV2Service.compressionInsertBatch(recordFileList);

            List<BRecordPkg> recFileList = paramFileList.stream()
                    .filter(file -> file.getFileType() == OnBoardTypeEnum.RECORD.getId())
                    .map(file -> BRecordPkg.builder()
                            .id(0L)
                            .recordName(file.getRecordName())
                            .fileName(file.getFileName())
                            .s3Url(file.getS3Url())
                            .module(file.getModule())
                            .startTime(file.getStartTime())
                            .endTime(file.getEndTime())
                            .firstMsgTime(file.getFirstMsgTime())
                            .msgCount(file.getMsgCount())
                            .topics(file.getTopics())
                            .fileSize(file.getFileSize())
                            .duration(file.getDuration())
                            .cluster(file.getCluster())
                            .compressionFormat(file.getCompressionFormat())
                            .build())
                    .collect(Collectors.toList());
            Integer recordPkgRows = 0;
            if (!CollectionUtils.isEmpty(recFileList)) {
                recordPkgRows = recordPkgService.compressionInsertBatch(recFileList);
            }
            String data = String.format("The file list contains %d files, of which %d are rec files. " +
                    "Successfully inserted %d rows into the 'record_file' table, " +
                    "%d rows into the 'record_pkg' table. ",
                    paramFileList.size(), recFileList.size(), recordFileRows, recordPkgRows);
            log.info("[/record_file/compression/insert_batch] param: {}. {}", JacksonUtil.serialize(paramFileList), data);
            return CommonResponse.success(data);
        } catch (Exception e) {
            log.error("[/record_file/compression/insert_batch] exception: {}, param: {}",
                    e.getMessage(), JacksonUtil.serialize(paramFileList), e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

}
