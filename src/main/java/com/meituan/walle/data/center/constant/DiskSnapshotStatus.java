package com.meituan.walle.data.center.constant;

public enum DiskSnapshotStatus {
    INITIAL(Byte.valueOf("0"), "初始化"),
    SUCCEED(Byte.valueOf("1"), "已解析"),

    RUNNING(Byte.valueOf("10"), "进行中"),
    ;

    DiskSnapshotStatus(Byte code, String description) {
        this.code = code;
        this.description = description;
    }

    private Byte code;
    private String description;

    public Byte getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
