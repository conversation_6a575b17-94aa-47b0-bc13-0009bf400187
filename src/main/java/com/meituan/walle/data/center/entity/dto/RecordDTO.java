package com.meituan.walle.data.center.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR> @Beijing
 * @date 2020/3/17 下午1:51
 * Description:
 * Modified by
 */
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public class RecordDTO {

    private String bid;
    private String recordName;
    private String purpose;
    private Long beginTime;
    private Long endTime;
    private String place;
    private String vin;
    private String vehicleName;
    private Integer vehicleType;
    private String gitBranch;
    private String gitCommit;
    private String hdmapVersion;
    private String hdmapName;
    private Integer status;
    private Long dataSize;
    private Integer fileCount;
}
