package com.meituan.walle.data.center.constant;

import com.meituan.walle.data.center.excel.mapping.Mapping;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022/04/01
 */
@Slf4j
public enum MiningTaskResultLabelStatusMappingEnum implements Mapping<String> {
    MALE("0", "未标注"), FEMALE("1", "标注");
    private String value;
    private String text;
    private static final String UNMAPPED_TEXT = "未知";

    MiningTaskResultLabelStatusMappingEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText(String value) {
        try {
            Optional<MiningTaskResultLabelStatusMappingEnum> result =
                    Stream.of(MiningTaskResultLabelStatusMappingEnum.values()).filter(
                    current -> Objects.equals(current.value, value)
            ).findFirst();
            return result.isPresent()
                    ? result.get().text
                    : UNMAPPED_TEXT;
        } catch (Exception ex) {
            log.warn("未能正确映射:" + value, ex);
            return UNMAPPED_TEXT;
        }
    }
}