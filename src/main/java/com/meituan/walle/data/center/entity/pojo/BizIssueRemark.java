package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/11/18
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Table(name = "biz_issue_remark")
public class BizIssueRemark {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * case_id
     */
    @Column(name = "case_id")
    private String caseId;

    /**
     * 父评论id
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 评论正文
     */
    @Column(name = "remark_text")
    private String remarkText;

    /**
     * 人员misId
     */
    @Column(name = "to_users")
    private String toUsers;

    /**
     * 附件
     */
    @Column(name = "resource")
    private String resource;

    /**
     * 创建人
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 类型字段:0.用户提交评论|1.通知类评论,不能删除
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 评论来源
     */
    @Column(name = "source")
    private String source;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;
}

