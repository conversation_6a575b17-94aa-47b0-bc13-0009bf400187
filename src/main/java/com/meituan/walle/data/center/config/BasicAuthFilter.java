package com.meituan.walle.data.center.config;

import com.google.common.net.HttpHeaders;
import com.meituan.walle.data.center.config.mcc.BasicAuthConfig;
import com.meituan.walle.data.center.config.mcc.RemoteRelatedConfig;
import com.meituan.walle.data.center.constant.BasicAuthPatternEnum;
import com.meituan.walle.data.center.constant.CharConstant;
import com.meituan.walle.data.center.util.BaAuthUtil;
import com.meituan.walle.data.center.util.CommonUtil;
import com.meituan.walle.data.center.util.JacksonUtil;
import com.meituan.walle.data.center.util.RemoteVideoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.util.*;

@Slf4j
@Component
@WebFilter
@Order(value = 3)
public class BasicAuthFilter implements Filter {

    @Value("${environment}")
    private static String environment;

    @Resource
    private RemoteVideoUtil remoteVideoUtil;

    private static final String BA_SUFFIX = "/ba";

    private static final String SSO_SUFFIX = "/sso";

    private static final AntPathMatcher MATCHER = new AntPathMatcher();

    private static final List<String> PATTERN_LIST = new ArrayList<>();

    private static final List<String> REMOTE_VIDEO_PATTERN_LIST = new ArrayList<>();

    private static final HashMap<String, String> BA_CLIENT_SECRET_MAP = new HashMap<>();

    @Override
    public void init(FilterConfig filterConfig) {
        initBaUri();
        initBaClientSecretMap();
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {

        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpServletResponse = ((HttpServletResponse) servletResponse);
        String uri = httpServletRequest.getRequestURI();
        String url = httpServletRequest.getRequestURL().toString();
        String method = httpServletRequest.getMethod();
        for (String pattern : PATTERN_LIST) {
            if (MATCHER.match(pattern, uri)) {
                if (!checkDataCenterBasicAuth(url, method, httpServletRequest, BasicAuthPatternEnum.COMMON.getCode())) {
                    handleAuthenticationFailure(uri, method, url, httpServletResponse);
                    return;
                }
            }
        }

        // TODO（liuqichun）:这里为啥需要对 remote video 单独设置 BA 路径，后面有时间还需看下原因
        for (String pattern : REMOTE_VIDEO_PATTERN_LIST) {
            if (MATCHER.match(pattern, uri) &&
                    !checkBasicAuth(url, method, httpServletRequest, remoteVideoUtil.getClientId(),
                            remoteVideoUtil.getClientSecret(), BasicAuthPatternEnum.REMOTE.getCode())) {
                log.warn("remote video ba check remote video ba failed, uri: {}, method: {}, url: {}",
                        uri, method, url);
                httpServletResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return;
            }
        }

        if (uri.endsWith(BA_SUFFIX)) {
            if (!checkDataCenterBasicAuth(url, method, httpServletRequest, BasicAuthPatternEnum.COMMON.getCode())) {
                handleAuthenticationFailure(uri, method, url, httpServletResponse);
                return;
            }
            String tmpUri = uri.substring(0, uri.lastIndexOf(BA_SUFFIX));
            RequestDispatcher requestDispatcher = servletRequest.getRequestDispatcher(tmpUri);
            if (requestDispatcher == null) {
                return;
            }
            requestDispatcher.forward(servletRequest, servletResponse);
        } else if (uri.endsWith(SSO_SUFFIX)) {
            String tmpUri = uri.substring(0, uri.lastIndexOf(SSO_SUFFIX));
            RequestDispatcher requestDispatcher = servletRequest.getRequestDispatcher(tmpUri);
            if (requestDispatcher == null) {
                return;
            }
            requestDispatcher.forward(servletRequest, servletResponse);
        } else {
            filterChain.doFilter(servletRequest, servletResponse);
        }
    }

    @Override
    public void destroy() {
        // no need of this method, do nothing
    }

    private void initBaUri() {
        PATTERN_LIST.addAll(
                Arrays.asList(BasicAuthConfig.BASE_BA_URI.split(CharConstant.CHAR_FH))
        );

        REMOTE_VIDEO_PATTERN_LIST.addAll(
                Arrays.asList(BasicAuthConfig.REMOTE_VIDEO_BA_URI.split(CharConstant.CHAR_FH))
        );
    }

    // 从 lion 中获取有效授权的 client id 和 对应的 secret
    private void initBaClientSecretMap() {
        String baClientSecret = BasicAuthConfig.BASE_BA_CLIENT_SECRET_MAP;
        if (!StringUtils.isBlank(baClientSecret)) {
            Arrays.stream(baClientSecret.split(CharConstant.CHAR_FH))
                    .map(clientSecret -> clientSecret.split(CharConstant.CHAR_MH))
                    .filter(clientSecretArr -> {
                        if (clientSecretArr.length != 2) {
                            log.error("Invalid BA client secret format: {}", Arrays.toString(clientSecretArr));
                            throw new IllegalArgumentException("Invalid BA client secret format");
                        }
                        return true;
                    })
                    .forEach(clientSecretArr -> BA_CLIENT_SECRET_MAP.put(clientSecretArr[0], clientSecretArr[1]));
        } else {
            log.error("BA client secret is empty");
            throw new IllegalStateException("BA client secret is empty");
        }
    }

    private boolean checkDataCenterBasicAuth(String url, String method,
                                             HttpServletRequest httpServletRequest, int patternType) {
        String requestAuth = httpServletRequest.getHeader(HttpHeaders.AUTHORIZATION);
        String clientId = BaAuthUtil.getBasicAuthClientIdByRequestAuth(requestAuth);
        if (StringUtils.isBlank(clientId)) {
            return false;
        }
        String secret = BA_CLIENT_SECRET_MAP.get(clientId);
        return checkBasicAuth(url, method, httpServletRequest, clientId, secret, patternType);
    }

    private boolean checkBasicAuth(String url, String method, HttpServletRequest httpServletRequest,
                                   String clientId, String clientSecret, int patternType) {
        String originDate = httpServletRequest.getHeader(HttpHeaders.DATE);
        String requestAuth = httpServletRequest.getHeader(HttpHeaders.AUTHORIZATION);

        Map<String, String> headers = buildHeaders(url, method, originDate, clientId, clientSecret, patternType);
        String serverAuth = headers.getOrDefault(HttpHeaders.AUTHORIZATION, "");

        String param = CharConstant.CHAR_EMPTY;
        if (url.contains("/data_list")) {
            param = JacksonUtil.serialize(httpServletRequest.getParameterMap());
        }
        log.info("[BasicAuthFilter#checkBasicAuth] result: {}, type: {}, method: {}, URL: {}, clientId: {}, param: {}",
                serverAuth.equals(requestAuth), BA_SUFFIX.substring(1), method, url, clientId, param);
        return serverAuth.equals(requestAuth);
    }

    private Map<String, String> buildHeaders(String url, String method, String date,
                                             String clientId, String clientSecret, int patterType) {
        Map<String, String> headers = new HashMap<>();
        try {
            String uri = new URI(url).getPath();
            if (patterType == BasicAuthPatternEnum.REMOTE.getCode()) {
                uri = CharConstant.CHAR_XX + RemoteRelatedConfig.forwardRule + uri;
            }
            log.info("url is {}, method is {}, date is {}, clientId is {}, clientSecret is {}, uri is {}",
                    url, method, date, clientId, clientSecret, uri);
            String authorizationHeader = CommonUtil.getAuthorization(uri, method, date, clientId, clientSecret);
            headers.put(HttpHeaders.DATE, date);
            headers.put(HttpHeaders.AUTHORIZATION, authorizationHeader);
        } catch (Exception e) {
            log.error("Build headers failed, url is {}", url, e);
        }
        return headers;
    }

    private void handleAuthenticationFailure(String uri, String method, String url,
                                             HttpServletResponse httpServletResponse) throws IOException {
        log.warn("ba check failed, uri: {}, method: {}, url: {}", uri, method, url);
        httpServletResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        httpServletResponse.getWriter().write("Unauthorized: Basic Authentication Failed.");
    }

}
