package com.meituan.walle.data.center.entity.po;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/07/19
 */
@Deprecated
@Data
public class VehicleChassisStatusPO {

    /**
     * 车架号
     */
    @JsonProperty(value = "vin")
    private String vin;

    /**
     * 时间戳
     */
    @JsonProperty(value = "timestamp")
    private Long timestamp;

    /**
     *
     */
    @JsonProperty(value = "wsu_sdcdc")
    private String wsuSdcdc;

    /**
     *
     */
    @JsonProperty(value = "spdu_status")
    private String spduStatus;

    /**
     *
     */
    @JsonProperty(value = "bcu_status")
    private String bcuStatus;

    /**
     *
     */
    @JsonProperty(value = "upu_status")
    private String upuStatus;

    /**
     *
     */
    @JsonProperty(value = "mcu_status")
    private String mcuStatus;

    /**
     *
     */
    @JsonProperty(value = "can_status")
    private String canStatus;

    /**
     *
     */
    @JsonProperty(value = "vcu_status")
    private String vcuStatus;

    /**
     *
     */
    @JsonProperty(value = "devices_version")
    private String devicesVersion;

    /**
     *
     */
    @JsonProperty(value = "vehicle_except_info")
    private String vehicleExceptInfo;

    /**
     *
     */
    @JsonProperty(value = "eps_status")
    private String epsStatus;

    /**
     *
     */
    @JsonProperty(value = "vehicle_status")
    private String vehicleStatus;

    @JsonProperty(value = "tpms_status")
    private String tpmsStatus;

    @JsonProperty(value = "avas_status")
    private String avasStatus;

    @JsonProperty(value = "ehb_status")
    private String ehbStatus;

    @JsonProperty(value = "esc_status")
    private String escStatus;

    @JsonProperty(value = "elc_add_status")
    private String elcAddStatus;

    @JsonProperty(value = "sdcdc_status")
    private String sdcdcStatus;

    @JsonProperty(value = "fcu_status")
    private String fcuStatus;

    @JsonProperty(value = "slab_add_status")
    private String slabAddStatus;

    @JsonProperty(value = "plgf_status")
    private String plgfStatus;

    @JsonProperty(value = "plfr_status")
    private String plfrStatus;

    @JsonProperty(value = "hmi_status")
    private String hmiStatus;

    @JsonProperty(value = "tester_functional")
    private String testerFunctional;

    @JsonProperty(value = "slab_bcm_status")
    private String slabBcmStatus;

    @JsonProperty(value = "other_status")
    private String otherStatus;

    @JsonProperty(value = "power_status")
    private String powerStatus;

    @JsonProperty(value = "mcu_temperature")
    private String mcuTemperature;

    /**
     * 创建时间
     */
    @JsonProperty(value = "create_time")
    private String createTime;

    /**
     * 更新时间
     */
    @JsonProperty(value = "update_time")
    private String updateTime;

    @JsonProperty(value = "event_time")
    private String eventTime;
}