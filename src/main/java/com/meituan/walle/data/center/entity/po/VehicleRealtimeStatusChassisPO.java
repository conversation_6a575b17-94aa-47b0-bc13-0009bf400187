package com.meituan.walle.data.center.entity.po;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/04/07
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleRealtimeStatusChassisPO {
    /**
     * 车架号
     */
    @JsonProperty(value = "vin")
    private String vin;

    /**
     * 时间戳
     */
    @JsonProperty(value = "timestamp")
    private Long timestamp;

    @JsonProperty(value = "chassis_status")
    private String chassisStatus;

    /**
     * 创建时间
     */
    @JsonProperty(value = "create_time")
    private String createTime;

    /**
     * 更新时间
     */
    @JsonProperty(value = "update_time")
    private String updateTime;

    @JsonProperty(value = "event_time")
    private String eventTime;

}