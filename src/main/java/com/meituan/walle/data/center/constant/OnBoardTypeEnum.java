package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OnBoardTypeEnum {

    UNKNOWN(0, "unknown"),
    RECORD(1, "rec"),
    LOG(2, "log"),
    CORE_DUMP(3, "coredump"),
    WORK_SPACE(4, "workspace"),
    INDEX(5, "index"),
    BIN(6, "bin"),
    SYS(7, "sys"),
    RECORD_INFO(8, "record_info.pb.txt"),
    PRECHECK(9, "precheck"),
    NOVATEL(10, "novatel"),
    OPERATION_DATA(11, "operation_data"),
    CAN_DATA(12, "candata"),
    CALIBRATION(13, "calibration"),
    J3_COREDUMP(14, "j3_coredump"),
    REC_FILE_META(15, "rec_file_meta"),
    // 16：用来存放OnboardHeader及其对应到rec的文件名和offset
    ONBOARD_HEADER_META(16, "onboard_header_meta"),
    ;

    private int id;
    private String msg;

    public static OnBoardTypeEnum getByMsg(String msg) {
        for (OnBoardTypeEnum type : values()) {
            if (type.getMsg().equals(msg)) {
                return type;
            }
        }
        return UNKNOWN;
    }

    public static String getById(int id) {
        for (OnBoardTypeEnum type : values()) {
            if (type.getId() == id) {
                return type.msg;
            }
        }
        return UNKNOWN.msg;
    }
}
