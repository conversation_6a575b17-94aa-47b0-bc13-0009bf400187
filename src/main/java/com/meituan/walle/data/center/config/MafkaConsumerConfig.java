package com.meituan.walle.data.center.config;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.walle.data.center.component.InterventionMatchEventHandler;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.*;
import com.meituan.walle.data.center.entity.dto.*;
import com.meituan.walle.data.center.entity.params.ScenerankingEventInformationParam;
import com.meituan.walle.data.center.entity.params.VehicleEventParam;
import com.meituan.walle.data.center.entity.pojo.*;
import com.meituan.walle.data.center.entity.vo.CloudNoticeEventVO;
import com.meituan.walle.data.center.entity.vo.VehicleEventVO;
import com.meituan.walle.data.center.handle.IRedisHandler;
import com.meituan.walle.data.center.handle.impl.*;
import com.meituan.walle.data.center.service.*;
import com.meituan.walle.data.center.service.impl.*;
import com.meituan.walle.data.center.util.JacksonUtil;
import com.mysql.jdbc.exceptions.jdbc4.MySQLIntegrityConstraintViolationException;
import com.sankuai.call.sdk.entity.push.AutoCallNebulaCallBackDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.DuplicateKeyException;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.meituan.walle.data.center.constant.AutoCallConstant.UNKNOWN_CALL_TYPE;

@Slf4j
@Configuration
public class MafkaConsumerConfig {
    @Resource
    private IProducerProcessor<String, String> onboardEventProducer;

    @Value("${appkey}")
    private String appkey;

    @Value("${mafka.producer.mvizAppKey}")
    private String mvizAppKey;

    @Resource
    private DiskSnapshotService diskSnapshotService;

    @Resource
    private RecordV2Service recordV2Service;

    @Resource
    private RecordFileV2Service recordFileV2Service;

    @Resource
    private BizOnboardFileService bizOnboardFileService;

    @Resource
    private RecordService recordService;

    @Resource
    private VehicleEventContext vehicleEventContext;

    @Resource
    private AutodriveEventService autodriveEventService;

    @Resource
    private InterventionMatchEventHandler interventionMatchEventHandler;

    @Resource
    private IRedisHandler redisHandler;

    @Resource
    private CoreDumpService coreDumpService;

    @Resource
    private VehicleStatusChangeAggNodeService vehicleStatusChangeAggNodeService;

    @Resource
    private DataCenterAutoCallService dataCenterAutoCallService;

    @Resource
    private RemoteVideoService remoteVideoService;

    @Resource
    private WorkstationCaseService workstationCaseService;

    @Resource
    private EventDrivingModeChangeStrategy eventDrivingModeChangeStrategy;

    @Resource
    private EventUploadTaskService eventUploadTaskService;

    @Resource
    private CaseSortableHandler caseSortableHandler;

    @Resource
    private DataCenterServiceImpl dataCenterService;

    @Resource
    private RecordInfoExtractorHandler recordInfoExtractorHandler;

    @Resource
    private ScenerankingEventInformationService scenerankingEventInformationService;

    @Resource
    private GenCoreDumpCaseHandler genCoreDumpCaseHandler;

    @Resource
    private S3FileCleanupHandler s3FileCleanupHandler;

    @Resource
    private OfflineInboundRecordSnapshotHandler offlineInboundRecordSnapshotHandler;

    @Resource
    private InboundRecordInitializeServiceImpl inboundRecordInitializeService;

    @Autowired
    private InboundRecordUploadService inboundRecordUploadService;

    @Resource
    private InboundRecordParseServiceImpl inboundRecordParseService;

    @Autowired
    private InboundRecordValidateService inboundRecordValidateService;

    @Autowired
    private DiskCollectRecordUploadService diskCollectRecordUploadService;

    @Autowired
    private DiskCollectRecordParseService diskCollectRecordParseService;

    @Resource
    private RecordSparkJobCreateService recordSparkJobCreateService;

    @Resource
    private InterventionSourceEventService interventionSourceEventService;

    @Bean(destroyMethod = "close")
    public IConsumerProcessor snapshotNoticeConsumer() throws Exception {
        return buildSnapshotNoticeConsumer();
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor recordConsumer() throws Exception {
        return buildRecordConsumer();
    }


    @Bean(destroyMethod = "close")
    public IConsumerProcessor recordFileConsumer() throws Exception {
        return buildRecordFileConsumer();
    }


    @Bean(destroyMethod = "close")
    public IConsumerProcessor onboardFileMetadataConsumer() throws Exception {
        return buildOnboardFileMetadataConsumer();
    }


    @Bean(destroyMethod = "close")
    public IConsumerProcessor realtimeRecordConsumer() throws Exception {
        return buildRealtimeRecordConsumer();
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor onboardEventConsumer() throws Exception {
        return buildOnboardEventConsumer();
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor fastTaskNoticeConsumer() throws Exception {
        return buildFastTaskNoticeConsumer();
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor securitySystemAccidentEventConsumer() throws Exception {
        return buildSecuritySystemAccidentEventConsumer();
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor interventionMileageDelayConsumer() throws Exception {
        return buildInterventionMileageConsumer();
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor caseSortableConsumer() throws Exception {
        return buildCaseSortableConsumer();
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor diskStatusNoticeConsumer() throws Exception {
        return buildDiskStatusNoticeConsumer();
    }

    private IConsumerProcessor buildCaseSortableConsumer() throws Exception {
        Properties properties = getProperties(MafkaConstant.RECORD_FOR_APP_CASE_SORTABLE_CONSUMER_GROUP);
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_RECORD_FOR_APP);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildCaseSortableConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                caseSortableHandler.caseSortable(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[MafkaConsumerConfig#buildCaseSortableConsumer] case sortable consumer failed, msg: {}",
                        msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        });
        return consumer;
    }

    private IConsumerProcessor buildSecuritySystemAccidentEventConsumer() throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer =
                MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_SAFEGUARD_SYSTEM_EVENT_NOTICE);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            CloudNoticeEventVO cloudNoticeEventVO = JSON.parseObject(msg, CloudNoticeEventVO.class);
            if (cloudNoticeEventVO.getEventType() != CloudNoticeEventConstant.ACCIDENT_DETECTION_EVENT_CODE
                    && cloudNoticeEventVO.getEventType() != CloudNoticeEventConstant.ACCIDENT_DETECTION_LEVEL_EVENT_CODE) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            log.info("[buildSecuritySystemAccidentEventConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                eventUploadTaskService.saveFilteredCollisionEvent(cloudNoticeEventVO);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (DuplicateKeyException e) {
                log.error("[MafkaConsumerConfig/buildSecuritySystemAccidentEventConsumer] Duplicate in mysql, msg: {}",
                        msg, e);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[MafkaConsumerConfig/buildSecuritySystemAccidentEventConsumer] Failed to consume "
                        + "security accident event, msg: {}", msg, e);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

        });
        return consumer;
    }

    private IConsumerProcessor buildSnapshotNoticeConsumer() throws Exception {
        Properties properties = getProperties(MafkaConstant.TOPIC_SNAPSHOT_NOTICE_CONSUMER);
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_SNAPSHOT_NOTICE);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildSnapshotNoticeConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                SnapshotNoticeDTO diskSnapshot = JSON.parseObject(msg, SnapshotNoticeDTO.class);
                diskSnapshotService.parseSnapshot(diskSnapshot);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to handle diskSnapshot, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    private IConsumerProcessor buildRecordConsumer() throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_RECORD);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildRecordConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                recordV2Service.parseRecordInfo(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to handle record, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    private IConsumerProcessor buildRecordFileConsumer() throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_RECORD_FILE);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildRecordFileConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                recordFileV2Service.updateRecordInfoFromS3(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to update recordFile, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    private IConsumerProcessor buildOnboardFileMetadataConsumer() throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties,
                MafkaConstant.TOPIC_ONBOARD_FILE_METADATA);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildOnboardFileMetadataConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                bizOnboardFileService.consumer(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (MySQLIntegrityConstraintViolationException | DuplicateKeyException e) {
                log.error("failed to consumer onboard file metadata, msg: {}", msg, e);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer onboard file metadata, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    private IConsumerProcessor buildRealtimeRecordConsumer() throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_REALTIME_RECORD);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildRealtimeRecordConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                recordService.filterRealtimeRecord(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer realtime record, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }


    private IConsumerProcessor buildOnboardEventConsumer() throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_ONBOARD_EVENT);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildOnboardEventConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                VehicleEventVO eventVO = JSON.parseObject(msg, VehicleEventVO.class);
                if (StringUtils.isBlank(eventVO.getVin()) || eventVO.getNoticeType() == null ||
                        eventVO.getMessage() == null) {
                    log.info("onboard event key param is empty");
                    return ConsumeStatus.CONSUME_SUCCESS;
                }

                if (!addLock(eventVO.getVin(), eventVO.getNoticeType(), eventVO.getSendTimestamp())) {
                    log.warn("onboard event repeat in db: {}, {}, {}", eventVO.getVin(),
                            eventVO.getNoticeType(), eventVO.getSendTimestamp());
                    return ConsumeStatus.CONSUME_SUCCESS;
                }

                List<Integer> intervention_source_event_code_list = Arrays.stream(
                                SysParamsConfig.intervention_source_event_codes.split(CharConstant.CHAR_DD))
                        .map(String::trim)
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                if (intervention_source_event_code_list.contains(eventVO.getNoticeType())) {
                    interventionSourceEventService.identifyInterventionEvent(eventVO);
                } else {
                    vehicleEventContext.saveEvent(eventVO); // 历史架构，后面会下线

                    VehicleEventParam vehicleEventParam = new VehicleEventParam();
                    BeanUtils.copyProperties(eventVO, vehicleEventParam);
                    autodriveEventService.handle(vehicleEventParam); // 事件中心重写后的新架构
                }

                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (MySQLIntegrityConstraintViolationException | DuplicateKeyException e) {
                log.error("duplicate in mysql, msg: {}", msg, e);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer onboard event, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    private boolean addLock(String vin, Integer noticeType, Long timestamp) {
        StringBuilder duplicateKey = new StringBuilder(vin).append(noticeType).append(timestamp);
        try {
            return redisHandler.setIfNotExists(RedisConstant.EVENT_AVOID_REPEAT_LOCK,
                    duplicateKey.toString(), 0, SysParamsConfig.event_avoid_repeat_timeout);
        } catch (Exception e) {
            log.error("MD5 string error: {}", duplicateKey, e);
        }
        return true;
    }


    @Bean(destroyMethod = "close")
    public IConsumerProcessor interventionMatchEventResult() throws Exception {
        return buildInterventionMatchEventResultConsumer();
    }

    private IConsumerProcessor buildInterventionMatchEventResultConsumer() throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties,
                MafkaConstant.TOPIC_INTERVENTION_EVENT_MATCH_RESULT);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {
            String msg = (String) message.getBody();
            log.info("[buildInterventionMatchEventResultConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                interventionMatchEventHandler.handleMatchMsg(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer onboard event, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        });
        return consumer;
    }


    @Bean(destroyMethod = "close")
    public IConsumerProcessor delayPullRemoteVideoEventConsumer() throws Exception {
        return buildDelayPullRemoteVideoEventConsumer();
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor buildAutoCallCallbackConsumer() throws Exception {
        return buildJupiterCallbackConsumer();
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor aukOnboardEventConsumer()
            throws Exception {
        return buildAukOnboardEventConsumer();
    }

    private IConsumerProcessor buildDelayPullRemoteVideoEventConsumer() throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties,
                MafkaConstant.TOPIC_DELAY_PULL_REMOTE_VIDEO_EVENT);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {
            String msg = (String) message.getBody();
            log.info("[buildDelayPullRemoteVideoEventConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                BizEvents eventSpecific = JSON.parseObject(msg, BizEvents.class);
                remoteVideoService.getRemoteVideoForEvents(eventSpecific);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer collision event, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        });
        return consumer;
    }

    private IConsumerProcessor buildFastTaskNoticeConsumer() throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties,
                MafkaConstant.TOPIC_USER_FAST_TASK_NOTICE);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {
            String msg = (String) message.getBody();
            log.info("[buildFastTaskNoticeConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                VehicleUploadRequest vehicleUploadInfo = JSON.parseObject(msg, VehicleUploadRequest.class);
                if (vehicleUploadInfo == null) {
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
                String vin = vehicleUploadInfo.getVin();
                Long taskId = vehicleUploadInfo.getId();
                String eventId = vehicleUploadInfo.getEventId();
                Integer status = vehicleUploadInfo.getStatus();
                if (status != null && status.equals(FastUploadTaskStatusEnum.WRONG.getCode())) {
                    // 404状态，跳过不分拣
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
                log.info("begin auto assign realtime coreDump, vin:{}, taskId:{}, eventId:{}", vin, taskId, eventId);
                CoreDump coreDump = CoreDump.builder().vin(vin).caseId(eventId)
                        .recordName(vehicleUploadInfo.getRecordName()).build();
                coreDumpService.realTimeCoreDumpAssign(coreDump, String.valueOf(taskId));
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer fast task notice message, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        });
        return consumer;
    }

    /**
     * 收到接管消息，延迟30秒消费，使用接管消息去匹配biz_vehicle_drive_mode_change_node_agg的聚合数据，
     * 匹配上则更新实时接管表的驾驶时间里程字段
     *
     * @return
     * @throws Exception
     */
    private IConsumerProcessor buildInterventionMileageConsumer() throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties,
                MafkaConstant.TOPIC_INTERVENTION_MILEAGE_DELAY);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {
            String msg = (String) message.getBody();
            log.info("[buildInterventionMileageConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                BizEventDeriveIntervention intervention = JSON.parseObject(msg, BizEventDeriveIntervention.class);
                if (!Objects.isNull(intervention)) {
                    vehicleStatusChangeAggNodeService.matchInterventionAndNode(intervention);
                }
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer intervention mileage message, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        });
        return consumer;
    }

    private IConsumerProcessor buildJupiterCallbackConsumer() throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties,
                MafkaConstant.TOPIC_JUPITER_CALLBACK);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {
            String msg = (String) message.getBody();
            log.info("[buildJupiterCallbackConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                AutoCallNebulaCallBackDTO callBackDTO = JSON.parseObject(msg, AutoCallNebulaCallBackDTO.class);
                Integer callType = dataCenterAutoCallService.getCallType(callBackDTO);
                if (UNKNOWN_CALL_TYPE.equals(callType)) {
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
                if (dataCenterAutoCallService.isNeedCallNextPerson(callBackDTO)) {
                    dataCenterAutoCallService.callNextPerson(callBackDTO);
                }
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer jupiter callback event, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        });
        return consumer;
    }

    private IConsumerProcessor buildAukOnboardEventConsumer()
            throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties,
                MafkaConstant.TOPIC_AUK_ONBOARD_EVENT);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {
            String msg = (String) message.getBody();
            log.info("[buildAukOnboardEventConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                AukEventMsgDTO aukEventMsgDTO = JSON.parseObject(msg, AukEventMsgDTO.class);
                if (aukEventMsgDTO.getTopic().endsWith(CommonConstant.AUK_EVENT_MSG_TOPIC_SUFFIX)) {
                    String eventStr = new String(Base64.getDecoder().decode(aukEventMsgDTO.getMessage()));
                    onboardEventProducer.sendMessage(eventStr);
                }
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer jupiter callback event, msg: {}", msg, e);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        });
        return consumer;
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor caseInfoAggregationConsumer() throws Exception {
        return buildCaseIndexConsumer();
    }

    private IConsumerProcessor buildCaseIndexConsumer() throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties,
                MafkaConstant.TOPIC_CASE_INFO_AGGREGATION);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {
            String msg = (String) message.getBody();
            log.info("[buildCaseIndexConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                workstationCaseService.realTimeCaseIndexBuild(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer case index info, msg: {}", msg, e);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        });
        return consumer;
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor judgePersonInterventionMsgConsumer() throws Exception {
        Properties properties = getProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties,
                MafkaConstant.TOPIC_JUDGE_PERSON_INTERVENTION_MSG);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {
            String msg = (String) message.getBody();
            log.info("[judgePersonInterventionMsgConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                EventDrivingModeDelayMsgDTO dto = JSON.parseObject(msg, EventDrivingModeDelayMsgDTO.class);
                int delayMillis = dto.getDelayMillis();
                BizEventDrivingModeChange eventDrivingModeChange = dto.getEventDrivingModeChange();
                if (delayMillis < MafkaConstant.MAX_DELAY_MILLIS) {
                    eventDrivingModeChangeStrategy.handlePreviousEvent(eventDrivingModeChange,
                            delayMillis * 10, null);
                }
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to judgePersonInterventionMsg, msg: {}", msg, e);
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        });
        return consumer;
    }

    private IConsumerProcessor buildDiskStatusNoticeConsumer() throws Exception {
        Properties properties = getProperties(MafkaConstant.TOPIC_DISK_STATUS_CONSUMER);
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_DISK_STATUS);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            try {
                DiskStatusDTO diskStatusDTO = JacksonUtil.deSerialize(msg, DiskStatusDTO.class);
                log.info("[buildDiskStatusNoticeConsumer] msgId: {}, partition: {}, offset: {}, msg: {}, diskStatusDTO: {}",
                        message.getMessageID(), message.getParttion(), message.getOffset(), msg, diskStatusDTO);
                dataCenterService.parseDiskStatus(diskStatusDTO);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[buildDiskStatusNoticeConsumer] exception: {}, msg: {}", e.getMessage(), msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }


    @Bean(destroyMethod = "close")
    public IConsumerProcessor recordInfoExtractorConsumer() throws Exception {
        return buildRecordInfoExtractorConsumer();
    }

    private IConsumerProcessor buildRecordInfoExtractorConsumer() throws Exception {
        Properties properties = getProperties(MafkaConstant.RECORD_INFO_EXTRACTOR_CONSUMER_GROUP);
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_UPLOADED_RECORD);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildDiskStatusNoticeConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                recordInfoExtractorHandler.handle(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[MafkaConsumerConfig#buildRecordInfoExtractorConsumer] record info extractor consumer failed, msg: {}",
                        msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        });
        return consumer;
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor scenerankingEventInformationConsumer() throws Exception {
        return buildScenerankingEventInformationConsumer();
    }

    private IConsumerProcessor buildScenerankingEventInformationConsumer() throws Exception {
        Properties properties = getProperties(MafkaConstant.TOPIC_SCENERANKING_EVENT_INFORMATION_CONSUMER);
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_SCENERANKING_EVENT_INFORMATION);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildScenerankingEventInformationConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                ScenerankingEventInformationParam scenerankingEventInformationParam =
                        JacksonUtil.deSerialize(msg, ScenerankingEventInformationParam.class);
                scenerankingEventInformationService.handle(scenerankingEventInformationParam);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[buildScenerankingEventInformationConsumer] consume failed, msg: {}",
                        msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        });
        return consumer;
    }


    @Bean(destroyMethod = "close")
    public IConsumerProcessor genCoreDumpCaseConsumer() throws Exception {
        return buildGenCoreDumpCaseConsumer();
    }

    private IConsumerProcessor buildGenCoreDumpCaseConsumer() throws Exception {
        Properties properties = getProperties(MafkaConstant.RECORD_FOR_APP_GEN_CORE_DUMP_CASE_CONSUMER_GROUP);
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_RECORD_FOR_APP);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildGenCoreDumpCaseConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                genCoreDumpCaseHandler.genCoreDumpCase(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[MafkaConsumerConfig#buildGenCoreDumpCaseConsumer] gen core dump case consumer failed, msg: {}",
                        msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor s3CleanupFileConsumer() throws Exception {
        return buildS3CleanupFileConsumer();
    }

    private IConsumerProcessor buildS3CleanupFileConsumer() throws Exception {
        Properties properties = getCastleDaojiaProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_S3_CLEANUP_FILE);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildS3CleanupFileConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                s3FileCleanupHandler.deleteS3CleanupFile(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[MafkaConsumerConfig#buildS3CleanupFileConsumer] s3 cleanup file consumer failed, msg: {}",
                        msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    private Properties getProperties() {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace,
                MafkaConstant.NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appkey);
        properties.setProperty(ConsumerConstants.SubscribeGroup, appkey);
        return properties;
    }

    private Properties getProperties(String subscribeGroup) {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace,
                MafkaConstant.NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appkey);
        properties.setProperty(ConsumerConstants.SubscribeGroup, subscribeGroup);
        return properties;
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor inboundRecordSnapshotOfflineConsumer() throws Exception {
        return buildInboundRecordSnapshotOfflineConsumer();
    }

    private IConsumerProcessor buildInboundRecordSnapshotOfflineConsumer() throws Exception {
        Properties properties = getCastleDaojiaProperties(MafkaConstant.CONSUMER_INBOUND_RECORD_SNAPSHOT_OFFLINE);
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_INBOUND_RECORD_SNAPSHOT);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildInboundRecordSnapshotOfflineConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                offlineInboundRecordSnapshotHandler.handle(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[buildInboundRecordSnapshotOfflineConsumer] failed, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor inboundRecordInitializeConsumer() throws Exception {
        return buildInboundRecordInitializeConsumer();
    }

    private IConsumerProcessor buildInboundRecordInitializeConsumer() throws Exception {
        Properties properties = getCastleDaojiaProperties(MafkaConstant.CONSUMER_INBOUND_RECORD_INITIALIZE);
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_INBOUND_RECORD_SNAPSHOT);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildInboundRecordInitializeConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                inboundRecordInitializeService.handle(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[buildInboundRecordInitializeConsumer] failed, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor inboundRecordFileMetadataConsumer() throws Exception {
        return buildInboundRecordFileMetadataConsumer();
    }

    private IConsumerProcessor buildInboundRecordFileMetadataConsumer() throws Exception {
        Properties properties = getCastleDaojiaProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_INBOUND_RECORD_FILE_METADATA);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildInboundRecordFileMetadataConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                recordFileV2Service.handle(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[buildInboundRecordFileMetadataConsumer] failed, msg: {}",
                        msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor inboundRecordUploadConsumer() throws Exception {
        return buildInboundRecordUploadConsumer();
    }

    private IConsumerProcessor buildInboundRecordUploadConsumer() throws Exception {
        Properties properties = getCastleDaojiaProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_INBOUND_RECORD_INITIALIZED);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildInboundRecordInitializedConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                inboundRecordUploadService.handle(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[buildInboundRecordInitializedConsumer] failed, msg: {}",
                        msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor inboundRecordParseConsumer() throws Exception {
        return buildInboundRecordParseConsumer();
    }

    private IConsumerProcessor buildInboundRecordParseConsumer() throws Exception {
        Properties properties = getCastleDaojiaProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_INBOUND_RECORD_UPLOADED);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildInboundRecordParseConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                inboundRecordParseService.handle(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[buildInboundRecordParseConsumer] failed, msg: {}",
                        msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor recordInboundValidateConsumer() throws Exception {
        return buildRecordInboundValidateConsumer();
    }

    private IConsumerProcessor buildRecordInboundValidateConsumer() throws Exception {
        Properties properties = getCastleDaojiaProperties();
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_INBOUND_RECORD_PARSED);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildRecordInboundValidateConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                inboundRecordValidateService.handle(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[buildRecordInboundValidateConsumer] failed, msg: {}",
                        msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor diskCollectRecordUploadConsumer() throws Exception {
        return buildDiskCollectRecordUploadConsumer();
    }

    private IConsumerProcessor buildDiskCollectRecordUploadConsumer() throws Exception {
        Properties properties = getCastleDaojiaProperties(MafkaConstant.CONSUMER_DISK_COLLECT_RECORD_UPLOAD);
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_INBOUND_RECORD_INITIALIZED);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildDiskCollectRecordUploadConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                diskCollectRecordUploadService.handle(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[buildDiskCollectRecordUploadConsumer] failed, msg: {}",
                        msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor diskCollectRecordParseConsumer() throws Exception {
        return buildDiskCollectRecordParseConsumer();
    }

    private IConsumerProcessor buildDiskCollectRecordParseConsumer() throws Exception {
        Properties properties = getCastleDaojiaProperties(MafkaConstant.CONSUMER_DISK_COLLECT_RECORD_PARSE);
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_INBOUND_RECORD_UPLOADED);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[buildDiskCollectRecordParseConsumer] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                diskCollectRecordParseService.handle(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[buildDiskCollectRecordParseConsumer] failed, msg: {}",
                        msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    @Bean(destroyMethod = "close")
    public IConsumerProcessor RecordSparkTaskCreateConsumer() throws Exception {
        return buildConsumerRecordSparkJobCreate();
    }

    private IConsumerProcessor buildConsumerRecordSparkJobCreate() throws Exception {
        Properties properties = getCastleDaojiaProperties(MafkaConstant.CONSUMER_RECORD_SPARK_JOB_CREATE);
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, MafkaConstant.TOPIC_INBOUND_RECORD_COMPLETED);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {

            String msg = (String) message.getBody();
            log.info("[Inbound Record Spark Job] msgId: {}, partition: {}, offset: {}, msg: {}",
                    message.getMessageID(), message.getParttion(), message.getOffset(), msg);
            try {
                recordSparkJobCreateService.handle(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[Inbound Record Spark Job] failed, msg: {}",
                        msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }

        });
        return consumer;
    }

    private Properties getCastleDaojiaProperties() {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace,
                MafkaConstant.CASTLE_DAOJIA_NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appkey);
        properties.setProperty(ConsumerConstants.SubscribeGroup, appkey);
        return properties;
    }

    private Properties getCastleDaojiaProperties(String subscribeGroup) {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace,
                MafkaConstant.CASTLE_DAOJIA_NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appkey);
        properties.setProperty(ConsumerConstants.SubscribeGroup, subscribeGroup);
        return properties;
    }

}