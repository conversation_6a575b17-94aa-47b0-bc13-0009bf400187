package com.meituan.walle.data.center.entity.po;

import com.meituan.walle.data.center.entity.dto.BizEventDeriveInterventionLabelDTO;
import com.meituan.walle.data.center.entity.pojo.BizEventDeriveIntervention;
import com.meituan.walle.data.center.entity.pojo.LiveIssueTag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/28
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class DeriveInterventionCreate {

    private List<BizEventDeriveIntervention> listEventIntervention;

    private List<LiveIssueTag> listOnMatchedLiveIssueTag;

    private List<BizEventDeriveInterventionLabelDTO> listEventInterventionLabelDTO;
}