package com.meituan.walle.data.center.entity.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompressionAddParam {
    // 以下为RecordFileV2的字段
    private String recordName;
    private Integer recordDate;
    private String fileName;
    private String filePath;
    private String s3Url;
    private Integer fileType;
    private Long fileSize;
    private Long fileSizeS3;
    private Integer datekey;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastModified;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;
    private Integer cluster;
    private String compressionFormat;
    // 以下为BRecordPkg的字段
    private String module;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Timestamp startTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Timestamp endTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Timestamp firstMsgTime;
    private Integer msgCount;
    private String topics;
    private Double duration;
}
