package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/04/01
 */
@Getter
@AllArgsConstructor
public enum MiningResultVisualStatusEnum {
    PRE_RECORDING(0, "待生成视频任务"),
    RECORDING(1, "生成中"),
    RECORDING_FAILED(-1, "生成失败"),
    RECORDING_SUCCESS(2, "生成成功");

    private int code;
    private String msg;

    public static MiningResultVisualStatusEnum byOrdinal(int ord) {
        for (MiningResultVisualStatusEnum e : MiningResultVisualStatusEnum.values()) {
            if (e.code == ord) {
                return e;
            }
        }
        return null;
    }
}