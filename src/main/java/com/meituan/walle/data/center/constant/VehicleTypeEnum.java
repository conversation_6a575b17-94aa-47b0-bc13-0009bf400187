package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/18 下午5:31
 * Description:
 * Modified by
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum VehicleTypeEnum {

    /**
     * 未知
     */
    UNKNOWN(0, "unknown"),
    /**
     * 大车
     */
    BIG_VEHICLE(1, "big"),
    /**
     * 中车
     */
    MIDDLE_VEHICLE(2, "middle");


    public static String getMsgByCode(Integer code) {
        if (Objects.isNull(code)) {
            return UNKNOWN.getMsg();
        }
        for (VehicleTypeEnum type : VehicleTypeEnum.values()) {
            if (type.getCode() == code.intValue()) {
                return type.getMsg();
            }
        }
        return UNKNOWN.getMsg();
    }

    public static VehicleTypeEnum getByMsg(String msg) {
        for (VehicleTypeEnum type : values()) {
            if (type.getMsg().equals(msg)) {
                return type;
            }
        }
        return UNKNOWN;
    }

    private int code;
    private String msg;

}
