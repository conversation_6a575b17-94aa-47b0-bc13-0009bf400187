package com.meituan.walle.data.center.dal.recordmanager.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InboundRecordSparkTaskPO {
    private String recordName;
    private String jobUuid;
    private String instanceUuid;
    private Byte instanceType;
    private String dynamicParam;
    private Byte status;
    private Integer isDeleted;
}
