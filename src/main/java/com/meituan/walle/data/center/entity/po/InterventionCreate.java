package com.meituan.walle.data.center.entity.po;

import com.meituan.walle.data.center.entity.dto.InterventionLabelDTO;
import com.meituan.walle.data.center.entity.pojo.LiveIssueTag;
import com.meituan.walle.data.center.entity.pojo.VehicleInterventionOffline;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/19
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class InterventionCreate {
    private List<VehicleInterventionOffline> listIntervention;
    private List<InterventionLabelDTO> listInterventionLabelDTO;
    private List<LiveIssueTag> listOnMatchedLiveIssueTag;
}