package com.meituan.walle.data.center.algorithm.astar;

import com.meituan.walle.data.center.constant.AStarFlagEnum;
import com.meituan.walle.data.center.entity.algorithm.astar.ListGraph;
import com.meituan.walle.data.center.entity.algorithm.astar.Node;
import com.meituan.walle.data.center.entity.algorithm.astar.Point;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class AStar {
    /**
     * 用最小堆来记录扩展的点
     */
    final MinHeap heap = new MinHeap();
    private List<Node> closeList = new ArrayList<>();
    private List<Node> out = new ArrayList<>();
    /**
     * 策略选择标识：0:最短距离|1:最短时间|2:最大mpi
     */
    private int flag;
    private double defaultVelocity;
    private double defaultMpi;

    public MinHeap getHeap() {
        return heap;
    }

    public List<Node> getCloseList() {
        return closeList;
    }

    public List<Node> getOut() {
        return out;
    }

    public void setCloseList(List<Node> closeList) {
        this.closeList = closeList;
    }

    public void setOut(List<Node> out) {
        this.out = out;
    }

    /**
     * 开始算法
     */
    public void start(ListGraph listGraph, int flag) {
        if (listGraph == null || listGraph.getStartNode() == null
                || listGraph.getGraphs() == null || listGraph.getEndNode() == null) {
            log.info("listGraph is empty or is invalid, just return");
            return;
        }
        this.flag = flag;
        // 开始搜索
        Node startNode = listGraph.getStartNode();
        heap.add(startNode);
        moveNodes(listGraph);
    }

    /**
     * 移动当前结点
     */
    private void moveNodes(ListGraph listGraph) {
        while (!heap.isEmpty()) {
            Node current = heap.getAndRemoveMin();
            closeList.add(current);
            addNeighborNodeInOpen(listGraph, current);
            Node endNode = listGraph.getEndNode();
            if (endNode == null) {
                log.info("end node is null, just return");
                break;
            }
            if (isPointInClose(endNode.getId())) {
                drawPath(listGraph.getGraphs(), listGraph.findNodeById(endNode.getId()));
                break;
            }
        }
    }

    /**
     * 在二维数组中绘制路径
     */
    private void drawPath(List<Node> graphs, Node end) {
        if (end == null || graphs == null) {
            return;
        }
        while (end != null) {
            Node node = end;
            end = end.getParent();
            out.add(node);
        }
    }

    /**
     * 添加所有邻结点到open表
     */
    private void addNeighborNodeInOpen(ListGraph listGraph, Node current) {
        List<String> nodeList = current.getDirection().getNodeList();
        int w1 = 0;
        int w2 = 0;
        int w3 = 0;
        if (flag == AStarFlagEnum.SHORTEST_LENGTH.getCode()) {
            w1 = 1;
        } else if (flag == AStarFlagEnum.SHORTEST_TIME.getCode()) {
            w2 = 1;
        } else if (flag == AStarFlagEnum.LARGEST_MPI.getCode()) {
            w3 = 1;
        }
        for (String id : nodeList) {
            Node next = listGraph.findNodeById(id);
            if (next == null) {
                continue;
            }
            double cost = w1 * next.getLength() + w2 * next.getTimeCost() + w3 * next.getLaneCost();
            addNeighborNodeInOpen(listGraph, current, next, cost);
        }
    }

    /**
     * 添加一个邻结点到open表
     */
    private void addNeighborNodeInOpen(ListGraph listGraph, Node current, Node next, double value) {
        if (listGraph == null || current == null || next == null) {
            log.info("current node or next node is empty, just return");
            return;
        }
        if (next.getId() == null) {
            log.info("next node id is null, return");
            return;
        }
        if (!canAddNodeToOpen(next.getId())) {
            return;
        }
        Node end = listGraph.getEndNode();
        if (end == null) {
            return;
        }
        Point point = next.getPoint();
        // 计算邻结点的G值
        double g = current.getG() + value;
        Node child = heap.find(next.getId());
        if (child != null && !current.getDirection().getNodeList().contains(child.getId())) {
            child = null;
        }
        if (child == null) {
            // 计算H值
            double h = calcH(end.getPoint(), point);
            if (isEndNode(end.getPoint(), point)) {
                child = end;
                child.setParent(current);
                child.setG(g);
                child.setH(h);
            } else {
                child = next;
                child.setG(g);
                child.setH(h);
                child.setParent(current);
            }
            heap.add(child);
        } else if (child.getG() > g) {
            child.setG(g);
            child.setParent(current);
            heap.add(child);
        }
    }

    /**
     * 计算H的估值：“曼哈顿”法，坐标分别取差值相加
     */
    private double calcH(Point end, Point point) {
        double h = Math.abs(end.getX() - point.getX()) + Math.abs(end.getY() - point.getY());
        if (flag == AStarFlagEnum.SHORTEST_TIME.getCode()) {
            h = h / defaultVelocity;
        } else if (flag == AStarFlagEnum.LARGEST_MPI.getCode()) {
            h = h / defaultMpi;
        }
        return h;
    }

    /**
     * 判断结点是否是最终结点
     */
    private boolean isEndNode(Point end, Point point) {
        return end != null && end.equals(point);
    }

    /**
     * 判断结点能否放入Open列表
     */
    private boolean canAddNodeToOpen(String nodeId) {
        return !isPointInClose(nodeId);
    }

    /**
     * 判断坐标是否在close表中
     */
    private boolean isPointInClose(String nodeId) {
        if (closeList.isEmpty()) {
            return false;
        }
        for (Node node : closeList) {
            if (node.getId().equals(nodeId)) {
                return true;
            }
        }
        return false;
    }

    public void setDefaultVelocity(double defaultVelocity) {
        this.defaultVelocity = defaultVelocity;
    }

    public void setDefaultMpi(double defaultMpi) {
        this.defaultMpi = defaultMpi;
    }
}
