package com.meituan.walle.data.center.entity.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.io.Serializable;
import java.text.DecimalFormat;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class RealUploadProgress implements Serializable {
    @JsonIgnore
    private DecimalFormat decimalFormat = new DecimalFormat("0.00");

    private Integer fileTotal = 0;

    private Integer fileUploaded = 0;

    private Long sizeTotal = 0L;

    private Long sizeUploaded = 0L;

    public Double getFileProgress() {
        return fileTotal != 0 ?
                Double.parseDouble(decimalFormat.format((double) fileUploaded / (double) fileTotal)) : -1.0;
    }

    public Double getSizeProgress() {
        return sizeTotal != 0 ?
                Double.parseDouble(decimalFormat.format((double) sizeUploaded / (double) sizeTotal)) : -1.0;
    }
}
