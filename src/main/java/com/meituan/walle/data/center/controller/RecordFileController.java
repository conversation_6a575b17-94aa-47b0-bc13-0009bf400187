package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.vo.RecordFileVO;
import com.meituan.walle.data.center.service.RecordFileService;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Deprecated
@RestController
@RequestMapping(value = "/record_file")
@Log4j2
public class RecordFileController {
    @Resource
    private RecordFileService recordFileService;

    @RequestMapping(value = "/hdfs_path", method = RequestMethod.POST)
    public Map<String, Object> updateRecordFileHdfsPath(@RequestBody RecordFileVO vo) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 404);
        result.put("msg", "interface offline!");
        return result;
    }

    @RequestMapping(value = "/hdfs_path/batch", method = RequestMethod.POST)
    public Map<String, Object> updateRecordFileHdfsPathBatch(@RequestBody List<RecordFileVO> list) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 404);
        result.put("msg", "interface offline!");
        return result;
    }

    /**
     * 给diskserver检查并且更新hdfs_path用的一个接口
     *
     * @param dayAgo
     * @return
     */
    @GetMapping(value = "/hdfs_path/check")
    public Map<String, Object> findWithoutHdfsPath(@RequestParam(required = false) Integer dayAgo) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 404);
        result.put("msg", "interface offline!");
        return result;
    }

    /**
     * 给diskserver检查并且更新hdfs_path用的一个接口
     *
     * @param recordName
     * @return
     */
    @GetMapping(value = "/hdfs_path/check/record_name")
    public Map<String, Object> findWithoutHdfsPath(@RequestParam(required = true) String recordName) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 404);
        result.put("msg", "interface offline!");
        return result;
    }
}
