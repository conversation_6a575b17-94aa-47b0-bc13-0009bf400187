package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/11/23
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Table(name = "sim_task_state")
public class SimTaskState {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * record name
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * case id
     */
    @Column(name = "case_id")
    private String caseId;

    /**
     * case time
     */
    @Column(name = "case_time")
    private Date caseTime;

    /**
     * 需要在仿真集群上跑的代码对应的commit id
     */
    @Column(name = "commit_id")
    private String commitId;

    /**
     * 同一批次仿真任务唯一标识
     */
    @Column(name = "job_id")
    private String jobId;

    /**
     * 同一批次仿真任务的任务类型
     */
    @Column(name = "job_type")
    private String jobType;

    /**
     * 批次内部的task类型
     */
    @Column(name = "task_type")
    private String taskType;

    /**
     * 仿真验证状态
     */
    @Column(name = "sim_state")
    private String simState;

    /**
     * 仿真整体通过结果
     */
    @Column(name = "level")
    private String level;

    /**
     * 仿真验证详情
     */
    @Column(name = "sim_detail")
    private String simDetail;

    /**
     * 仿真返回的结果标签集
     */
    @Column(name = "case_tags")
    private String caseTags;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}

