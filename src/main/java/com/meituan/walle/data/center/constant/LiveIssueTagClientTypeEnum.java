package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/08/20
 * 离线tag clientType = 1、him tag clientType = 2、Dreamview tag clientType = 3、运营HMI tag clientType = 4
 */
@Getter
@AllArgsConstructor
public enum LiveIssueTagClientTypeEnum {
    /**
     * 离线寄盘
     */
    OFFLINE(1, "离线寄盘"),
    /**
     * 远遥HMI
     */
    REMOTE_HMI(2, "远遥HMI"),
    /**
     * DreamView
     */
    DREAM_VIEW(3, "DreamView"),
    /**
     * 运营HMI
     */
    BUSINESS(4, "运营HMI"),

    DEMOTION_AUTO(5, "降级自动上报"),

    /**
     * H5上报
     */
    H5_REPORT(6, "网页"),

    REMOTE_BUTTON(7, "方向盘按钮")
    ;

    private Integer ret;
    private String msg;
}