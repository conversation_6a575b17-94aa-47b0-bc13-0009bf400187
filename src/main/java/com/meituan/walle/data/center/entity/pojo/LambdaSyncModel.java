package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "lambda_sync_model")
public class LambdaSyncModel {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    private String modelId;
    private String name;
    private String targetTable;
    private String uniqueColumns;
    private Date createTime;
    private Date updateTime;

}
