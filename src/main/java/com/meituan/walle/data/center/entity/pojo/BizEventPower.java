package com.meituan.walle.data.center.entity.pojo;


import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/1/31
 */

@Data
@Table(name = "biz_event_power")
public class BizEventPower {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 事件的业务id
     */
    private String eventId;

    /**
     * 表record的record_name外键
     */
    private String recordName;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 事件发生时间
     */
    private Date eventTime;

    /**
     * utm坐标的x（来源于localization的x）
     */
    private String x;

    /**
     * utm坐标的y（来源于localization的y）
     */
    private String y;

    /**
     * 事件类型，38表示进入省电模式，39表示退出省电模式
     */
    private Integer eventType;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private Boolean isDeleted;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * update_time
     */
    private Date updateTime;
}
