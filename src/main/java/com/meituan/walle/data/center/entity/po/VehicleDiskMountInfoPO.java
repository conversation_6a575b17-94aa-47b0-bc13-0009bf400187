package com.meituan.walle.data.center.entity.po;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.sql.Timestamp;

@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class VehicleDiskMountInfoPO implements Serializable {

    private Long id;
    private String traceId;
    private String bid;
    private String vin;
    private String diskId;
    private String originDiskId;
    private Long capacity;
    private Long remaining;
    private Timestamp mountTime;
    private String dfInfo;
    private String duInfo;
    private Integer beSend;
    private Timestamp beSendTime;
    private Timestamp createTime;
    private Timestamp updateTime;

    @Override
    public String toString(){
        try {
            return new ObjectMapper().writeValueAsString(this);
        } catch (JsonProcessingException e) {
            log.error("VehicleDiskMountInfoPO toString failed", e);
        }
        return "";
    }
}
