package com.meituan.walle.data.center.entity.algorithm.astar;


import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ListGraph {
    private List<Node> graphs;
    private Node startNode;
    private Node endNode;
    private Map<String, Node> idToNodeMap;

    public List<Node> getGraphs() {
        return graphs;
    }

    public Node getStartNode() {
        return startNode;
    }

    public Node getEndNode() {
        return endNode;
    }

    public void setGraphs(List<Node> graphs) {
        this.graphs = graphs;
    }

    public void setStartNode(Node startNode) {
        this.startNode = startNode;
    }

    public void setEndNode(Node endNode) {
        this.endNode = endNode;
    }

    public Node findNodeById(String id) {
        if (id == null || id.isEmpty()) {
            log.info("id is empty, return");
            return null;
        }
        if (idToNodeMap == null || idToNodeMap.isEmpty()) {
            idToNodeMap = new HashMap<>();
            for (Node node : graphs) {
                if (node.getId() == null || node.getId().isEmpty()) {
                    continue;
                }
                idToNodeMap.put(node.getId(), node);
            }
        }
        return idToNodeMap.get(id);
    }
}
