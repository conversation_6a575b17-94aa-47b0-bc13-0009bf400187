package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "biz_event_simulation_scenario_switch")
public class BizEventSimulationScenarioSwitch {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 事件的业务id
     */
    private String eventId;

    /**
     * 表record的record_name外键
     */
    private String recordName;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 事件发生时间
     */
    private Date eventTime;

    /**
     * utm坐标的x（来源于localization的x）
     */
    private String x;

    /**
     * utm坐标的y（来源于localization的y）
     */
    private String y;

    /**
     * 序列号
     */
    private Integer sequenceId;

    /**
     * 场景标识
     */
    private String adsId;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private int isDeleted;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * update_time
     */
    private Date updateTime;

}
