package com.meituan.walle.data.center.config;

import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.meituan.walle.data.center.constant.KmsConstant;
import com.sankuai.meituan.poros.client.PorosHighLevelClientBuilder;
import com.sankuai.meituan.poros.client.PorosProtocol;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/1/16 10:09
 * @description
 */
@Configuration
@Slf4j
public class EagleConfig {

    @Value("${appkey}")
    private String appKey;

    private static final String ES_CLUSTER_NAME = "eaglenode_search-esserver";

    @Bean("esClient")
    public RestHighLevelClient esClient() throws KmsResultNullException {
        String accessKey = Kms.getByName(appKey, KmsConstant.ES_APP_ACCESS_KEY);
        return PorosHighLevelClientBuilder.builder()
                .clusterName(ES_CLUSTER_NAME)
                .appKey(appKey)
                .accessKey(accessKey)
                .porosProtocol(PorosProtocol.PIGEON)
                .callESDirectly(true)
                .timeoutMillis(100000)
                .build();
    }

}
