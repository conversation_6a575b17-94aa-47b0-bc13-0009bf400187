package com.meituan.walle.data.center.entity.pojo;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2021/7/10 12:11 下午
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@Table(name = "biz_routing_verify_history")
public class BizRoutingVerifyHistory {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 路由ID
     */
    @Column(name = "routing_id")
    private String routingId;

    /**
     * job id
     */
    @Column(name = "job_id")
    private String jobId;

    /**
     * commit id
     */
    @Column(name = "commit_id")
    private String commitId;

    /**
     * 结果
     */
    @Column(name = "status")
    private String status;

    /**
     * 结果详情
     */
    @Column(name = "info")
    private String info;

    /**
     * 里程
     */
    @Column(name = "mileage")
    private Double mileage;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
