package com.meituan.walle.data.center.config;

import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.walle.data.center.service.DiskUploadManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2023/02/09
 */
@Slf4j
@Configuration
public class DiskUploadConsumerMafkaConfig extends AbstractDiskUploadMafkaConfig {
    @Autowired
    private DiskUploadManagerService diskUploadManagerService;

    @Bean(destroyMethod = "close")
    public IConsumerProcessor diskUploadFileConsumer() throws Exception {
        return buildDiskUploadFileConsumer();
    }

    private IConsumerProcessor buildDiskUploadFileConsumer() throws Exception {
        Properties properties = getProperties(appkey);
        IConsumerProcessor consumer = MafkaClient.buildConsumerFactory(properties, TOPIC_S3_DISK_UPLOAD_FILE_NOTICE);
        consumer.recvMessageWithParallel(String.class, (MafkaMessage message, MessagetContext context) -> {
            String msg = (String) message.getBody();
            log.info("[DiskUploadConsumerMafkaConfig#buildDiskUploadFileConsumer] " +
                    "message: {}, messageId: {}, partition: {}, offset: {}",
                    msg, message.getMessageID(), message.getParttion(), message.getOffset());
            try {
                diskUploadManagerService.diskUploadFileConsumer(msg);
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("failed to consumer disk upload file, msg: {}", msg, e);
                return ConsumeStatus.RECONSUME_LATER;
            }
        });
        return consumer;
    }
}