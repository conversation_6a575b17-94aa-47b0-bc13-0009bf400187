package com.meituan.walle.data.center.adaptor;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.walledelivery.basic.client.enums.DelivererTypeEnum;
import com.sankuai.walledelivery.basic.client.request.deliverer.DeliverRpcRequest;
import com.sankuai.walledelivery.basic.client.response.businessStation.BusinessStationPositionResponse;
import com.sankuai.walledelivery.basic.client.response.businessStation.dto.BusinessStationDTO;
import com.sankuai.walledelivery.basic.client.thrift.inner.deliverer.DelivererQueryThriftService;
import com.sankuai.walledelivery.commons.enums.ErrorCode;
import com.sankuai.walledelivery.thrift.response.ThriftResponse;
import com.sankuai.walledelivery.utils.JacksonUtils;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

@Component
@Slf4j
public class WalleDeliveryBasicAdaptor {
    @MdpThriftClient(remoteAppKey = "com.sankuai.walledelivery.basic", timeout = 5000)
    private DelivererQueryThriftService delivererQueryThriftService;

    @Validated
    public List<String> queryBusinessStationListByVin(@NotBlank String vin) {
        // 1. 参数校验
        if (StringUtils.isBlank(vin)) {
            log.error("queryBusinessStationListByVin# VIN不能为空");
            throw new IllegalArgumentException("VIN不能为空");
        }

        // 2. 构建请求
        DeliverRpcRequest request = DeliverRpcRequest.builder()
                .account(vin.trim().toUpperCase()) // VIN统一转大写
                .delivererTypeEnum(DelivererTypeEnum.VEHICLE_POSITION_TYPE)
                .build();

        // 3. 记录开始日志
        log.info("queryBusinessStationListByVin# 开始查询, VIN: {}", vin);
        try {
            // 4. 调用远程服务
            ThriftResponse<BusinessStationPositionResponse> response =
                    delivererQueryThriftService.queryBusinessStationAndPositionByDeliver(request);

            // 5. 响应检查
            if (response == null || response.getCode() != ErrorCode.StandardErrorCode.OK.getCode()) {
                log.error("queryBusinessStationListByVin# 远程服务返回异常, VIN: {}, response: {}",
                        vin, JacksonUtils.to(response));
                return Collections.emptyList();
            }
            BusinessStationPositionResponse data = response.getData();

            // 6. 数据处理
            if (data == null || CollectionUtils.isEmpty(data.getBusinessStationDTO())) {
                log.error("queryBusinessStationListByVin# 远程服务返回数据为空, VIN: {}", vin);
                return Collections.emptyList();
            }

            List<String> stationNames = data.getBusinessStationDTO().stream()
                    .map(BusinessStationDTO::getName)
                    .filter(StringUtils::isNotBlank)
                    .distinct() // 去重
                    .collect(Collectors.toList());
            // 7. 记录
            log.info("queryBusinessStationListByVin# 查询成功, VIN: {}, 站点数量: {}",
                    vin, stationNames.size());

            return stationNames;

        } catch (Exception e) {
            // 8. 异常处理
            log.error("queryBusinessStationListByVin# 查询异常, VIN: {}",
                    vin, e);

            return Collections.emptyList();
        }
    }

}
