package com.meituan.walle.data.center.dal.wcdpbasedata.mapper;

import com.meituan.walle.data.center.dal.wcdpbasedata.entity.BdsSensorInfoPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2024/01/08
 */
public interface BdsSensorInfoMapper {

    @Select({
            "select * from bds_sensor_info where status = 1 and alias = #{diskId} order by last_update_time desc limit 1"
    })
    BdsSensorInfoPO selectDiskInfo(@Param("diskId") String diskId);

}