package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/01/20
 */
@Getter
@AllArgsConstructor
public enum SysDictEnum {

    /**
     * 字符类型
     */
    STRING(0, "string"),
    /**
     * 数值类型
     */
    NUMBER(1, "number"),
    ;

    private int code;
    private String msg;

    public static SysDictEnum byOrdinal(int ord) {
        for (SysDictEnum e : SysDictEnum.values()) {
            if (e.code == ord) {
                return e;
            }
        }
        return null;
    }
}