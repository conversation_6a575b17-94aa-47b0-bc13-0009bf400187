package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "biz_event_pnc_parking_related")
public class BizEventPncParkingRelated {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 事件id
     */
    private String eventId;

    /**
     * 表record的record_name外键
     */
    private String recordName;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 事件发生时间
     */
    private Date eventTime;

    /**
     * 档位
     */
    private Integer gear;

    /**
     * 规划器类型
     */
    private Integer plannerType;

    /**
     * 车辆状态类型
     */
    private Integer vehicleStateType;

    /**
     * 场景类型
     */
    private Integer freespaceScenes;

    /**
     * freeSpace状态值
     */
    private Integer freespaceStatus;

    /**
     * 该事件是否成功完成
     */
    private Boolean isSuccess;

    /**
     * xy精度
     */
    private String xyAccuracy;

    /**
     * theta角精度
     */
    private String thetaAccuracy;

    /**
     * 车速
     */
    private String speed;

    /**
     * debug消息
     */
    private String eventMsg;

    /**
     * 是否逻辑删除
     */
    private Boolean isDeleted;

    private Date createTime;

    private Date updateTime;
}
