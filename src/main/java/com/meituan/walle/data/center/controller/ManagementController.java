package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.walle.data.center.constant.CatConstant;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@InterfaceDoc(
        displayName = "服务存活监控接口",
        type = "restful",
        description = "服务存活监控",
        scenarios = "服务存活返回存活信息字符串"
)
@RestController
public class ManagementController {

    @MethodDoc(
            requestMethods = {HttpMethod.GET},
            displayName = "服务存活监控",
            description = "服务存活监控",
            returnValueDescription = "存活信息字符串"
    )
    @GetMapping("/monitor/alive")
    public String monitor() {
        Transaction t = Cat.newTransaction(CatConstant.VEHICLE_ALIVE, "vehicleMonitorAlive");
        long start = System.currentTimeMillis();
        t.setDurationInMillis(System.currentTimeMillis() - start);
        t.setSuccessStatus();
        return "I'm alive";
    }

}