package com.meituan.walle.data.center.constant;

public class CatConstant {

    public static final String API = "API";
    public static final String Tag = "tag";
    public static final String SendTagCount = "send.tag.count";
    public static final String SendTagDuration = "send.tag.duration";

    public static final String Sim = "sim";

    public static final String Record = "record";
    public static final String SyncSimTaskCount = "sync.sim.task.count";
    public static final String SyncSimTaskDuration = "sync.sim.task.duration";

    public static final String SimTaskFileCount = "sim.task.file.count";
    public static final String SimTaskFileDuration = "sim.task.file.duration";

    public static final String SimTaskFilePkgCount = "sim.task.file.pkg.count";
    public static final String SimTaskFilePkgDuration = "sim.task.file.pkg.duration";

    public static final String UpdateSimTaskStatusCount = "update.sim.task.status.count";
    public static final String UpdateSimTaskStatusDuration = "update.sim.task.status.duration";

    public static final String InsertRecordFileBatchCount = "insert.record.file.batch.count";
    public static final String InsertRecordFileBatchDuration = "insert.record.file.batch.duration";

    public static final String VehicleUploadRequest = "vehicle.upload.request";
    public static final String VehicleUploadRecFileCount = "vehicle.upload.rec.file.count";
    public static final String VehicleUploadRecFileDuration = "vehicle.upload.rec.file.duration";
    public static final String VehicleUploadGetDataParamCount = "vehicle.upload.get.data.param.count";
    public static final String VehicleUploadGetDataParamDuration = "vehicle.upload.get.data.param.duration";
    public static final String VehicleUploadSucceedUploadFileCount = "vehicle.upload.succeed.upload.file.count";
    public static final String VehicleUploadSucceedUploadFileDuration = "vehicle.upload.succeed.upload.file.duration";
    public static final String VehicleUploadNotFoundUploadFileCount = "vehicle.upload.not.found.upload.file.count";
    public static final String VehicleUploadNotFoundUploadFileDuration
            = "vehicle.upload.not.found.upload.file.duration";
    public static final String VehicleUploadCheckUploadCount = "vehicle.upload.check.upload.count";
    public static final String VehicleUploadCheckUploadDuration = "vehicle.upload.check.upload.duration";
    public static final String VEHICLE_EVENT = "vehicle.event";
    public static final String GET_VEHICLE_TAG_LIST = "get.vehicle.tag.list";
    public static final String VEHICLE_TAG_REPORT = "vehicle.tag.report";
    public static final String TAG_REPORT_PICTURE = "tag.report.picture";
    public static final String GET_VEHICLE_EVENT_DETAIL = "get.vehicle.tag.detail";
    public static final String VEHICLE_TAG_REPORT_UPDATE = "vehicle.tag.report.update";
    public static final String DELETE_TAG_PICTURE = "delete.tag.picture";
    public static final String GET_HMI_TAGS = "get.hmi.tags";
    public static final String FILE_OPERATE = "file.operate";
    public static final String ACCIDENT_INFO = "vehicle.accident.info";
    public static final String ACCIDENT_REVIEW = "vehicle.accident.review";
    public static final String VEHICLE_ALIVE = "vehicle.monitor.alive";
    public static final String ONBOARD_DISK_DELETE_RULE = "onboard.disk.delete.rule";
    public static final String ONBOARD_DISK_DELETE_DISTRIBUTION = "onboard.disk.delete.distribution";
    public static final String ONBOARD_DISK_DELETE_FILES_INFO = "onboard.disk.delete.files.info";
    public static final String ONBOARD_DISK_DELETE_FAIL = "onboard.disk.delete.fail";
    public static final String VEHICLE_UPLOAD_FILE_FAIL = "vehicle.upload.file.fail";
    public static final String ONBOARD_FILE = "onboard.file";
    public static final String ASTAR = "astar";
    public static final String REALTIME_RECORD = "realtime.record";
    public static final String ACCIDENT_RESP_UPDATE = "vehicle.accident.resp.update";
    public static final String QUERY_DISK_NUM = "vehicle.client.query.disk.num";

    public static final String RECORD_VERSION = "record.version";
    public static final String UPDATE_RECORD_VERSION_STATUS_COUNT = "update.record.version.status.count";
    public static final String UPDATE_RECORD_VERSION_STATUS_DURATION = "update.record.version.status.duration";

    public static final String RECORD_VERSION_FILE = "record.version.file";
    public static final String RECORD_VERSION_FILE_COUNT = "record.version.file.count";
    public static final String RECORD_VERSION_FILE_DURATION = "record.version.file.duration";

    public static final String FAST_TASK_FILE = "/mviz/fast_task_file";
    public static final String ACTIVE_VEHICLE_COUNT = "/active/vehicle/count";
}
