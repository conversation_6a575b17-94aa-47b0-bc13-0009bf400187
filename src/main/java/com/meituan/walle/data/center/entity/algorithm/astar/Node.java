package com.meituan.walle.data.center.entity.algorithm.astar;

public class Node {
    private String id;
    private Point point;
    private String geom;
    private Direction direction;
    private int roadType;
    private double laneCost;
    private double length;
    private double timeCost;
    /**
     * 父节点
     */
    private Node parent;
    /**
     * G：是个准确的值，是起点到当前结点的代价
     */
    private double G;
    /**
     * H：是个估值，当前结点到目的结点的估计代价
     */
    private double H;

    public String getId() {
        return id;
    }

    public Point getPoint() {
        return point;
    }

    public String getGeom() {
        return geom;
    }

    public Direction getDirection() {
        return direction;
    }

    public int getRoadType() {
        return roadType;
    }

    public double getLaneCost() {
        return laneCost;
    }

    public double getLength() {
        return length;
    }

    public Node getParent() {
        return parent;
    }

    public double getG() {
        return G;
    }

    public double getH() {
        return H;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setPoint(Point point) {
        this.point = point;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    public void setDirection(Direction direction) {
        this.direction = direction;
    }

    public void setRoadType(int roadType) {
        this.roadType = roadType;
    }

    public void setLaneCost(double laneCost) {
        this.laneCost = laneCost;
    }

    public void setLength(double length) {
        this.length = length;
    }

    public void setParent(Node parent) {
        this.parent = parent;
    }

    public void setG(double g) {
        G = g;
    }

    public void setH(double h) {
        H = h;
    }

    public double getTimeCost() {
        return timeCost;
    }

    public void setTimeCost(double timeCost) {
        this.timeCost = timeCost;
    }

    public double f() {
        return G + H;
    }
}
