package com.meituan.walle.data.center.dal.recordmanager.mapper;

import com.meituan.walle.data.center.dal.recordmanager.entity.BRecordFile;
import com.meituan.walle.data.center.entity.po.RecordFilePO;
import com.meituan.walle.data.center.entity.pojo.RecordFileV2;
import com.meituan.walle.data.center.entity.pojo.RecordV2;
import com.meituan.walle.data.center.entity.request.RecordFilePageQueryRequest;
import com.meituan.walle.data.center.entity.request.RecordFileQueryRequest;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/05/30
 */
public interface BRecordFileMapper {

    @Select({
            "<script>" +
                    "    SELECT " +
                    "        t.`record_name`, " +
                    "        t.`file_name`, " +
                    "        t.`file_path`, " +
                    "        t.`file_type`, " +
                    "        t.`file_size`, " +
                    "        t.`s3_url`, " +
                    "        t.`cluster`, " +
                    "        t.`compression_format` " +
                    "    FROM `record_file` AS t " +
                    "    WHERE t.`s3_is_deleted` = '0' " +
                    "        AND t.`s3_is_marked_for_deletion` = '0' " +
                    "        AND t.`file_size` > 0" +
                    "        AND t.`record_name` = #{recordName} " +
                    "    <if test='fileNameLike != null and &apos;&apos; != fileNameLike'>" +
                    "        AND t.`file_name` like concat('%', #{fileNameLike}, '%') " +
                    "    </if>" +
                    "    <if test='fileTypeList != null and fileTypeList.size() > 0'>" +
                    "        AND t.`file_type` in " +
                    "        <foreach item='item' collection='fileTypeList' open='(' separator=',' close=')'>" +
                    "            #{item}" +
                    "        </foreach>" +
                    "    </if>" +
                    "</script>"
    })
    @Results(value = {
            @Result(property = "recordName", column = "record_name"),
            @Result(property = "fileName", column = "file_name"),
            @Result(property = "filePath", column = "file_path"),
            @Result(property = "fileType", column = "file_type"),
            @Result(property = "fileSize", column = "file_size"),
            @Result(property = "s3Url", column = "s3_url")
    })
    List<BRecordFile> selectRecordFile(RecordFileQueryRequest query);

    @Select({
            "<script>" +
                    "    SELECT " +
                    "        t.`record_name`, " +
                    "        t.`file_name`, " +
                    "        t.`file_path`, " +
                    "        t.`s3_url`, " +
                    "        t.`cluster` " +
                    "    FROM `record_file` AS t " +
                    "    WHERE record_name = #{recordName} " +
                    "       AND file_type = '8' " +
                    "    LIMIT 1" +
                    "</script>"
    })
    @Results(value = {
            @Result(property = "recordName", column = "record_name"),
            @Result(property = "fileName", column = "file_name"),
            @Result(property = "filePath", column = "file_path"),
            @Result(property = "s3Url", column = "s3_url")
    })
    BRecordFile getRecordInfoPbTxt(@Param("recordName") String recordName);

    @Update({
            "<script>" +
                    "    UPDATE `record_file` SET " +
                    "        <if test='null != s3Url and &apos;&apos; != s3Url'>" +
                    "            `s3_url`=#{s3Url}," +
                    "        </if> " +
                    "        <if test='null != fileSizeS3'>" +
                    "            `file_size_s3`=#{fileSizeS3}," +
                    "        </if> " +
                    "        <if test='null != hdfsPath and &apos;&apos; != hdfsPath'>" +
                    "            `hdfs_path`=#{hdfsPath}," +
                    "        </if> " +
                    "        <if test='null != fileSizeHdfs'>" +
                    "            `file_size_hdfs`=#{fileSizeHdfs}," +
                    "        </if> " +
                    "        <if test='null != datekey'>" +
                    "            `datekey`=#{datekey}," +
                    "        </if> " +
                    "        <if test='null != uploadTime'>" +
                    "            `upload_time`=#{uploadTime}," +
                    "        </if> " +
                    "        <if test='null != s3IsMarkedForDeletion'>" +
                    "            `s3_is_marked_for_deletion`=#{s3IsMarkedForDeletion}," +
                    "        </if> " +
                    "        <if test='null != s3IsMarkedForDeletionTime'>" +
                    "            `s3_is_marked_for_deletion_time`=#{s3IsMarkedForDeletionTime}," +
                    "        </if> " +
                    "        <if test='null != s3IsDeleted'>" +
                    "            `s3_is_deleted`=#{s3IsDeleted}," +
                    "        </if> " +
                    "        <if test='null != s3DeleteTime'>" +
                    "            `s3_delete_time`=#{s3DeleteTime}," +
                    "        </if> " +
                    "        `update_time`=CURRENT_TIMESTAMP " +
                    "    WHERE `record_name`=#{recordName} " +
                    "        AND `file_name` = #{fileName} " +
                    "        AND `file_path` = #{filePath} " +
                    "</script>"})
    int update(BRecordFile recordFile);

    @Select({
            "select * from `record_file` " +
                    "where `record_name` = #{recordName} and `file_name` = #{fileName} and `file_path` = #{filePath} "
    })
    BRecordFile getRecordFileByRecordNameAndFileInfo(@Param("recordName") String recordName,
                                                     @Param("fileName") String fileName,
                                                     @Param("filePath") String filePath);

    // RecordFileMapper.java 迁移 begin

    List<BRecordFile> selectRecordFileByRecordName(@Param("recordName") String recordName);

    List<BRecordFile> selectRecordFileByRecordNameAndFileType(@Param("recordName") String recordName,
                                                              @Param("fileType") int fileType);

    @Select("select record_name from record_file \n" +
            " where create_time >= concat(date_add(curdate(), interval -1 day),' ','00:00') \n" +
            " and s3_url = '' and file_size > 0 ")
    List<String> getUpload2S3FailedRecord();

    @Select("select record_name from record_file \n" +
            "where create_time >= concat(date_add(curdate(), interval -1 day),' ','00:00')  \n" +
            "and file_type = 1 and (hdfs_path is null or hdfs_path = '' or " +
            "file_size_hdfs is null or file_size_hdfs = 0 ) and file_size > 0 ")
    List<String> getUpload2HdfsFailedRecord();

    @Select("select record_name\n" +
            "from record_file \n" +
            "where create_time >= concat(date_add(curdate(), interval -1 day),' ','00:00')\n" +
            "and file_type = 8 and file_size = 0")
    List<String> getZeroSizeRecordInfoPb();

    List<String> getExtractRecordInfoPbFailedRecord();

    @Select("select record_name\n" +
            "      from record_file \n" +
            "      where create_time>= concat(date_add(curdate(), interval -1 day),' ','00:00') " +
            "      and file_type = 1 and file_size != 0 ")
    List<String> getRecFileRecord();

    @Select({
            "<script>",
            "select distinct record_name from record_file where 1=1 ",
            "<trim>",
            "<if test='vehicleName != null'>",
            "and record_name like concat('%', #{vehicleName})",
            "</if>",
            "<if test='startDate != null'>",
            "and record_name &gt;= #{startDate}",
            "</if>",
            "<if test='endDate != null'>",
            "and record_name &lt;= date_format(date_add(#{endDate}, interval 1 day), '%Y%m%d')",
            "</if>",
            "</trim>",
            " limit 1000",
            "</script>"
    })
    List<String> queryRecordNameList(String vehicleName, String startDate, String endDate);

    @Select({
            "<script>",
            " SELECT DISTINCT record_name ",
            " from record_file ",
            " where record_name in ",
            "<foreach item='item' collection='recordNameSet' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            " and file_size > 0 and s3_url = '' and file_size_s3 = 0",
            "</script>",
    })
    Set<String> countIncompleteS3Record(@Param("recordNameSet") Set<String> recordNameSet);

    @Select({
            "<script>",
            "select * from record_file ",
            "where (file_type = 3 or (file_type = 2 and file_name = 'onboard_main.out') ",
            "or (file_type = 7 and file_name = 'dmesg.log')) ",
            "and record_name in ",
            "<foreach item='recordName' collection='recordNames' open='(' separator=',' close=')'> ",
            "#{recordName}",
            "</foreach>",
            "</script>",
    })
    List<RecordFilePO> findCoredumpS3PathByRecordNames(@Param("recordNames") List<String> recordNames);

    // RecordFileMapper.java 迁移 end

    // RecordFileV2Mapper.java 迁移 begin

    @Insert({
            "<script>",
            "insert ignore into record_file",
            "(record_name, record_date, file_name, s3_url, file_size_s3, datekey, ",
            "upload_time, file_path, file_type, file_size, last_modified, cluster) ",
            "values",
            "<foreach item='item' collection='list' separator=',' >",
            "(#{item.recordName}, #{item.recordDate}, #{item.fileName}, #{item.s3Url}, #{item.fileSizeS3}, ",
            "#{item.datekey}, #{item.uploadTime}, #{item.filePath}, #{item.fileType}, #{item.fileSize}, ",
            "#{item.lastModified}, #{item.cluster})",
            "</foreach>",
            "</script>"
    })
    int save(List<RecordFileV2> recordFileV2List);

    @Insert({
            "<script>",
            "insert ignore into record_file",
            "<trim prefix='(' suffix=')' suffixOverrides=','>",
            "<if test='list[0].id != null'>",
            "id,",
            "</if>",
            "<if test='list[0].recordName != null'>",
            "record_name,",
            "</if>",
            "<if test='list[0].recordDate != null'>",
            "record_date,",
            "</if>",
            "<if test='list[0].fileName != null'>",
            "file_name,",
            "</if>",
            "<if test='list[0].filePath != null'>",
            "file_path,",
            "</if>",
            "<if test='list[0].md5 != null'>",
            "md5,",
            "</if>",
            "<if test='list[0].s3Url != null'>",
            "s3_url,",
            "</if>",
            "<if test='list[0].fileType != null'>",
            "file_type,",
            "</if>",
            "<if test='list[0].fileSize != null'>",
            "file_size,",
            "</if>",
            "<if test='list[0].fileSizeS3 != null'>",
            "file_size_s3,",
            "</if>",
            "<if test='list[0].datekey != null'>",
            "datekey,",
            "</if>",
            "<if test='list[0].lastModified != null'>",
            "last_modified,",
            "</if>",
            "<if test='list[0].uploadTime != null'>",
            "upload_time,",
            "</if>",
            "<if test='list[0].hdfsPath != null'>",
            "hdfs_path,",
            "</if>",
            "<if test='list[0].fileSizeHdfs != null'>",
            "file_size_hdfs,",
            "</if>",
            "<if test='list[0].s3IsDeleted != null'>",
            "s3_is_deleted,",
            "</if>",
            "<if test='list[0].cluster != null'>",
            "cluster,",
            "</if>",
            "<if test='list[0].compressionFormat != null'>",
            "compression_format,",
            "</if>",
            "</trim>",
            "values",
            "<foreach item='item' collection='list' separator=',' >",
            "<trim prefix='(' suffix=')' suffixOverrides=','>",
            "<if test='item.id != null'>",
            "#{item.id},",
            "</if>",
            "<if test='item.recordName != null'>",
            "#{item.recordName},",
            "</if>",
            "<if test='item.recordDate != null'>",
            "#{item.recordDate},",
            "</if>",
            "<if test='item.fileName != null'>",
            "#{item.fileName},",
            "</if>",
            "<if test='item.filePath != null'>",
            "#{item.filePath},",
            "</if>",
            "<if test='item.md5 != null'>",
            "#{item.md5},",
            "</if>",
            "<if test='item.s3Url != null'>",
            "#{item.s3Url},",
            "</if>",
            "<if test='item.fileType != null'>",
            "#{item.fileType},",
            "</if>",
            "<if test='item.fileSize != null'>",
            "#{item.fileSize},",
            "</if>",
            "<if test='item.fileSizeS3 != null'>",
            "#{item.fileSizeS3},",
            "</if>",
            "<if test='item.datekey != null'>",
            "#{item.datekey},",
            "</if>",
            "<if test='item.lastModified != null'>",
            "#{item.lastModified},",
            "</if>",
            "<if test='item.uploadTime != null'>",
            "#{item.uploadTime},",
            "</if>",
            "<if test='item.hdfsPath != null'>",
            "#{item.hdfsPath},",
            "</if>",
            "<if test='item.fileSizeHdfs != null'>",
            "#{item.fileSizeHdfs},",
            "</if>",
            "<if test='item.s3IsDeleted != null'>",
            "#{item.s3IsDeleted},",
            "</if>",
            "<if test='item.cluster != null'>",
            "#{item.cluster},",
            "</if>",
            "<if test='item.compressionFormat != null'>",
            "#{item.compressionFormat},",
            "</if>",
            "</trim>",
            "</foreach>",
            "</script>"
    })
    int insertBatch(List<BRecordFile> recordFileList);

    @Select({
            "<script>"
                    + "SELECT DISTINCT record_name FROM record_file "
                    + "WHERE record_name IN "
                    + "<foreach item='item' collection='recordNames' open='(' separator=',' close=')'>"
                    + "#{item}"
                    + "</foreach>"
                    + " AND file_size > 0 AND file_type = 1 AND s3_url='' "
                    + "</script>"
    })
    List<String> distinctRecordByEmptyS3After(@Param("recordNames") List<String> recordNames);

    @Select({
            "select distinct record_name from record_file ",
            "where create_time >= #{beginTime} and create_time < #{endTime}  ",
            "and s3_url='' and file_size>0",
    })
    List<String> distinctRecordByEmptyS3AfterCreateTime(@Param("beginTime") String beginTime,
                                                        @Param("endTime") String endTime);

    @Select({
            "select",
            "`id`,record_name,record_date,file_name,file_path,`md5`,s3_url,file_type,file_size,file_size_s3,",
            "`datekey`,create_time,update_time,last_modified,upload_time,hdfs_path,file_size_hdfs,s3_is_deleted",
            "from record_file where record_name=#{recordName} and s3_url='' and file_size>'0'"
    })
    List<RecordFileV2> selectS3Empty(String recordName);

    @Select({
            "<script>",
            "select",
            "`id`,record_name,record_date,file_name,file_path,`md5`,s3_url,file_type,file_size,file_size_s3,",
            "`datekey`,create_time,update_time,last_modified,upload_time,hdfs_path,file_size_hdfs,s3_is_deleted",
            "from record_file",
            "where record_name in ",
            "<foreach item='item' collection='recordNames' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "and s3_url &lt;&gt; ''",
            "and file_name = #{fileName}",
            "</script>"
    })
    List<RecordFileV2> selectReadyS3(
            @Param("recordNames") List<String> recordNames,
            @Param("fileName") String fileName);

    @Select({
            "<script>",
            "select record_name, max(upload_time) as latest_upload_time from record_file",
            "where record_name in ",
            "<foreach item='item' collection='recordNames' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "group by record_name",
            "</script>"
    })
    List<Map<String, Object>> selectLatestUploadTime(@Param("recordNames") List<String> notReadyRecords);

    @Select({
            "select",
            "`id`,record_name,record_date,file_name,file_path,`md5`,s3_url,file_type,file_size,file_size_s3,",
            "`datekey`,create_time,update_time,last_modified,upload_time,hdfs_path,file_size_hdfs,s3_is_deleted,cluster",
            "from record_file",
            "where record_name = #{recordName}",
            "and file_type = #{fileType}",
            "and file_size <> 0"
    })
    List<RecordFileV2> selectFile(@Param("recordName") String recordName, @Param("fileType") int fileType);

    @Select({
            "<script>",
            "select record_name, count(*) as cou from record_file",
            "where record_name in ",
            "<foreach item='item' collection='recordNames' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "and file_size>0 and file_size_s3>0 and file_type=1",
            "group by record_name",
            "</script>"
    })
    List<Map<String, Object>> selectRecord2Count(@Param("recordNames") List<String> recordNames);


    @Select({
            "select",
            "`id`,record_name,record_date,file_name,file_path,`md5`,s3_url,file_type,file_size,file_size_s3,",
            "`datekey`,create_time,update_time,last_modified,upload_time,hdfs_path,file_size_hdfs,s3_is_deleted",
            "from record_file",
            "where record_name = #{recordName}",
            "and file_name = #{fileName}"
    })
    List<RecordFileV2> selectFileByFileName(@Param("recordName") String recordName, @Param("fileName") String fileName);

    @Select({
            "<script>"
                    + "SELECT "
                    + "DISTINCT record_name "
                    + "FROM record_file "
                    + "WHERE record_name IN "
                    + "<foreach item='item' collection='recordNames' open='(' separator=',' close=')'>"
                    + "#{item}"
                    + "</foreach>"
                    + " AND file_size > 0 AND file_type = 1 AND hdfs_path is null"
                    + "</script>"
    })
    List<String> distinctRecordByEmptyHdfsAfter(@Param("recordNames") List<String> recordNames);

    @Select({
            "<script>",
            "select * from record_file",
            "where record_name=#{recordName}",
            "<if test='fileTypes != null and !fileTypes.isEmpty()'>",
            "and file_type in ",
            "<foreach item='item' collection='fileTypes' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</if>",
            "and file_size > 0",
            "</script>"
    })
    List<RecordFileV2> selectCountNotUpload2S3(@Param("recordName") String recordName,
                                               @Param("fileTypes") List<Integer> fileTypes);

    @Update({
            "<script>",
            "<foreach item='item' collection='list' separator=';'>",
            "update record_file set s3_is_deleted = #{item.s3IsDeleted}, s3_delete_time = #{item.s3DeleteTime} ",
            "where record_name = #{item.recordName}",
            "</foreach>",
            "</script>"
    })
    int updateS3LevelByRecordNameBatch(List<RecordV2> coldRecordLevelPOList);

    @Select({
            "select * from record_file where record_name = #{recordName} and file_name = #{fileName} and file_path like concat('%', #{filePath}) "
    })
    RecordFileV2 getRecordFileLike(@Param("recordName") String recordName,
                                   @Param("fileName") String fileName,
                                   @Param("filePath") String filePath);

    @Update({
            "<script>" +
                    "    UPDATE `record_file` SET " +
                    "        <if test='null != s3Url and &apos;&apos; != s3Url'>" +
                    "            `s3_url`=#{s3Url}," +
                    "        </if> " +
                    "        <if test='null != fileSizeS3'>" +
                    "            `file_size_s3`=#{fileSizeS3}," +
                    "        </if> " +
                    "        <if test='null != hdfsPath and &apos;&apos; != hdfsPath'>" +
                    "            `hdfs_path`=#{hdfsPath}," +
                    "        </if> " +
                    "        <if test='null != fileSizeHdfs'>" +
                    "            `file_size_hdfs`=#{fileSizeHdfs}," +
                    "        </if> " +
                    "        <if test='null != datekey'>" +
                    "            `datekey`=#{datekey}," +
                    "        </if> " +
                    "        <if test='null != uploadTime'>" +
                    "            `upload_time`=#{uploadTime}," +
                    "        </if> " +
                    "        `update_time`=CURRENT_TIMESTAMP " +
                    "    WHERE `record_name` = #{recordName} " +
                    "        AND `file_name` = #{fileName} " +
                    "        AND `file_path` like concat('%', #{filePath}) " +
                    "</script>"})
    int updateByUnique(RecordFileV2 recordFileUpdateParam);

    @Select("  select record_name \n" +
            "  from record_file \n" +
            "  where create_time >= concat(date_add(curdate(), interval -1 day),' ','08:00') " +
            "       and file_type = 8 and file_size != 0 \n" +
            "  group by record_name \n"
    )
    List<String> getPbFileEffectiveRecordInfoForNotice();

    @Select(" select record_name \n" +
            " from record_file \n" +
            " where create_time >= concat(date_add(curdate(), interval -1 day),' ','08:00') " +
            "   and file_type = 1 and file_size != 0  \n" +
            " group by record_name \n"
    )
    List<String> getRecFileEffectiveRecordInfoForNotice();

    @Update({
            "<script>",
            "update record_file",
            "set hdfs_path = CONCAT('/user/hadoop-wcdp/onboard-record-data', SUBSTRING(file_path, LOCATE('/', file_path, 2))),",
            "file_size_hdfs = file_size",
            "where file_type = 1 and file_size > 0 and (hdfs_path is null or hdfs_path = '')",
            "and record_name = #{recordName}",
            "</script>"
    })
    int updateHdfsPathByRecordName(@Param("recordName") String recordName);

    @Update({
            "<script>",
            "update record_file",
            "set hdfs_path = CONCAT('/user/hadoop-wcdp/onboard-record-data', SUBSTRING(file_path, LOCATE('/', file_path, 2))),",
            "file_size_hdfs = file_size",
            "where file_size > 0 and (hdfs_path is null or hdfs_path = '')",
            "and (file_name like '%_extrinsic.pb.txt' or file_name like '%_intrinsic.pb.txt')",
            "and record_name = #{recordName}",
            "</script>"
    })
    int updatePbtxtHdfsPathByRecordName(@Param("recordName") String recordName);

    @Update({
            "<script>",
            "update record_file",
            "set s3_url = CONCAT('s3://onboard-record-data', SUBSTRING(file_path, LOCATE('/', file_path, 2)), '/', file_name),",
            "file_size_s3 = file_size",
            "where file_size > 0 and (s3_url is null or s3_url = '')",
            "and record_name = #{recordName}",
            "</script>"
    })
    int updateS3PathByRecordName(@Param("recordName") String recordName);

    // RecordFileV2Mapper.java 迁移 end

    @Select({
            "<script>" +
                    "    SELECT " +
                    "        t.`id`," +
                    "        t.`record_name`," +
                    "        t.`record_date`," +
                    "        t.`file_name`," +
                    "        t.`file_path`," +
                    "        t.`file_type`," +
                    "        t.`md5`," +
                    "        t.`datekey`," +
                    "        t.`s3_url`," +
                    "        t.`file_size`," +
                    "        t.`cluster`," +
                    "        t.`create_time`," +
                    "        t.`update_time`" +
                    "    FROM `record_file` as t " +
                    "    WHERE t.`record_name` = #{recordName} " +
                    "        <if test='fileNameLike != null and &apos;&apos; != fileNameLike'>" +
                    "            AND t.`file_name` like concat('%', #{fileNameLike}, '%') " +
                    "        </if>" +
                    "        <if test='filePath != null and &apos;&apos; != filePath'>" +
                    "            AND t.`file_path` = #{filePath} " +
                    "        </if>" +
                    "        <if test='fileTypeList != null and fileTypeList.size() > 0'>" +
                    "            AND t.`file_type` in " +
                    "               <foreach item='item' collection='fileTypeList' open='(' separator=',' close=')'>" +
                    "                   #{item}" +
                    "               </foreach>" +
                    "        </if>" +
                    "            AND t.`is_deleted` = 0 " +
                    "            ORDER BY t.`file_name` ASC LIMIT #{offset}, #{pageSize} " +
                    "</script>"
    })
    @Results({
            @Result(column = "id", property = "id", id = true),
            @Result(column = "record_name", property = "recordName"),
            @Result(column = "record_date", property = "recordDate"),
            @Result(column = "file_name", property = "fileName"),
            @Result(column = "file_path", property = "filePath"),
            @Result(column = "file_type", property = "fileType"),
            @Result(column = "md5", property = "md5"),
            @Result(column = "datekey", property = "datekey"),
            @Result(column = "s3_url", property = "s3Url"),
            @Result(column = "file_size", property = "fileSize"),
            @Result(column = "cluster", property = "cluster"),
            @Result(column = "create_time", property = "createTime"),
            @Result(column = "update_time", property = "updateTime")
    })
    List<BRecordFile> selectByPage(RecordFilePageQueryRequest pageQuery);

    @Select({
            "<script>" +
                    "    SELECT COUNT(t.`id`) " +
                    "    FROM `record_file` as t " +
                    "    WHERE t.`record_name` = #{recordName} " +
                    "        <if test='fileNameLike != null and &apos;&apos; != fileNameLike'>" +
                    "            AND t.`file_name` like concat('%', #{fileNameLike}, '%') " +
                    "        </if>" +
                    "        <if test='filePath != null and &apos;&apos; != filePath'>" +
                    "            AND t.`file_path` = #{filePath} " +
                    "        </if>" +
                    "        <if test='fileTypeList != null and fileTypeList.size() > 0'>" +
                    "            AND t.`file_type` in " +
                    "               <foreach item='item' collection='fileTypeList' open='(' separator=',' close=')'>" +
                    "                   #{item}" +
                    "               </foreach>" +
                    "        </if>" +
                    "            AND t.`is_deleted` = 0 " +
                    "</script>"
    })
    Integer count(RecordFilePageQueryRequest pageQuery);

    // stream.server迁移 begin

    @Select({
            "select id, s3_url, file_name from record_file",
            "where record_name=#{recordName}",
            "and file_type='1' and file_size_s3>'0'",
            "and file_name not like 'ScheduleNode%'"
    })
    List<BRecordFile> selectAllValidS3Url(String recordName);

    @Select({
            "select 1 from record_file " +
                    "where record_name = #{recordName} and file_type = 1 and file_size > 0 " +
                    "and (s3_url = '' or s3_url is null) limit 1"
    })
    List<Integer> checkRecordReady(String recordName);

    @Select({
            "select s3_url, file_name, file_size from record_file " +
                    "where record_name = #{recordName} and file_type = 1 and file_size > 0 " +
                    "and s3_url <> '' and s3_url is not null"
    })
    List<BRecordFile> queryByRecordName(String recordName);

    // stream.server迁移 end

    @Select({
            "select count(*) from record_file",
            "where record_name = #{recordName} and file_type = 1 and file_size > 0",
            "and (hdfs_path is null or hdfs_path = '')"
    })
    Integer countHDFSNotUploaded(@Param("recordName") String recordName);

    @Select({
            "select count(*) from record_file",
            "where record_name = #{recordName} and file_type = 1 and file_size > 0"
    })
    Integer countHDFSTotal(@Param("recordName") String recordName);

    @Select({
            "select count(*) from record_file",
            "where record_name = #{recordName} and file_size > 0 and (s3_url is null or s3_url = '')"
    })
    Integer countS3NotUploaded(@Param("recordName") String recordName);

    @Select({
            "select count(*) from record_file",
            "where record_name = #{recordName} and file_size > 0"
    })
    Integer countS3Total(@Param("recordName") String recordName);

    @Update({
            "update record_file set cluster = 0 where record_name = #{recordName} and cluster = 1"
    })
    Integer updateRecordCluster(@Param("recordName") String recordName);

    @Update({
            "update record_file set cluster = 1 where record_name = #{recordName} and file_name = #{fileName} and file_path = #{filePath}"
    })
    Integer updateRecordFileCluster(@Param("recordName") String recordName, @Param("fileName") String fileName, @Param("filePath") String filePath);

    @Update({
            "update record_file set cluster = 1 where s3_url = #{s3Url}"
    })
    Integer updateRecordFileClusterByS3Url(@Param("s3Url") String s3Url);

    @Update({
            "update record_file set cluster = 1 where file_type = 16 and cluster = 0"
    })
    Integer updateHeaderFileCluster();

    @Select({
            "select count(*) from record_file where record_name = #{recordName} and (s3_is_deleted = 0 or s3_is_marked_for_deletion = 0)"
    })
    Integer selectCountDeletedMark(@Param("recordName") String recordName);

    @Update({
            "update record_file set s3_is_deleted = 1, s3_delete_time = #{s3DeleteTime}, ",
            "s3_is_marked_for_deletion = 1, s3_is_marked_for_deletion_time = #{s3DeleteTime}",
            "where record_name = #{recordName}"
    })
    Integer updateRecordFileDeletedMark(@Param("recordName") String recordName, @Param("s3DeleteTime") Date s3DeleteTime);

    @Update({
            "update record_file set s3_is_deleted = 1, s3_delete_time = #{s3DeleteTime}, ",
            "s3_is_marked_for_deletion = 1, s3_is_marked_for_deletion_time = #{s3DeleteTime}",
            "where s3_url = #{s3Url}"
    })
    Integer updateRecordFileDeletedMarkByS3Url(@Param("s3Url") String s3Url, @Param("s3DeleteTime") Date s3DeleteTime);

    // S3 迁移 元数据修复 start

    @Select({
            "select * from record_file",
            "where record_name = #{recordName}",
            "and s3_is_deleted = 0 and s3_is_marked_for_deletion = 0 and is_deleted = 0",
            "and s3_url is not null and s3_url != '' and file_size > 0 and cluster = 0",
            "and file_type != 16"
    })
    List<BRecordFile> selectMigrateFileListByRecordName(String recordName);

    @Select({
            "select record_name from record_file",
            "where create_time > #{startDate} and create_time < #{endDate}",
            "and s3_is_deleted = 0 and s3_is_marked_for_deletion = 0 and is_deleted = 0",
            "and s3_url is not null and s3_url != '' and file_size > 0 and cluster = 0",
            "and file_type != 16",
            "group by record_name"
    })
    List<String> selectMigrateRecordNameByCreateTime(String startDate, String endDate);

    @Select({
            "select record_name from record_file",
            "where record_name > #{startDateKey} and record_name < #{endDateKey}",
            "and s3_is_deleted = 0 and s3_is_marked_for_deletion = 0 and is_deleted = 0",
            "and s3_url is not null and s3_url != '' and file_size > 0 and cluster = 0",
            "and file_type != 16",
            "group by record_name"
    })
    List<String> selectMigrateRecordNameByRecordDate(String startDateKey, String endDateKey);

    // S3 迁移 元数据修复 end

    @Select({
            "select count(*) from record_file where record_name = #{recordName}"
    })
    Integer countTotal(@Param("recordName") String recordName);
}