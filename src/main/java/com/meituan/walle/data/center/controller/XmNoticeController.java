package com.meituan.walle.data.center.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.walle.data.center.service.XmNoticeService;
import com.sankuai.walle.wcdp.data.center.iface.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping("/xmNotice")
public class XmNoticeController {

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Resource
    private XmNoticeService xmNoticeService;

    @PostMapping("/sendMessageToGroup")
    public Result sendMessageToGroup(@RequestBody String jsonStr) {
        Result result = new Result();
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            Long gid = jsonNode.get("gid").asLong();
            String bodyText = jsonNode.get("bodyText").asText();
            result.message = xmNoticeService.sendMessageToGroup(gid, bodyText);
            result.code = 10000;
        } catch (Exception e) {
            log.error("XmNoticeController sendMessageToGroup exception: ", e);
            result.code = 10020;
            result.message = e.getMessage();
        }
        return result;
    }

    @PostMapping("/sendMessageToClient")
    public Result sendMessageToClient(@RequestBody String jsonStr) {
        Result result = new Result();
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            String content = jsonNode.get("content").asText();
            JsonNode misArray = jsonNode.get("misSet");
            Set<String> misSet = new HashSet<>(misArray.size());
            for (JsonNode node : misArray) {
                misSet.add(node.asText());
            }
            result.message = xmNoticeService.sendMessageToClient(content, misSet);
            result.code = 10000;
        } catch (Exception e) {
            log.error("XmNoticeController sendMessageToClient exception: ", e);
            result.code = 10020;
            result.message = e.getMessage();
        }
        return result;
    }

}