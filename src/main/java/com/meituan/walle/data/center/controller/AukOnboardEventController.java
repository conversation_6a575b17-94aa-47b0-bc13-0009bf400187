package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.entity.vo.AukDeviceVO;
import com.meituan.walle.data.center.service.VehicleInfoService;
import com.meituan.walle.data.center.util.CallRejectConstant;
import com.meituan.walle.data.center.util.CommonUtil;
import com.meituan.walle.mad.logger.client.annotation.MadLog;
import com.sankuai.auk.open.api.contracts.service.DeviceSecret;
import com.sankuai.banma.auk.server.sdk.AukClient;
import com.sankuai.banma.auk.server.sdk.request.DeviceCreateRequest;
import com.sankuai.banma.auk.server.sdk.request.DeviceSecretRequest;
import com.sankuai.banma.auk.server.sdk.response.DeviceCreateResponse;
import com.sankuai.banma.auk.server.sdk.response.DeviceSecretResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/10/28
 */
@RestController
@Slf4j
public class AukOnboardEventController {

    private static final String AUTOCAR_AUK_PRODUCT_KEY = "UGV";

    @Resource
    private AukClient aukClient;

    @Resource
    private VehicleInfoService vehicleInfoService;

    @MadLog
    @GetMapping(value = "/vehicle/secret")
    public CommonResponse queryDeviceSecretKey(String vin) {
        if (!vin.matches("[a-zA-Z\\d]*")) {
            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.GET_AUK_SECRET_REJECT,
                    "vin is invalid", vin);
            return CommonResponse.fail();
        }
        if (StringUtils.isBlank(vehicleInfoService.getVehicleName(vin))) {
            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.GET_AUK_SECRET_REJECT,
                    "vin is not in vehicle_info table", vin);
            
            return CommonResponse.fail();
        }

        DeviceSecretRequest request = new DeviceSecretRequest();
        request.setProductKey(AUTOCAR_AUK_PRODUCT_KEY);
        request.setDeviceKey(vin);
        AukDeviceVO aukDevice = queryDeviceSecret(vin);
        if (aukDevice != null) {
            return CommonResponse.success(aukDevice);
        }

        // 如果没有查找到，则说明还未注册，那么先注册
        DeviceCreateRequest createRequest = new DeviceCreateRequest();
        createRequest.setDeviceKey(vin);
        createRequest.setProductKey(AUTOCAR_AUK_PRODUCT_KEY);
        createRequest.setDeviceName(vin);
        DeviceCreateResponse device = aukClient.createDevice(createRequest);
        if (device.getData() == null) {
            return CommonResponse.fail();
        }
        aukDevice = queryDeviceSecret(vin);
        if (aukDevice == null) {
            return CommonResponse.fail();
        }

        return CommonResponse.success(aukDevice);
    }

    public AukDeviceVO queryDeviceSecret(String deviceNo) {
        DeviceSecretRequest request = new DeviceSecretRequest();
        request.setProductKey(AUTOCAR_AUK_PRODUCT_KEY);
        request.setDeviceKey(deviceNo);
        AukDeviceVO aukDevice = new AukDeviceVO();
        try {
            DeviceSecretResponse deviceSecretResponse = aukClient.queryDeviceSecret(request);
            DeviceSecret data = deviceSecretResponse.getData();
            if (data != null) {
                BeanUtils.copyProperties(data, aukDevice);
                return aukDevice;
            }
        } catch (Exception e) {
            log.warn("query device secret error: {}", deviceNo, e);
        }
        return null;
    }


}
