package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Vehicle upload intervention configuration entity
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VehicleUploadInterventionConfigPO {

    /**
     * Primary key
     */
    private Long id;

    /**
     * Configuration name
     */
    private String name;

    /**
     * Vehicle name list, comma separated
     */
    private String vehicleNameList;

    /**
     * Vehicle type list, comma separated
     */
    private String vehicleTypeList;

    /**
     * Time span list, comma separated, format: "startHour-endHour"
     */
    private String timeSpanList;

    /**
     * Module name
     */
    private String module;

    /**
     * Priority
     */
    private Integer priority;

    /**
     * Concurrent number
     */
    private Integer concurrentNum;

    /**
     * Create time
     */
    private Date createTime;

    /**
     * Update time
     */
    private Date updateTime;
} 