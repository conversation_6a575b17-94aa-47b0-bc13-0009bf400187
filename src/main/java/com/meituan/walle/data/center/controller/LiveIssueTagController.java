package com.meituan.walle.data.center.controller;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Sets;
import com.meituan.walle.data.center.component.PikeConnectListener;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.config.mcc.VehicleTagReportConfig;
import com.meituan.walle.data.center.constant.*;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.dto.BizEventTagDTO;
import com.meituan.walle.data.center.entity.dto.TagReportPictureDTO;
import com.meituan.walle.data.center.entity.dto.VehicleAuthDTO;
import com.meituan.walle.data.center.entity.pojo.BizEventDeriveIntervention;
import com.meituan.walle.data.center.entity.response.HttpResponse;
import com.meituan.walle.data.center.entity.response.HmiTagsResponse;
import com.meituan.walle.data.center.entity.response.PageResponse;
import com.meituan.walle.data.center.entity.pojo.LiveIssueTag;
import com.meituan.walle.data.center.entity.vo.*;
import com.meituan.walle.data.center.handle.IRedisHandler;
import com.meituan.walle.data.center.service.CasesService;
import com.meituan.walle.data.center.service.LiveIssueTagService;
import com.meituan.walle.data.center.service.VehicleInfoService;
import com.meituan.walle.data.center.service.*;
import com.meituan.walle.data.center.util.*;
import com.meituan.walle.mad.logger.client.annotation.MadLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;

import static com.meituan.walle.data.center.constant.TagConstant.OPERATION_TAG;

@Slf4j
@RestController
@RequestMapping("/live_issue_tag")
public class LiveIssueTagController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LiveIssueTagController.class);

    @Resource
    private LiveIssueTagService liveIssueTagService;

    @Autowired
    private VehicleInfoService vehicleInfoService;

    @Autowired
    private CasesService casesService;

    @Resource
    private IRedisHandler redisHandler;

    @Resource
    private VehicleEventTagService vehicleEventTagService;

    @Autowired
    private VehicleEventTagReportService vehicleEventTagReportService;

    @Resource
    private VresvRecordHistoryService vresvRecordHistoryService;

    @Resource
    private BizEventDeriveInterventionService interventionService;

    @Resource
    private RecordService recordService;

    @Resource
    private PikeConnectListener pikeConnectListener;

    @Resource
    private VehicleUploadRequestService vehicleUploadRequestService;

    @Resource
    private InterventionLabelService interventionLabelService;

    @PostMapping("/sendLiveIssueTag")
    public Object sendLiveIssueTag(@RequestBody LiveIssueTagVO liveIssueTagVO) {
        String traceId = Trace.generateId();
        Map<String, Object> result = new HashMap<>();
        Transaction t = Cat.newTransaction(CatConstant.Tag, "sendLiveIssueTag");
        long start = System.currentTimeMillis();
        try {
            LOGGER.info("LiveIssueTagController sendLiveIssueTag : {}", GsonUtil.toJson(liveIssueTagVO));
            List<LiveIssueTag> liveIssueTagList = new ArrayList();
            LiveIssueTag liveIssueTag = new LiveIssueTag();
            // TODO: 通过API方式只上报车辆名，暂且将车辆名当做recordName
            liveIssueTag.setVin(liveIssueTagVO.getVehicleVin());
            liveIssueTag.setCategoryL1(liveIssueTagVO.getCategoryL1());
            liveIssueTag.setCategoryL2(liveIssueTagVO.getCategoryL2());
            liveIssueTag.setMeasurementTimestamp(liveIssueTagVO.getMeasurementTimestamp());
            liveIssueTag.setAlertTimestamp(liveIssueTagVO.getAlertTimestamp());
            liveIssueTag.setT(new Date(liveIssueTagVO.getReportTimestamp() / 1000000));
            liveIssueTag.setTagName(liveIssueTagVO.getTagName());
            // TODO: 通过API方式上报clientType，如果上报是空的，则认为是fusion hmi上报的。
            // 通过API方式上报clientType: 1-onboard；2-fusion hmi; 3-dream view

            int clientType = liveIssueTagVO.getClientType() != null ? liveIssueTagVO.getClientType() : -1;

            switch (clientType) {
                case 2:
                    liveIssueTag.setRecordName(liveIssueTagVO.getVehicleName());
                    liveIssueTag.setVehicleName(liveIssueTagVO.getVehicleName());
                    liveIssueTag.setClientType(2);
                    break;
                case 3:
                    String vehicleName = liveIssueTagVO.getVehicleName();
                    if (StringUtils.isBlank(vehicleName)) {
                        vehicleName = StringUtils.isNotBlank(liveIssueTagVO.getVehicleVin()) ?
                                vehicleInfoService.getVehicleName(liveIssueTagVO.getVehicleVin()) :
                                "UNKNOWN";
                    }

                    liveIssueTag.setRecordName(vehicleName);
                    liveIssueTag.setVehicleName(vehicleName);
                    liveIssueTag.setClientType(3);
                    break;
                default:
                    liveIssueTag.setRecordName(liveIssueTagVO.getVehicleName());
                    liveIssueTag.setVehicleName(liveIssueTagVO.getVehicleName());
                    liveIssueTag.setClientType(liveIssueTagVO.getClientType());
            }


            liveIssueTag.setMisid(liveIssueTagVO.getMisid());
            liveIssueTag.setTriggerType(liveIssueTagVO.getTriggerType());
            liveIssueTagList.add(liveIssueTag);
            result = liveIssueTagService.insertLiveIssueTagBatch(liveIssueTagList);

            MetricHelper.build().name(CatConstant.SendTagCount)
                    .tag("ret", "success")
                    .count(1);
            MetricHelper.build().name(CatConstant.SendTagDuration)
                    .tag("ret", "success")
                    .duration(System.currentTimeMillis() - start);

            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
        } catch (Exception exp) {
            result.put("success", false);
            result.put("msg", exp.getMessage());
            LOGGER.error("traceId: {}, sendLiveIssueTag encounter Exception : {}",
                    traceId, ExceptionHelper.getStackTraceString(exp));
            Cat.logError(exp);
            t.setStatus(exp);
        }
        return Response.succ(result);
    }

    @PostMapping("/restore_issue_tag")
    public Response restoreIssueTag(@RequestParam String beginDate, @RequestParam String endDate) {
        try {
            casesService.restoreIssueTag(beginDate, endDate);
        } catch (Exception e) {
            return Response.succ(e);
        }
        return Response.succ();
    }

    @GetMapping("/tags/query")
    public Response tagsQuery() {
        Transaction t = Cat.newTransaction(CatConstant.GET_VEHICLE_TAG_LIST, "getVehicleTagList");
        long start = System.currentTimeMillis();
        try {
            String[] tagsQuery = VehicleTagReportConfig.TAGS_QUERY.split(CharConstant.CHAR_DD);
            List<String> resultQuery = new ArrayList<>();
            for (String s : tagsQuery) {
                resultQuery.add(s.trim());
            }
            log.info("tagsQuery result is {}", resultQuery);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(resultQuery);
        } catch (Exception e) {
            log.error("Get tags failed，post request parameters", e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), "Get tag failed");
        } finally {
            t.complete();
        }
    }

    @GetMapping("/tags")
    public Response tags(VehicleEventTagVO eventTagVO) {
        Transaction t = Cat.newTransaction(CatConstant.GET_VEHICLE_TAG_LIST, "getVehicleTagList");
        long start = System.currentTimeMillis();
        if (StringUtils.isBlank(eventTagVO.getVin())) {
            log.info("Tags query post request lack of vin information");
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail(ResponseCodeEnum.LACK_PARAMETER.getCode(),
                    "post request lack of vin information");
        }
        Map<String, Object> result;
        PageResponse queryDTO = new PageResponse();
        log.info("Tags query, key parameter is {}", eventTagVO);
        try {
            result = vehicleEventTagService.buildTagsResponse(eventTagVO);
            if (result == null) {
                return Response.fail(ResponseCodeEnum.REQUEST_FAILED.getCode(), "No related data is found");
            }
            queryDTO.setPage(eventTagVO.getPage());
            queryDTO.setSize(eventTagVO.getSize());
            queryDTO.setTotal((Integer) result.get("total"));
            queryDTO.setResult(result.get("result"));
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(queryDTO);
        } catch (Exception e) {
            log.error("Get tag failed，post request parameters：{}", eventTagVO, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), "Get tag failed");
        } finally {
            t.complete();
        }
    }

    @MadLog
    @PostMapping("/tag/report")
    public Response tagReport(@RequestBody VehicleEventTagReportVO eventTagReportVO) {
        log.info("[/live_issue_tag/tag/report] request: {}", JacksonUtil.serialize(eventTagReportVO));
        Transaction t = Cat.newTransaction(CatConstant.VEHICLE_TAG_REPORT, "vehicleTagReport");
        long start = System.currentTimeMillis();
        String eventId = eventTagReportVO.getEventId();
        String misid = eventTagReportVO.getMisid();
        if (StringUtils.isBlank(eventId) || StringUtils.isBlank(misid)) {
            log.info("Tag report post request lack of event_id or misid information");
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.REPORT_TAG_REJECT,
                    "eventId or misId is empty", eventTagReportVO);
            return Response.fail(ResponseCodeEnum.LACK_PARAMETER.getCode(),
                    "post request lack of event_id or misid information");
        }
        String[] eventIdList = eventId.split("_");
        String vin = vehicleInfoService.getVin(eventIdList[eventIdList.length - 1]);
        eventTagReportVO.setVin(vin);
        log.info("Tag report post key parameter, {}", eventTagReportVO.toGetTagReportParamString());

        try {
            int isInsert = vehicleEventTagReportService.insertTabel(eventTagReportVO);
            if (isInsert == 1) {
                return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), "insert table failed");
            } else if (isInsert == 210) {
                return Response.fail(ResponseCodeEnum.NEED_UPDATE.getCode(), "Do you want to update table");
            } else if (isInsert == 3) {
                return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), "this intervention is not insert to table");
            }
            log.info("isInsert = {}", isInsert);
            if (isInsert != 2 && eventTagReportVO.getSendToOncall() != null && eventTagReportVO.getSendToOncall()) {
                vehicleEventTagReportService.sendMessageToDx(eventTagReportVO);
            }
            delEventCache(eventTagReportVO.getVin(), eventTagReportVO.getEventId());
            if (OPERATION_TAG.equals(eventTagReportVO.getTagName())) {
                vehicleUploadRequestService.updateStatusOrPriority(eventId, vin);
            }
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            log.error("Tag report failed, post request parameters: {}", eventTagReportVO, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), "Tag report failed");
        } finally {
            t.complete();
        }
    }


    @MadLog
    @PostMapping("/tag/report/update")
    public Response tagRepostUpdate(@RequestBody VehicleEventTagReportVO eventTagReportVO) {
        log.info("[/live_issue_tag/tag/report/update] request: {}", JacksonUtil.serialize(eventTagReportVO));
        Transaction t = Cat.newTransaction(CatConstant.VEHICLE_TAG_REPORT_UPDATE, "vehicleTagReportUpdate");
        long start = System.currentTimeMillis();
        String eventId = eventTagReportVO.getEventId();
        String misid = eventTagReportVO.getMisid();
        if (StringUtils.isBlank(eventId) || StringUtils.isBlank(misid)) {
            log.info("Tag report update post request lack of event_id or misid information");
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.UPDATE_TAG_REJECT,
                    "eventId or misId is empty", eventTagReportVO);
            return Response.fail(ResponseCodeEnum.LACK_PARAMETER.getCode(),
                    "post request lack of event_id or misid information");
        }
        String[] eventIdList = eventId.split("_");
        String vin = vehicleInfoService.getVin(eventIdList[eventIdList.length - 1]);
        eventTagReportVO.setVin(vin);
        log.info("Tag report post key parameter, {}", eventTagReportVO.toGetTagReportParamString());

        try {
            Boolean isUpdate = vehicleEventTagReportService.updateTable(eventTagReportVO);
            if (isUpdate == Boolean.FALSE) {
                return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), "update table failed");
            }
            if (eventTagReportVO.getSendToOncall() != null && eventTagReportVO.getSendToOncall()) {
                vehicleEventTagReportService.sendMessageToDx(eventTagReportVO);
            }
            if (OPERATION_TAG.equals(eventTagReportVO.getTagName())) {
                vehicleUploadRequestService.updateStatusOrPriority(eventId, vin);
            } else {
                vehicleUploadRequestService.updateStatusOrPriorityIfCase(eventTagReportVO.getEventId(), vin);
            }
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            log.error("Tag report failed, post request parameters: {}", eventTagReportVO, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), "update table failed");
        } finally {
            t.complete();
        }
    }

    public void delEventCache(String vin, String eventId) {
        String cacheEventId = redisHandler.get(RedisConstant.WAITING_EVENT, vin);
        if (StringUtils.isNotBlank(cacheEventId) && StringUtils.isNotBlank(eventId)
                && cacheEventId.contains(eventId)) {
            redisHandler.delete(RedisConstant.WAITING_EVENT, vin);
        }
    }

    @MadLog
    @PostMapping("/tag/report/picture")
    public Response tagReportPicture(VehicleTagReportPictureVO tagReportPictureVO,
                                     MultipartFile[] files) {
        Transaction t = Cat.newTransaction(CatConstant.TAG_REPORT_PICTURE, "toUploadPicture");
        long start = System.currentTimeMillis();
        if (tagReportPictureVO.getVin() == null ||
                tagReportPictureVO.getEventId() == null) {
            log.info("Picture report post request lack of vin or eventId information");
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.UPLOAD_TAG_PICTURE_REJECT,
                    "vin or eventId is empty", tagReportPictureVO);
            return Response.fail(ResponseCodeEnum.LACK_PARAMETER.getCode(),
                    "post request lack of vin or eventId information");
        }
        log.info("Picture report post key parameter, event_id is {}", tagReportPictureVO.getEventId());
        try {
            TagReportPictureDTO result = vehicleEventTagReportService.getTagReportPictureResult(
                    tagReportPictureVO, files);
            if (result == null) {
                log.info("Upload picture to s3 picture failed");
                return Response.fail(ResponseCodeEnum.NEED_UPDATE.getCode(), "Upload picture to s3 failed");
            }
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            log.error("Upload picture to s3 failed, eventId is {}", tagReportPictureVO.getEventId(), e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), "Upload picture to s3 failed");
        } finally {
            t.complete();
        }
    }

    @MadLog
    @PostMapping("/tag/report/picture/delete")
    public Response tagReportPictureDelete(@RequestBody VehiclePictureDeleteVO pictureDeleteVO) {
        Transaction t = Cat.newTransaction(CatConstant.DELETE_TAG_PICTURE, "delete tag picture");
        long start = System.currentTimeMillis();
        String eventId = pictureDeleteVO.getEventId();
        String pictureUrl = pictureDeleteVO.getPictureUrl();
        if (StringUtils.isBlank(eventId) || StringUtils.isBlank(pictureUrl)) {
            log.info("Picture delete post request lack of eventId or pictureUrl information");
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.DELETE_TAG_PICTURE_REJECT,
                    "vin or eventId is empty", pictureDeleteVO);
            return Response.fail(ResponseCodeEnum.LACK_PARAMETER.getCode(),
                    "post request lack of eventId or pictureUrl information");
        }
        log.info("Picture delete post key parameter, event_id is {}, picture_url is {}", eventId, pictureUrl);
        try {
            vehicleEventTagReportService.deletePictureWithEventId(eventId, pictureUrl);
            return Response.succ();
        } catch (Exception e) {
            log.error("Delete picture from table failed, eventId is {}", eventId, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), "Delete picture from table failed");
        } finally {
            t.complete();
        }
    }

    @GetMapping("/tags/event_detail")
    public Response tagsEventDetail(String eventId) {
        Transaction t = Cat.newTransaction(CatConstant.GET_VEHICLE_EVENT_DETAIL, "toGetVehicleEventDetail");
        long start = System.currentTimeMillis();
        if (StringUtils.isEmpty(eventId)) {
            log.info("Event detail get request lack of eventId information");
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail(ResponseCodeEnum.LACK_PARAMETER.getCode(),
                    "post request lack of eventId information");
        }
        log.info("Event detail get request key parameter, event_id is {}", eventId);
        BizEventTagDTO tagResponseDTO;
        try {
            tagResponseDTO = vehicleEventTagService.buildTagsEventDetail(eventId);
            if (tagResponseDTO == null) {
                tagResponseDTO = new BizEventTagDTO();
            }
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(tagResponseDTO);
        } catch (Exception e) {
            log.error("Event detail query failed, eventId is {}", eventId, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), "Event detail query failed");
        } finally {
            t.complete();
        }
    }

    @GetMapping("/hmi/tags")
    public Response hmiTags(String vin) {
        Transaction t = Cat.newTransaction(CatConstant.GET_HMI_TAGS, "toGetVehicleEventDetail");
        long start = System.currentTimeMillis();
        if (StringUtils.isBlank(vin)) {
            log.info("hmi tags get request lack of vin information");
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail(ResponseCodeEnum.LACK_PARAMETER.getCode(),
                    "request lack of vin information");
        }
        log.info("Hmi tags get request vin is {}", vin);
        HmiTagsResponse hmiTagsResponse = new HmiTagsResponse();
        try {
            log.info(SysParamsConfig.tag_report_vin_list);
            if (Sets.newHashSet(SysParamsConfig.tag_report_vin_list.split(",")).contains(vin)
                    || StringUtils.isBlank(SysParamsConfig.tag_report_vin_list)) {
                hmiTagsResponse.setReportTag(Boolean.TRUE);
            } else {
                hmiTagsResponse.setReportTag(Boolean.FALSE);
            }
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(hmiTagsResponse);
        } catch (Exception e) {
            log.error("Get response failed, vin is {}", vin, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(ResponseCodeEnum.ERROR_CODE.getCode(), "Get response failed");
        } finally {
            t.complete();
        }
    }

    @GetMapping(value = "/vehicle/auth")
    public HttpResponse vehicleAuth(String vin) {
        HttpResponse httpResponse = new HttpResponse();
        boolean vinOnline = SysParamsConfig.tag_report_vin_list.contains(vin);
        String vehicleName = vehicleInfoService.getVehicleName(vin);
        String today = DatetimeUtil.format(new Date(), "yyyyMMdd");
        boolean existRecord = recordService.existCurrentDayRecord(today, vehicleName);

        boolean vehicleAuth = vinOnline && existRecord;
        VehicleAuthDTO vehicleAuthDTO = new VehicleAuthDTO();
        vehicleAuthDTO.setVehicleAuth(vehicleAuth);
        return httpResponse.result(vehicleAuthDTO);
    }

    @PostMapping(value = "/fusion/tag/report")
    public HttpResponse fusionTagReport(@RequestBody LiveIssueTagVO liveIssueTagVO) {
        Transaction t = Cat.newTransaction(CatConstant.Tag, "fusionTagReport");
        long start = System.currentTimeMillis();

        String vin = liveIssueTagVO.getVehicleVin().toUpperCase();
        String misid = liveIssueTagVO.getMisid();
        log.info("remote button tag report, misid is {}, vin is {}", misid, vin);

        try {
            LOGGER.info("LiveIssueTagController fusionTagReport : {}", JSON.toJSON(liveIssueTagVO));
            LiveIssueTag liveIssueTag = new LiveIssueTag();
            liveIssueTag.setVin(vin);
            liveIssueTag.setVehicleName(liveIssueTagVO.getVehicleName());
            Long measurementTimestamp = liveIssueTagVO.getMeasurementTimestamp();
            liveIssueTag.setMeasurementTimestamp(measurementTimestamp);
            liveIssueTag.setAlertTimestamp(liveIssueTagVO.getAlertTimestamp());
            liveIssueTag.setT(new Date(liveIssueTagVO.getReportTimestamp() / CommonConstant.NANO_TO_MILLIS));
            liveIssueTag.setTagName(liveIssueTagVO.getTagName());
            liveIssueTag.setMisid(misid);
            liveIssueTag.setTriggerType(liveIssueTagVO.getTriggerType());
            liveIssueTag.setClientType(LiveIssueTagClientTypeEnum.REMOTE_BUTTON.getRet());
            liveIssueTag.setUuid(UUID.randomUUID().toString().replace("-", ""));
            liveIssueTag.setDescription(LiveIssueTagClientTypeEnum.REMOTE_BUTTON.getMsg());

            long millis = measurementTimestamp / CommonConstant.NANO_TO_MILLIS;
            String startTime = DatetimeUtil.format(new Date(millis - SysParamsConfig.button_match_event_millis),
                    DateTimeFormatterConstant.yyyyMMddHHmmssSSS_Str);
            String endTime = DatetimeUtil.format(new Date(millis + 2000),
                    DateTimeFormatterConstant.yyyyMMddHHmmssSSS_Str);
            List<BizEventDeriveIntervention> interventions
                    = interventionService.queryNearestIntervention(vin, startTime, endTime);
            String eventId = null;
            if (!interventions.isEmpty()) {
                // 如果匹配到接管，则填充信息
                BizEventDeriveIntervention intervention = interventions.get(0);
                eventId = intervention.getEventId();
                liveIssueTag.setRecordName(intervention.getRecordName());
                liveIssueTag.setEventId(eventId);
                liveIssueTag.setMeasurementTimestamp(intervention.getInterventionTimestamp());

                //通过方向盘按钮上报的标签，也需要删除redis中的缓存
                String eventInfo = redisHandler.get(RedisConstant.WAITING_EVENT, vin);
                if (StringUtils.isNotBlank(eventInfo) && eventInfo.contains(eventId)) {
                    redisHandler.delete(RedisConstant.WAITING_EVENT, vin);
                }
                VehicleEventTagReportVO tagReportVO = new VehicleEventTagReportVO();
                tagReportVO.setEventId(intervention.getEventId());
                tagReportVO.setTagName(liveIssueTagVO.getTagName());
                interventionLabelService.updateInterventionLabelWithEventId(tagReportVO, intervention);
            }
            liveIssueTagService.insertTag(liveIssueTag);

            if (eventId != null) {
                pikeConnectListener.sendCloseWindow(vin, eventId);
                if (OPERATION_TAG.equals(liveIssueTagVO.getTagName())) {
                    vehicleUploadRequestService.updateStatusOrPriority(eventId, vin);
                }
            }
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return new HttpResponse();
        } catch (Exception exp) {
            log.error("方向盘上报运营接管标签失败", exp);
            Cat.logError(exp);
            t.setStatus(exp);
        }
        return new HttpResponse().code(HttpStatus.SC_INTERNAL_SERVER_ERROR);
    }

}
