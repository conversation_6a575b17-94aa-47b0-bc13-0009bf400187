package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/10/19
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "big_electric_fence_offline")
public class BigElectricFenceOffline {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * electric fence id
     */
    @Column(name = "electric_fence_id")
    private String electricFenceId;

    /**
     * electric fence开始的时间戳
     */
    @Column(name = "begin_timestamp")
    private Long beginTimestamp;

    /**
     * electric fence结束的时间戳
     */
    @Column(name = "end_timestamp")
    private Long endTimestamp;

    /**
     * 车架号
     */
    @Column(name = "vin")
    private String vin;

    /**
     * electric fence object id
     */
    @Column(name = "electric_fence_object_id")
    private String electricFenceObjectId;

    /**
     * electric fence type
     */
    @Column(name = "electric_fence_type")
    private Integer electricFenceType;

    /**
     * record name
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 是否逻辑删除，1：是，0：否
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private String operationAreaId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getElectricFenceId() {
        return electricFenceId;
    }

    public void setElectricFenceId(String electricFenceId) {
        this.electricFenceId = electricFenceId;
    }

    public Long getBeginTimestamp() {
        return beginTimestamp;
    }

    public void setBeginTimestamp(Long beginTimestamp) {
        this.beginTimestamp = beginTimestamp;
    }

    public Long getEndTimestamp() {
        return endTimestamp;
    }

    public void setEndTimestamp(Long endTimestamp) {
        this.endTimestamp = endTimestamp;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getElectricFenceObjectId() {
        return electricFenceObjectId;
    }

    public void setElectricFenceObjectId(String electricFenceObjectId) {
        this.electricFenceObjectId = electricFenceObjectId;
    }

    public Integer getElectricFenceType() {
        return electricFenceType;
    }

    public void setElectricFenceType(Integer electricFenceType) {
        this.electricFenceType = electricFenceType;
    }

    public String getRecordName() {
        return recordName;
    }

    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperationAreaId() {
        return operationAreaId;
    }

    public void setOperationAreaId(String operationAreaId) {
        this.operationAreaId = operationAreaId;
    }
}

