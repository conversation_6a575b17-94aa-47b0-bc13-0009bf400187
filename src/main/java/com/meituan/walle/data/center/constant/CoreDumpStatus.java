package com.meituan.walle.data.center.constant;

import lombok.Getter;

@Getter
public enum CoreDumpStatus {
    /**
     * 初始化
     */
    INITIAL(Byte.valueOf("0"), "初始化"),
    /**
     * 已创建case
     */
    CREATED_CASE(Byte.valueOf("3"), "已创建case"),
    /**
     * 补充stack信息
     */
    UPDATED_STACK(Byte.valueOf("1"), "补充stack信息"),
    /**
     * 分拣coredump
     */
    DISPATCHED(Byte.valueOf("2"), "分拣coredump"),
    ;

    private Byte code;
    private String msg;

    CoreDumpStatus(Byte code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
