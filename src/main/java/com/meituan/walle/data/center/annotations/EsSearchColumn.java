package com.meituan.walle.data.center.annotations;

import com.meituan.walle.data.center.constant.EsSearchTypeEnum;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/1/18 16:30
 * @description
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface EsSearchColumn {
    String columnName();
    EsSearchTypeEnum searchType() default EsSearchTypeEnum.TERM_QUERY;
}
