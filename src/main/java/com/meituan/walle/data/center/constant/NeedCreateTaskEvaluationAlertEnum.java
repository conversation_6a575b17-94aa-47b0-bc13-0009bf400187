package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2022/11/15
 */

@Getter
@AllArgsConstructor
public enum NeedCreateTaskEvaluationAlertEnum {

    ROUTIGN_ERROR_STATUS(1, "routing_error_status"),

    REFERENCE_LINES_NOT_MATCH_ROUTING(2, "reference_lines_not_match_routing"),
    ;

    private int code;

    private String msg;

    public static boolean isNeedCreateUploadTask(String evaluationAlertType) {
        if (StringUtils.isBlank(evaluationAlertType)) {
            return false;
        }
        for (NeedCreateTaskEvaluationAlertEnum evaluationAlertEnum: NeedCreateTaskEvaluationAlertEnum.values()) {
            if (String.valueOf(evaluationAlertEnum).equals(evaluationAlertType)) {
                return true;
            }
        }
        return false;
    }


}
