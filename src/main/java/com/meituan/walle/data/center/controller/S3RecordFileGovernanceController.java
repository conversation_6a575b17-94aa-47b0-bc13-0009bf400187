package com.meituan.walle.data.center.controller;

import com.meituan.servicecatalog.api.annotations.HttpMethod;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.params.S3ColdFileParam;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.service.impl.S3RecordFileGovernanceServiceImpl;
import com.meituan.walle.data.center.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@InterfaceDoc(
        displayName = "S3 Record File生命周期管理接口",
        type = "restful",
        description = "S3 Record File生命周期管理接口",
        scenarios = "S3 Record File生命周期管理接口"
)
@Slf4j
@RestController
@RequestMapping("/s3RecordFileGovernance")
public class S3RecordFileGovernanceController {

    @Autowired
    private S3RecordFileGovernanceServiceImpl s3RecordFileGovernanceService;

    @MethodDoc(
            requestMethods = {HttpMethod.POST},
            displayName = "删除S3原始record file 数据",
            description = "清理cold record file",
            returnValueDescription = "调用成功与否的Response对象，包含code、msg、data信息"
    )
    @PostMapping("/delete")
    public Response deleteColdRecordFileToS3Trash(@RequestParam(value = "startDate") String startDate,
                                                  @RequestParam(value = "endDate") String endDate) {
        try {
            s3RecordFileGovernanceService.deleteFileToS3Trash(startDate, endDate);
        } catch (Exception e) {
            log.error("s3RecordFileGovernance delete cold record file exception : {}", e.getMessage(), e);
            return Response.fail("10020", e.getMessage());
        }
        return Response.succ();
    }

    @PostMapping("/coldFile/addBatch")
    public CommonResponse coldFileAddBatch(@RequestBody List<S3ColdFileParam> paramFileList) {
        if (CollectionUtils.isEmpty(paramFileList)) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "The param cannot be null or empty");
        }
        if (paramFileList.size() > 100) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "The number of parameters cannot exceed 100! ");
        }
        try {
            Integer rows = s3RecordFileGovernanceService.insertBatch(paramFileList);
            String data = String.format("The file list contains %d files. Successfully inserted %d rows into table. ",
                    paramFileList.size(), rows);
            log.info("[/s3RecordFileGovernance/coldFile/addBatch] param: {}. {}", JacksonUtil.serialize(paramFileList), data);
            return CommonResponse.success(data);
        } catch (Exception e) {
            log.error("[/s3RecordFileGovernance/coldFile/addBatch] exception: {}, param: {}",
                    e.getMessage(), JacksonUtil.serialize(paramFileList), e);
            return CommonResponse.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode(), e.getMessage());
        }
    }

}