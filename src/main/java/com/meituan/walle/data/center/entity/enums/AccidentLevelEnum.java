package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/06/08
 */

@Getter
@AllArgsConstructor
public enum AccidentLevelEnum {

    UNKNOWN(-1, "未知"),
    MISINFORMATION(0, "查否"),
    EVIL_OMEN(1, "险兆"),
    LOW_LOSS(7,"低损事件"),
    GRAY(2, "灰色"),
    BLUE(3, "蓝色"),
    YELLOW(4, "黄色"),
    ORANGE(5, "橙色"),
    RED(6, "红色")
    ;

    private int code;
    private String msg;

    public static Map<Integer, String> getCodeMsgMap() {
        Map<Integer, String> result = new LinkedHashMap<>();
        for (AccidentLevelEnum accidentLevelEnum : AccidentLevelEnum.values()) {
            result.put(accidentLevelEnum.getCode(), accidentLevelEnum.getMsg());
        }
        return result;
    }
}
