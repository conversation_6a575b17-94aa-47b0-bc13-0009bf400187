package com.meituan.walle.data.center.config;

import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.banma.auk.server.sdk.AukClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/10/28
 */

@Configuration
@Slf4j
public class AukClientConfig {
    private static final String AUK_ACCESS_KEY = "data.center.auk.access.key";

    private static final String AUK_SECRET_KEY = "data.center.auk.secret.key";


    @Bean
    public AukClient aukClient() {
        String accessKey;
        String secretKey;
        try {
            accessKey = Kms.getByName(MccConstant.MCC_APPKEY, AUK_ACCESS_KEY);
            secretKey = Kms.getByName(MccConstant.MCC_APPKEY, AUK_SECRET_KEY);
        } catch (KmsResultNullException e) {
            log.error("get auk access key failed", e);
            throw new RuntimeException(e);
        }
        return new AukClient(accessKey, secretKey);
    }

}