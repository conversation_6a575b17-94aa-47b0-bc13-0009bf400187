package com.meituan.walle.data.center.entity.dto;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/04/18
 */
@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ColdRecordFileLevelDTO implements BinlogDTO {

    private Long id;
    private String recordName;
    private String fileName;
    private String filePath;
    private Long fileSize;
    private String s3Url;
    private Integer s3Level;
    private Long recordId;
    private Integer isDeleted;
    private Integer s3IsMarkedForDeletion;
    private Date createTime;
    private Date updateTime;



}