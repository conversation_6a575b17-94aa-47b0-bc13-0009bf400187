package com.meituan.walle.data.center.config;

import com.meituan.kafka.javaclient.common.utils.BrokerVerifiableProperties;
import com.meituan.mafka.client.producer.Partitioner.MafkaPartitionerImpl;
import lombok.extern.slf4j.Slf4j;

/**
 * 新接管逻辑中事件根据vin进行分区
 */
@Slf4j
public class MafkaVinPartitioner extends MafkaPartitionerImpl {

    public MafkaVinPartitioner(BrokerVerifiableProperties props) {
        super(props);
    }

    @Override
    public int partition(Object key, int numPartitions) {
        try {
            if (key == null) {
                return 0;
            }
            String partKet = (String) key;
            return Math.abs(partKet.hashCode() % numPartitions);
        } catch (Exception e) {
            log.error("Failed to get partition for event, key: {}", key, e);
            return 0;
        }
    }
}
