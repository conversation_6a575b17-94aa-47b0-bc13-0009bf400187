package com.meituan.walle.data.center.entity.pojo;

import com.dianping.zebra.util.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.walle.data.center.constant.CharConstant;
import lombok.Data;
import org.junit.Assert;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import javax.persistence.*;

/**
 * <AUTHOR>
 * @ 2021/01/27
 * 理解为数据库的持久化对象
 */
@Table(name = "vehicle_upload_request")
@Data
public class VehicleUploadRequest implements Comparable<VehicleUploadRequest> {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 起始时间
     */
    private Date start;

    /**
     * 终止时间
     */
    private Date end;

    /**
     * 底盘时间（case 发生时间）
     */
    @Column(name = "measurement_timestamp")
    private Long measurementTimestamp;

    /**
     * module列表(以英文逗号分隔)
     */
    private String module;

    /**
     * 快速上传额外信息
     */
    private String extraInfo;

    /**
     * 0.初始|10.处理中|100.完成
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc = CharConstant.CHAR_EMPTY;

    private Integer priority;

    @Column(name = "is_case")
    private Boolean oneCase;

    @Column(name = "is_discern")
    private Integer isDiscern;

    private String uuid;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "record_name")
    private String recordName;

    /**
     * 根据record_name + intervention_time生成的caseId
     */
    @Column(name = "intervention_id")
    private String interventionId;

    /**
     * 事件ID
     */
    @Column(name = "event_id")
    private String eventId;

    /**
     * 任务类别，1.实时Case回传|2.事故数据回传|3.指定模块基础数据回传|4用户自定义数据回传
     */
    @Column(name = "task_type")
    private Integer taskType;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    private Boolean isDeleted;

    /**
     * 创建人
     */
    @Column(name = "creator")
    private String creator;

    @Column(name = "task_md5")
    private String taskMd5;

    @Column(name = "is_recording")
    private Boolean isRecording;

    @Column(name = "status_detail")
    private String statusDetail;

    @Column(name = "finish_time")
    private Date finishTime;

    @Column(name = "cost_time")
    private Integer costTime;

    @Override
    public int compareTo(VehicleUploadRequest vehicleUploadRequest) {
        if (this.getStart().getTime() > vehicleUploadRequest.getStart().getTime()) {
            return 1;
        } else if (this.getStart().getTime() < vehicleUploadRequest.getStart().getTime()) {
            return -1;
        } else {
            return 0;
        }
    }

    /**
     * 统一修改extraInfo方法，输入为json字符串类型
     * 避免原始extraInfo字段被覆盖
     *
     * @param extraInfo
     */
    public void updateOrPutExtraInfo(String extraInfo) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        if (StringUtils.isNotBlank(extraInfo)) {
            Map<String, Object> extraInfoMap = mapper.readValue(extraInfo, new TypeReference<Map<String, Object>>() {});
            updateOrPutExtraInfo(extraInfoMap);
        }
    }

    /**
     * 统一修改extraInfo方法，输入为Map<String, Object>类型
     * 避免原始extraInfo字段被覆盖
     *
     * @param extraInfoMap
     */
    public void updateOrPutExtraInfo(Map<String, Object> extraInfoMap) throws IOException {
        Assert.assertNotNull(extraInfoMap);
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> originExtraInfoMap = new HashMap<>();
        if (StringUtils.isNotBlank(this.extraInfo)) {
            originExtraInfoMap = mapper.readValue(this.extraInfo, new TypeReference<Map<String, Object>>() {});
        }
        originExtraInfoMap.putAll(extraInfoMap);
        this.setExtraInfo(mapper.writeValueAsString(originExtraInfoMap));
    }
}