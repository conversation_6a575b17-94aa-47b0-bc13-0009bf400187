package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/06/10
 */

@Getter
@AllArgsConstructor
public enum AffiliationEnum {

    OTHER(0, "其他"),
    PUBLIC_ROAD(1, "公开道路"),
    CAMPUS(2, "校园"),
    SHANGHAI_KANGYI(3, "上海抗疫"),
    ALL(100, "所有地区"),
    ;
    private int code;
    private String msg;

    public static String getMsgByCode(Integer code) {
        String result = "未知";
        if (code == null) {
            return result;
        }
        for (AffiliationEnum affiliationEnum : AffiliationEnum.values()) {
            if (affiliationEnum.getCode() == code.intValue()) {
                return affiliationEnum.getMsg();
            }
        }
        return result;
    }

    public static List<Integer> getCodeByMsg(List<String> msgList) {
        List<Integer> result = new ArrayList<>();
        for (AffiliationEnum affiliationEnum : AffiliationEnum.values()) {
            for (String msg : msgList) {
                if (msg.equals(affiliationEnum.getMsg())) {
                    result.add(affiliationEnum.getCode());
                    break;
                }
            }
        }
        return result;
    }
}
