package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.request.LaunchTraceRequest;
import com.meituan.walle.data.center.service.VehicleBusinessService;
import com.meituan.walle.data.center.util.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2022/09/20
 */

@Slf4j
@RestController
@RequestMapping("/report")
public class ReportController {

    @Resource
    private VehicleBusinessService vehicleBusinessService;

    private static final String API_PREFIX = "/report";

    @PostMapping("/trace")
    public Response launchTrace(@RequestBody LaunchTraceRequest request) {
        String api = "/trace";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        if (StringUtils.isBlank(request.getTraceId()) || StringUtils.isBlank(request.getCategory()) ||
                request.getReportTime() == null || StringUtils.isBlank(request.getReportSource())) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(), "missing parameter");
        }
        if (request.getReportTime().toString().length() != 19) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    "parameter reportTime format error");
        }
        try {
            log.info("traceId is {}, start to insert launch trace, request is [{}]", traceId, request);
            vehicleBusinessService.insertTrace(request);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            log.error("traceId is {}, insert biz_trace table failed", traceId, e);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(),
                "service not available");
    }
}
