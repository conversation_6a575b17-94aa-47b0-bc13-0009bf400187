package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.constant.SparkJobEnum;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.request.TaskCreateRequest;
import com.meituan.walle.data.center.service.TaskService;
import com.meituan.walle.data.center.task.autocar.ManualRecordFromS3JobExtract;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/12/25
 */
@InterfaceDoc(
        displayName = "创建调度任务接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供创建调度任务的能力。",
        scenarios = "创建调度任务接口"
)
@RestController
@RequestMapping(value = "/task")
@Slf4j
public class TaskController {

    @Autowired
    private ManualRecordFromS3JobExtract manualRecordFromS3JobExtract;

    @Autowired
    private TaskService taskService;

    private static final String API_PREFIX = "/task";

    @MethodDoc(
            displayName = "创建record调度任务",
            description = "创建record调度任务")
    @PostMapping(value = "/create_record_schedule_task")
    public Response createRecordScheduleTask(@Validated @RequestBody TaskCreateRequest request) {
        String api = "/create_record_schedule_task";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        long start = System.currentTimeMillis();
        try {
            final String jobUuid = Objects.requireNonNull(SparkJobEnum.get(request.getJobName())).getValue();
            final Response response = manualRecordFromS3JobExtract.createTask(request.getRecordName(), jobUuid);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return response;
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("create record schedule task fail !", e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(),
                    WebResponseStatusEnum.LOGICAL_EXCEPTION.getMsg());
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "获取record调度任务信息",
            description = "获取record调度任务信息")
    @GetMapping(value = "/get_record_schedule_task_info")
    public Response getRecordScheduleTaskInfo(@Param("recordName") String recordName,
                                              @Param("jobName") String jobName) {
        String api = "/get_record_schedule_task_info";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        long start = System.currentTimeMillis();
        try {
            final String jobUuid = Objects.requireNonNull(SparkJobEnum.get(jobName)).getValue();
            final Response response = taskService.getTaskInfo(recordName, jobUuid);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return response;
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("get record schedule task info fail !", e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(),
                    WebResponseStatusEnum.LOGICAL_EXCEPTION.getMsg());
        } finally {
            t.complete();
        }
    }
}