package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/02/10
 */
@Getter
@AllArgsConstructor
public enum DiskUploadStageEnum {
    S3("s3", "s3");

    private String value;
    private String desc;

    public static DiskUploadStageEnum get(String value) {
        for (DiskUploadStageEnum en : DiskUploadStageEnum.values()) {
            if (Objects.equals(en.value, value)) {
                return en;
            }
        }
        return null;
    }
}