package com.meituan.walle.data.center.dal.recordmanager.mapper;

import com.meituan.walle.data.center.dal.recordmanager.entity.InboundRecordSparkTaskPO;
import com.meituan.walle.data.center.entity.params.InboundRecordSparkTaskParam;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5
 */
public interface InboundRecordSparkTaskMapper {
    @Insert({
            "<script>" +
                    "    INSERT ignore INTO `inbound_record_spark_task` " +
                    "    <trim prefix='(' suffix=')' suffixOverrides=','>" +
                    "        <if test='null != recordName and &apos;&apos; != recordName'>" +
                    "            `record_name`," +
                    "        </if>" +
                    "        <if test='null != jobUuid and &apos;&apos; != jobUuid'>" +
                    "            `job_uuid`," +
                    "        </if>" +
                    "        <if test='null != instanceUuid and &apos;&apos; != instanceUuid'>" +
                    "            `instance_uuid`," +
                    "        </if>" +
                    "        <if test='null != instanceType'>" +
                    "            `instance_type`," +
                    "        </if>" +
                    "        <if test='null != dynamicParam and &apos;&apos; != dynamicParam'>" +
                    "            `dynamic_param`," +
                    "        </if>" +
                    "        <if test='null != status'>" +
                    "            `status`," +
                    "        </if>" +
                    "        <if test='null != isDeleted'>" +
                    "            `is_deleted`," +
                    "        </if>" +
                    "    </trim>" +
                    "    <trim prefix='values (' suffix=')' suffixOverrides=','>" +
                    "        <if test='null != recordName and &apos;&apos; != recordName'>" +
                    "            #{recordName}," +
                    "        </if>" +
                    "        <if test='null != jobUuid and &apos;&apos; != jobUuid'>" +
                    "            #{jobUuid}," +
                    "        </if>" +
                    "        <if test='null != instanceUuid and &apos;&apos; != instanceUuid'>" +
                    "            #{instanceUuid}," +
                    "        </if>" +
                    "        <if test='null != instanceType'>" +
                    "            #{instanceType}," +
                    "        </if>" +
                    "        <if test='null != dynamicParam and &apos;&apos; != dynamicParam'>" +
                    "            #{dynamicParam}," +
                    "        </if>" +
                    "        <if test='null != status'>" +
                    "            #{status}," +
                    "        </if>" +
                    "        <if test='null != isDeleted'>" +
                    "            #{isDeleted}," +
                    "        </if>" +
                    "    </trim>" +
                    "</script>"})
    int insert(InboundRecordSparkTaskPO inboundRecordSparkTaskPO);

    @Update({
            "<script>" +
                    "    UPDATE `inbound_record_spark_task` SET " +
                    "        <if test='null != instanceUuid and &apos;&apos; != instanceUuid'>" +
                    "            `instance_uuid`=#{instanceUuid}," +
                    "        </if> " +
                    "        <if test='null != instanceType'>" +
                    "            `instance_type`=#{instanceType}," +
                    "        </if> " +
                    "        <if test='null != dynamicParam and &apos;&apos; != dynamicParam'>" +
                    "            `dynamic_param`=#{dynamicParam}," +
                    "        </if> " +
                    "        <if test='null != status'>" +
                    "            `status`=#{status}," +
                    "        </if> " +
                    "        <if test='null != isDeleted'>" +
                    "            `is_deleted`=#{isDeleted}," +
                    "        </if> " +
                    "        `update_time`=CURRENT_TIMESTAMP " +
                    "    WHERE `record_name`=#{recordName} AND `job_uuid`=#{jobUuid} " +
                    "</script>"})
    int update(InboundRecordSparkTaskPO inboundRecordSparkTaskPO);

    @Select({"select * from `inbound_record_spark_task` where `record_name`=#{recordName} and `job_uuid`=#{jobUuid} "})
    InboundRecordSparkTaskPO getInboundRecordSparkTask(@Param("recordName") String recordName, @Param("jobUuid") String jobUuid);

    @Select({
            "<script>" +
                    "    SELECT " +
                    "        t.`record_name`, " +
                    "        t.`job_uuid`, " +
                    "        t.`instance_uuid`, " +
                    "        t.`instance_type`, " +
                    "        t.`dynamic_param`, " +
                    "        t.`status`, " +
                    "        t.`is_deleted` " +
                    "    FROM `inbound_record_spark_task` AS t " +
                    "    WHERE t.`record_name` = #{recordName} " +
                    "</script>"
    })
    @Results(value = {
            @Result(property = "recordName", column = "record_name"),
            @Result(property = "jobUuid", column = "job_uuid"),
            @Result(property = "instanceUuid", column = "instance_uuid"),
            @Result(property = "instanceType", column = "instance_type"),
            @Result(property = "dynamicParam", column = "dynamic_param"),
            @Result(property = "status", column = "status"),
            @Result(property = "isDeleted", column = "is_deleted")
    })
    List<InboundRecordSparkTaskPO> selectInboundRecordSparkTasks(InboundRecordSparkTaskParam param);
}
