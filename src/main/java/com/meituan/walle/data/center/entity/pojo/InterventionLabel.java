package com.meituan.walle.data.center.entity.pojo;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON> on 2021/7/27.
 */
@Table(name = "intervention_label")
public class InterventionLabel {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 接管时间
     */
    @Column(name = "intervention_time")
    private Date interventionTime;

    /**
     * 根据record_name + intervention_time生成的caseId
     */
    @Column(name = "intervention_id")
    private String interventionId;

    /**
     * 标签类型
     * 1 tag，2 系统接管，3 终点前接管，4 电子围栏接管， 5 进场接管，6 远遥接管
     */
    @Column(name = "label_type")
    private Byte labelType;

    /**
     * 标签名称
     */
    @Column(name = "label_name")
    private String labelName;

    /**
     * 区分标签是自动打的还是人工打的 0 自动，1 人工
     */
    private Byte source;
    /**
     * 来源，离线还是实时 0，离线，1是实时
     */
    private Byte channel;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    public InterventionLabel() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRecordName() {
        return recordName;
    }

    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    public Date getInterventionTime() {
        return interventionTime;
    }

    public void setInterventionTime(Date interventionTime) {
        this.interventionTime = interventionTime;
    }

    public String getInterventionId() {
        return interventionId;
    }

    public void setInterventionId(String interventionId) {
        this.interventionId = interventionId;
    }

    public Byte getLabelType() {
        return labelType;
    }

    public void setLabelType(Byte labelType) {
        this.labelType = labelType;
    }

    public String getLabelName() {
        return labelName;
    }

    public void setLabelName(String labelName) {
        this.labelName = labelName;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getChannel() {
        return channel;
    }

    public void setChannel(Byte channel) {
        this.channel = channel;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        InterventionLabel that = (InterventionLabel) o;
        return interventionId.equals(that.interventionId) &&
                labelType.equals(that.labelType) &&
                labelName.equals(that.labelName) &&
                source.equals(that.source) &&
                channel.equals(that.channel);
    }

    @Override
    public int hashCode() {
        return Objects.hash(interventionId, labelType, labelName, source, channel);
    }

    public interface InterventionLabelBuilder {
        InterventionLabelBuilder recordName(String value);
        InterventionLabelBuilder interventionTime(Date value);
        InterventionLabelBuilder interventionId(String value);
        InterventionLabelBuilder labelType(Byte value);
        InterventionLabelBuilder labelName(String value);
        InterventionLabelBuilder source(Byte value);
        InterventionLabelBuilder channel(Byte value);
        InterventionLabel build();
    }

    public static final class DefaultInterventionLabelBuilder implements InterventionLabelBuilder {

        private InterventionLabel interventionLabel = new InterventionLabel();

        @Override
        public InterventionLabelBuilder recordName(String value) {
            interventionLabel.setRecordName(value);
            return this;
        }

        @Override
        public InterventionLabelBuilder interventionTime(Date value) {
            interventionLabel.setInterventionTime(value);
            return this;
        }

        @Override
        public InterventionLabelBuilder interventionId(String value) {
            interventionLabel.setInterventionId(value);
            return this;
        }

        @Override
        public InterventionLabelBuilder labelType(Byte value) {
            interventionLabel.setLabelType(value);
            return this;
        }

        @Override
        public InterventionLabelBuilder labelName(String value) {
            interventionLabel.setLabelName(value);
            return this;
        }

        @Override
        public InterventionLabelBuilder source(Byte value) {
            interventionLabel.setSource(value);
            return this;
        }

        @Override
        public InterventionLabelBuilder channel(Byte value) {
            interventionLabel.setChannel(value);
            return this;
        }

        @Override
        public InterventionLabel build() {
            return interventionLabel;
        }
    }
}
