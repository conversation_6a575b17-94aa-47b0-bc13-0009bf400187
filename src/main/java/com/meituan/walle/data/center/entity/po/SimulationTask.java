package com.meituan.walle.data.center.entity.po;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/12/3.
 */

public class SimulationTask {

    private String primaryTaskId;

    private String taskId;

    private String adsId;

    public Integer loopIndex;

    private Integer syncToWopState;

    private volatile Long lastUpdatedTimestamp = System.currentTimeMillis();

    public String getPrimaryTaskId() {
        return primaryTaskId;
    }

    public void setPrimaryTaskId(String primaryTaskId) {
        this.primaryTaskId = primaryTaskId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getAdsId() {
        return adsId;
    }

    public void setAdsId(String adsId) {
        this.adsId = adsId;
    }

    public Integer getLoopIndex() {
        return loopIndex;
    }

    public void setLoopIndex(Integer loopIndex) {
        this.loopIndex = loopIndex;
    }

    public Integer getSyncToWopState() {
        return syncToWopState;
    }

    public void setSyncToWopState(Integer syncToWopState) {
        this.syncToWopState = syncToWopState;
    }

    public Long getLastUpdatedTimestamp() {
        return lastUpdatedTimestamp;
    }

    public void setLastUpdatedTimestamp(Long lastUpdatedTimestamp) {
        this.lastUpdatedTimestamp = lastUpdatedTimestamp;
    }
}
