package com.meituan.walle.data.center.entity.enums;

public enum  VehicleSpecificEventTypeEnum {
    VEHICLE_CONFIG_DOWNLOAD(201),
    FREESPACE_PARKING_START(372),
    FREESPACE_PARKING_FAILED(373),
    FREESPACE_PARKING_EXIT(374),
    FREESPACE_PARKING_COMPLETE(375),
    FREESPACE_UTURN_START(376),
    FREESPACE_UTURN_FAILED(377),
    FREESPACE_UTURN_COMPLETE(378),
    FREESPACE_STARTING_START(379),
    FREESPACE_STARTING_FAILED(380),
    FREESPACE_STARTING_COMPLETE(381),
    FREESPACE_RESCUE_START(382),
    FREESPACE_RESCUE_FAILED(383),
    FREESPACE_RESCUE_COMPLETE(384),
    FREESPACE_MEETING_START(385),
    FREESPACE_MEETING_FAILED(386),
    FREESPACE_MEETING_COMPLETE(387),
    FREESPACE_ROUTING_SWITCH_START(388),
    FREESPACE_ROUTING_SWITCH_FAILED(389),
    FREESPACE_ROUTING_SWITCH_COMPLETE(390),
    FRENET_PULL_OVER_START(3059),
    FRENET_PULL_OVER_FAILED(3060),
    FRENET_PULL_OVER_COMPLETE(3061),
    RIDE_DISCOMFORT_EVENT(3063),
    ;

    private int code;
    VehicleSpecificEventTypeEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static VehicleSpecificEventTypeEnum getEnumByCode(int code) {
        for (VehicleSpecificEventTypeEnum specificEventTypeEnum : VehicleSpecificEventTypeEnum.values()) {
            if (code == specificEventTypeEnum.getCode()) {
                return specificEventTypeEnum;
            }
        }
        return null;
    }
}
