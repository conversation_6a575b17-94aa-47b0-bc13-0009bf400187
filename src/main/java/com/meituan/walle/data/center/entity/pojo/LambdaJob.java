package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "lambda_job")
public class LambdaJob {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    private String lambdaId;
    private String owner;
    private String recordName;
    private String vehicleName;
    private String module;
    private Date startTime;
    private Date endTime;
    private String pyS3Url;
    private Integer jobType;
    private Integer status;
    private String resultHdfsPath;
    private String resultS3Url;
    private String modelId;
    private Integer syncStatus;
    private Date createTime;
    private Date updateTime;

}
