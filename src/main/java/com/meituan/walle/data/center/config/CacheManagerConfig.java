package com.meituan.walle.data.center.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCache;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

@Configuration
@EnableCaching
public class CacheManagerConfig {
    @Bean
    public CacheManager cacheManager() {
        SimpleCacheManager cacheManager = new SimpleCacheManager();
        cacheManager.setCaches(Arrays.asList(
                new ConcurrentMapCache("name2Vin"),
                new ConcurrentMapCache("vin2Name"),
                new ConcurrentMapCache("vin2VehicleId"),
                new ConcurrentMapCache("vinDateKey2misIds"),
                new ConcurrentMapCache("vin2VehicleNameWithId"),
                new ConcurrentMapCache("dateVehicleName2ExistsRecord"),
                new ConcurrentMapCache("vin2VehicleType")
        ));
        return cacheManager;
    }
}
