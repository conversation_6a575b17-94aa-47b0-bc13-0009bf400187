package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2022/10/31
 */
@Getter
@AllArgsConstructor
public enum FreeSpaceStatusEnum {

    UNKNOWN(-1, "unknown"),
    NONE(0, "none"),
    OBSTACLE_BLOCKED_PLOT(1, "obstacle_blocked_plot"),
    WAITING_OBSTACLE_MOVE_AWAY(2, "waiting_obstacle_move_away"),
    EGO_OBSTACLE_COLLISION(3, "ego_obstacle_collision"),
    NONE_PLOT(4, "none_plot"),
    INPUT_PLOT_CONFLICT(5, "input_plot_conflict"),
    ;

    private int code;
    private String msg;
}
