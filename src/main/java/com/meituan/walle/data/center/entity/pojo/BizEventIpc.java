package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "biz_event_ipc")
@Data
public class BizEventIpc {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    private String eventId;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * IPC事件发生事件
     */
    private Date eventTime;

    private Integer eventType;

    /**
     * ipc事件中的额外信息
     */
    private String content;

    private Boolean isDeleted;

    private Date createTime;

    private Date updateTime;

}
