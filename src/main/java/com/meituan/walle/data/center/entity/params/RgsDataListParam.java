package com.meituan.walle.data.center.entity.params;

import lombok.Data;

@Data
public class RgsDataListParam {
    /**
     * record_name
     */
    private String recordName;

    /**
     * 车辆名称
     */
    private String carName;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 数据下载起始时间
     */
    private Long startTime;

    /**
     * 数据下载结束时间
     */
    private Long endTime;

    /**
     * 需要下载的模块
     */
    private String modules;

    /**
     * 不需要下载的模块
     */
    private String noModules;

    /**
     * 需要下载文件类型
     */
    private String fileTypes;

    private Boolean forceDownload;

    private String misId;

    /**
     * 只从离线寄盘回收中获取数据
     */
    private Boolean downloadFromOffline;

    /**
     * 只从在线快速回传中获取数据
     */
    private Boolean downloadFromOnline;

    /**
     * 压缩格式：请求需要的压缩格式
     */
    private String compressionFormatList;

    /**
     * 用户clientId
     */
    private String userClientId;
    /**
     * （数据刷新场景需要数据合并）是否 Module 合并
     */
    private Boolean isModuleMerge;

}
