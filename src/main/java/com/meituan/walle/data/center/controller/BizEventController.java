package com.meituan.walle.data.center.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.walle.data.center.entity.pojo.BizEvents;
import com.meituan.walle.data.center.entity.request.BizEventWorkbenchCommonRequest;
import com.meituan.walle.data.center.entity.response.CommonPageResponse;
import com.meituan.walle.data.center.service.BizEventService;
import com.meituan.walle.data.center.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/06/05
 */
@InterfaceDoc(
        displayName = "事件中心统一开放接口", type = "restful", description = "事件中心统一开放接口", scenarios = "事件中心统一开放接口")
@Slf4j
@RestController
@RequestMapping("/bizEvent")
public class BizEventController {

    @Autowired
    private BizEventService bizEventService;

    @GetMapping("/workbench/common")
    public CommonPageResponse queryBizEventsCommon(
            @RequestParam(required = false) String eventId,
            @RequestParam(required = false) String recordName,
            @RequestParam(required = false) String vin,
            @RequestParam(required = false) Integer source,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer size
    ) {
        BizEventWorkbenchCommonRequest request = BizEventWorkbenchCommonRequest.builder()
                .eventId(eventId)
                .recordName(recordName)
                .vin(vin)
                .source(source)
                .startTime(startTime)
                .endTime(endTime)
                .page(page)
                .size(size)
                .build();
        try {
            List<BizEvents> bizEventsList = bizEventService.workbenchPageQuery(request);
            int totalCount = 0;
            if (!CollectionUtils.isEmpty(bizEventsList)) {
                totalCount = bizEventService.workbenchCountQuery(request);
            }
            return CommonPageResponse.success(bizEventsList, totalCount, page, size);
        } catch (Exception e) {
            log.error("[/bizEvent/workbench/common] exception: {}, request: {}",
                    e.getMessage(), JacksonUtil.serialize(request), e);
            return CommonPageResponse.error(e.getMessage());
        }
    }

}