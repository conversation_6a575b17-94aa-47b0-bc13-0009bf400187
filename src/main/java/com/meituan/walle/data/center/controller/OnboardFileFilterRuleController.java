package com.meituan.walle.data.center.controller;

import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.pojo.BizOnboardFileFilterRule;
import com.meituan.walle.data.center.entity.request.BizOnboardFileFilterRuleRequest;
import com.meituan.walle.data.center.service.BizOnboardFileFilterRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/02/07
 */
@InterfaceDoc(
        displayName = "车端文件过滤规则相关接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供车端文件过滤规则管理能力。",
        scenarios = "车端文件过滤规则的所有接口"
)
@RestController
@RequestMapping(value = "/onboard_file_filter_rule")
@Slf4j
public class OnboardFileFilterRuleController {
    @Autowired
    private BizOnboardFileFilterRuleService bizOnboardFileFilterRuleService;

    @MethodDoc(
            displayName = "添加车端文件过滤规则",
            description = "添加车端文件过滤规则")
    @RequestMapping("/add")
    public Response add(@RequestBody BizOnboardFileFilterRuleRequest onboardFileFilterRuleRequest) {
        if (onboardFileFilterRuleRequest == null) {
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(), "param is null !");
        }
        if (StringUtils.isBlank(onboardFileFilterRuleRequest.getType())) {
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    "Type is null or empty !");
        }
        if (StringUtils.isBlank(onboardFileFilterRuleRequest.getRule())) {
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    "Rule is null or empty !");
        }
        try {
            BizOnboardFileFilterRule onboardFileFilterRule = new BizOnboardFileFilterRule();
            BeanUtils.copyProperties(onboardFileFilterRuleRequest, onboardFileFilterRule);
            bizOnboardFileFilterRuleService.add(onboardFileFilterRule);
            return Response.succ();
        } catch (Exception e) {
            log.error("Add onboard file filter rule failed, request param is {}", onboardFileFilterRuleRequest, e);
            return Response.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode().toString(),
                    "Add onboard file filter rule failed !");
        }
    }

    @MethodDoc(
            displayName = "更新车端文件过滤规则",
            description = "更新车端文件过滤规则")
    @RequestMapping("/update")
    public Object update(@RequestBody BizOnboardFileFilterRuleRequest onboardFileFilterRuleRequest) {
        if (onboardFileFilterRuleRequest == null) {
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(), "param is null !");
        }
        if (onboardFileFilterRuleRequest.getId() == null) {
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(), "ID is null !");
        }
        try {
            BizOnboardFileFilterRule onboardFileFilterRule = new BizOnboardFileFilterRule();
            BeanUtils.copyProperties(onboardFileFilterRuleRequest, onboardFileFilterRule);
            bizOnboardFileFilterRuleService.update(onboardFileFilterRule);
            return Response.succ();
        } catch (Exception e) {
            log.error("Update onboard file filter rule failed, request param is {}", onboardFileFilterRuleRequest, e);
            return Response.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode().toString(),
                    "Update onboard file filter rule failed !");
        }
    }
}