package com.meituan.walle.data.center.entity.po.mgc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/12/23 10:18
 * @description
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class GeneralCaseInfoPO {
    private Long id;
    private String caseId;
    private String caseType;
    private String recordName;
    private String vin;
    private Date caseTime;
    private String column1;
    private String column2;
    private String column3;
    private String column4;
    private String column5;
    private String column6;
    private String column7;
    private String column8;
    private String column9;
    private String column10;
    private Integer isDeleted;
    private Date createTime;
    private Date updateTime;
    private String column11;
    private String column12;
    private String column13;
    private String column14;
    private String column15;
    private String column16;
    private String column17;
    private String column18;
    private String column19;
    private String column20;
    private String textColumn1;
    private String textColumn2;
    private String textColumn3;
    private String textColumn4;
    private String textColumn5;

}
