package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

/**
 * <AUTHOR>
 * @date 2023/1/4 17:22
 * @description
 */
public class MadGeneralCaseConfig {
    /**
     * 发车失败原因code和描述配置
     * code:
     * description：
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "departure.failed.error.codes")
    public static volatile String DEPARTURE_FAILED_ERROR_CODE_LIST = "[]";
}
