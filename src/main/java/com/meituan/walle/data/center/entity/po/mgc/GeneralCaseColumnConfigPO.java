package com.meituan.walle.data.center.entity.po.mgc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/12/23 10:18
 * @description
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class GeneralCaseColumnConfigPO {
    private Long id;
    private String originDatasourceName;
    @Column(name = "case_type")
    private String caseType;
    private String name;
    private String description;
    private Integer type;
    private String originColumnName;
    private String targetColumnName;
    private String columnDataType;
    private Integer sorted;
    private Integer isDeleted;
    private Date createTime;
    private Date updateTime;
    private Boolean concatToId;
}
