package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

public class TimeInterval {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "time.interval.tag.intervention")
    public static volatile long LIVE_ISSUE_TAG_INTERVENTION = 3 * 1000L * 1000L * 1000L; // 上下3秒

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "time.interval.case.matching.intervention")
    public static volatile long CASE_MATCHING_INTERVENTION = 3 * 1000L * 1000L * 1000L; // 上下3秒
}
