package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/02/06
 */
@Table(name = "user_encrypt_phone_number")
@Data
public class UserEncryptPhoneNumber {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 用户misId
     */
    private String misId;

    /**
     * 用户加密手机号码
     */
    private String phoneNumber;

    private Boolean isDeleted;

    private Date createTime;

    private Date updateTime;
}
