package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

/**
 * <AUTHOR>
 * @date 2022/05/18
 */
public class RoutingRequestConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "routing.request.blacklisted.road")
    public static volatile String ROUTING_REQUEST_BLACKLISTED_ROAD = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "routing.request.blacklisted.lane")
    public static volatile String ROUTING_REQUEST_BLACKLISTED_LANE = "";
}
