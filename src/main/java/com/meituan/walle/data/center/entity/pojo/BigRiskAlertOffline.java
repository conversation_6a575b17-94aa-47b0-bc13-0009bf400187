package com.meituan.walle.data.center.entity.pojo;

import lombok.Builder;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/12/13
 */
@Builder
@Data
@Table(name = "big_event_risk_alert_offline")
public class BigRiskAlertOffline {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 事件id
     */
    @Column(name = "event_id")
    private String eventId;

    /**
     * 开始时间戳 19位纳秒时间戳
     */
    @Column(name = "begin_timestamp")
    private Long beginTimestamp;

    /**
     * 结束时间戳 19位纳秒时间戳
     */
    @Column(name = "end_timestamp")
    private Long endTimestamp;

    /**
     * 持续时间 单位毫秒
     */
    @Column(name = "duration_time")
    private Integer durationTime;

    /**
     * 提示原因数字编号,和原因枚举一对一
     */
    @Column(name = "evaluation_alert_number")
    private Integer evaluationAlertNumber;

    /**
     * 提示原因名-枚举
     */
    @Column(name = "evaluation_alert_name")
    private String evaluationAlertName;

    /**
     * 判定提示名称，目前风险提示都是REQUEST_TAKEOVER_IN_GENERAL
     */
    @Column(name = "arbitration_alert_name")
    private String arbitrationAlertName;

    /**
     * 判定提示数字，目前都为8
     */
    @Column(name = "arbitration_alert_number")
    private Integer arbitrationAlertNumber;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}

