package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2021/7/8 8:23 下午
 */
@Getter
@AllArgsConstructor
public enum OKOrNotEnum {
    NOT_OK(0, "NOT OK"),
    OK(1, "OK");

    private int code;
    private String msg;

    public static OKOrNotEnum getByMsg(String msg) {
        for (OKOrNotEnum type : values()) {
            if (type.getMsg().equals(msg)) {
                return type;
            }
        }
        return NOT_OK;
    }
}
