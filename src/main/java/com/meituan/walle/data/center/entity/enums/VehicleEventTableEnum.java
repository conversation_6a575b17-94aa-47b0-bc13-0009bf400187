package com.meituan.walle.data.center.entity.enums;

public enum VehicleEventTableEnum {
    ROUTING(1),
    DRIVING_MODE_CHANGE(3),
    DERIVE_INTERVENTION(13),
    AUTODRIVE_HARDBRAKE(4),
    ESTOP(5),
    PARKING(6),
    MAEB(7),
    DESTINATION(9),
    ELECTRIC_FENCE(10),
    SCENARIO_SWITCH(14),
    HDMAP_EXCEPTION(16),
    BUMPER_TRIGGER(17),
    PNC_PARKING(18), // 前端类型显示为FreeSpace

    POWER(38),
    IPC(100),
    COREDUMP(110),
    AUTOCAR_RELEASE(111);

    private int source;

    VehicleEventTableEnum(int source){
        this.source = source;
    }

    public int getSource() {
        return source;
    }
}
