package com.meituan.walle.data.center.entity.dto;

import lombok.*;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON> on 2021/7/27.
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@Setter
@ToString
public class InterventionLabelDTO {
    /**
     * 表record的record_name外键
     */
    private String recordName;

    /**
     * 接管时间
     */
    private Date interventionTime;

    /**
     * 根据record_name + intervention_time生成的caseId
     */
    private String interventionId;

    /**
     * 标签类型
     * 1 tag，2 系统接管，3 终点前接管，4 电子围栏接管， 5 进场接管，6 远遥接管
     */
    private Byte labelType;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 区分标签是自动打的还是人工打的 0 自动，1 人工
     */
    private Byte source;

    /**
     * 来源，离线还是实时 0，离线，1是实时
     */
    private Byte channel;

    /**
     * 车架号
     */
    private String vin;

    private Integer clientType;

    public String getRecordName() {
        return recordName;
    }

    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    public Date getInterventionTime() {
        return interventionTime;
    }

    public void setInterventionTime(Date interventionTime) {
        this.interventionTime = interventionTime;
    }

    public String getInterventionId() {
        return interventionId;
    }

    public void setInterventionId(String interventionId) {
        this.interventionId = interventionId;
    }

    public Byte getLabelType() {
        return labelType;
    }

    public void setLabelType(Byte labelType) {
        this.labelType = labelType;
    }

    public String getLabelName() {
        return labelName;
    }

    public void setLabelName(String labelName) {
        this.labelName = labelName;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public Byte getChannel() {
        return channel;
    }

    public void setChannel(Byte channel) {
        this.channel = channel;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

}
