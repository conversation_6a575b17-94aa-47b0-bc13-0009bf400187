package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/08
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum InboundPipelineStageEnum {
    DEFAULT_STAGE("DEFAULT_STAGE", "默认值"),
    INITIALIZE_STAGE("INITIALIZE_STAGE", "初始化阶段"),
    UPLOAD_STAGE("UPLOADED_STAGE", "上传阶段"),
    PARSE_STAGE("PARSE_STAGE", "解析阶段"),
    VALIDATE_STAGE("VALIDATE_STAGE", "数据校验阶段"),
    ;

    private String code;
    private String desc;


    public static InboundPipelineStageEnum byCode(String code) {
        for (InboundPipelineStageEnum en : InboundPipelineStageEnum.values()) {
            if (en.code.equalsIgnoreCase(code)) {
                return en;
            }
        }
        return null;
    }
}
