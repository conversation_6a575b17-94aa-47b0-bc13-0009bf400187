package com.meituan.walle.data.center.entity.po;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/4 下午4:17
 * Description:
 * Modified by
 */
@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OnesInfoPO implements Serializable {

    private Long id;
    private Integer projectId;
    private String projectName;
    private Integer issueId;
    private String issueName;
    private String diyState;
    private Integer diyStateOrder;
    private String state;
    private String iterationId;
    private String packageVersion;
    private String gitBranch;
    private String version;
    private Integer versionOrder;
    private String vin;
    private String hdmap;
    private String assigned;
    private String firstGroup;
    private String secondGroup;
    private String createdAt;
    private String updatedAt;
    private String closeAt;
    private Double costTime;
    private String associatedProject;
    private String associatesRequestName;
    private String associatesRequestState;
    private Integer associatesRequestStateOrder;
    private String type;
    private String labels;
    private String source;
    private String associatesRequestCreatedAt;
    private Timestamp createTime;
    private Timestamp updateTime;

    @Override
    public String toString(){
        try {
            return new ObjectMapper().writeValueAsString(this);
        } catch (JsonProcessingException e) {
            log.error("OnesInfoPO toString failed", e);
        }
        return "";
    }

}
