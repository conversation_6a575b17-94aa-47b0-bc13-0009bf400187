package com.meituan.walle.data.center.dal.recordmanager.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InboundRecordSnapshotPO {
    private String recordName;
    private String bucketName;
    private String s3Path;
    private Long fileSize;
    private Byte status;
    private String statusDetail;
    private Integer isDeleted;
}
