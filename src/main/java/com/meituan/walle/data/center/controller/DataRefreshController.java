package com.meituan.walle.data.center.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.meituan.walle.data.center.constant.CharConstant;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.dto.DataRefreshListDTO;
import com.meituan.walle.data.center.entity.request.DataRefreshListRequest;
import com.meituan.walle.data.center.entity.response.HttpResponse;
import com.meituan.walle.data.center.entity.vo.RecordCommonFileVO;
import com.meituan.walle.data.center.handle.impl.S3DsnUrlHandler;
import com.meituan.walle.data.center.service.DataRefreshService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2024/6/4
 */
@Slf4j
@RestController
@RequestMapping("/data_refresh")
public class DataRefreshController {
    private static final Logger LOGGER = LoggerFactory.getLogger(DataRefreshController.class);
    @Resource
    private S3DsnUrlHandler s3DsnUrlHandler;
    @Autowired
    private DataRefreshService dataRefreshService;

    @GetMapping("/data_list")
    public HttpResponse dataList(DataRefreshListRequest request) {
        try {
            DataRefreshListDTO dataRefreshListDTO = dataRefreshService.dataList(request);
            HttpResponse s3DsnUrlCheckResponse = setS3DsnUrl(dataRefreshListDTO);
            if (s3DsnUrlCheckResponse != null) return s3DsnUrlCheckResponse;

            return new HttpResponse()
                    .code(WebResponseStatusEnum.MVIZ_SUCCESS.getCode())
                    .result(dataRefreshListDTO);
        } catch (Exception e) {
            log.error("Data Refresh get data list error: {}", request, e);
        }
        return new HttpResponse()
                .code(WebResponseStatusEnum.MVIZ_FAILED.getCode())
                .msg(WebResponseStatusEnum.MVIZ_FAILED.getMsg());
    }

    private HttpResponse setS3DsnUrl(DataRefreshListDTO dataRefreshListDTO) throws JsonProcessingException {
        HttpResponse response = null;
        // 获取S3 DSN URL
        Set<String> s3DsnUrlSet = new HashSet<>();
        for (RecordCommonFileVO recordCommonFileVO : dataRefreshListDTO.getFileList()) {
            s3DsnUrlSet.add(recordCommonFileVO.getS3DsnUrl());
        }
        Map<String, Object> s3DsnUrl = s3DsnUrlHandler.getS3DsnUrl(s3DsnUrlSet);
        // 校验S3 DSN URL
        String missingUrls = s3DsnUrl.entrySet().stream()
                .filter(entry -> entry.getValue() == null)
                .map(Map.Entry::getKey)
                .collect(Collectors.joining(CharConstant.CHAR_DD));
        if (!missingUrls.isEmpty()) {
            log.error("[DataRefreshController#]setS3DsnUrl]DSN URL:[{}], could not get connection info", missingUrls);
            response = new HttpResponse()
                    .code(WebResponseStatusEnum.MVIZ_S3_DSN_URL_COULD_NOT_GET_CONNECTION_INFO.getCode())
                    .msg(String.format(WebResponseStatusEnum.MVIZ_S3_DSN_URL_COULD_NOT_GET_CONNECTION_INFO.getMsg(), missingUrls));
        }
        // 设置S3 DSN URL
        dataRefreshListDTO.setDsnUrlMap(s3DsnUrl);
        return response;
    }
}