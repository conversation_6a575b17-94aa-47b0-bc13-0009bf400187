package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/04/20
 */
@Data
@Table(name = "sys_dict")
public class SysDict {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 字典编码
     */
    @Column(name = "dict_code")
    private String dictCode;

    /**
     * 字典名称
     */
    @Column(name = "dict_name")
    private String dictName;

    /**
     * 编码索引
     */
    @Column(name = "code_index")
    private String codeIndex;

    /**
     * 索引中文名
     */
    @Column(name = "index_name_cn")
    private String indexNameCn;

    /**
     * 索引英文名
     */
    @Column(name = "index_name_en")
    private String indexNameEn;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    @Column(name = "sort_order")
    private Integer sortOrder;

    /**
     * 状态（1启用 0不启用）
     */
    private Integer status;

    @Column(name = "is_deleted")
    private Integer isDeleted;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

}