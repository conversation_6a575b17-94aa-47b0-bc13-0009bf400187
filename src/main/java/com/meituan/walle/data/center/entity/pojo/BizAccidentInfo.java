package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Table(name = "biz_accident_info")
@Data
public class BizAccidentInfo {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 事故业务id
     */
    @Column(name = "event_id")
    private String eventId;

    /**
     * 车架号
     */
    @Column(name = "vin")
    private String vin;

    /**
     * record名称
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 事故标题
     */
    private String title;

    /**
     * 事故发生的时间，这个事故时间，有可能是经过推理，比如最近一次接管的时间
     */
    @Column(name = "accident_time")
    private Date accidentTime;

    /**
     * 安全员上报的事故时间
     */
    @Column(name = "report_accident_time")
    private Date reportAccidentTime;

    /**
     * 事故发生时车速
     */
    @Column(name = "speed")
    private Double speed;

    /**
     * 事故发生时车辆驾驶状态
     */
    @Column(name = "drive_mode_desc")
    private String driveModeDesc;

    /**
     * 事故发生的位置地名
     */
    @Column(name = "location_name")
    private String locationName;

    /**
     * 事故发生的位置经纬度坐标
     */
    @Column(name = "location_gps")
    private String locationGps;

    /**
     * 事故上报人的mis号
     */
    @Column(name = "reporter")
    private String reporter;

    /**
     * 近场安全员的mis号
     */
    @Column(name = "site_mis_id")
    private String siteMisId;

    /**
     * 远遥安全员的mis号
     */
    @Column(name = "remote_mis_id")
    private String remoteMisId;

    /**
     * 快速上传任务的id
     */
    @Column(name = "upload_task_id")
    private String uploadTaskId;

    /**
     * 事故描述
     */
    @Column(name = "accident_desc")
    private String accidentDesc;

    /**
     * 事故等级,-1|未知,1|险兆,2|灰色,3|蓝色,4|黄色,5|橙色,6|红色
     */
    @Column(name = "accident_level")
    private Integer accidentLevel;

    /**
     * 我方责任类型,-1|未知,1|无责,2|次责,3|同责,4|主责,5|全责
     */
    @Column(name = "responsibility_type")
    private Integer responsibilityType;

    /**
     * 事故类型,-1|未知,1|单方责任事故,2|三方责任事故
     */
    @Column(name = "accident_type")
    private Integer accidentType;

    /**
     * 自动驾驶责任模块
     */
    @Column(name = "responsibility_module")
    private String responsibilityModule;

    /**
     * 现场处置时间
     */
    @Column(name = "site_dispose_time")
    private Date siteDisposeTime;

    /**
     * 现场风险排除时间
     */
    @Column(name = "risk_elimination_time")
    private Date riskEliminationTime;

    /**
     * 事故直接原因, 1|自动驾驶系统;2|云控操作;3|近场操作;4|三方人员;5|内部其他人员操作不当;6|其他不可控;7|车载系统;8|云控系统;9|维修操作;10|业务运营操作;11|三方车辆;12|信号故障;13|硬件故障;14|自然灾害;15|意外坠物;16|地图&定位;17|感知;18|应急策略;19|决策&规划&控制;20|行为理解&预测;21|自动驾驶系统其他;22|三方其他人员;23|我方人员操作或维保不当;24|三方人员或车辆
     */
    @Column(name = "accident_responsibility")
    private String accidentResponsibility;

    /**
     * 事故责任描述
     */
    @Column(name = "accident_responsibility_desc")
    private String accidentResponsibilityDesc;

    /**
     * 是否报告交警, -1|未知,0|否,1|是
     */
    @Column(name = "is_call_police")
    private Integer isCallPolice;

    /**
     * 事故状态：10新建|11处理中|12处理完成
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 安全处置人
     */
    @Column(name = "security_group_disposer")
    private String securityGroupDisposer;

    /**
     * 处置升级类型
     */
    @Column(name = "dispose_upgrade_type")
    private String disposeUpgradeType;
    /**
     * 配件费
     */
    @Column(name = "accessory_cost")
    private BigDecimal accessoryCost;
    /**
     * 工时费
     */
    @Column(name = "labor_cost")
    private BigDecimal laborCost;
    /**
     * 预估我方车辆维修金额
     */
    @Column(name = "estimated_repair_vehicle_cost")
    private BigDecimal estimatedRepairVehicleCost;

    /**
     * 安全管理组响应时间
     */
    @Column(name = "security_group_response_time")
    private Date securityGroupResponseTime;

    /**
     * 事发路段,-1|未知;1|非机动车道/机动车道;2|交叉路口有灯/无灯;3|公开道路停车区域;4|内部道路/园区道路;5|园区道路交叉路口;6|园区停车区域;7|基地库房
     */
    @Column(name = "road_type")
    private Integer roadType;

    /**
     * 事故时我方车辆状态,-1|未知;1|直行;2|停车(完全断电停车);3|倒车;4|右转弯;5|左转弯;6|直行刹停;7|掉头;8|溜车;9|起步;10|变更车道;11|躲避障碍;12|超车
     */
    @Column(name = "vehicle_status")
    private Integer vehicleStatus;

    /**
     * 事故时双方车辆形态,-1|未知;1xx|车与车;2xx|车与人;3xx|单方事故
     */
    @Column(name = "two_sides_form")
    private String twoSidesForm;

    /**
     * 事故信息补充
     */
    @Column(name = "accident_info_supplement")
    private String accidentInfoSupplement;

    /**
     * 是否有舆情风险,-1|未知;0|没有;1|有
     */
    @Column(name = "is_public_opinion_risk")
    private Integer isPublicOpinionRisk;

    /**
     * 具体报道的媒体名称
     */
    @Column(name = "media_name")
    private String mediaName;

    /**
     * 处置策略
     */
    @Column(name = "disposal_strategy")
    private String disposalStrategy;

    /**
     * 复盘信息
     */
    @Column(name = "double_disc_info")
    private String doubleDiscInfo;

    /**
     * 事故间接原因,-1|未知;1|自动驾驶系统;2|云控操作;3|近场操作;4|三方人员;5|内部其他人员操作不当;6|其他不可控;7|车载系统;8|云控系统;9|维修操作;10|业务运营操作;11|三方车辆;12|信号故障;13|硬件故障;14|自然灾害;15|意外坠物;
     */
    @Column(name = "remote_cause")
    private String remoteCause;

    /**
     * 事故间接原因
     */
    @Column(name = "root_cause")
    private String rootCause;

    /**
     * 安全员是否违反准则
     */
    @Column(name = "is_security_officer_violated")
    private Integer isSecurityOfficerViolated;

    /**
     * 违反准则岗位,-1|未知,0|其他;1|云代驾;2|近场安全员
     */
    @Column(name = "violated_station")
    private String violatedStation;

    /**
     * 事故成本类型,-1|未知;1|我方总支出;2|我方获赔金额
     */
    @Column(name = "cost_type")
    private String costType;

    /**
     * 给对方的保险理赔金
     */
    @Column(name = "opposite_insurance_payment")
    private Double oppositeInsurancePayment;

    /**
     * 我方的保险理赔金
     */
    @Column(name = "our_insurance_payment")
    private Double ourInsurancePayment;

    /**
     * 应急赔付金
     */
    @Column(name = "emergency_payment")
    private Double emergencyPayment;

    /**
     * 车务维修金
     */
    @Column(name = "vehicle_maintenance_payment")
    private Double vehicleMaintenancePayment;

    /**
     * 我方获赔付金额
     */
    @Column(name = "compensation_awarded")
    private Double compensationAwarded;

    /**
     * 是否有人受伤,-1|未知;0|否;1|是
     */
    @Column(name = "is_anyone_injured")
    private Integer isAnyoneInjured;

    /**
     * 是否双方事故,-1|未知;0|否;1|是
     */
    @Column(name = "is_both_parties_accident")
    private Integer isBothPartiesAccident;

    /**
     * 现场是否有人拍照,-1|未知;0|否;1|是
     */
    @Column(name = "is_photograph")
    private Integer isPhotograph;

    /**
     * 人员伤亡情况
     */
    @Column(name = "personnel_injuries")
    private String personnelInjuries;

    /**
     * 修复版本号
     */
    @Column(name = "fix_version")
    private String fixVersion;

    /**
     * 是否hotfix,-1|未知;0|否;1|是
     */
    @Column(name = "is_fix_hotfix")
    private Integer isFixHotfix;

    /**
     * 修复Pr链接
     */
    @Column(name = "fix_pr")
    private String fixPr;

    /**
     * 是否需要验证,-1|未知;0|否;1|是
     */
    @Column(name = "is_fix_need_verify")
    private Integer isFixNeedVerify;

    /**
     * 不需要验证的原因
     */
    @Column(name = "fix_not_need_verify_reason")
    private String fixNotNeedVerifyReason;

    /**
     * 验证结论,-1|未知;1|验证通过;2|验证不通过;3|无法验证;4|其他
     */
    @Column(name = "fix_verify_result")
    private Integer fixVerifyResult;

    /**
     * 验证描述
     */
    @Column(name = "fix_verify_desc")
    private String fixVerifyDesc;

    /**
     * 事故上报来源,-1|未知;1|云分诊;2|手机h5|3;云代驾触屏
     */
    @Column(name = "source")
    private Integer source;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "affiliation")
    private Integer affiliation;

    @Column(name = "place")
    private String place;

    @Column(name = "purpose")
    private String purpose;
    /**
     * 三方是否离开
     */
    @Column(name = "is_third_party_leave")
    private Integer isThirdPartyLeave;

    /**
     * 车辆碰撞情况,-1|未知;0|严重;1|轻微;2|疑似
     */
    @Column(name = "collision_severity")
    private Integer collisionSeverity;

    /**
     * MPA 数据
     */
    @Column(name = "mpa")
    private String mpa;

    /**
     * 关联事件ID
     */
    @Column(name = "related_event_id")
    private String relatedEventId;

    /**
     * 预计修复时间
     */
    @Column(name = "expect_repair_time")
    private Date expectRepairTime;

    /**
     * 修复完成时间
     */
    @Column(name = "finish_repair_time")
    private Date finishRepairTime;

    /**
     * 验收时间
     */
    @Column(name = "acceptance_time")
    private Date acceptanceTime;

    /**
     * 测试复盘信息
     */
    @Column(name = "test_debrief_info")
    private String testDebriefInfo;

    /**
     * 是否复现
     */
    @Column(name = "simulation_status")
    private Byte simulationStatus;

    /**
     * hotfix链接
     */
    @Column(name = "hotfix_url")
    private String hotfixUrl;

    /**  状态：1开启、2修复中、3已修复、4已验证、5重启 **/
    @Column(name = "fix_status")
    private Integer fixStatus;
}