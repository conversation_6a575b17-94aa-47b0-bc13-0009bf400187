package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> @Beijing
 * @date 2022/01/21
 * Description:
 * Modified by
 */
@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ColdRecordLevelPO implements Serializable {

    private Long id;
    private String recordName;
    private Integer s3Level;
    private Integer isDeleted;
    private Date createTime;
    private Date updateTime;

}
