package com.meituan.walle.data.center.config;

import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.uac.sdk.filter.UacFilterFactoryBean;
import com.sankuai.meituan.uac.sdk.service.UacAuthRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.DelegatingFilterProxy;

import static javax.servlet.DispatcherType.REQUEST;

@Configuration
@Slf4j
public class UacFilterConfiguration {

    private static String APP_KEY = null;

    private static String APP_SECRET = null;

    private static final String KMS_UAC_APPKEY = "data.center.uac.appkey";

    private static final String KMS_UAC_SECRET = "data.center.uac.secret";

    @Value("${uac.host}")
    private String uacHost;

    static {
        try {
            APP_KEY = Kms.getByName(MccConstant.MCC_APPKEY, KMS_UAC_APPKEY);
            APP_SECRET = Kms.getByName(MccConstant.MCC_APPKEY, KMS_UAC_SECRET);
        } catch (KmsResultNullException e) {
            log.error("get uac appkey or secret failed", e);
        }
    }

    @Bean
    public FilterRegistrationBean uacFilter() {
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        FilterRegistrationBean registration = new FilterRegistrationBean();
        filter.setTargetBeanName("uacFilterBean");
        filter.setTargetFilterLifecycle(true);

        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setDispatcherTypes(REQUEST);
        registration.setName("uacFilter");

        // 注意顺序应设置在SSO filter之后
        registration.setOrder(4);
        return registration;
    }

    /**
     * uacFilterBean配置
     */
    @Bean
    public UacFilterFactoryBean uacFilterBean() {
        UacFilterFactoryBean filterFactoryBean = new UacFilterFactoryBean();
        // 必须配置，以下二者需先到开放平台(http://open.sankuai.com)申请接入UAC后颁发，对应企平开放平台的AppKey和AppSecret
        filterFactoryBean.setAppKey(APP_KEY);
        filterFactoryBean.setSecret(APP_SECRET);

        // 接入的UAC HOST地址，线下为：http://uac.it.test.sankuai.com 线上为：http://uac.vip.sankuai.com
        filterFactoryBean.setHost(uacHost);

        // ------------------------以下配置为可选配置--------------------------------------
        // 表示需要经过UAC鉴权的URI，多个之间以','分割,支持Ant风格路径表达式。
        // includedUriList中的URI必须经过SSO进行过滤，否则UAC无法获取当前登录人信息进行鉴权。
        filterFactoryBean.setIncludedUriList("/vehicle_event/sso_test");

        // 表示不需要经过UAC鉴权的URI。两者都配置的情况下，excludedUriList会失效，includedUriList优先级更高。
        // filterFactoryBean.setExcludedUriList("/octo/checkAlive/**,/pass/**");

        // UAC授权失败后返回的错误信息
        // filterFactoryBean.setAuthFailedResponse("{\"status\":0,\"message\":\"您没有权限访问\",\"version\":\"1.0.0-SNAPSHOT\",\"data\":{\"message\":\"您没有权限访问\"}}");

        // 是否关闭UAC日志功能（URI鉴权过程打印一些日志，方便调试），true表示关闭，默认为false，即开启日志功能。
        //filterFactoryBean.setLogClosed(true);

        // URL鉴权时是否需要对HTTP方法进行鉴权
        // filterFactoryBean.setAuthUrlWithMethod(true);
        return filterFactoryBean;
    }

    @Bean
    public UacAuthRemoteService uacAuthRemoteService() {
        return new UacAuthRemoteService();
    }

    public static String getAppKey() {
        return APP_KEY;
    }

    public static String getAppSecret() {
        return APP_SECRET;
    }

}
