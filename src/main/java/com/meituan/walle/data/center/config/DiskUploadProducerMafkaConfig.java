package com.meituan.walle.data.center.config;

import com.meituan.mafka.client.producer.IProducerProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

/**
 * <AUTHOR>
 * @date 2023/02/09
 */
@Slf4j
@Configuration
public class DiskUploadProducerMafkaConfig extends AbstractDiskUploadMafkaConfig {

    @Lazy
    @Bean(value = "diskUploadFileProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> diskUploadFileProducer() throws Exception {
        return buildProducer(TOPIC_S3_DISK_UPLOAD_FILE_NOTICE, appkey);
    }

    @Lazy
    @Bean(value = "diskUploadFileDelayProducer", destroyMethod = "close")
    public IProducerProcessor<String, String> diskUploadFileDelayProducer() throws Exception {
        return buildDelayProducer(TOPIC_S3_DISK_UPLOAD_FILE_NOTICE, appkey);
    }
}