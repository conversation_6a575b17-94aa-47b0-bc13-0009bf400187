package com.meituan.walle.data.center.entity.dto;

import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OctoProviderStatusDTO implements Serializable {

    @NonNull
    private String appkey;
    private Integer type;
    private Integer env;
    private Integer status;
    private String ip;
    private Integer pageSize;

    @Override
    public String toString() {
        return "OctoProviderStatusDTO{" +
                "appkey='" + appkey + '\'' +
                ", type=" + type +
                ", env=" + env +
                ", status=" + status +
                ", ip='" + ip + '\'' +
                ", pageSize=" + pageSize +
                '}';
    }

}