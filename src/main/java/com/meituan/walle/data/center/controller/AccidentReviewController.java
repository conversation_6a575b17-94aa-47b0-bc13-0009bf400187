package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.pojo.BizAccidentReview;
import com.meituan.walle.data.center.entity.request.BizAccidentReviewRequest;
import com.meituan.walle.data.center.entity.response.PageResponse;
import com.meituan.walle.data.center.service.BizAccidentReviewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/05
 */
@InterfaceDoc(
        displayName = "事故分析相关接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供事故分析管理的能力。",
        scenarios = "事故分析所有接口"
)
@RestController
@RequestMapping(value = "/accident_review")
@Slf4j
public class AccidentReviewController {

    @Resource
    private BizAccidentReviewService accidentReviewService;

    @MethodDoc(
            displayName = "获取事故分析列表",
            description = "根据条件获取事故分析列表",
            parameters = {
                    @ParamDoc(name = "BizAccidentReviewRequest", description = "事故分析请求参数")
            },
            returnValueDescription = "事故分析列表",
            restExampleResponseData = "{\n" +
                    "\t\"ret\": \"0\",\n" +
                    "\t\"msg\": \"ok\",\n" +
                    "\t\"data\": {\n" +
                    "\t\t\"page\": 1,\n" +
                    "\t\t\"size\": 10,\n" +
                    "\t\t\"total\": 1,\n" +
                    "\t\t\"result\": [{\n" +
                    "\t\t\t\"id\": 2,\n" +
                    "\t\t\t\"accidentId\": \"1\",\n" +
                    "\t\t\t\"level\": \"blue\",\n" +
                    "\t\t\t\"cause\": \"cause\",\n" +
                    "\t\t\t\"damage\": \"damage\",\n" +
                    "\t\t\t\"compensation\": \"comp\",\n" +
                    "\t\t\t\"impact\": \"impact\",\n" +
                    "\t\t\t\"confirmResponsibility\": \"cfr\",\n" +
                    "\t\t\t\"rewardPunishment\": \"rp\",\n" +
                    "\t\t\t\"suggestion\": \"s\",\n" +
                    "\t\t\t\"isDeleted\": 0,\n" +
                    "\t\t\t\"createTime\": 1636447023000,\n" +
                    "\t\t\t\"updateTime\": 1636447023000\n" +
                    "\t\t}]\n" +
                    "\t}\n" +
                    "}"
    )
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Response query(BizAccidentReviewRequest accidentReviewRequest) {
        Transaction t = Cat.newTransaction(CatConstant.ACCIDENT_REVIEW, "accident.review.list");
        long start = System.currentTimeMillis();
        if (accidentReviewRequest == null) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param is illegal");
        }
        accidentReviewRequest.setIsDeleted("0");
        PageResponse queryDTO = new PageResponse();
        try {
            List<BizAccidentReview> tmpList = accidentReviewService.queryByPage(accidentReviewRequest);
            if (tmpList == null) {
                t.setDurationInMillis(System.currentTimeMillis() - start);
                t.setSuccessStatus();
                t.complete();
                return Response.fail("1", "get accident review list fail");
            }
            List<BizAccidentReview> accidentReviewList = accidentReviewService.queryByPage(accidentReviewRequest);
            queryDTO.setPage(accidentReviewRequest.getPage());
            queryDTO.setSize(accidentReviewRequest.getSize());
            queryDTO.setTotal(accidentReviewService.countByPage(accidentReviewRequest));
            queryDTO.setResult(accidentReviewList);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(queryDTO);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Get accident list fail, request param is {}", accidentReviewRequest, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "get accident list fail");
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "获取事故分析详情",
            description = "根据ID获取事故分析详情",
            parameters = {
                    @ParamDoc(name = "id", description = "事故分析ID")
            },
            returnValueDescription = "事故分析详细信息",
            restExampleResponseData = "{\n" +
                    "\t\"ret\": \"0\",\n" +
                    "\t\"msg\": \"ok\",\n" +
                    "\t\"data\": {\n" +
                    "\t\t\"id\": 2,\n" +
                    "\t\t\"accidentId\": \"1\",\n" +
                    "\t\t\"level\": \"blue\",\n" +
                    "\t\t\"cause\": \"cause\",\n" +
                    "\t\t\"damage\": \"damage\",\n" +
                    "\t\t\"compensation\": \"comp\",\n" +
                    "\t\t\"impact\": \"impact\",\n" +
                    "\t\t\"confirmResponsibility\": \"cfr\",\n" +
                    "\t\t\"rewardPunishment\": \"rp\",\n" +
                    "\t\t\"suggestion\": \"s\",\n" +
                    "\t\t\"isDeleted\": 0,\n" +
                    "\t\t\"createTime\": 1636447023000,\n" +
                    "\t\t\"updateTime\": 1636447023000\n" +
                    "\t}\n" +
                    "}"
    )
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public Response detail(@RequestParam Long id) {
        Transaction t = Cat.newTransaction(CatConstant.ACCIDENT_REVIEW, "accident.review.detail");
        long start = System.currentTimeMillis();
        if (id == null) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param is illegal");
        }
        try {
            BizAccidentReview accidentReview = accidentReviewService.getById(id);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(accidentReview);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Get accident review by id fail, request param is {}", id, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "get accident review by id fail");
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "新增事故分析",
            description = "新增事故分析",
            parameters = {
                    @ParamDoc(name = "BizAccidentReview", description = "事故分析对象")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Response add(@RequestBody BizAccidentReview accidentReview) {
        Transaction t = Cat.newTransaction(CatConstant.ACCIDENT_REVIEW, "accident.review.add");
        long start = System.currentTimeMillis();
        if (accidentReview == null) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param is illegal");
        }
        try {
            accidentReviewService.add(accidentReview);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ("");
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Add accident review fail, request param is {}", accidentReview, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "add accident review fail");
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "更新事故分析",
            description = "更新事故分析",
            parameters = {
                    @ParamDoc(name = "BizAccidentReview", description = "事故分析对象")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public Response update(@RequestBody BizAccidentReview accidentReview) {
        Transaction t = Cat.newTransaction(CatConstant.ACCIDENT_REVIEW, "accident.review.update");
        long start = System.currentTimeMillis();
        if (accidentReview == null) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param is illegal");
        }
        try {
            accidentReviewService.update(accidentReview);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ("");
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Update accident review fail, request param is {}", accidentReview, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "update accident review fail");
        } finally {
            t.complete();
        }
    }
}
