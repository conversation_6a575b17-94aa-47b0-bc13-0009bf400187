package com.meituan.walle.data.center.entity.dto;

import com.meituan.walle.data.center.entity.pojo.OnesIssue;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/18 下午5:02
 * Description:
 * Modified by
 */
@Getter
@Setter
public class OnesIssueDTO extends OnesIssue {


    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Body implements Serializable {
        private String recordName;
        private String caseId;
        private String vin;
        private String vehicleName;
        private String vehicleType;
        private String hdmapVersion;
        private String packageVersion;
        private String gitBranch;
        private String gitCommit;
        private String caseTime;
        private String longitude;
        private String latitude;
        private String x;
        private String y;
        private String source;
        private String extend;
        private String logUrl;
        private String sysLogUrl;

        @Override
        public String toString() {
            String template = "<p><strong>recordName：</strong>" +
                    "<a href='http://wop.sankuai.com/app/record/%s?case_id=%s' " +
                    "target='_blank' style='color: #1C6CDC;text-decoration: underline;'>%s</a></p>" +
                    "<p><strong>车架号：</strong>%s</p>" +
                    "<p><strong>车辆名：</strong>%s</p>" +
                    "<p><strong>车辆类型：</strong>%s</p>" +
                    "<p><strong>CASE发生时间：</strong>%s</p>" +
                    "<p><strong>高精地图版本：</strong>%s</p>" +
                    "<p><strong>package-version：</strong>%s</p>" +
                    "<p><strong>git-branch：</strong>%s</p>" +
                    "<p><strong>git-commit：</strong>%s</p>" +
                    "<p><strong>UTM坐标-x：</strong>%s</p>" +
                    "<p><strong>UTM坐标-y：</strong>%s</p>" +
                    "<p><strong>source：</strong>%s</p>" +
                    "<p>%s</p>" +
//                    "<p><strong>log下载：</strong></p>%s" +
//                    "<p><strong>sys log下载：</strong></p>%s" +
                    "";
            return String.format(template,
                    recordName, caseId, recordName, vin, vehicleName,
                    vehicleType, caseTime,
                    hdmapVersion, packageVersion, gitBranch, gitCommit,
                    x == null ? "" : x,
                    y == null ? "" : y,
                    source,
                    extend
//                    ,logUrl
//                    ,sysLogUrl
            );
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Title implements Serializable {

        private String caseId;

        @Override
        public String toString() {
            return caseId;
        }
    }


}
