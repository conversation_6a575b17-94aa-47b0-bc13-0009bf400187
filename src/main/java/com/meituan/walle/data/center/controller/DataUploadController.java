package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.request.ExternalVehicleRequest;
import com.meituan.walle.data.center.service.DataUploadService;
import com.meituan.walle.data.center.util.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/02/14
 */

@RestController
@RequestMapping(value = "/data_upload")
@Slf4j
public class DataUploadController {

    @Resource
    private DataUploadService dataUploadService;

    @PostMapping("/cruise_data")
    public Response cruiseData(@RequestBody ExternalVehicleRequest request) {
        String traceId = Trace.generateId();
        if (StringUtils.isBlank(request.getDataContent()) || StringUtils.isBlank(request.getDataType())) {
            log.info("external data request lack some parameters, request=[{}]", request);
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    WebResponseStatusEnum.PARAMETER_ERROR.getMsg());
        }
        try {
            dataUploadService.insertCruiseData(request);
            return Response.succ();
        } catch (Exception e) {
            log.error("traceId: {}, external data upload failed, request is [{}]", traceId, request, e);
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }
}
