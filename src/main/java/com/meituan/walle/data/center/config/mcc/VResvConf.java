package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

public class VResvConf {
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vresv.purpose.url")
    public static volatile String purposeUrl = "http://wop.sankuai.com";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vresv.task.url")
    public static volatile String vresv_task_url = "https://wop.sankuai.com/app/api/vresv/get_vresv_tasks";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vresv.task.base.url")
    public static volatile String vresv_base_url = "https://walledata.mad.test.sankuai.com/app/api/vresv";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vresv.extra.notice.members")
    public static volatile String vresv_extra_notice_members = "liuqichun,zhangchenyu07";

}
