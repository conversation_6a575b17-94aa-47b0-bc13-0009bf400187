package com.meituan.walle.data.center.component;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.meituan.walle.data.center.config.mcc.AccidentConfig;
import com.meituan.walle.data.center.config.mcc.FastUploadConfig;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.CharConstant;
import com.meituan.walle.data.center.constant.CommonConstant;
import com.meituan.walle.data.center.constant.MccConstant;
import com.meituan.walle.data.center.entity.dto.AccidentInfoDTO;
import com.meituan.walle.data.center.entity.dto.DemandQueryResultDTO;
import com.meituan.walle.data.center.entity.enums.AffiliationEnum;
import com.meituan.walle.data.center.entity.enums.VehiclePurposeEnum;
import com.meituan.walle.data.center.entity.pojo.BizAccidentGroupRule;
import com.meituan.walle.data.center.entity.pojo.BizAccidentInfo;
import com.meituan.walle.data.center.entity.pojo.BizMapArea;
import com.meituan.walle.data.center.entity.pojo.RecordV2;
import com.meituan.walle.data.center.entity.vo.VresvVO;
import com.meituan.walle.data.center.mapper.BizAccidentGroupRuleMapper;
import com.meituan.walle.data.center.mapper.BizMapAreaMapper;
import com.meituan.walle.data.center.mapper.RecordV2Mapper;
import com.meituan.walle.data.center.service.VresvRecordHistoryService;
import com.meituan.walle.data.center.thread.RedisProductor;
import com.meituan.walle.data.center.util.DatetimeUtil;
import com.meituan.walle.data.center.util.DxAppUtil;
import com.sankuai.xm.ems.open.EmsGroupOpenThriftService;
import com.sankuai.xm.ems.open.model.CommonResp;
import com.sankuai.xm.ginfo.thrift.service.GinfoOpenThriftClientService;
import com.sankuai.xm.openplatform.api.entity.*;
import com.sankuai.xm.openplatform.api.service.open.XmOpenGroupServiceI;
import com.sankuai.xm.openplatform.api.service.open.XmOpenMessageServiceI;
import com.sankuai.xm.openplatform.auth.entity.AccessToken;
import com.sankuai.xm.openplatform.auth.entity.AccessTokenResp;
import com.sankuai.xm.openplatform.auth.entity.AppAuthInfo;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import com.sankuai.xm.openplatform.common.entity.EmptyResp;
import com.sankuai.xm.openplatform.common.entity.RespStatus;
import com.sankuai.xm.udb.thrift.UdbOpenThriftClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DxGroupHandler {

    @Value("${tenant}")
    private String tenant;

    @MdpConfig("sc.daxiangvideo.thread.queryTimeConfig")
    HashMap<String,Long> queryTimeConfig;

    @MdpConfig("sc.daxiangvideo.urltext")
    public String videoMsg = "";

    @Resource
    private UdbOpenThriftClient udbOpenThriftClient;

    @Resource
    private RecordV2Mapper recordV2Mapper;

    @Resource
    private BizMapAreaMapper bizMapAreaMapper;

    @Resource
    private BizAccidentGroupRuleMapper bizAccidentGroupRuleMapper;

    @Resource
    private GinfoOpenThriftClientService openThriftClientService;

    @Resource
    private EmsGroupOpenThriftService.Iface emsGroupOpenThriftService;

    @Resource
    private VresvRecordHistoryService vresvRecordHistoryService;

    @Resource
    private XmOpenGroupServiceI.Iface openGroupThriftService;

    @Resource
    private XmAuthServiceI.Iface openAuthThriftService;

    @Resource
    private XmOpenMessageServiceI.Iface openMessageThriftService;

    @Resource
    RedisProductor redisProductor;

    private static final String ALL = "all";

    private static final String ACCIDENT_GROUP_ROBOT_APP_ID_KMS = "accident.group.robot.app.id";

    private String accidentGroupRobotAppId;

    private static final String ACCIDENT_GROUP_ROBOT_APP_SECRET_KMS = "accident.group.robot.app.secret";

    private String accidentGroupRobotAppSecret;

    private AccessToken accessToken = null;

    private static final int ADD_MEMBER_LIMIT = 45;

    private static final int UID_NO_EXISTS_CODE = 60001;

    private static final String EXCEPTION_UID_STR = "异常uid";

    @PostConstruct
    public void init() {
        try {
            accidentGroupRobotAppId = Kms.getByName(MccConstant.MCC_APPKEY, ACCIDENT_GROUP_ROBOT_APP_ID_KMS);
            accidentGroupRobotAppSecret = Kms.getByName(MccConstant.MCC_APPKEY, ACCIDENT_GROUP_ROBOT_APP_SECRET_KMS);
        } catch (KmsResultNullException e) {
            throw new RuntimeException(e);
        }
    }

    public void getOrUpdateAccessToken() throws TException {
        long expireGap = CommonConstant.SECOND_TO_MINUTE * CommonConstant.MILLIS_TO_SECOND;
        if (accessToken != null && System.currentTimeMillis() + expireGap < accessToken.expireTime) {
            return;
        }

        AppAuthInfo appAuthInfo = new AppAuthInfo();
        appAuthInfo.setAppkey(accidentGroupRobotAppId);
        appAuthInfo.setAppSecret(accidentGroupRobotAppSecret);
        AccessTokenResp accessTokenResp = openAuthThriftService.accessToken(appAuthInfo);
        accessToken = accessTokenResp.getAccessToken();
    }

    public Long createDxGroup(Set<String> members, String groupName) throws Exception {
        getOrUpdateAccessToken();
        log.info("To create group room, tenant is [{}]", tenant);
        Set<Long> groupManagerUid = new HashSet<>();
        Set<Long> membersUid = new HashSet<>();
        for (String id : members) {
            Long uid = udbOpenThriftClient.getUid(id, tenant);
            if(uid == 0){
                continue;
            }
            membersUid.add(uid);
        }
        log.info("To create group room, request param is [{}], [{}], [{}]",
                groupManagerUid, membersUid, groupName);
        CreateGroupReq createGroupReq = new CreateGroupReq();
        if (StringUtils.isNotBlank(groupName) && groupName.length() > SysParamsConfig.dx_group_name_max_len) {
            groupName = groupName.substring(0, SysParamsConfig.dx_group_name_max_len);
        }
        createGroupReq.setName(groupName);

        List<Long> moreThanLimitMembers = null;
        if (membersUid.size() <= ADD_MEMBER_LIMIT) {
            createGroupReq.setUsers(membersUid);
        } else {
            moreThanLimitMembers = new ArrayList<>();
            Set<Long> addMember = new HashSet<>();
            for (Long uid : membersUid) {
                if (addMember.size() < ADD_MEMBER_LIMIT) {
                    addMember.add(uid);
                } else {
                    moreThanLimitMembers.add(uid);
                }
            }
            createGroupReq.setUsers(addMember);
        }

        createGroupReq.setAdmins(groupManagerUid);
        CreateGroupResp createGroupResp = createGroupRemoveExceptionUid(createGroupReq);

        if (createGroupResp.gid < 1) {
            log.error("create group failed: {}, param: {} ", createGroupResp, createGroupReq);
            return 0L;
        }
        if (moreThanLimitMembers != null && !moreThanLimitMembers.isEmpty()) {
            List<List<Long>> partition = Lists.partition(moreThanLimitMembers, ADD_MEMBER_LIMIT);
            for (List<Long> list : partition) {
                AddGroupMembersReq addGroupMembersReq = new AddGroupMembersReq();
                addGroupMembersReq.setUsers(new HashSet<>(list));
                addGroupMembersReq.setGid(createGroupResp.gid);
                AddGroupMembersResp addGroupMembersResp = addGroupMemberRemoveExceptionUid(addGroupMembersReq);

                //创建群聊后直接拉人进群有概率出现机器人不存在的情况，原因是主从同步延迟原因
                int retryCount = 3;
                while( addGroupMembersResp.status.getCode() != 0 && retryCount > 0){
                    log.error("Add group member failed after create group : {}, groupId: {}, retryCount: {}",
                            addGroupMembersResp, createGroupResp.gid, retryCount);
                    addGroupMembersResp = addGroupMemberRemoveExceptionUid(addGroupMembersReq);
                    retryCount--;
                }

                if (addGroupMembersResp.status.getCode() != 0) {
                    log.error("Add group member failed after create group : {}, groupId: {}, users: {}",
                            addGroupMembersResp, createGroupResp.gid, list);
                    removeDxGroups(createGroupResp.gid + "");
                    return 0L;
                }
            }
        }
        return createGroupResp.gid;
    }

    public Set<String> getSafetyMembers(String vin, Date accidentTime) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(accidentTime);
        instance.add(Calendar.HOUR, -1);
        String startTime = DatetimeUtil.format(instance.getTime());
        String endTime = DatetimeUtil.format(accidentTime);
        List<VresvVO> vresvVOS = vresvRecordHistoryService.callVresvByBeginAndEndTime(vin, startTime, endTime);
        Set<String> safetyMemberList = new HashSet<>();
        for (VresvVO vo : vresvVOS) {
            if (StringUtils.isNotBlank(vo.getSubstitute())) {
                safetyMemberList.addAll(Arrays.asList(vo.getSubstitute().split(CharConstant.CHAR_DD)));
            }
            if (StringUtils.isNotBlank(vo.getTelecontrol())) {
                safetyMemberList.addAll(Arrays.asList(vo.getTelecontrol().split(CharConstant.CHAR_DD)));
            }
            if (StringUtils.isNotBlank(vo.getRoadTest())) {
                safetyMemberList.addAll(Arrays.asList(vo.getRoadTest().split(CharConstant.CHAR_DD)));
            }
        }
        return safetyMemberList;
    }

    public Set<String> getAllTypesSafetyMembers(String vin, String startTime, String endTime) {
        List<VresvVO> vresvVOs = vresvRecordHistoryService.callVresvByBeginAndEndTime(vin, startTime, endTime);
        Set<String> safetyMemberList = new HashSet<>();
        for (VresvVO vo : vresvVOs) {
            if (StringUtils.isNotBlank(vo.getSubstitute())) {
                safetyMemberList.addAll(Arrays.asList(vo.getSubstitute().split(CharConstant.CHAR_DD)));
            }
            if (StringUtils.isNotBlank(vo.getTelecontrol())) {
                safetyMemberList.addAll(Arrays.asList(vo.getTelecontrol().split(CharConstant.CHAR_DD)));
            }
            if (StringUtils.isNotBlank(vo.getRoadTest())) {
                safetyMemberList.addAll(Arrays.asList(vo.getRoadTest().split(CharConstant.CHAR_DD)));
            }
            if (StringUtils.isNotBlank(vo.getDelivery())) {
                safetyMemberList.addAll(Arrays.asList(vo.getDelivery().split(CharConstant.CHAR_DD)));
            }
            if (StringUtils.isNotBlank(vo.getRescue())) {
                safetyMemberList.addAll(Arrays.asList(vo.getRescue().split(CharConstant.CHAR_DD)));
            }
        }
        return safetyMemberList;
    }

    private CreateGroupResp createGroupRemoveExceptionUid(CreateGroupReq req) throws Exception {
        CreateGroupResp createGroupResp = openGroupThriftService.createGroup(accessToken.token, req);
        RespStatus status = createGroupResp.getStatus();
        String msg = status.getMsg();
        int count = req.getUsers().size();
        while( status.getCode() != 0 && msg.contains(EXCEPTION_UID_STR) && count > 0){
            log.error("Create group not exists uid: {}, param: {},", createGroupResp, req);
            removeExceptionUid(msg, req.getUsers());
            createGroupResp =  openGroupThriftService.createGroup(accessToken.token, req);
            status = createGroupResp.getStatus();
            msg = status.getMsg();
            count = count -1;
        }
//        if (status.getCode() != 0 && msg.contains(EXCEPTION_UID_STR)) {
//            log.error("Create group not exists uid: {}, param: {},", createGroupResp, req);
//            removeExceptionUid(msg, req.getUsers());
//            return openGroupThriftService.createGroup(accessToken.token, req);
//        }
        return createGroupResp;
    }

    private AddGroupMembersResp addGroupMemberRemoveExceptionUid(AddGroupMembersReq req) throws Exception {
        AddGroupMembersResp addGroupMembersResp = openGroupThriftService.addGroupMembersByBot(accessToken.token, req);
        RespStatus status = addGroupMembersResp.getStatus();
        String msg = status.getMsg();

        int count = req.getUsers().size();
        while (status.getCode() != 0 && msg.contains(EXCEPTION_UID_STR) && count > 0) {
            log.error("Add group member not exists uid: {}, param: {},", addGroupMembersResp, req);
            removeExceptionUid(msg, req.getUsers());
            addGroupMembersResp = openGroupThriftService.addGroupMembersByBot(accessToken.token, req);
            status = addGroupMembersResp.getStatus();
            msg = status.getMsg();
            count = count - 1;
        }
        return addGroupMembersResp;
    }

//    private void removeExceptionUid(String msg, Set<Long> users) {
//        String[] uidArr = msg.substring(msg.indexOf(EXCEPTION_UID_STR) + EXCEPTION_UID_STR.length())
//                .replace(CharConstant.CHAR_LKH, "")
//                .replace(CharConstant.CHAR_RKH, "")
//                .replace(CharConstant.CHAR_BK, "")
//                .split(CharConstant.CHAR_DD);
//        for (String uidStr : uidArr) {
//            users.remove(Long.valueOf(uidStr));
//        }
//    }

    private void removeExceptionUid(String msg, Set<Long> users) {
        String[] msgList = msg.split(":");
        String uidsMsg = msgList[1].trim();
        String[] uidArray;
        if(uidsMsg.startsWith("[")){
            uidsMsg = uidsMsg.substring(1, uidsMsg.length()-1);
            uidArray = uidsMsg.split(",");
        }else{
            uidArray = new String[]{uidsMsg.trim()};
        }
        for (String uid: uidArray) {
            users.remove(Long.parseLong(uid.trim()));
        }
    }

    public Long createGroupRoom(String misId, Set<String> groupManager,
                                Set<String> members, String groupName) throws Exception {
        log.info("To create group room, tenant is [{}]", tenant);
        Long groupUid = udbOpenThriftClient.getUid(misId, tenant);
        Set groupManagerUid = new HashSet();
        for (String id : groupManager) {
            Long uid = udbOpenThriftClient.getUid(id, tenant);
            groupManagerUid.add(uid);
        }
        Set membersUid = new HashSet();
        for (String id : members) {
            Long uid = udbOpenThriftClient.getUid(id, tenant);
            membersUid.add(uid);
        }
        log.info("To create group room, request param is [{}], [{}], [{}], [{}]",
                groupUid, groupManagerUid, membersUid, groupName);
        Long result = openThriftClientService.createRoomAndSetPub(
                groupUid, groupManagerUid, membersUid, 1, groupName, true,
                DxAppUtil.getDxAppPubId());
        return result;
    }

    public void createGroupAndSendMsgOutDated(AccidentInfoDTO accidentInfoDTO) {
        BizAccidentInfo bizAccidentInfo = accidentInfoDTO.getBizAccidentInfo();
        String accidentGroupOwner = AccidentConfig.accident_group_owner;
        Set groupManager = Collections.emptySet();
        String members = "";
        if (StringUtils.isNotBlank(bizAccidentInfo.getReporter())) {
            members += bizAccidentInfo.getReporter() + CharConstant.CHAR_DD;
        }
        RecordV2 recordV2 = recordV2Mapper.selectByRecordName(bizAccidentInfo.getRecordName());
        BizMapArea bizMapArea = bizMapAreaMapper.queryByCode(recordV2.getPlace());
        if (bizMapArea == null) {
            bizMapArea = new BizMapArea();
            bizMapArea.setAffiliation(AffiliationEnum.ALL.getCode());
            bizMapArea.setCity(CommonConstant.UNKNOWN);
        }
        String purpose = recordV2.getPurpose();
        if (StringUtils.isBlank(purpose)) {
            DemandQueryResultDTO resvInfo = vresvRecordHistoryService.getPurposeFromVresvSystem(
                    bizAccidentInfo.getVin(), recordV2.getBeginTime());
            if (Objects.isNull(resvInfo)) {
                purpose = CharConstant.CHAR_EMPTY;
            } else {
                purpose = resvInfo.getUsedTypeName();
            }
        }
        List<BizAccidentGroupRule> groupRules = bizAccidentGroupRuleMapper.queryAll();
        for (BizAccidentGroupRule groupRule : groupRules) {
            if (groupRule.getAffiliation() == AffiliationEnum.ALL.getCode() ||
                    groupRule.getAffiliation() == bizMapArea.getAffiliation()) {
                if (ALL.equals(groupRule.getCity()) || bizMapArea.getCity().equals(groupRule.getCity())) {
                    if (ALL.equals(groupRule.getPurpose()) || purpose.equals(groupRule.getPurpose())) {
                        log.info("These members pass filter, add to create group room, " +
                                        "affiliation is [{}], purpose is [{}], city is [{}], members is [{}]",
                                bizMapArea.getAffiliation(), purpose,
                                bizMapArea.getCity(), groupRule.getMembers());
                        members += groupRule.getMembers() + CharConstant.CHAR_DD;
                    } else if (!VehiclePurposeEnum.OPERATION_PURPOSE.getMsg().equals(purpose) &&
                            VehiclePurposeEnum.OTHER.getMsg().equals(groupRule.getPurpose())) {
                        log.info("These members pass filter, add to create group room, " +
                                        "affiliation is [{}], purpose is [{}], city is [{}], members is [{}]",
                                bizMapArea.getAffiliation(), purpose,
                                bizMapArea.getCity(), groupRule.getMembers());
                        members += groupRule.getMembers() + CharConstant.CHAR_DD;
                    }
                }
            }
        }
        members = members.substring(0, members.length() - 1);
        Set<String> groupMembers = new HashSet<>(Splitter.on(CharConstant.CHAR_DD).splitToList(members));
        long groupId = 0L;
        try {
            log.info("Start to create group room, groupOwner is [{}], groupMembers is [{}], groupTitle is [{}]",
                    accidentGroupOwner, groupMembers, accidentInfoDTO.getGroupTitle());
            groupId = createGroupRoom(
                    accidentGroupOwner, groupManager, groupMembers, accidentInfoDTO.getGroupTitle());
        } catch (Exception e) {
            log.error("Auto create group failed, accident is [{}]", bizAccidentInfo, e);
        }
        if (groupId > 0) {
            sendMsgToDxGroup(groupId, accidentInfoDTO);
            //写 redis
           // writeMessageDataToRedis(groupId, accidentInfoDTO, 1);
            makeAccidentAnnouncementAndUpdate(groupId, accidentInfoDTO);
        }

    }

    @Async
    public void createGroupAndSendMsg(AccidentInfoDTO accidentInfoDTO) {
        BizAccidentInfo bizAccidentInfo = accidentInfoDTO.getBizAccidentInfo();
        String accidentGroupOwner = AccidentConfig.accident_group_owner;
        Set<String> groupMembers = new HashSet<>();
        if (StringUtils.isNotBlank(bizAccidentInfo.getReporter())) {
            groupMembers.addAll(Arrays.asList(bizAccidentInfo.getReporter().split(CharConstant.CHAR_DD)));
        }
        Set<String> safetyMembers = getSafetyMembers(bizAccidentInfo.getVin(), bizAccidentInfo.getAccidentTime());
        groupMembers.addAll(safetyMembers);
        RecordV2 recordV2 = recordV2Mapper.selectByRecordName(bizAccidentInfo.getRecordName());
        BizMapArea bizMapArea = bizMapAreaMapper.queryByCode(recordV2.getPlace());
        if (bizMapArea == null) {
            bizMapArea = new BizMapArea();
            bizMapArea.setAffiliation(AffiliationEnum.ALL.getCode());
            bizMapArea.setCity(CommonConstant.UNKNOWN);
        }
        String purpose = recordV2.getPurpose();
        if (StringUtils.isBlank(purpose)) {
            DemandQueryResultDTO resvInfo = vresvRecordHistoryService.getPurposeFromVresvSystem(
                    bizAccidentInfo.getVin(), recordV2.getBeginTime());
            if (Objects.isNull(resvInfo)) {
                purpose = CharConstant.CHAR_EMPTY;
            } else {
                purpose = resvInfo.getUsedTypeName();
            }
        }
        List<BizAccidentGroupRule> groupRules = bizAccidentGroupRuleMapper.queryAll();
        for (BizAccidentGroupRule groupRule : groupRules) {
            if (groupRule.getAffiliation() == AffiliationEnum.ALL.getCode() ||
                    groupRule.getAffiliation().equals(bizMapArea.getAffiliation())) {
                if (ALL.equals(groupRule.getCity()) || bizMapArea.getCity().equals(groupRule.getCity())) {
                    if (ALL.equals(groupRule.getPurpose()) || purpose.equals(groupRule.getPurpose())) {
                        log.info("These members pass filter all, add to create group room, " +
                                        "affiliation is [{}], purpose is [{}], city is [{}], members is [{}]",
                                bizMapArea.getAffiliation(), purpose,
                                bizMapArea.getCity(), groupRule.getMembers());
                        groupMembers.addAll(Arrays.asList(groupRule.getMembers().split(CharConstant.CHAR_DD)));
                    } else if (!VehiclePurposeEnum.OPERATION_PURPOSE.getMsg().equals(purpose) &&
                            VehiclePurposeEnum.OTHER.getMsg().equals(groupRule.getPurpose())) {
                        log.info("These members pass filter, add to create group room, " +
                                        "affiliation is [{}], purpose is [{}], city is [{}], members is [{}]",
                                bizMapArea.getAffiliation(), purpose,
                                bizMapArea.getCity(), groupRule.getMembers());
                        groupMembers.addAll(Arrays.asList(groupRule.getMembers().split(CharConstant.CHAR_DD)));
                    }
                }
            }
        }
        long groupId = 0;
        try {
            log.info("Start to create group room, groupOwner is [{}], groupMembers is [{}], groupTitle is [{}]",
                    accidentGroupOwner, groupMembers, accidentInfoDTO.getGroupTitle());
            try {
                groupId = createDxGroup(groupMembers, accidentInfoDTO.getGroupTitle());
            } catch (Exception e) {
                log.error("Create dx group error", e);
            }

            if (groupId < 1) {
                // 新上功能防止出现意外，建群失败时，采用老的方式建群
                createGroupAndSendMsgOutDated(accidentInfoDTO);
                return;
            }
            addRobot(groupId);
            updateNotice(groupId, accidentInfoDTO.getBizAccidentInfo().getId());
            sendMsgToDxGroupByRobot(groupId, accidentInfoDTO.getMsg());
            //写 redis
            //writeMessageDataToRedis(groupId, accidentInfoDTO, 0);
            // 机器人作为群主或管理员时，才能发布公告，但建群时机器人不能设置为管理员，只能设置为群主
            // 等公告发布完成之后，再把群主还给具体的人
            // （询问了企平的人，说是后续会优化，到时候建群可以把机器人设置为管理员，这样下面这一步就可以去掉）
            transferGroupOwner(groupId, accidentGroupOwner);
        } catch (Exception e) {
            log.error("Auto create group failed, accident is [{}]", bizAccidentInfo, e);
        }

    }

    //redis 写数据
    private void writeMessageDataToRedis(Long groupId, AccidentInfoDTO accidentInfoDTO, int creatGroupType){
        Long accidentTime = accidentInfoDTO.getBizAccidentInfo().getAccidentTime().getTime() / 1000; //时间需要转化成秒级
        String vin = accidentInfoDTO.getBizAccidentInfo().getVin();
        try{
            redisProductor.sendDateToRedisQueue("HDvideoParamsQueue",vin, accidentTime,videoMsg, groupId,  creatGroupType);
            log.info("writeMessageDataToRedis, vin = {}, accidentTime = {}, videoMsg = {}, groupId = {} ", vin, accidentTime,videoMsg, groupId);
        }
        catch (Exception e){
            log.error("writeMessageDataToRedis, writeMessageDataToRedis is failed",e);
        }
    }
    //大象群发送视频链接
    public void sendMessageDataNew(Long GroupId,String videoUrl) throws TException {
        getOrUpdateAccessToken();
        sendMsgToDxGroupByRobotCustomized(GroupId, videoUrl);
    }

    public void sendMessageDataOld(Long GroupId,String videoUrl) throws TException {
        DxAppUtil.sendMesToGroup(GroupId, videoUrl);
    }

    public HashMap<String, Long> getqueryTimeConfig(){
        return queryTimeConfig;
    }

    public String removeDxGroups(String groupIds) {
        String[] groupIdList = groupIds.split(CharConstant.CHAR_DD);
        StringBuilder failedRemoveGroupIds = new StringBuilder();
        try {
            getOrUpdateAccessToken();
        } catch (TException e) {
            return groupIds;
        }
        for (String groupId : groupIdList) {
            try {
                long gid = Long.parseLong(groupId);
                GroupBotReq groupBotReq = new GroupBotReq();
                groupBotReq.setGid(gid);
                EmptyResp emptyResp = openGroupThriftService.dismissGroup(accessToken.token, groupBotReq);
                if (emptyResp.status.getCode() == 0) {
                    return "";
                }
                // 如果不成功，则使用老的方式再尝试一次
                boolean result = openThriftClientService.removeOccupant(gid, 1, true);
                if (!result) {
                    log.warn("Call interface success, but remove dx group failed, groupId is [{}]", groupId);
                    failedRemoveGroupIds.append(groupId);
                }
            } catch (Exception e) {
                log.error("Remove dx group failed, groupId is [{}]", groupId, e);
                failedRemoveGroupIds.append(groupId);
            }
        }
        return failedRemoveGroupIds.toString();
    }

    private void makeAccidentAnnouncementAndUpdate(long groupId, AccidentInfoDTO accidentInfoDTO) {
        String announcement = makeAccidentAnnouncement(accidentInfoDTO);
        updateAnnouncement(groupId, announcement);
    }

    private void updateNotice(long groupId, long accidentId) {
        String content = String.format("事故ID=%s\n" +
                        "[事故链接|%s?id=%s&mode=drawer]",
                accidentId, FastUploadConfig.VEHICLE_UPLOAD_REQUEST_ACCIDENT_DETAIL_URL, accidentId);
        try {
            UpdateGroupNoticeReq noticeReq = new UpdateGroupNoticeReq();
            noticeReq.setGid(groupId);
            noticeReq.setNoticeContent(content);
            EmptyResp emptyResp = openGroupThriftService.updateGroupNotice(accessToken.token, noticeReq);
            if (emptyResp.status.getCode() != 0) {
                log.error("Update group notice failed: {}, groupId: {}, accidentId: {}", emptyResp, groupId, accidentId);
            }
        } catch (Exception e) {
            log.error("Update announcement failed", e);
        }
    }

    private String makeAccidentAnnouncement(AccidentInfoDTO accidentInfoDTO) {
        BizAccidentInfo bizAccidentInfo = accidentInfoDTO.getBizAccidentInfo();
        Long id = bizAccidentInfo.getId();
        String announcement = String.format("事故ID=%s\n" +
                        "[事故链接|%s?id=%s&mode=drawer]",
                id, FastUploadConfig.VEHICLE_UPLOAD_REQUEST_ACCIDENT_DETAIL_URL, id);
        return announcement;
    }

    private void updateAnnouncement(long groupId, String content) {
        try {
            openThriftClientService.updateGroupAnnouncement(groupId, 1, content, false);
        } catch (Exception e) {
            log.error("Update announcement failed", e);
        }
    }

    private void sendMsgToDxGroup(long groupId, AccidentInfoDTO accidentInfoDTO) {
        log.info("Start to send msg to group, groupId is [{}]", groupId);
        try {
            CommonResp resp = emsGroupOpenThriftService.setVisibleTime(groupId, "-1d");
            if (resp.getCode() == 0) {
                log.info("Set group visible time success, groupId is [{}]", groupId);
            } else {
                log.warn("Set group visible permission failed, groupId is [{}], CommonResp is [{}]", groupId, resp);
            }
        } catch (Exception e) {
            log.warn("Set group visible permission failed, groupId is [{}]", groupId, e);
        }
        DxAppUtil.sendMesToGroup(groupId, accidentInfoDTO.getMsg());
       //注释掉发送模版的通知
       // DxAppUtil.sendMesToGroup(groupId, AccidentConfig.accident_group_notice_content);
    }

    private void addRobot(Long groupId) throws TException {
        AddGroupMembersReq addGroupMembersReq = new AddGroupMembersReq();
        String[] split = SysParamsConfig.accident_other_robot.split(CharConstant.CHAR_DD);
        Set<Long> robotList = Arrays.stream(split).map(Long::valueOf).collect(Collectors.toSet());
        addGroupMembersReq.setBots(robotList);
        addGroupMembersReq.setGid(groupId);
        AddGroupMembersResp resp = openGroupThriftService.addGroupMembersByBot(accessToken.token, addGroupMembersReq);
        if (resp.status.getCode() != 0) {
            log.error("Add robot to group failed: {}, groupId: {}, robotList: {}", resp, groupId, robotList);
        }
    }


    public void sendMsgToDxGroupByRobot(long groupId, String message) throws TException {
        log.info("Start to send msg to group, groupId is [{}]", groupId);
        SendGroupMsgByRobotReq req = new SendGroupMsgByRobotReq();
        req.setGid(groupId);
        SendMsgInfo sendMsgInfo = new SendMsgInfo();
        Map<String, String> messageMap = new HashMap<>();
        messageMap.put("text", message);
        sendMsgInfo.setBody(JSON.toJSONString(messageMap));
        sendMsgInfo.setType("text");
        req.setSendMsgInfo(sendMsgInfo);
        SendGroupMsgByRobotRes msgResp = openMessageThriftService.sendGroupMsgByRobot(accessToken.token, req);
        if (msgResp.getStatus().getCode() != 0) {
            log.error("Send group msg failed: {}, groupId: {}, message: {}", msgResp, groupId, messageMap);
        }
        //注释掉发送模版的通知
//        messageMap.put("text", AccidentConfig.accident_group_notice_content);
//        sendMsgInfo.setBody(JSON.toJSONString(messageMap));
//        msgResp = openMessageThriftService.sendGroupMsgByRobot(accessToken.token, req);
//
//        if (msgResp.getStatus().getCode() != 0) {
//            log.error("Send group msg failed: {}, groupId: {}, message: {}", msgResp, groupId, messageMap);
//        }
    }
    public void sendMsgToDxGroupByRobotCustomized(long groupId, String message) throws TException {
        log.info("Start to send msg to group, groupId is [{}]", groupId);
        SendGroupMsgByRobotReq req = new SendGroupMsgByRobotReq();
        req.setGid(groupId);
        SendMsgInfo sendMsgInfo = new SendMsgInfo();
        Map<String, String> messageMap = new HashMap<>();
        messageMap.put("text", message);
        sendMsgInfo.setBody(JSON.toJSONString(messageMap));
        sendMsgInfo.setType("text");
        req.setSendMsgInfo(sendMsgInfo);
        SendGroupMsgByRobotRes msgResp = openMessageThriftService.sendGroupMsgByRobot(accessToken.token, req);
        if (msgResp.getStatus().getCode() != 0) {
            log.error("Send group msg failed: {}, groupId: {}, message: {}", msgResp, groupId, messageMap);
        }
    }

    private void transferGroupOwner(long groupId, String groupOwnerMis) throws Exception {
        Long groupOwnerId = udbOpenThriftClient.getUid(groupOwnerMis, tenant);
        GroupOwner groupOwner = new GroupOwner();
        groupOwner.setGid(groupId);
        groupOwner.setId(groupOwnerId);
        groupOwner.setType(OwnerTypeEnum.USER);
        EmptyResp emptyResp = openGroupThriftService.transGroupOwner(accessToken.token, groupOwner);
        if (emptyResp.status.getCode() != 0) {
            log.error("Transfer group owner group notice failed: {}, groupId: {}, groupOwnerMis: {}",
                    emptyResp, groupId, groupOwnerMis);
        }
    }
    
    public List<String> getGroupMember(String vin, String recordName, Date accidentTime){
        Set<String> groupMembers = new HashSet<>();
        Set<String> safetyMembers = getSafetyMembers(vin, accidentTime);
        groupMembers.addAll(safetyMembers);
        RecordV2 recordV2 = recordV2Mapper.selectByRecordName(recordName);
        BizMapArea bizMapArea = bizMapAreaMapper.queryByCode(recordV2.getPlace());
        if (bizMapArea == null) {
            bizMapArea = new BizMapArea();
            bizMapArea.setAffiliation(AffiliationEnum.ALL.getCode());
            bizMapArea.setCity(CommonConstant.UNKNOWN);
        }
        String purpose = recordV2.getPurpose();
        if (StringUtils.isBlank(purpose)) {
            DemandQueryResultDTO resvInfo = vresvRecordHistoryService.getPurposeFromVresvSystem(
                    vin, recordV2.getBeginTime());
            if (Objects.isNull(resvInfo)) {
                purpose = CharConstant.CHAR_EMPTY;
            } else {
                purpose = resvInfo.getUsedTypeName();
            }
        }
        List<BizAccidentGroupRule> groupRules = bizAccidentGroupRuleMapper.queryAll();
        for (BizAccidentGroupRule groupRule : groupRules) {
            if (groupRule.getAffiliation() == AffiliationEnum.ALL.getCode() ||
                    groupRule.getAffiliation().equals(bizMapArea.getAffiliation())) {
                if (ALL.equals(groupRule.getCity()) || bizMapArea.getCity().equals(groupRule.getCity())) {
                    if (ALL.equals(groupRule.getPurpose()) || purpose.equals(groupRule.getPurpose())) {
                        log.info("These members pass filter all, add to create group room, " +
                                        "affiliation is [{}], purpose is [{}], city is [{}], members is [{}]",
                                bizMapArea.getAffiliation(), purpose,
                                bizMapArea.getCity(), groupRule.getMembers());
                        groupMembers.addAll(Arrays.asList(groupRule.getMembers().split(CharConstant.CHAR_DD)));
                    } else if (!VehiclePurposeEnum.OPERATION_PURPOSE.getMsg().equals(purpose) &&
                            VehiclePurposeEnum.OTHER.getMsg().equals(groupRule.getPurpose())) {
                        log.info("These members pass filter, add to create group room, " +
                                        "affiliation is [{}], purpose is [{}], city is [{}], members is [{}]",
                                bizMapArea.getAffiliation(), purpose,
                                bizMapArea.getCity(), groupRule.getMembers());
                        groupMembers.addAll(Arrays.asList(groupRule.getMembers().split(CharConstant.CHAR_DD)));
                    }
                }
            }
        }
        return  new ArrayList<>(groupMembers);
    }

}
