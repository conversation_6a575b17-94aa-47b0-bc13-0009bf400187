package com.meituan.walle.data.center.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.dto.OctoProviderDTO;
import com.meituan.walle.data.center.entity.dto.OctoProviderStatusDTO;
import com.meituan.walle.data.center.entity.params.OctoProviderParam;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.service.OctoOpenApiService;
import com.meituan.walle.data.center.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@InterfaceDoc(
        displayName = "调用OCTO Open API的接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问OCTO Open API的接口",
        scenarios = "octoOpenApiService方法对应的接口"
)
@Slf4j
@RestController
@RequestMapping("/octoOpenApi")
public class OctoOpenApiController {

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private OctoOpenApiService octoOpenApiService;

    @MethodDoc(
            requestMethods = {HttpMethod.GET},
            displayName = "查询OCTO服务提供者",
            description = "根据服务、协议、ip、状态、泳道等查询对应的提供者，" +
                "返回结果包含：节点（机器+端口）、权值、状态、泳道信息、机器名称、主备、sdk版本、环境、协议等详细信息。",
            parameters = {
                    @ParamDoc(name = "OctoProviderParam", description = "appkey字段必填",
                            requiredness = Requiredness.REQUIRED)
            },
            restExamplePostData = "{\"appkey\": \"com.sankuai.cloud.xxx.proxy\"}",
            returnValueDescription = "调用成功与否的Response对象，包含code、msg、data信息"
    )
    @GetMapping("/provider")
    public CommonResponse getProviderStatus(@ModelAttribute OctoProviderParam octoProviderParam) {
        if (octoProviderParam == null || octoProviderParam.getAppkey() == null
                || octoProviderParam.getAppkey().isEmpty()) {
            log.warn("API /octoOpenApi/provider parameter error, {}", JacksonUtil.serialize(octoProviderParam));
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(),
                    "parameter appkey cannot be empty!");
        }
        List<OctoProviderDTO> result;
        try {
            result = octoOpenApiService.getProvider(octoProviderParam);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return CommonResponse.fail(WebResponseStatusEnum.SERVICE_SUSPENSION.getCode(), e.getMessage());
        }
        return CommonResponse.success(result);
    }

    @MethodDoc(
            requestMethods = {HttpMethod.POST},
            displayName = "查询OCTO服务提供者状态",
            description = "查询OCTO服务提供者状态",
            parameters = {
                    @ParamDoc(name = "OctoProviderStatusDTO", description = "appkey字段必填",
                            requiredness = Requiredness.REQUIRED)
            },
            restExamplePostData = "{\"appkey\": \"com.sankuai.cloud.xxx.proxy\"}",
            returnValueDescription = "调用成功与否的Response对象，包含code、msg、data信息"
    )
    @PostMapping("/provider/status")
    public Response getProviderStatus(@RequestBody OctoProviderStatusDTO octoProviderStatusDTO) {
        if (octoProviderStatusDTO == null || octoProviderStatusDTO.getAppkey() == null
                || "".equals(octoProviderStatusDTO.getAppkey())) {
            log.warn("API /octoOpenApi/provider/status parameter error, {}", octoProviderStatusDTO);
            return Response.fail("10004", "parameter appkey cannot be empty!");
        }
        String resultString;
        try {
            resultString = octoOpenApiService.getProviderStatus(octoProviderStatusDTO);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.fail("10020", e.getMessage());
        }
        return Response.succ(resultString);
    }

}