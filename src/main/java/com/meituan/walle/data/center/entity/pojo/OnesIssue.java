package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/18 下午1:54
 * Description:
 * Modified by
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OnesIssue implements Serializable {

    private Integer id;
    private Integer projectId;
    private Integer moduleId;
    private String state;
    private String type;
    private Integer versionId;
    private String createdBy;
    private String updatedBy;
    private String assigned;
    private List<String> cc;
    private String name;
    private String desc;
    private Long createdAt;
    private Long startAt;
    private Long updatedAt;
    private Long closeAt;
    private Long expectStart;
    private Long expectClose;
    private Integer expectTime;
    private Integer actualWorkTime;
    private Integer priority;
    private List<Integer> labels;
    private List<Integer> associatedProjects;

}
