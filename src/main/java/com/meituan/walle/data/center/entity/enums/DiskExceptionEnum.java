package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Beijing
 * @date 2022/07/26
 * Description: https://km.sankuai.com/page/1341354344
 * Modified by
 */
@Getter
@AllArgsConstructor
public enum DiskExceptionEnum {
    /**
     * EXCEPTION_TYPE
     */
    MOUNT(1, "挂载异常"),
    CRYPT(2, "加解密异常"),
    /**
     * EXCEPTION_CODE
     */
    WRONG_FS_TYPE(101, "错误文件系统类型"),
    UNKNOWN_FS_TYPE(102, "未知文件系统类型"),
    UNKNOWN(199, "未知"),
    ;

    private int code;
    private String message;
}
