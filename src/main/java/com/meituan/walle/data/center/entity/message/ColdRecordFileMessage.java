package com.meituan.walle.data.center.entity.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/12/02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ColdRecordFileMessage extends AbstractEventMessage {
    @JsonProperty(value = "record_name")
    private String recordName;
    @JsonProperty(value = "file_name")
    private String fileName;
    @JsonProperty(value = "file_path")
    private String filePath;
    @JsonProperty(value = "s3_url")
    private String s3Url;
    @JsonProperty(value = "file_size")
    private Long fileSize;
    @JsonProperty(value = "cluster")
    private Integer cluster;
    @JsonProperty(value = "s3_is_deleted")
    private Byte s3IsDeleted;
    @JsonProperty(value = "s3_delete_time")
    private Date s3DeleteTime;
    @JsonProperty(value = "s3_is_marked_for_deletion")
    private Byte s3IsMarkedForDeletion;
    @JsonProperty(value = "s3_is_marked_for_deletion_time")
    private Date s3IsMarkedForDeletionTime;
    @JsonProperty(value = "is_deleted")
    private Byte isDeleted;
}