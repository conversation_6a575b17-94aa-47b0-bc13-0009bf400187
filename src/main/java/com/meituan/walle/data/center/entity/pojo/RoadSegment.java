package com.meituan.walle.data.center.entity.pojo;

import lombok.Builder;

import java.util.Date;
import javax.persistence.*;

@Builder
@Table(name = "road_segment")
public class RoadSegment {
    /**
     * 自增主键
     */
    @Id
    @Column(name = "primary_id")
    private Long primaryId;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 对应高精地图中road_segment的id
     */
    @GeneratedValue(generator = "JDBC")
    private String id;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 获取自增主键
     *
     * @return primary_id - 自增主键
     */
    public Long getPrimaryId() {
        return primaryId;
    }

    /**
     * 设置自增主键
     *
     * @param primaryId 自增主键
     */
    public void setPrimaryId(Long primaryId) {
        this.primaryId = primaryId;
    }

    /**
     * 获取表record的record_name外键
     *
     * @return record_name - 表record的record_name外键
     */
    public String getRecordName() {
        return recordName;
    }

    /**
     * 设置表record的record_name外键
     *
     * @param recordName 表record的record_name外键
     */
    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    /**
     * 获取对应高精地图中road_segment的id
     *
     * @return id - 对应高精地图中road_segment的id
     */
    public String getId() {
        return id;
    }

    /**
     * 设置对应高精地图中road_segment的id
     *
     * @param id 对应高精地图中road_segment的id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}