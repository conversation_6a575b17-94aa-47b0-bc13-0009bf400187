package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.constant.CommonConstant;
import com.meituan.walle.data.center.constant.FileSourceEnum;
import com.meituan.walle.data.center.constant.FileTypeEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.enums.ReturnCodeEnum;
import com.meituan.walle.data.center.entity.pojo.BizFileInfo;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.entity.vo.BizFileInfoVO;
import com.meituan.walle.data.center.service.BizFileInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/5
 */
@InterfaceDoc(
        displayName = "文件操作接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供文件上传下载等文件处理能力。",
        scenarios = "文件上传下载"
)
@Slf4j
@RestController
@RequestMapping("/file")
public class FileInfoController {

    @Resource
    private BizFileInfoService fileInfoService;

    @MethodDoc(
            displayName = "文件上传",
            description = "文件上传，支持多文件上传",
            parameters = {
                    @ParamDoc(name = "refId", description = "关联ID"),
                    @ParamDoc(name = "source", description = "来源"),
                    @ParamDoc(name = "files", description = "上传文件，支持多文件")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    public Response upload(@RequestParam String refId, @RequestParam String source, MultipartFile[] files) {
        Transaction t = Cat.newTransaction(CatConstant.FILE_OPERATE, "file.upload");
        long start = System.currentTimeMillis();
        if (files == null || files.length < 1) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "File is empty");
        }
        try {
            List<BizFileInfo> fileInfoList = fileInfoService.upload(files, refId, source, FileTypeEnum.UNKNOWN);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            if (fileInfoList == null || fileInfoList.isEmpty()) {
                return Response.fail("1", "Upload file to s3 fail");
            }
            t.setSuccessStatus();
            fileInfoList.forEach(item -> item.setFileUrl(item.getFileUrl().replace(CommonConstant.S3_PREFIX,
                            CommonConstant.S3_PUBLIC_DOWNLOAD_URL)));
            return Response.succ(fileInfoList);

        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Upload file to s3 fail, refId is {}", refId, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "Upload picture to s3 failed");
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "事故文件上传",
            description = "事故文件上传，支持多文件上传",
            parameters = {
                    @ParamDoc(name = "accidentId", description = "事故ID"),
                    @ParamDoc(name = "files", description = "上传文件，支持多文件")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )
    @RequestMapping(value = "/upload/accident", method = RequestMethod.POST)
    public Response uploadAccidentFile(@RequestParam("accidentId") String accidentId, MultipartFile[] files) {
        Transaction t = Cat.newTransaction(CatConstant.FILE_OPERATE, "file.upload.accident");
        long start = System.currentTimeMillis();
        if (files == null || files.length < 1) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "File is empty");
        }
        try {
            List<BizFileInfo> fileInfoList = fileInfoService.upload(files, accidentId,
                    FileSourceEnum.ACCIDENT.getMsg(), FileTypeEnum.UNKNOWN);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            if (fileInfoList == null || fileInfoList.isEmpty()) {
                return Response.fail("1", "Upload accident file to s3 fail");
            } else {
                t.setSuccessStatus();
                return Response.succ(fileInfoList);
            }
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Upload accident file to s3 fail, accidentId is {}", accidentId, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "Upload accident picture to s3 failed");
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "文件删除",
            description = "删除文件",
            parameters = {
                    @ParamDoc(name = "id", description = "文件ID")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\"ret\": \"0\",\n" +
                    "\"msg\": \"ok\",\n" +
                    "\"data\": \"\"\n" +
                    "}"
    )
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public Response deleteFile(@RequestParam(value = "id") Long id) {
        Transaction t = Cat.newTransaction(CatConstant.FILE_OPERATE, "file.delete");
        long start = System.currentTimeMillis();
        if (id == null || id < 0) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param id is illegal");
        }
        try {
            boolean result = fileInfoService.delete(id);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            if (!result) {
                return Response.fail("1", "Delete file fail");
            } else {
                t.setSuccessStatus();
                return Response.succ("");
            }
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Delete file fail, file id is {}", id, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "Delete file fail");
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "根据关联ID获取文件列表",
            description = "根据关联ID获取文件列表",
            parameters = {
                    @ParamDoc(name = "refId", description = "关联文件ID")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "\t\"ret\": \"0\",\n" +
                    "\t\"msg\": \"ok\",\n" +
                    "\t\"data\": [{\n" +
                    "\t\t\"id\": 1,\n" +
                    "\t\t\"fileName\": \"test.mp4\",\n" +
                    "\t\t\"fileType\": 3,\n" +
                    "\t\t\"fileSize\": 0,\n" +
                    "\t\t\"fileUrl\": \"s3://vehicle-data/common-file/vedio/test.mp4\",\n" +
                    "\t\t\"refId\": \"1\",\n" +
                    "\t\t\"source\": \"accident\",\n" +
                    "\t\t\"memo\": \"test\",\n" +
                    "\t\t\"isDeleted\": 0,\n" +
                    "\t\t\"createTime\": 1636449650000,\n" +
                    "\t\t\"updateTime\": 1636463414000\n" +
                    "\t}, {\n" +
                    "\t\t\"id\": 2,\n" +
                    "\t\t\"fileName\": \"test.jpg\",\n" +
                    "\t\t\"fileType\": 2,\n" +
                    "\t\t\"fileSize\": 20,\n" +
                    "\t\t\"fileUrl\": \"s3://vehicle-data/common-file/image/test.jpg\",\n" +
                    "\t\t\"refId\": \"1\",\n" +
                    "\t\t\"source\": \"accident\",\n" +
                    "\t\t\"memo\": \"\",\n" +
                    "\t\t\"isDeleted\": 0,\n" +
                    "\t\t\"createTime\": 1636471929000,\n" +
                    "\t\t\"updateTime\": 1636471929000\n" +
                    "\t}]\n" +
                    "}"
    )
    @RequestMapping(value = "/get/ref_id", method = RequestMethod.GET)
    public Response getByRefId(@RequestParam("refId") String refId) {
        Transaction t = Cat.newTransaction(CatConstant.FILE_OPERATE, "file.get.refId");
        long start = System.currentTimeMillis();
        if (refId == null || refId.isEmpty()) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return Response.fail("1", "Param refId is illegal");
        }
        try {
            List<BizFileInfo> fileInfoList = fileInfoService.queryByRefId(refId);
            fileInfoList.forEach(item -> item.setFileUrl(item.getFileUrl().replace(CommonConstant.S3_PREFIX,
                    CommonConstant.S3_PUBLIC_DOWNLOAD_URL)));
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(fileInfoList);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Get file by ref id fail, file ref id is {}", refId, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail("1", "get file by ref id fail");
        } finally {
            t.complete();
        }
    }


    @MethodDoc(
            displayName = "模糊或精确查询文件",
            description = "模糊或精确查询文件",
            parameters = {
                    @ParamDoc(name = "refId", description = "文件业务ID"),
                    @ParamDoc(name = "source", description = "来源"),
                    @ParamDoc(name = "mode", description = "查询模式，不传或0为精确查询，其他值为模糊查询")
            },
            returnValueDescription = "成功/失败",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"msg\": \"ok\",\n" +
                    "    \"data\": [\n" +
                    "        {\n" +
                    "            \"fileName\": \"zhonglihai_admap_v3.9.1.r_1638876931462_1.txt\",\n" +
                    "            \"fileSize\": 3272,\n" +
                    "            \"fileUrl\": \"https://s3plus.meituan.net/v1/mss_914377ab071e4a8b8b50cc10a00ec5af" +
                    "/vehicle-object/common-file/txt/zhonglihai_admap_v3.9.1.r_1638876931462_1.txt\",\n" +
                    "            \"refId\": \"zhonglihai_admap_v3.9.1.r\",\n" +
                    "            \"source\": \"map\"\n" +
                    "        }\n" +
                    "    ]\n" +
                    "}"
    )
    @GetMapping(value = "/get/refs")
    public CommonResponse getByRefId(String refId, String source, @RequestParam(required = false) Integer mode) {
        Transaction t = Cat.newTransaction(CatConstant.FILE_OPERATE, "file.get.refs");
        long start = System.currentTimeMillis();
        if (refId == null || refId.isEmpty()) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            t.complete();
            return CommonResponse.fail(ReturnCodeEnum.PARAM_ERROR.getCode(), "Param refId is illegal");
        }
        try {
            List<BizFileInfo> fileInfoList;
            if (mode == null || mode == 0) {
                fileInfoList = fileInfoService.queryByRefs(refId, source, null);
            } else {
                fileInfoList = fileInfoService.queryByRefsLikeMode(refId, source, null);
            }
            List<BizFileInfoVO> resultList = fileInfoList.stream().map(item -> {
                BizFileInfoVO vo = new BizFileInfoVO();
                BeanUtils.copyProperties(item, vo);
                String fileUrl = vo.getFileUrl();
                String fileHttpUrl = new StringBuilder(CommonConstant.S3_PUBLIC_DOWNLOAD_URL)
                        .append(fileUrl.replace(CommonConstant.S3_PREFIX, "")).toString();
                vo.setFileUrl(fileHttpUrl);
                return vo;
            }).collect(Collectors.toList());
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return CommonResponse.success(resultList);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Get file by ref id fail, file ref id is {}", refId, e);
            Cat.logError(e);
            t.setStatus(e);
            return CommonResponse.fail(ReturnCodeEnum.SYSTEM_ERROR.getCode(), "get file by ref id fail");
        } finally {
            t.complete();
        }
    }

}
