package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/10/28
 */
@Getter
@AllArgsConstructor
public enum ResponseCodeEnum {

    AUTH_FAILED("401", "auth failed"),
    NEED_UPDATE("210", "Need to update"),
    LACK_PARAMETER("400", "Lack key parameter"),
    REQUEST_FAILED("404", "Request failed"),
    ERROR_CODE("500", "Error occurred");
    private String code;
    private String message;
}
