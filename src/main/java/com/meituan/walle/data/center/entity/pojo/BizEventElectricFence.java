package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "biz_event_electric_fence")
@Data
public class BizEventElectricFence {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    private String eventId;

    /**
     * 表record的record_name外键
     */
    private String recordName;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 电子围栏事件发生事件
     */
    private Date eventTime;

    /**
     * Utm坐标的x（来源于Localization的x）
     */
    private String x;

    /**
     * Utm坐标的y（来源于Localization的y）
     */
    private String y;

    private Integer sequenceId;

    private Integer direction;

    private String areaId;

    private String operationAreaId;

    private Boolean isDeleted;

    private Date createTime;

    private Date updateTime;

}
