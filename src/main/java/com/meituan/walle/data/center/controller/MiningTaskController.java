package com.meituan.walle.data.center.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.walle.data.center.constant.*;
import com.meituan.walle.data.center.entity.RequestContent;
import com.meituan.walle.data.center.entity.dto.*;
import com.meituan.walle.data.center.entity.pojo.BizIssueRemark;
import com.meituan.walle.data.center.entity.pojo.MiningTask;
import com.meituan.walle.data.center.entity.pojo.MiningTaskResult;
import com.meituan.walle.data.center.entity.vo.CaseOperateHistoryVO;
import com.meituan.walle.data.center.entity.vo.MiningTaskVO;
import com.meituan.walle.data.center.entity.vo.SysDictVO;
import com.meituan.walle.data.center.excel.util.ExportExcelUtil;
import com.meituan.walle.data.center.service.CaseOperateHistoryService;
import com.meituan.walle.data.center.service.MiningTaskService;
import com.meituan.walle.data.center.service.SysDictService;
import com.meituan.walle.data.center.util.StringUtil;
import com.meituan.walle.data.center.util.Trace;
import com.meituan.walle.data.center.excel.mapping.MiningTaskResultListMap;
import com.meituan.walle.data.center.excel.util.ImportExcelUtil;
import com.sankuai.walle.wcdp.core.entity.page.Page;
import com.sankuai.walle.wcdp.core.entity.request.RespCodeEnum;
import com.sankuai.walle.wcdp.core.entity.request.RespContent;
import com.sankuai.walle.wcdp.core.entity.request.RespContentResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.elasticsearch.common.Strings;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/03/22
 */
@InterfaceDoc(
        displayName = "挖掘任务",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供挖掘任务管理的能力。",
        scenarios = "挖掘任务所有接口"
)
@Slf4j
@RestController
@RequestMapping("/mining/task")
public class MiningTaskController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MiningTaskController.class);

    @Autowired
    private MiningTaskService miningTaskService;

    @Autowired
    private HttpServletResponse response;

    @Autowired
    private CaseOperateHistoryService caseOperateHistoryService;

    @Autowired
    private SysDictService sysDictService;

    ////////////////////////////////////////////////华丽分割线：挖掘任务///////////////////////////////////////////////

    @MethodDoc(
            displayName = "获取挖掘任务列表",
            description = "根据条件获取挖掘任务列表",
            parameters = {
                    @ParamDoc(name = "requContent", description = "挖掘任务列表请求参数")
            },
            returnValueDescription = "挖掘任务列表"
    )
    @RequestMapping(value = "/get_task_list", method = RequestMethod.POST)
    public Object queryMiningTask(@RequestBody RequestContent<MiningTaskDTO> requestContent) {

        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "get_task_list";

        Transaction t = Cat.newTransaction(CatConstant.API, "/mining/task/" + api);
        LOGGER.info("traceId: {}, received new request for {}.", traceId, api);

        try {
            response.addHeader("TraceId", traceId);

            Page page = requestContent.getPage();
            MiningTaskDTO miningTaskDTO = requestContent.getQueryObj();
            if (page == null || miningTaskDTO == null) {
                t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
                t.setSuccessStatus();
                t.complete();
                return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY,
                        String.format("%s request fail, request param is: %s", api, requestContent));
            }

            miningTaskDTO.setPageSize(page.getPageSize());
            miningTaskDTO.setPageNum(page.getPageNum());

            RespContentResult<MiningTaskVO> respContentResult = new RespContentResult<>();

            List<MiningTaskVO> miningTaskVOList =
                    miningTaskService.queryMiningTaskListByPage(miningTaskDTO, 1);
            int totalCnt = miningTaskService.queryMiningTaskListByPageCnt(miningTaskDTO);

            page.setTotal(Long.parseLong(String.valueOf(totalCnt)));

            respContentResult.setData(miningTaskVOList);
            respContentResult.setPage(page);

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            return RespContent.<Object>success(respContentResult);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param is: {}.", traceId, api, requestContent, e);

            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("%s request fail, error info: %s.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "根据任务ID获取挖掘任务",
            description = "根据任务ID获取挖掘任务",
            parameters = {
                    @ParamDoc(name = "taskId", description = "挖掘任务ID")
            },
            returnValueDescription = "挖掘任务"
    )
    @RequestMapping(value = "/get_task_by_id", method = RequestMethod.GET)
    public Object queryMiningTaskById(@RequestParam String taskId) {

        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "get_task_by_id";

        Transaction t = Cat.newTransaction(CatConstant.API, "/mining/task/" + api);
        LOGGER.info("traceId: {}, received new request for {}.", traceId, api);

        try {
            response.addHeader("TraceId", traceId);

            MiningTaskDTO miningTaskDTO = new MiningTaskDTO();
            miningTaskDTO.setTaskId(taskId);

            List<MiningTaskVO> miningTaskVOList =
                    miningTaskService.queryMiningTaskListByPage(miningTaskDTO, 0);

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            if (!CollectionUtils.isEmpty(miningTaskVOList)) {
                return RespContent.<Object>success(miningTaskVOList.get(0));
            } else {
                return RespContent.<Object>success(RespCodeEnum.RESPONSE_EMPTY,
                        String.format("%s request fail, data not exist by taskId: %s.", api, taskId));
            }

        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param is: {}.", traceId, api, taskId, e);

            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("%s request fail, error info: %s.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }

    ////////////////////////////////////////////////华丽分割线：任务结果///////////////////////////////////////////////

    @MethodDoc(
            displayName = "获取挖掘任务结果列表",
            description = "根据条件获取挖掘任务结果列表",
            parameters = {
                    @ParamDoc(name = "requContent", description = "挖掘任务结果列表请求参数")
            },
            returnValueDescription = "挖掘任务结果列表"
    )
    @RequestMapping(value = "/get_task_result_list", method = RequestMethod.POST)
    public Object queryMiningTaskResult(@RequestBody RequestContent<MiningTaskResultDTO> requestContent) {

        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "get_task_result_list";

        Transaction t = Cat.newTransaction(CatConstant.API, "/mining/task/" + api);
        LOGGER.info("traceId: {}, received new request for {}.", traceId, api);

        try {
            response.addHeader("TraceId", traceId);

            Page page = requestContent.getPage();
            MiningTaskResultDTO miningTaskResultDTO = requestContent.getQueryObj();
            if (page == null || miningTaskResultDTO == null) {
                t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
                t.setSuccessStatus();
                t.complete();
                return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY,
                        String.format("%s request fail, request param is: %s", api, requestContent));
            }

            miningTaskResultDTO.setPageSize(page.getPageSize());
            miningTaskResultDTO.setPageNum(page.getPageNum());

            // 匹配查询规则
            String encodeTag = miningTaskResultDTO.getTag();
            if (encodeTag != null) {
                String tag = URLDecoder.decode(encodeTag, StandardCharsets.UTF_8.name());
                log.info("queryMiningTaskResult encodeTag:[{}], tag:[{}]", encodeTag, tag);
                miningTaskService.ruleParsing(miningTaskResultDTO, tag);
            }

            RespContentResult<MiningTaskResult> respContentResult = new RespContentResult<>();

            List<MiningTaskResult> miningTaskVOList =
                    miningTaskService.queryMiningTaskResultListByPage(miningTaskResultDTO, 1);
            int totalCnt = miningTaskService.queryMiningTaskResultListByPageCnt(miningTaskResultDTO);

            page.setTotal(Long.parseLong(String.valueOf(totalCnt)));

            respContentResult.setData(miningTaskVOList);
            respContentResult.setPage(page);

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            return RespContent.<Object>success(respContentResult);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param is: {}.", traceId, api, requestContent, e);

            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("%s request fail, error info: %s.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "根据任务结果ID获取挖掘结果",
            description = "根据任务结果ID获取挖掘结果",
            parameters = {
                    @ParamDoc(name = "taskResultId", description = "挖掘结果ID")
            },
            returnValueDescription = "挖掘结果"
    )
    @RequestMapping(value = "/get_task_result_by_id", method = RequestMethod.GET)
    public Object queryMiningTaskResultById(@RequestParam String taskResultId) {

        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "get_task_result_by_id";

        Transaction t = Cat.newTransaction(CatConstant.API, "/mining/task/" + api);
        LOGGER.info("traceId: {}, received new request for {}.", traceId, api);

        try {
            response.addHeader("TraceId", traceId);

            if (Strings.isNullOrEmpty(taskResultId)) {
                t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
                t.setSuccessStatus();
                t.complete();
                return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY,
                        String.format("%s request fail, request param is: %s", api, taskResultId));
            }

            MiningTaskResultDTO miningTaskResultDTO = new MiningTaskResultDTO();
            miningTaskResultDTO.setTaskResultId(taskResultId);

            MiningTaskResultDTO dto = miningTaskService.getMiningTaskResult(miningTaskResultDTO);

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            if (dto != null) {
                return RespContent.<Object>success(dto);
            } else {
                return RespContent.<Object>success(RespCodeEnum.RESPONSE_EMPTY,
                        String.format("%s request fail, data not exist by taskResultId: %s.", api, taskResultId));
            }

        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param is: {}.", traceId, api, taskResultId, e);

            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("%s fail, error info: %s.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "根据任务结果ID获取挖掘结果",
            description = "根据任务结果ID获取挖掘结果",
            parameters = {
                    @ParamDoc(name = "miningTaskResultDTO", description = "挖掘结果ID")
            },
            returnValueDescription = "挖掘结果"
    )
    @RequestMapping(value = "/update_task_result_info", method = RequestMethod.POST)
    public Object updateTaskResultInfo(@RequestBody MiningTaskResultDTO miningTaskResultDTO) {

        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "update_task_result_info";

        Transaction t = Cat.newTransaction(CatConstant.API, "/mining/task/" + api);
        LOGGER.info("traceId: {}, received new request for {}, request param is: {}.",
                traceId, api, miningTaskResultDTO);

        try {
            response.addHeader("TraceId", traceId);

            if (StringUtils.isBlank(miningTaskResultDTO.getTaskResultId())) {
                t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
                t.setSuccessStatus();
                t.complete();
                return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY,
                        String.format("%s request fail, request param is: %s.", api, miningTaskResultDTO));
            }

            // 如果更新标注结果，则同时需要更新标注状态为已标注
            if (miningTaskResultDTO.getLabelResult() != null) {
                miningTaskResultDTO.setLabelStatus(1);
            }

            int result = miningTaskService.updateTaskResultByTaskResultId(miningTaskResultDTO);

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            if (result > 0) {
                return RespContent.<Object>success("操作成功。");
            } else {
                return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                        String.format("%s request fail, request param is: %s.", api, miningTaskResultDTO));
            }

        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param is: {}.", traceId, api, miningTaskResultDTO, e);

            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("%s fail, error info: %s.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }

    ////////////////////////////////////////////////华丽分割线：结果评论///////////////////////////////////////////////

    @MethodDoc(
            displayName = "新增挖掘结果的评论",
            description = "新增挖掘结果的评论",
            parameters = {
                    @ParamDoc(name = "bizIssueRemarkDTO", description = "挖掘结果评论")
            },
            returnValueDescription = "挖掘结果评论"
    )
    @RequestMapping(value = "/add_task_result_issue", method = RequestMethod.POST)
    public Object addTaskResultIssue(@RequestBody BizIssueRemarkDTO bizIssueRemarkDTO) {

        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "add_task_result_issue";

        Transaction t = Cat.newTransaction(CatConstant.API, "/mining/task/" + api);
        LOGGER.info("traceId: {}, received new request for {} , request param is: {}.",
                traceId, api, bizIssueRemarkDTO);

        try {
            response.addHeader("TraceId", traceId);

            int result = miningTaskService.addTaskResultIssue(bizIssueRemarkDTO);

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            if (result > 0) {
                return RespContent.<Object>success("评论成功。");
            } else {
                return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                        String.format("%s request fail, request param is: %s.", api, bizIssueRemarkDTO));
            }

        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param is: {}.", traceId, api, bizIssueRemarkDTO, e);

            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("%s request fail, error info: %s.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "获取挖掘结果的评论",
            description = "获取挖掘结果的评论",
            parameters = {
                    @ParamDoc(name = "taskResultId", description = "挖掘结果评论")
            },
            returnValueDescription = "挖掘结果评论"
    )
    @RequestMapping(value = "/get_task_result_issue", method = RequestMethod.GET)
    public Object getTaskResultIssue(@RequestParam String taskResultId) {

        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "get_task_result_issue";

        Transaction t = Cat.newTransaction(CatConstant.API, "/mining/task/" + api);
        LOGGER.info("traceId: {}, received new request for {} , request param is: {}.", traceId, api, taskResultId);

        try {
            response.addHeader("TraceId", traceId);

            if (Strings.isNullOrEmpty(taskResultId)) {
                t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
                t.setSuccessStatus();
                t.complete();
                return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY,
                        String.format("%s request fail, request param is: %s", api, taskResultId));
            }

            List<BizIssueRemark> taskResultIssueList = miningTaskService.queryTaskResultIssue(taskResultId);

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            return RespContent.<Object>success(taskResultIssueList);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param is: {}.", traceId, api, taskResultId, e);

            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("%s request fail, error info: %s.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }

    /////////////////////////////////////////////华丽分割线：任务的导入、导出////////////////////////////////////////////

    @MethodDoc(
            displayName = "导入挖掘任务及任务结果",
            description = "导入挖掘任务及任务结果",
            parameters = {
                    @ParamDoc(name = "taskInfo", description = "挖掘任务的基础信息"),
                    @ParamDoc(name = "uploadFile", description = "挖掘任务的结果文件")
            },
            returnValueDescription = "上传成功与否的信息"
    )
    @RequestMapping(value = "/upload_task_result", method = RequestMethod.POST)
    public Object fileUpload(@RequestParam("taskInfo") String taskInfo,
                             @RequestParam("uploadFile") MultipartFile uploadFile) {
        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "upload_task_result";

        Transaction t = Cat.newTransaction(CatConstant.API, "/mining/task/" + api);
        LOGGER.info("traceId: {}, received new request for {} , request param is: {}.", traceId, api, taskInfo);

        try {
            response.addHeader("TraceId", traceId);

            // 1.检查挖掘任务 数据的合法性
            JSONObject responseJSON = JSON.parseObject(taskInfo);
            MiningTaskDTO miningTaskDTO = responseJSON.toJavaObject(MiningTaskDTO.class);

            StringBuilder checkErrorInfoSB = new StringBuilder();
            if (Strings.isNullOrEmpty(miningTaskDTO.getTaskNameEn())) {
                checkErrorInfoSB.append("任务英文名必填;");
            }
            if (Strings.isNullOrEmpty(miningTaskDTO.getTaskNameCh())) {
                checkErrorInfoSB.append("任务中文名必填;");
            }
            if (Strings.isNullOrEmpty(miningTaskDTO.getTaskRuleDescription())) {
                checkErrorInfoSB.append("任务规则必填;");
            }

            if (StringUtil.isContainChinese(miningTaskDTO.getTaskNameEn())) {
                checkErrorInfoSB.append("任务英文名包含中文;");
            }

            String checkErrorInfoStr = checkErrorInfoSB.toString();
            if (!Strings.isNullOrEmpty(checkErrorInfoStr)) {
                t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
                t.setSuccessStatus();
                t.complete();
                return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY, String.format(
                        "%s request fail, request param empty info: %s, param is: %s.", api,
                        checkErrorInfoStr, taskInfo
                ));
            }

            // 2.检查任务结果 数据的合法性：解析Excel文件 以及判断Excel数据的合法性
            List<MiningTaskResultExcelDTO> miningTaskResultExcelDTOList =
                    ImportExcelUtil.parseMultipartFile(uploadFile, MiningTaskResultExcelDTO.class,
                            MiningTaskResultListMap.getMiningTaskResultListMap(), 0);
            List<String> errList = ImportExcelUtil.getErrList();
            if (!CollectionUtils.isEmpty(errList)) {
                t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
                t.setSuccessStatus();
                t.complete();
                return RespContent.<Object>success(RespCodeEnum.PARAMS_ILLEGAL, errList);
            }
            if (CollectionUtils.isEmpty(miningTaskResultExcelDTOList)) {
                t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
                t.setSuccessStatus();
                t.complete();
                return RespContent.<Object>success(RespCodeEnum.UNKNOWN_STATUS, "上传的是空数据文件！");
            }

            // 3.挖掘任务 和任务结果 数据入库
            miningTaskService.addMiningTaskData(miningTaskDTO, miningTaskResultExcelDTOList);

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            return RespContent.<Object>success("数据导入成功！");
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param is: {}.", traceId, api, taskInfo, e);

            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("%s request fail, error info: %s, request param is: %s .",
                            api, e.getMessage(), taskInfo)
            );
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "导出挖掘任务及任务结果",
            description = "导出挖掘任务及任务结果",
            parameters = {
                    @ParamDoc(name = "taskId", description = "挖掘任务Id")
            },
            returnValueDescription = "导出成功与否的信息"
    )
    @RequestMapping(value = "/download_task_result", method = RequestMethod.GET)
    public void downloadTaskResult(@RequestParam("taskId") String taskId) {
        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "export_task_result";

        Transaction t = Cat.newTransaction(CatConstant.API, "/mining/task/" + api);
        LOGGER.info("traceId: {}, received new request for {} , request param is: {}.", traceId, api, taskId);

        try {
            response.addHeader("TraceId", traceId);
            // 导出Excel文件
            MiningTask miningTask = miningTaskService.getMiningTask(taskId);
            if (Objects.nonNull(miningTask)) {
                try (
                        Workbook wb = miningTaskService.exportExcelByTaskId(miningTask)
                ) {
                    final String fileName = URLEncoder.encode(miningTask.getTaskNameEn()
                            + ExportExcelUtil.getXlsxSuffix(), "UTF-8");
                    response.setContentType("application/octet-stream");
                    response.setHeader("content-disposition", "attachment;filename=" +
                            new String(fileName.getBytes("ISO8859-1")));
                    response.setHeader("filename", fileName);
                    wb.write(response.getOutputStream());
                } catch (Exception e) {
                    log.error("{} request fail, taskId: {}, export exception.", api, taskId, e);
                }
            }

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param is: {}.", traceId, api, taskId, e);
        } finally {
            t.complete();
        }
    }

    /////////////////////////////////////////////华丽分割线：任务结果操作记录////////////////////////////////////////////

    @MethodDoc(
            displayName = "新增挖掘结果的操作记录",
            description = "新增挖掘结果的操作记录",
            parameters = {
                    @ParamDoc(name = "caseOperateHistoryVO", description = "挖掘结果的操作记录")
            },
            returnValueDescription = "挖掘结果的操作记录"
    )
    @RequestMapping(value = "/add_task_result_operation", method = RequestMethod.POST)
    public Object addTaskResultOperation(@RequestBody CaseOperateHistoryVO caseOperateHistoryVO) {

        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "add_task_result_operation";

        Transaction t = Cat.newTransaction(CatConstant.API, "/mining/task/" + api);
        LOGGER.info("traceId: {}, received new request for {} , request param is: {}.",
                traceId, api, caseOperateHistoryVO);

        try {
            response.addHeader("TraceId", traceId);
            StringBuilder checkErrorInfo = new StringBuilder();

            if (caseOperateHistoryVO == null) {
                t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
                t.setSuccessStatus();
                t.complete();
                return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY,
                        String.format("%s request fail, request param is not null.", api));
            }

            if (StringUtils.isBlank(caseOperateHistoryVO.getCaseId())) {
                checkErrorInfo.append("caseId（taskResultId）is not null or empty;");
            }

            if (caseOperateHistoryVO.getType() == null) {
                checkErrorInfo.append("operation type is not null;");
            }

            String checkErrorInfoStr = checkErrorInfo.toString();
            if (StringUtils.isNotBlank(checkErrorInfoStr)) {
                t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
                t.setSuccessStatus();
                t.complete();
                return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY,
                        String.format("%s request fail, request param is: %s.", api, caseOperateHistoryVO));
            }

            CaseOperateHistoryDTO dto = new CaseOperateHistoryDTO();
            BeanUtils.copyProperties(caseOperateHistoryVO, dto);
            int result = caseOperateHistoryService.add(dto);

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            if (result > 0) {
                return RespContent.<Object>success("添加成功。");
            } else {
                return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                        String.format("%s request fail, request param is: %s.", api, caseOperateHistoryVO));
            }

        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param is: {}.", traceId, api, caseOperateHistoryVO, e);

            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("%s request fail, error info: %s.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "获取挖掘结果的操作记录",
            description = "获取挖掘结果的操作记录",
            parameters = {
                    @ParamDoc(name = "taskResultId", description = "结果的操作记录")
            },
            returnValueDescription = "挖掘结果的操作记录"
    )
    @RequestMapping(value = "/get_task_result_operation", method = RequestMethod.GET)
    public Object getTaskResultOperation(@RequestParam String taskResultId) {

        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "get_task_result_operation";

        Transaction t = Cat.newTransaction(CatConstant.API, "/mining/task/" + api);
        LOGGER.info("traceId: {}, received new request for {} , request param is: {}.", traceId, api, taskResultId);

        try {
            response.addHeader("TraceId", traceId);

            if (Strings.isNullOrEmpty(taskResultId)) {
                t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
                t.setSuccessStatus();
                t.complete();
                return RespContent.<Object>success(RespCodeEnum.PARAMS_EMPTY,
                        String.format("%s request fail, request param is: %s", api, taskResultId));
            }

            CaseOperateHistoryDTO dto = new CaseOperateHistoryDTO();
            dto.setCaseId(taskResultId);
            dto.setSource(CaseOperateHistorySourceEnum.MINING_RESULT.getMessage());
            List<CaseOperateHistoryDTO> caseOperateHistoryDTOList =
                    caseOperateHistoryService.selectByPage(dto, 0, 1, 0, 0);

            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            return RespContent.<Object>success(caseOperateHistoryDTOList);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail, request param is: {}.", traceId, api, taskResultId, e);

            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("%s request fail, error info: %s.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "获取挖掘结果的操作映射关系",
            description = "获取挖掘结果的操作映射关系",
            returnValueDescription = "挖掘结果的操作映射关系"
    )
    @RequestMapping(value = "/get_task_result_operation_mapping", method = RequestMethod.GET)
    public Object getOperationMapping() {

        DateTime startTime = new DateTime();
        String traceId = Trace.generateId();

        String api = "get_task_result_operation_mapping";

        Transaction t = Cat.newTransaction(CatConstant.API, "/dict/" + api);
        LOGGER.info("traceId: {}, received new request for {}.", traceId, api);

        try {
            response.addHeader("TraceId", traceId);

            SysDictDTO sysDictDTO = new SysDictDTO();
            sysDictDTO.setDictCode(SysDictCodeEnum.CASE_OPERATE_HISTORY_TYPE_MINING_RESULT_OPERATION_MAPPING.getMsg());
            List<SysDictDTO> sysDictDTOList = sysDictService.selectByPage(sysDictDTO, 0, 0, 0);
            final List<SysDictVO> sysDictVOList = new ArrayList<>();
            for (SysDictDTO dto : sysDictDTOList) {
                SysDictVO vo = new SysDictVO();
                BeanUtils.copyProperties(dto, vo);
                sysDictVOList.add(vo);
            }
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            t.setSuccessStatus();
            LOGGER.info("traceId: {}, complete {} request, overall cost {} ms.",
                    traceId, api, System.currentTimeMillis() - startTime.getMillis());

            return RespContent.<Object>success(sysDictVOList);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - startTime.getMillis());
            Cat.logError(e);
            t.setStatus(e);
            log.error("traceId: {}, {} request fail.", traceId, api, e);

            return RespContent.<Object>success(RespCodeEnum.SERVER_EXECUTE_ERROR,
                    String.format("%s request fail, error info: %s.", api, e.getMessage()));
        } finally {
            t.complete();
        }
    }
}
