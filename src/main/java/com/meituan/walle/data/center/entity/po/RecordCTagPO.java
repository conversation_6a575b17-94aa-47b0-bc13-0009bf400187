package com.meituan.walle.data.center.entity.po;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/4 下午4:17
 * Description:
 * Modified by
 */
@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RecordCTagPO implements Serializable {
    private Long id;
    private String recordName;
    private String vin;
    private Timestamp tagTime;
    private Integer tagType;
    private String tagContent;
    private String categoryType;
    private String sender;
    private String senderName;
    private Integer recordDate;
    private Timestamp createTime;
    private Timestamp updateTime;

    @Override
    public String toString(){
        try {
            return new ObjectMapper().writeValueAsString(this);
        } catch (JsonProcessingException e) {
            log.error("RecordCTagPO toString failed", e);
        }
        return "";
    }
}
