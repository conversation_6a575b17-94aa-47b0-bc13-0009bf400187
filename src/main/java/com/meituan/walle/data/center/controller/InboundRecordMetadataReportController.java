package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.params.InboundRecordMetadataParam;
import com.meituan.walle.data.center.service.InboundRecordMetadataService;
import com.meituan.walle.data.center.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2024/10/29
 */
@RestController
@RequestMapping("/inbound/record/metadata")
@Slf4j
public class InboundRecordMetadataReportController {

    @Autowired
    private InboundRecordMetadataService inboundRecordMetadataService;

    @PostMapping("/report")
    public Response report(InboundRecordMetadataParam param,
                           @RequestParam("file") MultipartFile file,
                           HttpServletRequest httpServletRequest) {
        Transaction t = Cat.newTransaction(CatConstant.VehicleUploadRequest, "inbound.record.metadata.report");
        long start = System.currentTimeMillis();
        try {
            log.info("inbound record metadata report, param is {}", JacksonUtil.toJson(param));
            inboundRecordMetadataService.report(param, file);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("inbound record metadata report fail", e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), e.getMessage());
        } finally {
            t.complete();
        }
    }
}
