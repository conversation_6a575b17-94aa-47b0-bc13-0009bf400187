package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/11/05
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum FileTypeEnum {

    /**
     * 未知
     */
    UNKNOWN(-1, "unknown"),
    /**
     * txt
     */
    TXT(0, "txt"),
    /**
     * rec
     */
    REC(1, "rec"),

    /**
     * rec文件的zip压缩格式
     */
    REC_ZIP(1, "rec.zip"),
    /**
     * img
     */
    IMAGE(2, "img"),
    /**
     * video
     */
    VIDEO(3, "video"),
    /**
     * bin
     */
    BIN(4, "bin"),
    /**
     * music
     */
    MUSIC(5, "music"),
    /**
     * log
     */
    LOG(6, "log"),
    /**
     * zip
     */
    ZIP(7,"zip");

    public static String getMsgByCode(Integer code) {
        if (Objects.isNull(code)) {
            return UNKNOWN.getMsg();
        }
        for (FileTypeEnum type : FileTypeEnum.values()) {
            if (type.getCode() == code.intValue()) {
                return type.getMsg();
            }
        }
        return UNKNOWN.getMsg();
    }

    public static FileTypeEnum getByMsg(String msg) {
        for (FileTypeEnum type : values()) {
            if (type.getMsg().equals(msg)) {
                return type;
            }
        }
        return UNKNOWN;
    }

    private int code;
    private String msg;

}
