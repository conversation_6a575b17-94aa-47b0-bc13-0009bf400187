package com.meituan.walle.data.center.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InboundRecordDTO {
    private String recordName;
    private Long beginTimestampS;
    private Long endTimestampS;
    private String owner;
    private String groupName;
    private String belongTo;
}
