package com.meituan.walle.data.center.dal.recordmanager.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024/06/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AutodriveEventPO {

    private String eventId;
    private Integer eventCode;
    private String eventName;
    private Timestamp eventTimestamp;
    private Timestamp senderTimestamp;
    private Timestamp receiverTimestamp;
    private String vin;
    private String vehicleId;
    private String vehicleName;
    private String recordName;
    private Integer utmZone;
    private String utmX;
    private String utmY;
    private String datasource;
    private String content;

}
