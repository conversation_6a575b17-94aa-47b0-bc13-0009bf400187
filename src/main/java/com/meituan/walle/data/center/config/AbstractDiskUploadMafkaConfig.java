package com.meituan.walle.data.center.config;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import org.springframework.beans.factory.annotation.Value;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2023/02/09
 */
public class AbstractDiskUploadMafkaConfig {
    private static final String NAMESPACE = "waimai";
    protected static final String TOPIC_S3_DISK_UPLOAD_FILE_NOTICE = "walle.disk.upload.file.notice";

    @Value("${appkey}")
    protected String appkey;

    protected IProducerProcessor<String, String> buildProducer(String topic, String appKey) throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appKey);
        return MafkaClient.buildProduceFactory(properties, topic);
    }

    protected IProducerProcessor<String, String> buildDelayProducer(String topic, String appKey) throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appKey);
        properties.setProperty(ConsumerConstants.MafkaDelayRetryCount, "3");

        return MafkaClient.buildDelayProduceFactory(properties, topic);
    }

    protected Properties getProperties(String appKey) {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, NAMESPACE);
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, appKey);
        properties.setProperty(ConsumerConstants.SubscribeGroup, appKey);
        properties.setProperty("fetch.message.max.bytes", "5242880");
        return properties;
    }
}