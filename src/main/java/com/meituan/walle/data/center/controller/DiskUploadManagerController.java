package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.dto.UploadProgressDTO;
import com.meituan.walle.data.center.entity.params.RecordUploadProcessDeleteParam;
import com.meituan.walle.data.center.exception.HardessException;
import com.meituan.walle.data.center.service.DiskUploadManagerService;
import com.meituan.walle.data.center.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/02/22
 */
@Slf4j
@RestController
@RequestMapping("/disk_upload_manager")
public class DiskUploadManagerController {

    @Autowired
    private DiskUploadManagerService uploadManagerService;

    @GetMapping("/get_record_upload_process")
    public Response getRecordUploadProcess(@RequestParam("recordName") String recordName,
                                           @RequestParam("uploadStage") String uploadStage) throws Exception {
        try {
            final String recordUploadProcess = uploadManagerService.getRecordUploadProcess(recordName, uploadStage);
            return Response.succ(JacksonUtil.deSerialize(recordUploadProcess, UploadProgressDTO.class));
        } catch (Exception e) {
            log.error("DiskUploadManagerController getRecordUploadProcess Exception.", e);
            return Response.fail(WebResponseStatusEnum.SYSTEM_ERROR.getCode().toString(),
                    "DiskUploadManagerController getRecordUploadProcess Exception. " + e.getMessage());
        }
    }

    private static void checkParam(RecordUploadProcessDeleteParam recordUploadProcessDeleteParam) {
        StringBuilder stringBuilder = new StringBuilder()
                .append("recordName : ")
                .append(recordUploadProcessDeleteParam.getRecordName());
        if (StringUtils.isBlank(recordUploadProcessDeleteParam.getRecordName())) {
            throw new HardessException(stringBuilder.append(" is empty.").toString());
        }
        if (recordUploadProcessDeleteParam.getValid() == null) {
            throw new HardessException(stringBuilder.append(" valid parameter illegal ").toString());
        }
        if (StringUtils.isBlank(recordUploadProcessDeleteParam.getInvalidDesc())) {
            throw new HardessException(stringBuilder.append(" invalidDesc parameter illegal ").toString());
        }
    }
}