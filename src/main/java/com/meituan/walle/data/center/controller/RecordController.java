package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.pojo.Record;
import com.meituan.walle.data.center.service.RecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/record")
public class RecordController {
    @Resource
    private RecordService recordService;

    @PostMapping("/add")
    public Response add(@RequestBody Record record) {
        recordService.add(record);
        return Response.succ(record);
    }

    @PostMapping("/update")
    public Response update(@RequestBody Record record) {
        recordService.update(record);
        return Response.succ(record);
    }

    @GetMapping("/get")
    public Response get(Record record) {
        List<Record> recordList = recordService.get(record);
        return Response.succ(recordList);
    }
}
