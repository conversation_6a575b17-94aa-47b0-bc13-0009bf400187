package com.meituan.walle.data.center.component;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.walle.data.center.config.mcc.FastUploadConfig;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.*;
import com.meituan.walle.data.center.entity.enums.OnCallListAutoAssignedEnum;
import com.meituan.walle.data.center.entity.pojo.BizEventDeriveIntervention;
import com.meituan.walle.data.center.entity.pojo.BizEvents;
import com.meituan.walle.data.center.entity.pojo.LiveIssueTag;
import com.meituan.walle.data.center.entity.pojo.OnCallList;
import com.meituan.walle.data.center.entity.vo.EventMessageVO;
import com.meituan.walle.data.center.entity.vo.VehicleUploadRequestVO;
import com.meituan.walle.data.center.handle.IRedisHandler;
import com.meituan.walle.data.center.mapper.LiveIssueTagMapper;
import com.meituan.walle.data.center.service.OnCallListDualService;
import com.meituan.walle.data.center.service.OnCallListService;
import com.meituan.walle.data.center.service.VehicleInfoService;
import com.meituan.walle.data.center.service.VehicleUploadRequestService;
import com.meituan.walle.data.center.util.JacksonUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import walle.common.SystemTypeOuterClass;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@Log4j2
public class EventInterventionHandler {
    @Resource
    private IRedisHandler redisHandler;

    @Resource
    private PikeConnectListener pikeConnectListener;

    @Resource
    private VehicleUploadRequestService vehicleUploadRequestService;

    @Resource
    private VehicleInfoService vehicleInfoService;

    @Resource
    private LiveIssueTagMapper liveIssueTagMapper;

    @Resource
    private OnCallListService onCallListService;

    @Autowired
    private OnCallListDualService onCallListDualService;

    private static final ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("EventInterventionHandler-%d").build();
    private static final ThreadPoolExecutor THREAD_POOL =
            new ThreadPoolExecutor(32, 32, 0,
                    TimeUnit.SECONDS, new ArrayBlockingQueue<>(10000), namedThreadFactory);

    private static final String DEMOTION_TAG = "自动驾驶能力问题";

    private static final String SYSTEM_NAME = "walle";

    @Async
    public void judgeDemotionIntervention(EventMessageVO eventMessageVO) {
        BizEvents bizEvents = eventMessageVO.getBizEvents();
        List<Map<String, Object>> msgList = eventMessageVO.getMsgList();
        Map<String, Object> preEventMsg;
        Map<String, Object> eventMsg;
        if (msgList.size() == 2) {
            preEventMsg = msgList.get(0);
            eventMsg = msgList.get(0);
        } else {
            preEventMsg = msgList.get(0);
            eventMsg = msgList.get(1);
        }
        boolean isDemotionCase = isDemotion(preEventMsg);
        long timestamp = getTimestamp(eventMsg);
        log.info("[EventInterventionHandler#judgeDemotionIntervention] eventMessageVO: {}, isDemotionCase: {}",
                JacksonUtil.serialize(eventMessageVO), isDemotionCase);
        if (isDemotionCase) {
            //直接插入标签到数据库
            LiveIssueTag liveIssueTag = buildTagFromEvent(timestamp, bizEvents);
            liveIssueTagMapper.insertTag(liveIssueTag);
        }
    }

    public static long getTimestamp(Map<String, Object> eventMsg) {
        Map<String, Object> headerMap = (Map<String, Object>) eventMsg.get("header");
        return Long.parseLong(headerMap.get("timestamp").toString());
    }


    public void addVehicleUploadRequest(String vin, Long timestamp, String eventId) {
        VehicleUploadRequestVO vehicleUploadRequestVO = new VehicleUploadRequestVO();
        try {
            vehicleUploadRequestVO.setVin(vin);
            vehicleUploadRequestVO.setMeasurementTimestamp(timestamp);
            vehicleUploadRequestVO.setInterventionId(eventId);
            vehicleUploadRequestVO.setExtraInfo(FastUploadConfig.VEHICLE_UPLOAD_CASE_DATA_EXTRA_INFO);
            vehicleUploadRequestService.addRequestFromEventIntervention(
                    vehicleUploadRequestVO, true, null);
        } catch (Exception e) {
            log.error("Add task to vehicle_upload_request table failed, eventId is {}", eventId, e);
        }
    }


    public void emitTag(String vin, String eventId, int interventionType, long timestamp) {
        //允许别的设备去获取数据
        long eventMillis = timestamp / CommonConstant.NANO_TO_MILLIS;
        long currentTimeMillis = System.currentTimeMillis();
        if (currentTimeMillis - eventMillis > SysParamsConfig.abandon_event_millis) {
            log.info("event abandon by timeout: {}", eventId);
            return;
        }
        String value = String.format("%s,%s", eventId, interventionType);
        redisHandler.set(RedisConstant.WAITING_EVENT, vin, value, SysParamsConfig.event_cache_seconds);
        pikeConnectListener.sendFreshEvent(vin, eventId, interventionType);

    }

    // 是否为降级case
    public boolean isDemotion(Map<String, Object> eventMsg) {
        Map<String, Object> chassis = (Map<String, Object>) eventMsg.get("canbusChassis");
        if (!chassis.containsKey("softArbitrationSystem")) {
            return false;
        }
        String softArbitrationSystem = chassis.get("softArbitrationSystem").toString();
        return SystemTypeOuterClass.SystemType.PARKING.name().equals(softArbitrationSystem);
    }

    public LiveIssueTag buildTagFromEvent(long timestamp, BizEvents bizEvents) {
        LiveIssueTag liveIssueTag = new LiveIssueTag();
        BeanUtils.copyProperties(bizEvents, liveIssueTag);
        liveIssueTag.setMeasurementTimestamp(timestamp);
        liveIssueTag.setT(new Date(timestamp / CommonConstant.NANO_TO_MILLIS));
        liveIssueTag.setTagName(DEMOTION_TAG);
        liveIssueTag.setClientType(LiveIssueTagClientTypeEnum.DEMOTION_AUTO.getRet());
        liveIssueTag.setDescription(LiveIssueTagClientTypeEnum.DEMOTION_AUTO.getMsg());
        liveIssueTag.setMisid(SYSTEM_NAME);
        liveIssueTag.setVehicleName(vehicleInfoService.getVehicleName(bizEvents.getVin()));
        liveIssueTag.setUuid(UUID.randomUUID().toString().replace("-", ""));
        return liveIssueTag;
    }

    public DrivingModeEnum getDrivingMode(Map<String, Object> msg) {
        Map<String, Object> preChassis = (Map<String, Object>) msg.get("canbusChassis");
        String preDrivingMode = preChassis.get("drivingMode").toString();
        return DrivingModeEnum.valueOf(preDrivingMode);
    }


    // 实时的接管，先插入到oncall_list表
    public void addCaseToOnCallList(String vin, String eventId, Date eventTime,
                                    String recordName) {
        OnCallList onCallList = OnCallList.builder()
                .vin(vin)
                .state(CaseStateEnum.CREATED.getMessage())
                .operateTime(eventTime)
                .caseId(eventId)
                .dataSource(CasesSourceEnum.INTERVENTION.getMsg())
                .operationStatus(CaseOperationStatusEnum.CREATED.getMessage())
                .relateCase("") //这个字段没有默认值，所以需要设置一下
                .send("") //这个字段没有默认值，所以需要设置一下
                .isAutoAssigned(OnCallListAutoAssignedEnum.UNMATCHED.getCode())
                .recordName(recordName)
                .build();

        if (SysParamsConfig.intervention_case_sortable_switch) {
            onCallListService.insert(onCallList);
            onCallListDualService.insert(onCallList);
        } else {
            onCallListDualService.insert(onCallList);
        }
    }


    public void addCaseSync(BizEventDeriveIntervention intervention, int interventionType) {
        THREAD_POOL.submit(() -> {
            addCaseToOnCallList(intervention.getVin(), intervention.getEventId(),
                    intervention.getInterventionTime(), intervention.getRecordName());
            log.info("[EventInterventionHandler#addCaseSync] addCaseToOnCallList, " +
                            "intervention: {}, interventionType: {}",
                    JacksonUtil.serialize(intervention), interventionType);
            // 查看是否为降级case
            List<Integer> liveIssueTags = liveIssueTagMapper.selectByEventIdAndClientType(
                    intervention.getEventId(), LiveIssueTagClientTypeEnum.DEMOTION_AUTO.getRet());
            log.info("[EventInterventionHandler#addCaseSync] selectByEventIdAndClientType, " +
                            "intervention: {}, interventionType: {}, liveIssueTags: {}",
                    JacksonUtil.serialize(intervention), interventionType, JacksonUtil.serialize(liveIssueTags));
            // 如果是降级case，则需要等待计算端匹配完成后，创建快传任务(主要是需要降级事件的时间)，
            // 创建逻辑在InterventionMatchEventHandler这个类里。
            if (!liveIssueTags.isEmpty()) {
                return;
            }
            // 非降级case，则直接创建快传任务, 下发打标签
            addVehicleUploadRequest(intervention.getVin(), intervention.getInterventionTimestamp(),
                    intervention.getEventId());
            int consumeTimes = 0;
            while (consumeTimes < 5) {
                try {
                    emitTag(intervention.getVin(), intervention.getEventId(),
                            interventionType, intervention.getInterventionTimestamp());
                    log.info("[EventInterventionHandler#addCaseSync] emitTag, " +
                                    "intervention: {}, interventionType: {}",
                            JacksonUtil.serialize(intervention), interventionType);
                    return;
                } catch (Exception e) {
                    consumeTimes++;
                    log.error("emit tag failed: {}", intervention, e);
                }
            }
        });
    }
}
