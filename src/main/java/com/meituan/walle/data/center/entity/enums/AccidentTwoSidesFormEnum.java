package com.meituan.walle.data.center.entity.enums;

import com.meituan.walle.data.center.constant.CharConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/12/14
 */

@Getter
@AllArgsConstructor
public enum AccidentTwoSidesFormEnum {

    VEHICLE("0", "车与车"),
    VEHICLE_DIRECT_IMPACT("1", "车与车-正碰"),
    VEHICLE_SIDE_IMPACT("2", "车与车-侧碰"),
    VEHICLE_READ_END("3", "车与车-追尾"),
    VEHICLE_SYN_SCRAPE("4", "车与车-同向刮擦"),
    VEHICLE_OPPOSITE_SCRAPE("5", "车与车-对向刮擦"),
    VEHICLE_STATIC_COLLISION("6", "车与车-碰撞静止车辆"),
    PEDESTRIAN("100", "车与人"),
    PEDESTRIAN_BARGE("101", "车与人-乱撞行人"),
    PEDESTRIAN_GRIND("102", "车与人-碾压行人"),
    PEDESTRIAN_BARGE_GRIND("103", "车与人-碰撞后碾压行人"),
    ONE_SIDE("200", "单方事故"),
    ONE_SIDE_ROLLOVER("201", "单方事故-侧翻"),
    ONE_SIDE_ROLL("202", "单方事故-翻滚"),
    ONE_SIDE_CRASH("203", "单方事故-坠车"),
    ONE_SIDE_CRASH_SOLID("204", "单方事故-撞固定物"),
    ;

    private String code;
    private String msg;

    public static List<Object> getCodeMsgMap() {
        List<Object> result = new ArrayList<>();
        List<AccidentTwoSidesFormEnum> formEnums = new ArrayList<>();
        formEnums.add(VEHICLE);
        formEnums.add(PEDESTRIAN);
        formEnums.add(ONE_SIDE);
        for (AccidentTwoSidesFormEnum accidentTwoSidesFormEnum: formEnums) {
            String label = accidentTwoSidesFormEnum.getMsg();
            Map<String, Object> map = new HashMap<>();
            map.put("label", label);
            map.put("value", accidentTwoSidesFormEnum.getCode());
            List<Object> children = new ArrayList<>();
            for (AccidentTwoSidesFormEnum childrenEnum: AccidentTwoSidesFormEnum.values()) {
                String msg = childrenEnum.getMsg();
                String[] childrenLabels = msg.split(CharConstant.CHAR_HX);
                if (childrenLabels.length >= 2 && label.equals(childrenLabels[0])) {
                    Map<String, Object> childrenMap = new HashMap<>();
                    childrenMap.put("label", childrenLabels[1]);
                    childrenMap.put("value", childrenEnum.getCode());
                    children.add(childrenMap);
                }
            }
            map.put("children", children);
            result.add(map);
        }
        return result;
    }
}
