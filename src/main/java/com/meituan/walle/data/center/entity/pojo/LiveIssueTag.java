package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "live_issue_tag")
@Data
public class LiveIssueTag {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 表record的record_name外键
     */
    private String recordName;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    private Date t;

    private Integer categoryL1;

    private Integer categoryL2;

    private String voiceContent = "";

    private String description;

    private String interventionId;
    private Integer sponsor;

    /**
     * 事件Id
     */
    private String eventId;

    /**
     * 是否发送到oncall群
     */
    private Boolean sendToOncall;

    /**
     * 是否回传数据
     */
    private Boolean isUpload;

    private Long measurementTimestamp;

    private Long alertTimestamp;

    private Date createTime;

    private Date updateTime;

    private Integer clientType;

    private String triggerType;

    private String vehicleName;

    private String tagName;

    private String misid;

    private String uuid;

    @Column(name = "is_restore")
    private Integer restore;


    @Override
    public String toString() {
        return "LiveIssueTag{" +
                "id=" + id +
                ", recordName='" + recordName + '\'' +
                ", vin='" + vin + '\'' +
                ", t=" + t +
                ", categoryL1=" + categoryL1 +
                ", categoryL2=" + categoryL2 +
                ", description=" + description +
                ", eventId=" + eventId +
                ", voiceContent='" + voiceContent + '\'' +
                ", measurementTimestamp=" + measurementTimestamp +
                ", alertTimestamp=" + alertTimestamp +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", clientType=" + clientType +
                ", sendToOncall=" + sendToOncall +
                ", isUpload=" + isUpload +
                ", triggerType='" + triggerType + '\'' +
                ", vehicleName='" + vehicleName + '\'' +
                ", tagName='" + tagName + '\'' +
                ", misid='" + misid + '\'' +
                ", uuid='" + uuid + '\'' +
                ", restore=" + restore +
                '}';
    }

    public String getTagNameDx() {
        return tagName != null ? tagName : "无";
    }
}