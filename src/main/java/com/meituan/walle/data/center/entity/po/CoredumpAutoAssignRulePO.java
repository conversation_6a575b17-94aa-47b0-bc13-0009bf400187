package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2022/9/9 16:16
 * @descriotion
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class CoredumpAutoAssignRulePO {
    /**
     * 自增ID
     */
    private Long id;

    private String keyword;

    /**
     * misId
     */
    private String misId;

    private String fileName;
    /**
     * type
     */
    private String type;
    private Integer level;
    private Integer priority;
    private Integer timeLimit;
    private Boolean isDeleted;

    /**
     * 添加时间
     */
    private Timestamp createTime;

    /**
     * 更新时间
     */
    private Timestamp updateTime;

}