package com.meituan.walle.data.center.entity.dto;

import lombok.Data;

import java.util.Collection;

@Data
public class FastUploadTaskRecRangeDTO {
    // 任务id
    private String uploadTaskId;

    // 车架号
    private String vin;

    // 任务开始时间
    private Long taskStartNano;

    // 任务结束时间
    private Long taskEndNano;

    // 任务开始时间
    private String taskStartTime;

    // 任务结束时间
    private String taskEndTime;

    // recordName查询的开始时间
    private String recordNameStart;

    // recordName查询的结束时间
    private String recordNameEnd;

    // 模块列列表
    private Collection<String> modules;

    // 用于标识快速回传数据是否需要过滤掉任务3和6的部分
    private Integer dataFlag;
}
