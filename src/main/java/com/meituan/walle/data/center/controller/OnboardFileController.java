package com.meituan.walle.data.center.controller;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.*;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.pojo.BizOnboardPreUploadFile;
import com.meituan.walle.data.center.entity.request.BizOnboardPreUploadFileRequest;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.handle.impl.BizOnboardFileHandler;
import com.meituan.walle.data.center.service.BizOnboardFileService;
import com.meituan.walle.data.center.service.BizOnboardPreUploadFileService;
import com.meituan.walle.data.center.util.DatetimeUtil;
import com.meituan.walle.data.center.util.JacksonUtil;
import com.meituan.walle.data.center.util.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/01/19
 */
@InterfaceDoc(
        displayName = "文件信息上报相关接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供文件信息上报的能力。",
        scenarios = "文件信息上报处理所有接口"
)
@RestController
@RequestMapping(value = "/onboard_file")
@Slf4j
public class OnboardFileController {

    @Autowired
    private BizOnboardFileService bizOnboardFileService;

    @Autowired
    private BizOnboardFileHandler bizOnboardFileHandler;

    @Resource
    private BizOnboardPreUploadFileService bizOnboardPreUploadFileService;

    private static final String API_PREFIX = "/onboard_file";

    @MethodDoc(
            displayName = "文件信息上报",
            description = "上报文件信息")
    @PostMapping(value = "/file_metadata")
    public Response report(@RequestBody List<String> fileMetadataList) {
        if (fileMetadataList == null) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_FILE_METADATA_REPORT_REQUEST_PARAM_ILLEGAL
                            .getCode().toString(),"param is null !");
        }
        Transaction t = Cat.newTransaction(CatConstant.ONBOARD_FILE, "file.metadata");
        long start = System.currentTimeMillis();
        try {
            if (SysParamsConfig.onboard_file_metadata_produce_switch) {
                bizOnboardFileService.send(fileMetadataList);
            }
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            if (SysParamsConfig.onboard_file_metadata_produce_switch) {
                return Response.succ("");
            }
            return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(),
                    WebResponseStatusEnum.LOGICAL_EXCEPTION.getMsg());
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("onboard file metadata report fail !", e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(),
                    WebResponseStatusEnum.LOGICAL_EXCEPTION.getMsg());
        } finally {
            t.complete();
        }
    }

    @GetMapping("/getCachePreUploadFile")
    public Response getCachePreUploadFile() {
        final Map<String, String> result = new HashMap<>();
        try {
            JSONObject jsonObject = new JSONObject();
            Map<String, String> allCreateTaskRecordMap = bizOnboardFileHandler.getPreUploadFile();
            Set<String> allTreeSet = new TreeSet<>();
            for (Map.Entry<String, String> entry : allCreateTaskRecordMap.entrySet()) {
                allTreeSet.add(entry.getKey());
            }
            jsonObject.put("pre_upload_file_count", allTreeSet.size());
            jsonObject.put("pre_upload_file_list", JacksonUtil.toJsonArray(allTreeSet));

            result.put(ResponseConstant.RESULT_RET_NAME, WebResponseStatusEnum.SUCCESS.getCode().toString());
            result.put(ResponseConstant.RESULT_MSG_NAME, WebResponseStatusEnum.SUCCESS.getMsg());
            return Response.result(result, jsonObject);
        } catch (Exception e) {
            result.put(ResponseConstant.RESULT_RET_NAME, WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString());
            result.put(ResponseConstant.RESULT_MSG_NAME, WebResponseStatusEnum.LOGICAL_EXCEPTION.getMsg());
            result.put(ResponseConstant.RESULT_DATA_NAME, e.getMessage());
            log.error("getCachePreUploadFile fail !", e);
            return Response.result(result);
        }
    }

    @PostMapping("/create_task_by_file")
    public Response createTaskByFile(@RequestBody BizOnboardPreUploadFileRequest request) {
        String api = "/create_task_by_file";
        Transaction t = Cat.newTransaction(CatConstant.API, API_PREFIX + api);
        String traceId = Trace.generateId();
        long start = System.currentTimeMillis();
        if (request == null || StringUtils.isBlank(request.getFileName()) ||
                StringUtils.isBlank(request.getFilePath()) ||
                request.getFileType() == null || request.getFileSize() == null) {
            log.warn("traceId is {}, create task by file failed, request data is [{}]", request);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), "request lack some params");
        }
        if (StringUtils.isBlank(request.getFileCreateTime()) ||
                StringUtils.isBlank(request.getFileLastModifyTime()) ||
                StringUtils.isBlank(request.getFileAbsolutePath())) {
            log.warn("traceId is {}, create task by file failed, lack some field, request data is [{}", request);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), "request lack some params");
        }
        try {
            BizOnboardPreUploadFile bizOnboardPreUploadFile = new BizOnboardPreUploadFile();
            BeanUtils.copyProperties(request, bizOnboardPreUploadFile);
            Date fileCreateTime = DatetimeUtil.covertToDate(request.getFileCreateTime(), DatetimeUtil.YMDHMSDotSSS);
            Date fileLastModifyTime =
                    DatetimeUtil.covertToDate(request.getFileLastModifyTime(), DatetimeUtil.YMDHMSDotSSS);
            bizOnboardPreUploadFile.setFileCreateTime(fileCreateTime);
            bizOnboardPreUploadFile.setFileLastModifyTime(fileLastModifyTime);
            Long fileCreateTimeNano = fileCreateTime.getTime() * CommonConstant.NANO_TO_MILLIS;
            log.info("traceId: {}, start to create task by file, request is [{}]", traceId, request);
            bizOnboardPreUploadFileService.preUpload(bizOnboardPreUploadFile, fileCreateTimeNano);
            log.info("traceId: {}, success to create task by file, request is [{}]", traceId, request);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("traceId: {}, create task by file failed, request is [{}]", traceId, request, e);
            Cat.logError(e);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(), "Service error");
    }

    @GetMapping(value = "/config")
    public CommonResponse queryConfig(String vin) {
        // 这里的vin是预留参数，方便后续可以根据vin做更细的控制
        return CommonResponse.success(SysParamsConfig.vehicle_config);
    }
}