package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/08/10
 */

@Data
@Table(name = "biz_vehicle_data_related_config")
public class BizVehicleDataRelatedConfig {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 是否寄盘
     */
    private Boolean isSendDisk;

    /**
     * 是否开启快速上传开关
     */
    private Boolean isOpenFastUpload;

    /**
     * 磁盘模式（单盘/双盘）
     */
    private Integer diskMode;

    /**
     * 是否逻辑删除
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最近一次更新时间
     */
    private Date updateTime;

}
