package com.meituan.walle.data.center.entity.po;

import lombok.*;

import java.sql.Timestamp;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/26 下午2:45
 * Description:
 * Modified by
 */
@<PERSON><PERSON><PERSON>
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RecordPkgPO {

    private Long id;
    private String recordName;
    private String fileName;
    private String md5;
    private String s3Url;
    private String s3DsnUrl;
    private String module;
    private Timestamp startTime;
    private Timestamp endTime;
    private Timestamp firstMsgTime;
    private Integer msgCount;
    private String topics;
    private Long fileSize;
    private Double duration;
    private Integer s3IsDeleted;
    private Integer s3IsMarkedForDeletion;
    private Timestamp createTime;
    private Timestamp updateTime;

}
