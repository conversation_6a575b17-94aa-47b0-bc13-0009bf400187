package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * Created by leihui on 2020/12/31.
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Table(name = "tag")
public class Tag {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 所属分类code
     */
    @Column(name = "category_code")
    private String categoryCode;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 创建人misid
     */
    @Column(name = "create_by")
    private String createBy;


    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;


}
