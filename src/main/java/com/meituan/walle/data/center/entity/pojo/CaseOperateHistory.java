package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/04/13
 */
@Data
@Table(name = "case_operate_history")
public class CaseOperateHistory {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    @Column(name = "case_id")
    private String caseId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 操作描述
     */
    private String description;

    /**
     * 上一次记录描述
     */
    private String from;

    @Column(name = "operator_id")
    private String operatorId;

    private String source;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}