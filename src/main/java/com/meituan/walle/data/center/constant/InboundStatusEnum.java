package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum InboundStatusEnum {
    DEFAULT("DEFAULT", "默认值"),
    INITIALIZING("INITIALIZING", "初始化中"),
    //（合法性校验通过且写入Record file）
    INITIALIZED("INITIALIZED", "初始化完成"),
    UPLOADING("UPLOADING", "上传中"),
    UPLOADED("UPLOADED", "上传完成"),
    PARSING("PARSING", "解析中"),
    PARSED("PARSED", "解析完成"),
    VALIDATING("VALIDATING", "校验中"),
    COMPLETED("COMPLETED", "入库完成"),
    FAILED("FAILED", "入库失败"),
    ;

    private String code;
    private String desc;


    public static InboundStatusEnum byCode(String code) {
        for (InboundStatusEnum en : InboundStatusEnum.values()) {
            if (en.code.equalsIgnoreCase(code)) {
                return en;
            }
        }
        return null;
    }
}
