package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/12/28
 */
@Getter
@AllArgsConstructor
public enum SparkJobEnum {

    PARSE_MSTAT_INFO("parse_mstat_info", "e964631fd1f6e92f683889746798c2cf");
    private String name;
    private String value;

    public static SparkJobEnum get(String name) {
        for (SparkJobEnum en : SparkJobEnum.values()) {
            if (Objects.equals(en.name, name)) {
                return en;
            }
        }
        return null;
    }
}