package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "battery_cupboard_file")
@Data
public class BatteryCupboardFile {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 电池柜编号
     */
    private String batteryCupboardNumber;

    /**
     * CAN记录仪编号
     */
    private String canNumber;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * S3地址
     */
    private String s3Url;

    /**
     * 文件大小，单位Byte
     */
    private Long fileSize;

    /**
     * 文件开始时间
     */
    private Date startTime;

    /**
     * 文件结束时间
     */
    private Date endTime;

    private Boolean isDeleted;

    private Date createTime;

    private Date updateTime;
}
