package com.meituan.walle.data.center.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/03/23
 */
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public class BizIssueRemarkDTO implements Serializable {

    @NotNull(message = "评论内容remarkText不能为空")
    private String remarkText;

    @NotNull(message = "评论的对象ID不能为空")
    private String caseId;

    @NotNull(message = "评论者不能为空")
    private String createdBy;

    @NotNull(message = "评论的父级不能为空")
    private Long parentId;

    private String toUsers;

    private String resource;

}
