package com.meituan.walle.data.center.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.rhino.cluster.common.util.AssertUtil;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.walle.data.center.config.mcc.MadGeneralCaseConfig;
import com.meituan.walle.data.center.constant.MadDynamicColumnEnum;
import com.meituan.walle.data.center.constant.MadGeneralCaseTypeEnum;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.request.*;
import com.meituan.walle.data.center.entity.vo.CaseWorkstationPage;
import com.meituan.walle.data.center.entity.vo.MadGeneralCaseDetailVO;
import com.meituan.walle.data.center.entity.vo.OriginDatasourceConfigVO;
import com.meituan.walle.data.center.entity.vo.SearchCaseDetailVO;
import com.meituan.walle.data.center.handle.impl.DataConfigUpdater;
import com.meituan.walle.data.center.service.OnCallListService;
import com.meituan.walle.data.center.service.WorkstationCaseService;
import com.meituan.walle.data.center.service.impl.OriginDatasourceConfigServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/22
 * @description gmc工作台接口
 */
@RestController
@RequestMapping(value = "/mgc")
@Slf4j
public class MadGeneralCaseController {

    @Resource
    private OriginDatasourceConfigServiceImpl originDatasourceConfigService;

    @Resource
    private DataConfigUpdater dataConfigUpdater;

    @Resource
    private OnCallListService onCallListService;

    @Resource
    private WorkstationCaseService workstationCaseService;

    @Lazy
    @Resource(name = "toGeneralCaseProducer")
    private IProducerProcessor<String, String> toGeneralCaseProducer;

    /**
     * 数据源和字段配置新增更新接口，存在更新，不存在新增
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/datasource/upsert")
    public Response datasourceUpsert(@RequestBody DatasourceUpsertRequest request) {
        try {
            originDatasourceConfigService.upsert(request);
            return Response.succ();
        } catch (Exception e) {
            log.error("upsert mgc datasource config error, request param is {}", request, e);
            return Response.fail(String.valueOf(WebResponseStatusEnum.FAILED.getCode()),
                    WebResponseStatusEnum.FAILED.getMsg());
        }
    }

    /**
     * 数据源+字段配置列表
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/datasource/list")
    public Response datasourceList(@RequestBody DatasourceQueryRequest request) {
        try {
            CaseWorkstationPage<OriginDatasourceConfigVO> pageResponse = originDatasourceConfigService
                    .queryDatasourcePageList(request);
            return Response.succ(pageResponse);
        } catch (Exception e) {
            log.error("get mgc datasource config list error, request param is {}", request, e);
            return Response.fail(String.valueOf(WebResponseStatusEnum.FAILED.getCode()),
                    WebResponseStatusEnum.FAILED.getMsg());
        }
    }


    /**
     * 动态更新/创建数据提取器-立即部署
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/datasource/deploy")
    public Response datasourceDeploy(@RequestBody DatasourceConfigUpdateRequest request) {
        try {
            if (!CollectionUtils.isEmpty(request.getOriginDatasourceNames())) {
                for (String originDatasourceName : request.getOriginDatasourceNames()) {
                    dataConfigUpdater.update(originDatasourceName);
                }
            }
            return Response.succ();
        } catch (Exception e) {
            log.error("deploy mgc data config error, originDatasourceName: {}",
                    request.getOriginDatasourceNames(), e);
            return Response.fail(String.valueOf(WebResponseStatusEnum.FAILED.getCode()),
                    WebResponseStatusEnum.FAILED.getMsg());
        }
    }

    @PostMapping(value = "/case/conditions")
    public Response caseConditions(@RequestBody MGCConditionRequest request) {
        try {
            Map<String, Object> map = new HashMap<>();
            if (!CollectionUtils.isEmpty(request.getConditionKeys())) {
                for (String key : request.getConditionKeys()) {
                    if (MadDynamicColumnEnum.DEPARTURE_FAILED_ERROR_CODE.getName().equals(key)) {
                        map.put(key, JSON.parseArray(MadGeneralCaseConfig.DEPARTURE_FAILED_ERROR_CODE_LIST));
                    }
                }
            }
            return Response.succ(map);
        } catch (Exception e) {
            log.error("get mgc condition error, request param is {}", request, e);
            return Response.fail(String.valueOf(WebResponseStatusEnum.FAILED.getCode()),
                    WebResponseStatusEnum.FAILED.getMsg());
        }
    }

    /**
     * case列表查询.直接查mysql库的方式。暂时不用
     *
     * @param request
     * @returnc
     */
    @PostMapping(value = "/case/list")
    public Response caseList(@RequestBody MGCSearchRequest request) {
        try {
            AssertUtil.isTrue(!StringUtils.isEmpty(request.getCaseType())
                    || !CollectionUtils.isEmpty(request.getCaseTypes()), "caseType不能为空");
            CaseWorkstationPage<MadGeneralCaseDetailVO> response = onCallListService.searchCaseList(request);
            return Response.succ(response);
        } catch (Exception e) {
            log.error("search mgc case list error, request param is {}", request, e);
            return Response.fail(String.valueOf(WebResponseStatusEnum.FAILED.getCode()),
                    WebResponseStatusEnum.FAILED.getMsg());
        }
    }

    /**
     * es 二级索引方式查询列表
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/v1/case/list")
    public Response caseListV2(@RequestBody CaseSearchRequest request) {
        try {
            AssertUtil.isTrue(!StringUtils.isEmpty(request.getCaseType())
                    || !CollectionUtils.isEmpty(request.getCaseTypes()), "caseType不能为空");
            CaseWorkstationPage<SearchCaseDetailVO> response = workstationCaseService.search(request);
            return Response.succ(response);
        } catch (Exception e) {
            log.error("search mgc case list error, request param is {}", request, e);
            return Response.fail(String.valueOf(WebResponseStatusEnum.FAILED.getCode()),
                    WebResponseStatusEnum.FAILED.getMsg());
        }
    }

    /**
     * case详情
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/case/detail")
    public Response caseDetail(@RequestBody MGCSearchRequest request) {
        try {
            AssertUtil.assertNotBlank(request.getCaseId(), "caseId不能为空");
            MadGeneralCaseDetailVO detail = onCallListService.queryGeneralCaseDetail(request.getCaseId());
            return Response.succ(detail);
        } catch (Exception e) {
            log.error("search mgc case detail error, request param is {}", request, e);
            return Response.fail(String.valueOf(WebResponseStatusEnum.FAILED.getCode()),
                    WebResponseStatusEnum.FAILED.getMsg());
        }
    }


    /**
     * case详情
     *
     * @param message
     * @return
     */
    @PostMapping(value = "/case/report")
    public Response caseReport(@RequestBody String message) {
        try {
            JSONObject jsonObject = JSON.parseObject(message);
            MadGeneralCaseTypeEnum typeEnum = MadGeneralCaseTypeEnum.lookup(jsonObject.getString("caseType"));
            if (typeEnum == null) {
                return Response.fail(String.valueOf(WebResponseStatusEnum.FAILED.getCode()),
                        "wrong caseType parameter:" + jsonObject.getString("caseType"));
            }
            // 写入mafka
            toGeneralCaseProducer.sendMessage(message);
            return Response.succ();
        } catch (Exception e) {
            log.error("report mgc general case info error, request param is {}", message, e);
            return Response.fail(String.valueOf(WebResponseStatusEnum.FAILED.getCode()),
                    WebResponseStatusEnum.FAILED.getMsg());
        }
    }

}
