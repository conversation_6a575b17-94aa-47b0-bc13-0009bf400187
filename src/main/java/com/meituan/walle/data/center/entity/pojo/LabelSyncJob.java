package com.meituan.walle.data.center.entity.pojo;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Table(name = "label_sync_job")
public class LabelSyncJob {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 同步任务id
     */
    @Column(name = "batch_id")
    private String batchId;

    /**
     * 数据源地址
     */
    @Column(name = "hdfs_address")
    private String hdfsAddress;

    /**
     * 0.初始|10.处理中|100.完成
     */
    private Short status;

    /**
     * 获取主键id
     *
     * @return id - 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取同步任务id
     *
     * @return batch_id - 同步任务id
     */
    public String getBatchId() {
        return batchId;
    }

    /**
     * 设置同步任务id
     *
     * @param batchId 同步任务id
     */
    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    /**
     * 获取数据源地址
     *
     * @return hdfs_address - 数据源地址
     */
    public String getHdfsAddress() {
        return hdfsAddress;
    }

    /**
     * 设置数据源地址
     *
     * @param hdfsAddress 数据源地址
     */
    public void setHdfsAddress(String hdfsAddress) {
        this.hdfsAddress = hdfsAddress;
    }

    /**
     * 获取0.初始|10.处理中|100.完成
     *
     * @return status - 0.初始|10.处理中|100.完成
     */
    public Short getStatus() {
        return status;
    }

    /**
     * 设置0.初始|10.处理中|100.完成
     *
     * @param status 0.初始|10.处理中|100.完成
     */
    public void setStatus(Short status) {
        this.status = status;
    }
}