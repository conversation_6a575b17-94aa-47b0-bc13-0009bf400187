package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/6 下午3:02
 * Description:
 * Modified by
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RecordStatusEnum {
    /**
     * record启动录制
     */
    RECORD_START(0, "record启动录制"),
    /**
     * ecord结束录制
     */
    RECORD_END(1, "record结束录制"),
    /**
     * record开始解析
     */
    RECORD_PARSING_START(2, "record开始解析"),
    /**
     * record结束解析
     */
    RECORD_PARSING_END(3, "record结束解析"),
    /**
     * record开始上传
     */
    RECORD_UPLOADING_START(4, "record开始上传"),
    /**
     * record结束上传
     */
    RECORD_UPLOADING_END(5, "record结束上传"),
    /**
     * record数据ready
     */
    RECORD_DATA_READY(6, "record数据ready"),

    BEGIN_UPLOAD(100, "已开始上传"),
    UPLOAD_SUCCESS(200, "已上传完"),
    UPLOAD_INTERRUPT(250, "异常中断"),
    TRIGGER_SPARK(300, "已触发解析"),
    REC_AND_INFO_UPLOADED_S3(350, "rec和record_info.pb.txt文件已经上传到s3"),
    ALL_FILES_UPLOADED_S3(360, "record下的所有文件已经上传到s3"),

    ;

    private int code;
    private String msg;

}
