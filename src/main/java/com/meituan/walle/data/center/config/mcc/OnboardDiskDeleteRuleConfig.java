package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/11/29
 */
@Component
public class OnboardDiskDeleteRuleConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.disk.delete.rule.lower.limit.delete.rate")
    public static volatile int LOWER_LIMIT_DELETE_RATE = 70;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.disk.delete.upper.limit.delete.rate")
    public static volatile int UPPER_LIMIT_DELETE_RATE = 79;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.disk.delete.not.expected.thresh")
    public static volatile int DISK_DELETE_NOT_EXPECTED_THRESH = 81;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.disk.remove.record.days")
    public static volatile int REMOVE_RECORD_DAYS = 3;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.disk.delete.rule.priority.module")
    public static volatile String PRIORITY_MODULE = "Localization,Localization_v2,Canbus";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.disk.delete.rule.day.hours")
    public static volatile int DAY_HOURS = 6;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.disk.delete.rule.day.previous.time")
    public static volatile int PREVIOUS_TIME = 8;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.disk.delete.rule.day.behind.time")
    public static volatile int BEHIND_TIME = 2;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.disk.delete.rule.accident.freeze.days")
    public static volatile int ACCIDENT_FREEZE_DAYS = 3;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.disk.data.freeze.task.type.str")
    public static volatile String data_freeze_task_type_str = "4";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.disk.delete.rule.accident.freeze.before.minutes")
    public static volatile int ACCIDENT_FREEZE_BEFORE_MINUTES = 5;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.disk.delete.rule.accident.freeze.after.minutes")
    public static volatile int ACCIDENT_FREEZE_AFTER_MINUTES = 5;



}