package com.meituan.walle.data.center.entity.pojo;

import lombok.*;
import javax.persistence.*;
import java.util.Date;

@Builder
@Table(name = "disk_record_metric_history")
public class DiskRecordMetricHistory {

    @Id
    @GeneratedValue(strategy= GenerationType.IDENTITY)
    private Long id;

    @Column(name = "metric_flag")
    private Integer metricFlag;

    @Column(name = "metrics")
    private String metrics;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "notice_type")
    private Integer noticeType;

}
