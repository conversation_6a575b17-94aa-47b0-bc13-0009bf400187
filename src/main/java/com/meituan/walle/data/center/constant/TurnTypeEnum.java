package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/08/02
 */
@Getter
@AllArgsConstructor
public enum TurnTypeEnum {

    /**
     * 直行
     * 注：FORWARD是转向类型的一种，代表直行通过路口
     */
    FORWARD(0, "forward"),
    /**
     * 左转
     */
    TURN_LEFT(1, "turn_left"),
    /**
     * 右转
     */
    TURN_RIGHT(2, "turn_right"),
    /**
     * 掉头
     */
    TURN_ROUND(-1, "turn_round"),
    /**
     * None代表无转向类型
     */
    NONE(9, "none");

    private int code;
    private String message;
}
