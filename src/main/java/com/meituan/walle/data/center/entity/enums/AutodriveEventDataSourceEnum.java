package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AutodriveEventDataSourceEnum {

    UNKNOWN(-1, "unknown", "未知"),
    DATACENTER(0, "datacenter", "数据中心"),
    AUTOCAR(1, "autocar", "自动驾驶模块"),
    IPC(2, "ipc", "ipc"),
    HVI(3, "hvi", "车机"),
    SCENERANKING(4, "sceneranking", "保障系统"),
    ;

    private int code;
    private String name;
    private String desc;

    public static AutodriveEventDataSourceEnum byCode(int code) {
        for (AutodriveEventDataSourceEnum en : AutodriveEventDataSourceEnum.values()) {
            if (en.code == code) {
                return en;
            }
        }
        return null;
    }

    public static AutodriveEventDataSourceEnum byName(String name) {
        for (AutodriveEventDataSourceEnum en : AutodriveEventDataSourceEnum.values()) {
            if (en.name.equals(name)) {
                return en;
            }
        }
        return null;
    }

}
