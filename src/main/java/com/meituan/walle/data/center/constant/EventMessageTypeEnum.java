package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/12/03
 */
@Getter
@AllArgsConstructor
public enum EventMessageTypeEnum {
    /**
     * record s3 删除事件
     */
    RECORD_S3_DELETE_EVENT("record_s3_delete_event", "record s3 删除事件"),
    /**
     * record file s3 删除事件
     */
    RECORD_FILE_S3_DELETE_EVENT("record_file_s3_delete_event", "record file s3 删除事件"),
    /**
     * record s3 恢复事件
     */
    RECORD_S3_DELETE_RESTORE_EVENT("record_s3_delete_restore_event", "record s3 delete 恢复事件"),
    /**
     * record file s3 delete 恢复事件
     */
    RECORD_FILE_S3_DELETE_RESTORE_EVENT("record_file_s3_delete_restore_event",
            "record file s3 delete 恢复事件"),
    /**
     * record file s3 marked for deletion 恢复事件
     */
    RECORD_FILE_S3_MARKED_FOR_DELETION_RESTORE_EVENT("record_file_s3_marked_for_deletion_restore_event",
            "record file s3 marked for deletion 恢复事件"),

    ;

    private String value;
    private String desc;

    public static EventMessageTypeEnum get(String value) {
        for (EventMessageTypeEnum en : EventMessageTypeEnum.values()) {
            if (Objects.equals(en.value, value)) {
                return en;
            }
        }
        return null;
    }

}
