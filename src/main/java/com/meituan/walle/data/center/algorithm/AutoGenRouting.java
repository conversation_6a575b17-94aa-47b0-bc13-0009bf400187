package com.meituan.walle.data.center.algorithm;

import com.meituan.walle.data.center.entity.algorithm.AutoGenRouting.AutoGenRoutingEdge;
import com.meituan.walle.data.center.entity.algorithm.AutoGenRouting.AutoGenRoutingGraph;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class AutoGenRouting {

    private AutoGenRoutingGraph graph;

    public AutoGenRouting(int n) {
        this.graph = new AutoGenRoutingGraph(n);
    }

    public AutoGenRouting(int vertices, List<AutoGenRoutingEdge> edges) {
        this.graph = new AutoGenRoutingGraph(vertices, edges);
    }

    private void checkInitialised() {
        if (!graph.initialised) {
            throw new Error("Graph not initialised");
        }
        graph.initialised = false;
    }

    public void checkNotConnected() {
        checkInitialised();
        leastCostPaths();
        int count = 0;
        for (int i = 0; i < graph.n; i++) {
            for (int j = 0; j < graph.n; j++) {
                if (!graph.defined[i][j]) {
                    log.info(i + "  " + j);
                    System.out.println(i + " " + j);
                    count = count + 1;
                }
            }
        }
        log.info(graph.n + " vertex");

        if (count == 0) {
            log.info("Strongly Connected");
        }

    }

    private void checkValid() {
        for (int i = 0; i < graph.n; i++) {
            if (graph.c[i][i] < 0) {
                throw new Error("Negative cycles");
            } else {
                for (int j = 0; j < graph.n; j++) {
                    if (!graph.defined[i][j]) {
                        throw new Error("Not strongly connected");
                    }
                }
            }
        }
    }

    private void leastCostPaths() {
        for (int k = 0; k < graph.n; k++) {
            for (int i = 0; i < graph.n; i++) {
                if (graph.defined[i][k]) {
                    for (int j = 0; j < graph.n; j++) {
                        if (graph.defined[k][j]
                                && (!graph.defined[i][j] || graph.c[i][j] > graph.c[i][k] + graph.c[k][j])) {
                            graph.defined[i][j] = true;
                            graph.path[i][j] = graph.path[i][k];
                            graph.c[i][j] = graph.c[i][k] + graph.c[k][j];
                            // return on negative cycle
                            if (i == j && graph.c[i][j] < 0) {
                                return;
                            }
                        }
                    }
                }
            }
        }
    }

    private void findFeasible() {
        int nn = 0, np = 0;
        for (int i = 0; i < graph.n; i++) {
            if (graph.degree[i] < 0) {
                nn++;
            } else if (graph.degree[i] > 0) {
                np++;
            }
        }

        graph.neg = new int[nn];
        graph.pos = new int[np];
        nn = np = 0;
        for (int i = 0; i < graph.n; i++) {
            if (graph.degree[i] < 0) {
                graph.neg[nn++] = i;
            } else if (graph.degree[i] > 0) {
                graph.pos[np++] = i;
            }
        }

        for (int u = 0; u < nn; u++) {
            int i = graph.neg[u];
            for (int v = 0; v < np; v++) {
                int j = graph.pos[v];
                graph.f[i][j] = Math.min(-graph.degree[i], graph.degree[j]);
                graph.degree[i] += graph.f[i][j];
                graph.degree[j] -= graph.f[i][j];
            }
        }
    }

    private boolean improvements() {
        AutoGenRouting R = new AutoGenRouting(graph.n);
        for (int i : graph.neg) {
            for (int j : graph.pos) {
                if (graph.edges[i][j] > 0) {
                    R.graph.addEdge(null, i, j, graph.c[i][j]);
                }
                if (graph.f[i][j] != 0) {
                    R.graph.addEdge(null, j, i, -graph.c[i][j]);
                }
            }
        }
        // find a negative cycle
        R.leastCostPaths();
        // cancel the cycle (if any)
        for (int i = 0; i < graph.n; i++) {
            if (R.graph.c[i][i] < 0) {
                int k = 0, u, v;
                boolean kunset = true;
                u = i;
                do {
                    v = R.graph.path[u][i];
                    if (R.graph.c[u][v] < 0 && (kunset || k > graph.f[v][u])) {
                        k = graph.f[v][u];
                        kunset = false;
                    }
                } while ((u = v) != i);
                u = i;
                do { // cancel k along the cycle
                    v = R.graph.path[u][i];
                    if (R.graph.c[u][v] < 0) {
                        graph.f[v][u] = -k;
                    } else {
                        graph.f[u][v] += k;
                    }
                } while ((u = v) != i);
                // have another go
                return true;
            }
        }
        // no improvement found
        return false;
    }

    // Print an optimal Chinese Postman Tour
    private List<List<Integer>> printCPT(int start) {
        List<List<Integer>> circuit = new ArrayList<>();
        printPath:
        for (int v = start; ; ) {
            int u = v;
            // find an adjoined path, if any
            for (int i = 0; i < graph.n; i++) {
                if (graph.f[u][i] > 0) {
                    v = i;
                    graph.f[u][v]--;
                    for (int p; u != v; u = p) {
                        p = graph.path[u][v];

                        List<Integer> path0 = new ArrayList<>();
                        path0.add(u);
                        path0.add(Integer.parseInt(graph.label[u][p].elementAt(graph.cheapestEdge[u][p]).toString()));
                        path0.add(p);
                        circuit.add(path0);
                    }
                    continue printPath;
                }
            }
            // find an existing edge, using bridge last
            v = -1;
            for (int i = 0; i < graph.n; i++) {
                if (graph.edges[u][i] > 0) {
                    if (v == -1 || i != graph.path[u][start]) {
                        v = i;
                    }
                }
            }
            // finished when no more bridges
            if (v == -1) {
                return circuit;
            }

            List<Integer> path1 = new ArrayList<>();
            // note how this uses each edge in turn
            path1.add(u);
            path1.add(Integer.parseInt(graph.label[u][v].elementAt(graph.edges[u][v] - 1).toString()));
            path1.add(v);
            circuit.add(path1);
            // remove edge that's been used
            graph.edges[u][v]--;
        }
    }

    public List<List<Integer>> cpp() {
        checkNotConnected();
        checkValid();
        findFeasible();
        while (improvements()) ;
        return printCPT(0);
    }
}
