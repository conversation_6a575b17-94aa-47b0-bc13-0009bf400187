package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.pojo.VehicleRunningInfo;
import com.meituan.walle.data.center.entity.request.VehicleRunningInfoInRecordRequest;
import com.meituan.walle.data.center.service.VehicleRunningInfoService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/vehicle_running_info")
public class VehicleRunningInfoController {
    @Resource
    private VehicleRunningInfoService vehicleRunningInfoService;

    @PostMapping("/add")
    public Response add(@RequestBody VehicleRunningInfo vehicleRunningInfo) {
        vehicleRunningInfoService.add(vehicleRunningInfo);
        return Response.succ(vehicleRunningInfo);
    }

    @PostMapping("/get")
    public Response get(@RequestBody VehicleRunningInfo vehicleRunningInfo) {
        List<VehicleRunningInfo> vehicleRunningInfoList = vehicleRunningInfoService.get(vehicleRunningInfo);
        return Response.succ(vehicleRunningInfoList);
    }

    @GetMapping("latest_each_vehicle")
    public Response latestEachVehicle() {
        List<VehicleRunningInfo> vehicleRunningInfoList = vehicleRunningInfoService.latestEachVehicle();
        return Response.succ(vehicleRunningInfoList);
    }

    @PostMapping("in_one_record")
    public Response inOneRecord(@RequestBody VehicleRunningInfoInRecordRequest request) {
        List<VehicleRunningInfo> vehicleRunningInfoList = vehicleRunningInfoService.inOneRecord(request);
        return Response.succ(vehicleRunningInfoList);
    }
}
