package com.meituan.walle.data.center.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.constant.CharConstant;
import com.meituan.walle.data.center.constant.CommonConstant;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.enums.ReturnCodeEnum;
import com.meituan.walle.data.center.entity.enums.VehicleEventTypeEnum;
import com.meituan.walle.data.center.entity.pojo.BizEventHdMapException;
import com.meituan.walle.data.center.entity.pojo.BizEventRouting;
import com.meituan.walle.data.center.entity.pojo.BizEventSpecific;
import com.meituan.walle.data.center.entity.pojo.RecordV2;
import com.meituan.walle.data.center.entity.request.ActiveVehicleCountRequest;
import com.meituan.walle.data.center.entity.request.EventRequest;
import com.meituan.walle.data.center.entity.request.OnboardHistoryRequest;
import com.meituan.walle.data.center.entity.response.CommonPageResponse;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.entity.vo.ObjectReportVO;
import com.meituan.walle.data.center.entity.vo.OnboardOnlineHistoryVO;
import com.meituan.walle.data.center.entity.vo.VehicleEventVO;
import com.meituan.walle.data.center.entity.vo.VehicleServiceFailVO;
import com.meituan.walle.data.center.service.ObjectOnlineMonitorService;
import com.meituan.walle.data.center.service.VehicleEventMonitorService;
import com.meituan.walle.data.center.service.VehicleEventService;
import com.meituan.walle.data.center.service.VehicleInfoService;
import com.meituan.walle.data.center.util.*;
import com.meituan.walle.mad.logger.client.annotation.MadLog;
import com.mysql.jdbc.exceptions.jdbc4.MySQLIntegrityConstraintViolationException;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/vehicle_event")
public class VehicleEventController {

    @Resource
    private VehicleEventMonitorService vehicleEventMonitorService;

    @Resource
    private ObjectOnlineMonitorService objectOnlineMonitorService;

    @Resource
    private VehicleEventService vehicleEventService;

    @Resource
    private IProducerProcessor<String, String> onboardEventProducer;

    @Resource
    private VehicleInfoService vehicleInfoService;


    @PostMapping("/status_report")
    public Response eventStatusReport(@RequestParam("vin") String vin) {
        vehicleEventMonitorService.eventStatusReport(vin);
        Map<String, String> modeConfigMap = new HashMap<>();
        modeConfigMap.put("reportMode", SysParamsConfig.event_default_report_mode);
        Set<String> vins = Arrays.asList(
                SysParamsConfig.proxy_test_vins.split(CharConstant.CHAR_DD)).stream().collect(Collectors.toSet());
        if (vins.contains(vin)) {
            modeConfigMap.put("baseUrl", SysParamsConfig.proxy_test_base_url);
        } else {
            modeConfigMap.put("baseUrl", SysParamsConfig.proxy_prod_base_url);
        }
        return Response.succ(modeConfigMap);
    }

    @PostMapping("/change_config")
    public Response changeConfig(@RequestBody EventRequest eventRequest) {
        if (StringUtils.isBlank(eventRequest.getVin())) {
            log.warn("This request lack vin parameter, request={}", eventRequest);
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    WebResponseStatusEnum.PARAMETER_ERROR.getMsg());
        }
        try {
            String title = "【车辆事件上报环境变更通知】";
            String vehicleName = vehicleInfoService.getVehicleName(eventRequest.getVin());
            StringBuilder msgBuilder = new StringBuilder().append(title).append(CharConstant.CHAR_LF)
                    .append("车辆名称：").append(vehicleName).append(CharConstant.CHAR_LF);
            if (StringUtils.isNotBlank(eventRequest.getBaseUrl())) {
                String envDesc = SysParamsConfig.proxy_prod_base_url.equals(eventRequest.getBaseUrl())
                        ? "prod环境" : "test环境";
                msgBuilder.append("车辆事件当前上报环境：").append(envDesc).append(CharConstant.CHAR_LF);
            }
            if (StringUtils.isNotBlank(eventRequest.getReportMode())) {
                msgBuilder.append("车辆事件当前上报模式：").append(eventRequest.getReportMode());
            }
            DxAppUtil.notice(SysParamsConfig.real_time_data_notice_persons.split(CharConstant.CHAR_DD),
                    msgBuilder.toString());
            return Response.succ();
        } catch (Exception e) {
            log.error("report change config failed, params=[{}]", eventRequest, e);
        }
        return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), WebResponseStatusEnum.FAILED.getMsg());
    }

    @PostMapping("/fail")
    public Response proxyFail(@RequestBody VehicleServiceFailVO failVO) {
        if (failVO == null) {
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    "param is null !");
        }
        if (StringUtils.isBlank(failVO.getVin())) {
            return Response.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode().toString(),
                    "lack vin parameter");
        }
        Transaction t = Cat.newTransaction(CatConstant.VEHICLE_EVENT, "proxy.fail.notice");
        long start = System.currentTimeMillis();
        try {
            log.info("Start to notice proxy fail, param is {}", failVO);
            objectOnlineMonitorService.noticeProxyFail(failVO);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            log.error("Failed to report disk delete fail event, vin is {}, msg is {}",
                    failVO.getVin(), failVO.getMsg(), e);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(),
                "Failed to notice proxy fail message!");
    }

    @PostMapping("/object/report")
    public Response objectReport(@RequestBody ObjectReportVO VO) {
        try {
            objectOnlineMonitorService.objectStatusReport(
                    VO.getVin(), VO.getReportTimestamp(), VO.getObjectType());
        } catch (Exception e) {
            log.error("Heart report failed, vin is {}, objectType is {}", VO.getVin(), VO.getObjectType(), e);
            return Response.fail("10000", "ipc report failed");
        }
        return Response.succ();
    }

    @GetMapping("/sso_test")
    public Response ssoTest() {
        User user = UserUtils.getUser();
        String misid = user.getLogin();
        System.out.println("Test success! " + misid);
        return Response.succ();
    }

    /**
     * {
     * "notice_type": 1,
     * "send_timestamp": 1628651219412,
     * "transmit_timestamp": 1628651219532,
     * "vin": "3LN6L5SU1JR622134",
     * "message": []
     * }
     */
    @PostMapping("/notice")
    @MadLog
    public Map<String, Object> reportEvent(@RequestBody String eventStr) {
        Transaction t = Cat.newTransaction(CatConstant.VEHICLE_EVENT, "vehicleEvent");
        eventStr = eventStr.replace(": nan,", ": 0,");
        Map<String, Object> result = new HashMap<>();

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            VehicleEventVO eventVO = JSON.parseObject(eventStr, VehicleEventVO.class);
            if (eventStr.length() > 1000012) {
                log.info("OnboardNotice event is too large, vin is [{}], noticeType is [{}], sendTimestamp is [{}]",
                        eventVO.getVin(), eventVO.getNoticeType(), eventVO.getSendTimestamp());
                Map<String, Object> eventLog = new HashMap<>();
                eventLog.put("vin", eventVO.getVin());
                eventLog.put("eventType", eventVO.getNoticeType());
                eventLog.put("sendTimestamp", eventVO.getSendTimestamp());
                eventLog.put("transmitTimestamp", eventVO.getTransmitTimestamp());
                CommonUtil.reportLogToMadLoggerAndCat(CallRejectConstant.EVENT_MSG_TOO_LARGE,
                        "OnboardNotice event is too large", eventLog);
            }
            Integer noticeType = eventVO.getNoticeType();
            if (VehicleEventTypeEnum.ROUTING_REQUEST.getCode() == noticeType ||
                    VehicleEventTypeEnum.ROUTING_RESPONSE.getCode() == noticeType) {
                String removeBlackStr = removeRoutingBlackList(eventVO);
                if (StringUtils.isNotBlank(removeBlackStr)) {
                    eventStr = removeBlackStr;
                }
            }
            ProducerResult producerResult = onboardEventProducer.sendMessage(eventStr, eventVO.getVin());
            log.info("[/vehicle_event/notice] onboardEventProducer.sendMessage: {}, producerResult: {}",
                    eventStr, JacksonUtil.serialize(producerResult));
            result.put("ret", 0);
            stopWatch.stop();
            t.setDurationInMillis(stopWatch.getTotalTimeMillis());
            t.setSuccessStatus();
            return result;
        } catch (DuplicateKeyException | MySQLIntegrityConstraintViolationException e) {
            log.warn("该事件已在事件表中: {}", eventStr);
            t.setDurationInMillis(stopWatch.getTotalTimeMillis());
            t.setSuccessStatus();
        } catch (Exception e) {
            log.error("事件入库失败: {}", eventStr, e);
            t.setStatus(e);
        }
        result.put("ret", 1000);
        return result;
    }


    public String removeRoutingBlackList(VehicleEventVO eventVO) {
        if (VehicleEventTypeEnum.ROUTING_REQUEST.getCode() == eventVO.getNoticeType()) {
            String message = eventVO.getMessage().get(0);
            JSONObject jsonObject = JSON.parseObject(message);
            //routingRequest blacklistedLane
            if (!jsonObject.containsKey("routingRequest")) {
                return null;
            }
            JSONObject routingRequest = jsonObject.getObject("routingRequest", JSONObject.class);
            if (!routingRequest.containsKey("blacklistedLane")) {
                return null;
            }
            routingRequest.remove("blacklistedLane");
            eventVO.setMessage(Lists.newArrayList(jsonObject.toJSONString(), eventVO.getMessage().get(1)));
            return JSON.toJSONString(eventVO, CommonConstant.SERIALIZE_CONFIG);
        }

        if (VehicleEventTypeEnum.ROUTING_RESPONSE.getCode() == eventVO.getNoticeType()) {
            String message = eventVO.getMessage().get(0);
            JSONObject jsonObject = JSON.parseObject(message);

            removeSdMapFromRouteNavigation(jsonObject);

            //routingRequest refinedRoutingRequest blacklistedLane
            removeBlackListKey(jsonObject, "refinedRoutingRequest");
            removeBlackListKey(jsonObject, "routingRequest");

            eventVO.setMessage(Lists.newArrayList(jsonObject.toJSONString(), eventVO.getMessage().get(1)));
            return JSON.toJSONString(eventVO, CommonConstant.SERIALIZE_CONFIG);
        }
        return null;
    }

    public void removeBlackListKey(JSONObject jsonObject, String routingRequestKey) {
        if (!jsonObject.containsKey("routingResponse")) {
            return;
        }
        JSONObject routingResponse = jsonObject.getObject("routingResponse", JSONObject.class);
        if (!routingResponse.containsKey(routingRequestKey)) {
            return;
        }

        JSONObject refinedRoutingRequest = routingResponse.getObject(routingRequestKey, JSONObject.class);
        if (!refinedRoutingRequest.containsKey("blacklistedLane")) {
            return;
        }
        refinedRoutingRequest.remove("blacklistedLane");
    }

    public void removeSdMapFromRouteNavigation(JSONObject jsonObject) {
        if (!jsonObject.containsKey("routingResponse")) {
            return;
        }

        JSONObject routingResponse = jsonObject.getObject("routingResponse", JSONObject.class);
        if (routingResponse == null || !routingResponse.containsKey("rawRoutingRequest")) {
            return;
        }

        JSONObject rawRoutingRequest = routingResponse.getObject("rawRoutingRequest", JSONObject.class);
        if (rawRoutingRequest == null || !rawRoutingRequest.containsKey("routeNavigation")) {
            return;
        }

        JSONObject routeNavigation = rawRoutingRequest.getObject("routeNavigation", JSONObject.class);
        if (routeNavigation == null || !routeNavigation.containsKey("sdMap")) {
            return;
        }

        routeNavigation.remove("sdMap");
    }

    @GetMapping(value = "/active/vehicle/count")
    public Response queryActiveVehicleCount(ActiveVehicleCountRequest request) {
        Transaction t = Cat.newTransaction(CatConstant.VEHICLE_EVENT, CatConstant.ACTIVE_VEHICLE_COUNT);
        long start = System.currentTimeMillis();
        try {
            Map<String, Integer> result = vehicleEventService.queryActiveVehicleCount(request);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            log.error("queryActiveVehicleCount error", e);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.FAILED.getCode().toString(), WebResponseStatusEnum.FAILED.getMsg());
    }

    @GetMapping("/specific")
    public CommonPageResponse querySpecificEvent(EventRequest request) {
        if (request.getId() == null && request.getStartTime() == null && request.getEndTime() == null &&
                StringUtils.isBlank(request.getEventId()) && StringUtils.isBlank(request.getRecordName())) {
            return CommonPageResponse.builder()
                    .code(WebResponseStatusEnum.PARAMETER_ERROR.getCode())
                    .msg("param is empty")
                    .build();
        }
        buildEventRequest(request);

        List<BizEventSpecific> eventSpecificList = vehicleEventService.querySpecificEvent(request);
        if (request.getPage() != null && request.getSize() != null) {
            int count = 0;
            if (!eventSpecificList.isEmpty()) {
                count = vehicleEventService.countSpecificEvent(request);
            }
            int size = request.getSize();
            return CommonPageResponse.builder()
                    .code(ReturnCodeEnum.SUCCESS.getCode())
                    .msg(ReturnCodeEnum.SUCCESS.getMsg())
                    .data(eventSpecificList)
                    .page(request.getPage())
                    .size(size)
                    .totalSize(count)
                    .totalPage(count % size == 0 ? count / size : count / size + 1)
                    .build();
        }

        return CommonPageResponse.success(eventSpecificList);
    }

    @GetMapping("/routing")
    public CommonPageResponse queryRoutingEvent(EventRequest request) {
        buildEventRequest(request);
        boolean inPage = request.getPage() != null && request.getSize() != null;
        if (inPage) {
            // 选择了分页就不能选择限制条数
            request.setLimit(null);
        } else {
            int limit = request.getLimit() == null || request.getLimit() > CommonConstant.MAX_LIMIT ?
                    CommonConstant.MAX_LIMIT : request.getLimit();
            request.setLimit(limit);
        }

        List<BizEventRouting> eventRoutingList = vehicleEventService.queryRoutingEvent(request);
        if (inPage) {
            int count = 0;
            if (!eventRoutingList.isEmpty()) {
                count = vehicleEventService.countRoutingEvent(request);
            }
            int size = request.getSize();
            return CommonPageResponse.builder()
                    .code(ReturnCodeEnum.SUCCESS.getCode())
                    .msg(ReturnCodeEnum.SUCCESS.getMsg())
                    .data(eventRoutingList)
                    .page(request.getPage())
                    .size(size)
                    .totalSize(count)
                    .totalPage(count % size == 0 ? count / size : count / size + 1)
                    .build();
        }

        return CommonPageResponse.success(eventRoutingList);
    }

    private void buildEventRequest(EventRequest request) {
        if (request.getStartTime() != null) {
            request.setStartTimeStr(DatetimeUtil.format(request.getStartTime(), DatetimeUtil.yyMMddHHmmss));
        }
        if (request.getEndTime() != null) {
            request.setEndTimeStr(DatetimeUtil.format(request.getEndTime(), DatetimeUtil.yyMMddHHmmss));
        }
    }

    @GetMapping(value = "/onboard/history")
    public CommonResponse queryOnboardOnlineHistory(OnboardHistoryRequest request) {
        if (request.getStartTime() == null) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "startTime is null");
        }
        if ((request.getVehicleNameList() == null || request.getVehicleNameList().isEmpty()) &&
                StringUtils.isBlank(request.getMapName())) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(),
                    "vehicleName or mapName is empty");
        }

        request.setStartTimeStr(DatetimeUtil.format(request.getStartTime(), CommonConstant.RECORD_TIME_FORMAT));
        if (request.getEndTime() != null) {
            request.setEndTimeStr(DatetimeUtil.format(request.getEndTime(), CommonConstant.RECORD_TIME_FORMAT));
        }

        if (request.getEndTime() != null) {
            if (request.getEndTime().getTime() - request.getStartTime().getTime() > CommonConstant.TWO_DAY_MILLIS) {
                return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "time too long");
            }
            request.setEndTimeStr(DatetimeUtil.format(request.getEndTime(), CommonConstant.RECORD_TIME_FORMAT));
        } else if (System.currentTimeMillis() - request.getStartTime().getTime() > CommonConstant.TWO_DAY_MILLIS) {
            return CommonResponse.fail(WebResponseStatusEnum.PARAMETER_ERROR.getCode(), "time too long");
        }

        List<RecordV2> recordList = vehicleEventService.queryRecordHistory(request);
        List<OnboardOnlineHistoryVO> resultList = recordList.stream().map(item -> {
            OnboardOnlineHistoryVO vo = new OnboardOnlineHistoryVO();
            vo.setVehicleName(item.getVehicleName());
            vo.setMapName(item.getAdmapName());
            vo.setStartTime(DatetimeUtil.format(item.getBeginTime()));
            vo.setEndTime(item.getEndTime().getTime() < CommonConstant.TWO_THOUSAND_YEAR_MILLIS ? "" :
                    DatetimeUtil.format(item.getEndTime()));
            return vo;
        }).collect(Collectors.toList());
        return CommonResponse.success(resultList);
    }


    @GetMapping("/hdmap_exception")
    public CommonPageResponse queryHdmapException(EventRequest request) {
        if (request.getId() == null && request.getStartTime() == null && request.getEndTime() == null &&
                StringUtils.isBlank(request.getEventId()) && StringUtils.isBlank(request.getRecordName())) {
            return CommonPageResponse.builder()
                    .code(WebResponseStatusEnum.PARAMETER_ERROR.getCode())
                    .msg("param is empty")
                    .build();
        }
        buildEventRequest(request);

        List<BizEventHdMapException> eventSpecificList = vehicleEventService.queryHdmapException(request);
        if (request.getPage() != null && request.getSize() != null) {
            int count = 0;
            if (!eventSpecificList.isEmpty()) {
                count = vehicleEventService.countHdmapException(request);
            }
            int size = request.getSize();
            return CommonPageResponse.builder()
                    .code(ReturnCodeEnum.SUCCESS.getCode())
                    .msg(ReturnCodeEnum.SUCCESS.getMsg())
                    .data(eventSpecificList)
                    .page(request.getPage())
                    .size(size)
                    .totalSize(count)
                    .totalPage(count % size == 0 ? count / size : count / size + 1)
                    .build();
        }

        return CommonPageResponse.success(eventSpecificList);
    }

}
