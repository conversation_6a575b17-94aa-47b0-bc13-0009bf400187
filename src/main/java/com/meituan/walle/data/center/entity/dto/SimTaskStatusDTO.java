package com.meituan.walle.data.center.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SimTaskStatusDTO implements Serializable {

    private String primaryTaskId;
    private String taskId;
    private String adsId;
    public int loopIndex;
    public int syncToWopState;
}
