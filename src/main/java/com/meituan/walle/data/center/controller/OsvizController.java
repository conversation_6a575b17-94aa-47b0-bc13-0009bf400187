package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.entity.request.OsMvizJobStatusRequest;
import com.meituan.walle.data.center.entity.request.OsMvizTaskStatusRequest;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.service.OsvizService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/11/21
 */
@RestController
@RequestMapping(value = "/osviz")
@Slf4j
public class OsvizController {
    @Resource
    private OsvizService osvizService;

    @PostMapping("/job_status/callback")
    public CommonResponse jobStatusCallback(@RequestBody OsMvizJobStatusRequest request) {
        // 目前只关注task的状态即可，无需关注job的状态
        log.info("osviz job status callback: {}", request);
        try {
            osvizService.updateByJobId(request);
        } catch (Exception e) {
            log.error("update os task failed, e:{}", e.toString());
        }
        return CommonResponse.success();
    }

    @PostMapping("/task_result/callback")
    public CommonResponse taskResultCallback(@RequestBody OsMvizTaskStatusRequest request) {
        log.info("osviz task status callback: {}", request);
        return CommonResponse.success();
    }

}
