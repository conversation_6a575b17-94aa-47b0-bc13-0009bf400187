package com.meituan.walle.data.center.dal.wcdpbasedata.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BdsSensorInfoPO {
    private Long id;
    private String sensorId;
    private String name;
    private String type;
    private String brand;
    private String model;
    private String sn;
    private String owner;
    private String image;
    private Integer status;
    private String extend;
    private Date firstAddTime;
    private Date lastUpdateTime;
    private String alias;
    private String mtSn;
    private String subType;
}
