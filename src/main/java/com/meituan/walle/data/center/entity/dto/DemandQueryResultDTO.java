package com.meituan.walle.data.center.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class DemandQueryResultDTO {

    private Long id;

    @JsonProperty(value = "resv_id")
    private String resvId;

    private String location;

    @JsonProperty(value = "used_type_name")
    private String usedTypeName;

    @JsonProperty(value = "start_time")
    private String startTime;

    @JsonProperty(value = "end_time")
    private String endTime;
}
