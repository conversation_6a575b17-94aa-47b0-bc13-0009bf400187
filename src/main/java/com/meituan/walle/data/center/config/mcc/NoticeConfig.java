package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class NoticeConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID,
            key = "com.meituan.walle.data.center.dxNoticeOnCallGroup.taskUrl")
    public static volatile String DX_NOTICE_ON_CALL_TASK_URL = "https://bi.sankuai.com/dashboard/49939";
}
