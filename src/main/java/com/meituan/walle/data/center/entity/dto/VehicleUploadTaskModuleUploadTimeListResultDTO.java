package com.meituan.walle.data.center.entity.dto;

import com.meituan.walle.data.center.entity.vo.FastUploadModuleTimeVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
/**
 * 快速回传任务各个模块进行数据去重后，上传时间列表结果的DTO
 */
public class VehicleUploadTaskModuleUploadTimeListResultDTO {
    private List<String> recordNameList;
    private String taskIdListStr;
    private Map<String, List<FastUploadModuleTimeVO>> fastTaskModuleTimeMap;
}
