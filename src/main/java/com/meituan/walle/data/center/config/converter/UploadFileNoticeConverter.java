package com.meituan.walle.data.center.config.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.walle.data.center.util.JacksonUtil;
import com.sankuai.meituan.config.exception.MtConfigException;
import com.sankuai.meituan.config.function.MtConfigConverter;
import com.sankuai.walle.wcdp.core.entity.dto.UploadFileNoticeDTO;

import java.lang.reflect.Field;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/02/27
 */
public class UploadFileNoticeConverter implements MtConfigConverter<List<UploadFileNoticeDTO>> {
    @Override
    public List<UploadFileNoticeDTO> convert(Field field, String key, String newValue) throws MtConfigException {
        return JacksonUtil.fromListJson(newValue, new TypeReference<List<UploadFileNoticeDTO>>() {});
    }
}