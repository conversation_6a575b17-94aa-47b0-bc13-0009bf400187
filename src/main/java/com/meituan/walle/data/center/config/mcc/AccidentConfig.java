package com.meituan.walle.data.center.config.mcc;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/06/30
 */
public class AccidentConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "first.accident.id.attention.responsibility")
    public static volatile int accident_resp_id = 328;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.responsibility.delay.members")
    public static volatile String accident_members = "fuyuanbo,xiepengyan,panchuheng";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.responsibility.delay.hours")
    public static volatile int accident_resp_delay_hours = 12;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.disposed.delay.minutes")
    public static volatile int accident_disposed_delay_minutes = 17;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.disposed.delay.cycle.minutes")
    public static volatile int accident_disposed_delay_cycle_minutes = 5;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.test.vin")
    public static volatile String accident_test_vin = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.avoid.repeat.millis")
    public static volatile long accident_avoid_repeat_millis = 7200000;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.group.owner.id")
    public static volatile String accident_group_owner = "fuyuanbo";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.remote.video.previous.time")
    public static volatile long accident_rv_previous_time = 50000L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.remote.video.later.time")
    public static volatile long accident_rv_later_time = 10000L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.call.last.number")
    public static volatile String accident_call_last_number = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.call.last.mis.id")
    public static volatile String accident_call_last_mis_id = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.call.duty.partner")
    public static volatile String accident_call_duty_partner = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.group.notice.content")
    public static volatile String accident_group_notice_content = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.info.commit.url.prefix")
    public static volatile String accident_info_commit_url_prefix =
            "https://walle.meituan.com/m/h5-car-tool/accident/";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.unchecked.mis.id")
    public static volatile String accident_unchecked_mis_id = "panchuheng";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.unchecked.upgrade.mis.ids")
    public static volatile String accident_unchecked_upgrade_mis_id = "zhangchenyu07";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.unchecked.bottom.line.mis.ids")
    public static volatile String accident_unchecked_bottom_line_mis_id = "zhangchenyu07";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.undisposed.mis.id")
    public static volatile String accident_undisposed_mis_id = "panchuheng";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.undisposed.upgrade.mis.ids")
    public static volatile String accident_undisposed_upgrade_mis_id = "zhangchenyu07";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.undisposed.bottom.line.mis.ids")
    public static volatile String accident_undisposed_bottom_line_mis_id = "zhangchenyu07";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.undisposed.scanned.days")
    public static volatile int accident_undisposed_scanned_days = 7;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.extra.notice.person")
    public static volatile String accident_extra_notice_person = "fuyuanbo,zhangchenyu07";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.speed.up.concurrent.num")
    public static volatile Integer speed_up_concurrent_num = 15;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.update.time.task.prev.second")
    public static volatile Integer update_time_previous_second = 10;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.update.time.task.later.second")
    public static volatile Integer update_time_later_second = 2;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "is.open.accident.group.create")
    public static Boolean isOpenAccidentGroupCreate = false;

    /**
     * 运维平台相关服务域名
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.eve.host.name")
    public static String eveHostName;

    /**
     * 定时刷新一段时间内的事故状态，表示这段时间的起始值，结束值为当前时刻
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.report.data.bus.start.query.time")
    public static Long queryStartTime = 0L;

    /**
     * 事故TTRG值班组id
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.rg.oncall.rgid")
    public static Long accidentRgOnCallRgId = 11642L;

    /**
     * 事故自动外呼备选呼叫人
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.auto.call.backup.caller")
    public static String accidentAutoCallBackupCaller;

}
