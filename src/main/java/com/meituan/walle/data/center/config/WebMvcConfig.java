package com.meituan.walle.data.center.config;

import com.meituan.mtrace.http.TraceMethodInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * <NAME_EMAIL> on 2019/4/18 16:08
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        TraceMethodInterceptor traceHandlerInterceptor = new TraceMethodInterceptor();
        registry.addInterceptor(traceHandlerInterceptor).addPathPatterns("/**");
    }


}
