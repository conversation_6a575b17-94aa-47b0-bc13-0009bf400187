package com.meituan.walle.data.center.entity.dto;

import com.meituan.walle.data.center.constant.CharConstant;
import com.meituan.walle.data.center.constant.CommonConstant;
import com.meituan.walle.data.center.constant.FastUploadTaskStatusEnum;
import com.meituan.walle.data.center.entity.pojo.VehicleUploadRequest;
import com.meituan.walle.data.center.util.DatetimeUtil;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@Data
public class FastMapCollectTaskInfoDTO {

    private Long id;

    private String vehicleName;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;


    /**
     * module列表(以英文逗号分隔)
     */
    private String module;

    /**
     * 0.初始|10.处理中|100.完成
     */
    private Integer status;

    /**
     * 任务类别，1.实时case回传|2.事故数据回传|3.指定模块基础数据回传|4用户自定义数据回传
     */
    private Integer taskType;


    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 表record的record_name外键
     */
    private String recordName;


    /**
     * 创建人misid
     */
    private String creator;

    private String s3Path;

    private Long dataSize;

    private Integer fileCount;

    private String packageVersion;

    public static FastMapCollectTaskInfoDTO fromVehicleUploadRequest(VehicleUploadRequest request, String bucket) {
        FastMapCollectTaskInfoDTO taskInfo = new FastMapCollectTaskInfoDTO();
        BeanUtils.copyProperties(request, taskInfo);

        taskInfo.setStartTime(DatetimeUtil.format(request.getStart(), DatetimeUtil.YMDHMS));
        taskInfo.setEndTime(DatetimeUtil.format(request.getEnd(), DatetimeUtil.YMDHMS));
        String recordName = request.getRecordName();
        if (recordName != null && request.getStatus() == FastUploadTaskStatusEnum.FINISHED.getCode()) {
            String[] recordNameParts = recordName.split(CharConstant.CHAR_XH);
            String s3Path =
                    new StringBuilder(CommonConstant.S3_PREFIX)
                            .append(bucket)
                            .append(CharConstant.CHAR_XX)
                            .append(recordNameParts[0])
                            .append(CharConstant.CHAR_XX)
                            .append(recordNameParts[2])
                            .append(CharConstant.CHAR_XX)
                            .append(request.getId())
                            .append(CharConstant.CHAR_XX).toString();
            taskInfo.setS3Path(s3Path);

        }
        return taskInfo;
    }

}
