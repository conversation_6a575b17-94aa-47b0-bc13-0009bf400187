package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InterventionSourceEventPO {

    private String eventId;
    private Integer eventCode;
    private String eventName;
    private Timestamp eventTimestamp;
    private Timestamp senderTimestamp;
    private Timestamp receiverTimestamp;
    private String vin;
    private String vehicleId;
    private String vehicleName;
    private String recordName;
    private Integer utmZone;
    private String utmX;
    private String utmY;
    private String datasource;
    private String content;

}
