package com.meituan.walle.data.center.entity.message;

import com.meituan.walle.data.center.entity.pojo.RecordV2;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2021/10/09
 */
@Builder
@Data
public class RecordCirculation implements Serializable {
    private String recordName;
    private RecordV2 recordV2;
    private Integer circulationStatus;
    private String uploadBeginTime;
    private String uploadEndTime;
}