package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/31
 */
@Data
@Table(name = "biz_event_file_process")
public class BizEventFileProcess {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * record名称
     */
    private String recordName;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件内包含的事件条数
     */
    private Integer eventCount;

    /**
     * 第一条msg时间
     */
    private Date firstMsgTime;

    /**
     * 最后一条msg时间
     */
    private Date lastMsgTime;


    /**
     * 解析完成时间
     */
    private Date completeTime;

    /**
     * 0表示文件上传完成，1表示开始解析，2表示解析成功完成， 3表示解析失败
     */
    private Integer status;

    /**
     * 上传任务的id
     */
    private Long uploadTaskId;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private Boolean isDeleted;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * update_time
     */
    private Date updateTime;
}
