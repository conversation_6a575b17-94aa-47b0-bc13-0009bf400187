package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;
import javax.persistence.*;

@Data
@Table(name = "fast_record_file")
public class FastRecordFile {
    /**
     * 自增ID
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 快速上传任务的id
     */
    private String uploadTaskId;

    /**
     * 表record的record_name外键
     */
    private String recordName;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * S3地址
     */
    private String s3Url;

    /**
     * 文件类型:0.record_info|1.rec|2.log|3.coredump|4.workspace
     *        5.index|6.bin|7.sys|8.record_info.pb.txt文件|9.precheck|10.novatel
     */
    private Integer fileType;

    /**
     * 文件大小(B)
     */
    private Long fileSize;

    /**
     * 原始文件的大小（文件可能被压缩）
     */
    private Long originFileSize;

    /**
     * 模块名
     */
    private String module;

    /**
     * 开始录制时间
     */
    private Timestamp startTime;

    /**
     * 结束录制时间
     */
    private Timestamp endTime;

    private Long startNano;

    private Long endNano;

    /**
     * 添加时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 文件上传开始时间
     */
    private Date uploadStartTime;

    /**
     * 是否删除
     */
    private String isDeleted;

    /**
     * 所属集群[0:北京备份|1:中卫自动车]
     */
    private Integer cluster;

    /**
     * 压缩格式
     */
    private String compressionFormat;

}