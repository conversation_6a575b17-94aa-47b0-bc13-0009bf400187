package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2022/10/17
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class VehicleStatusChangeAggNodePO {
    private Long id;
    private String nodeId;
    private String nextNodeId;
    private String vin;
    private Double latitude;
    private Double longitude;
    private Integer driveMode;
    private Integer nextDriveMode;
    private Timestamp startTime;
    private Timestamp endTime;
    private Long duration;
    private Double mileage;
    private Integer isDeleted;
    private Timestamp updateTime;
    private Timestamp createTime;
}
