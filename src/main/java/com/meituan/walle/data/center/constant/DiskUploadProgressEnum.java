package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/02/10
 */
@Getter
@AllArgsConstructor
public enum DiskUploadProgressEnum {
    UPLOADING(0, "uploading"),
    UPLOADED(1, "uploaded");

    private Integer code;
    private String desc;

    public static DiskUploadProgressEnum get(String code) {
        for (DiskUploadProgressEnum en : DiskUploadProgressEnum.values()) {
            if (Objects.equals(en.getCode(), code)) {
                return en;
            }
        }
        return null;
    }
}