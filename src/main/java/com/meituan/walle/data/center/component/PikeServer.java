package com.meituan.walle.data.center.component;

import com.meituan.walle.data.center.config.mcc.SysParamsConfig;
import com.sankuai.pike.message.sdk.listener.ListenerHolder;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
public class PikeServer {

    // 在pike mws管理平台申请的业务ID， http://pike.mws-test.sankuai.com

    // 连接事件监听器
    @Resource
    private PikeConnectListener connectListener;

    @Resource
    private PikeMessageListener messageListener;

    @PostConstruct
    public void init() {
        // 根据业务服务端的服务端类型进行选择
        ThreadPoolExecutor executor = new ThreadPoolExecutor(256,256,0,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000));
        ListenerHolder.scheduleListenerOn(executor);

        ListenerHolder.registerLister(SysParamsConfig.pike_tag_bizId, connectListener);
        ListenerHolder.registerLister(SysParamsConfig.pike_tag_bizId, messageListener);
        ListenerHolder.publishListenerAsMTthriftService();
    }

}


