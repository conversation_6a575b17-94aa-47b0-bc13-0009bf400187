package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

public class OnesParamConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "ones.replay-launch-cmd-template")
    public static volatile String mcc_ones_replayLaunchCMDTemplate = "python3 replay_data.py -ones ";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "s3.public-download-url")
    public static volatile String mcc_s3_public_download_url =
            "https://s3mosnetmirror.sankuai.com/v1/mss_914377ab071e4a8b8b50cc10a00ec5af/";
}
