package com.meituan.walle.data.center.entity.enums;

import com.meituan.walle.data.center.constant.CharConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/06/08
 */

@Getter
@AllArgsConstructor
public enum AccidentRespEnum {

    // next_id 25
    AUTO_DRIVE("1", "自动驾驶算法"),
//    REMOTE_SYSTEM("8", "自动驾驶系统-云控系统(旧)"),
//    ONBOARD_SYSTEM("7", "自动驾驶系统-车载系统(旧)"),
    REMOTE_OPERATION("2", "我方人员操作或维保不当-云代驾安全员"),
    ON_SITE("3", "我方人员操作或维保不当-近场安全员"),
    MAINTAIN_OPERATION("9", "我方人员操作或维保不当-车务人员"),
    BUSINESS_OPERATION("10", "我方人员操作或维保不当-业务运营人员"),
    THIRD_PARTY_PERSON("4", "三方人员或车辆-三方人员"),
    THIRD_PARTY_VEHICLE("11", "三方人员或车辆-三方车辆"),
    SIGNAL_TROUBLE("12", "其他相对不可控-信号故障"),
//    HARDWARE_TROUBLE("13", "其他相对不可控-硬件故障"),
    NATURAL_DISASTER("14", "其他相对不可控-自然灾害"),
    FALLING_OBJECT("15", "其他相对不可控-意外坠物"),
    OTHER("6", "其他相对不可控"),
    OTHER_INSIDE_PARTY("5", "我方人员操作或维保不当-我方其他人员"),
    MAP_LOCALIZATION("16", "自动驾驶算法-高精地图和定位"),
    PERCEPTION("17", "自动驾驶算法-感知"),
//    ARBITRATION("18", "自动驾驶系统-应急策略(旧)"),
    PNC("19", "自动驾驶算法-决策规划"),
//    PREDICTION("20", "自动驾驶系统-行为理解&预测(旧)"),
    AUTO_OTHER("21", "自动驾驶算法-其他"),
    THIRD_PARTY_OTHER("22", "三方人员或车辆-三方其他人员"),
    OUR_OPERATION("23", "我方人员操作或维保不当"),
    THIRD_PARTY("24", "三方人员或车辆"),

    OTHER_SYSTEM("30","其他系统"),
    OTHER_SYSTEM_REMOTE_SYSTEM("31", "其他系统-云控系统"),
    OTHER_SYSTEM_ONBOARD_SYSTEM("32", "其他系统-车载系统"),
    OTHER_SYSTEM_ARBITRATION("33", "其他系统-应急策略"),
    OTHER_SYSTEM_FC("34", "其他系统-FC"),
    OTHER_SYSTEM_OTHER("35", "其他系统-其他"),
    AUTO_DRIVE_HARDWARE("40", "自动驾驶硬件"),
    AUTO_DRIVE_HARDWARE_VEHICLE("41", "自动驾驶硬件-车辆硬件"),
    AUTO_DRIVE_HARDWARE_OTHER("42", "自动驾驶硬件-其他"),
    OUR_OPERATION_REMOTE_ASSIST("50", "我方人员操作或维保不当-云辅助安全员"),
    OUR_OPERATION_RD_TEST("51", "我方人员操作或维保不当-研发测试人员"),
    OUR_OPERATION_FEEDER_RIDER("52", "我方人员操作或维保不当-接驳骑手"),
    OUR_OPERATION_ELECTROMECHANICAL_TEST("53", "我方人员操作或维保不当-机电测试人员"),

    ;

    private String code;
    private String msg;

    public static List<Object> getCodeMsgMap() {
        List<Object> result = new ArrayList<>();
        List<AccidentRespEnum> respEnums = new ArrayList<>();
        respEnums.add(AUTO_DRIVE);
        respEnums.add(AUTO_DRIVE_HARDWARE);
        respEnums.add(OTHER_SYSTEM);
        respEnums.add(OUR_OPERATION);
        respEnums.add(THIRD_PARTY);
        respEnums.add(OTHER);
        for (AccidentRespEnum accidentRespEnum: respEnums) {
            String label = accidentRespEnum.getMsg();
            Map<String, Object> map = new HashMap<>();
            map.put("label", label);
            map.put("value", accidentRespEnum.getCode());
            List<Object> children = new ArrayList<>();
            for (AccidentRespEnum childrenEnum: AccidentRespEnum.values()) {
                String msg = childrenEnum.getMsg();
                String[] childrenLabels = msg.split(CharConstant.CHAR_HX);
                if (childrenLabels.length >= 2 && label.equals(childrenLabels[0])) {
                    Map<String, Object> childrenMap = new HashMap<>();
                    childrenMap.put("label", childrenLabels[1]);
                    childrenMap.put("value", childrenEnum.getCode());
                    children.add(childrenMap);
                }
            }
            map.put("children", children);
            result.add(map);
        }
        return result;
    }
}
