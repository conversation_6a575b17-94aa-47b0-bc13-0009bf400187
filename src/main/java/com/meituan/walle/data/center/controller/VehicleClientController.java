package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.service.VehicleClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/vehicle_client")
public class VehicleClientController {

    @Resource
    private VehicleClientService vehicleClientService;

    @GetMapping("/query_disk_num")
    public Response queryDiskNums(@RequestParam String vin) {
        Map<String, Integer> result = new HashMap<>();
        Transaction t = Cat.newTransaction(CatConstant.QUERY_DISK_NUM, "query.disk.num");
        long start = System.currentTimeMillis();
        try {
            Integer diskNum = vehicleClientService.getSingleOrDoubleDisk(vin);
            result.put("disk_num", diskNum);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(result);
        } catch (Exception e) {
            log.error("query disk num failed", e);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setStatus(e);
        } finally {
            t.complete();
        }
        return Response.fail(WebResponseStatusEnum.LOGICAL_EXCEPTION.getCode().toString(),
                "query disk num failed");
    }
}
