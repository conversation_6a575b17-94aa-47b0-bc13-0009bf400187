package com.meituan.walle.data.center.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/06/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AutodriveEventDTO {

    private String eventId;
    private Integer eventCode;
    private String eventName;
    private Long eventTimestamp;
    private Long senderTimestamp;
    private Long receiverTimestamp;
    private String vin;
    private String vehicleId;
    private String vehicleName;
    private String recordName;
    private Integer utmZone;
    private String utmX;
    private String utmY;
    private String datasource;
    private Object content;

}
