package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "biz_event_autodrive_hardbrake")
@Data
public class BizEventAutodriveHardbrake {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    private String eventId;

    /**
     * 表record的record_name外键
     */
    private String recordName;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 接管时间
     */
    private Date eventTime;

    /**
     * Utm坐标的x（来源于Localization的x）
     */
    private String x;

    /**
     * Utm坐标的y（来源于Localization的y）
     */
    private String y;

    private double acceleration;

    private double speed;

    private Integer sequenceId;

    private Boolean isDeleted;

    private Date createTime;

    private Date updateTime;

    private String decisionMinAcc;

    private String planningAcc;

    private String controlCmdAcc;

    private String observedAcc;

    private String targetBrakePercentage;

    private String chassisBrakePercentage;

    private String constraintSources;

    private String sources;

    private String obstaclesInfo;

    private Boolean isAutoDriveMode;
}
