package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "biz_record_remote_video")
@Data
public class BizRecordRemoteVideo {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 视频存放s3地址
     */
    private String s3Url;

    /**
     * 相关联的事件id, 可能是事故id也可能是事件id
     */
    private String targetId;

    /**
     * 相关联的事件来源,
     * -1:位置, 1:事故, 2:事件
     */
    private Integer targetResource;

    /**
     * 远遥视频开始时间
     */
    private Date remoteVideoStartTime;

    /**
     * 远遥视频结束时间
     */
    private Date remoteVideoEndTime;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
