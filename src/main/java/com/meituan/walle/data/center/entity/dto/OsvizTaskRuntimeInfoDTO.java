package com.meituan.walle.data.center.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/21
 */
@Data
public class OsvizTaskRuntimeInfoDTO {
    @JsonProperty("case_source")
    private String caseSource;

    @JsonProperty("trace_name")
    private String traceName;

    @JsonProperty("fast_id")
    private String fastId;

    /**
     * webviz2.0 使用该字段作为唯一任务标识
     */
    @JsonProperty("webviz_case_id")
    private String webvizCaseId;

    @JsonProperty("begin_time_stamp")
    private Long beginTimeStamp;

    @JsonProperty("end_time_stamp")
    private Long endTimeStamp;

    @JsonProperty("callback_data")
    private OsvizTaskCallbackDataDTO callBackData;
}
