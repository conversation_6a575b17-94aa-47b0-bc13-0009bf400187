package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/08
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum InboundPipelineRerunModeEnum {
    FULL_RERUN("FULL_RERUN", "全流程重跑"),
    STAGE_RERUN("STAGE_RERUN", "阶段重跑"),
    ;

    private String code;
    private String desc;


    public static InboundPipelineRerunModeEnum byCode(String code) {
        for (InboundPipelineRerunModeEnum en : InboundPipelineRerunModeEnum.values()) {
            if (en.code.equalsIgnoreCase(code)) {
                return en;
            }
        }
        return null;
    }
}
