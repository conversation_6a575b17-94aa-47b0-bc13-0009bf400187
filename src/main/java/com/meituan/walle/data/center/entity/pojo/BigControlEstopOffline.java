package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/09/14
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "big_control_estop_offline")
public class BigControlEstopOffline {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * estop id
     */
    @Column(name = "estop_id")
    private String estopId;

    /**
     * estop reason
     */
    @Column(name = "estop_reason")
    private String estopReason;

    /**
     * estop开始的时间戳
     */
    @Column(name = "begin_timestamp")
    private Long beginTimestamp;

    /**
     * estop结束的时间戳
     */
    @Column(name = "end_timestamp")
    private Long endTimestamp;

    /**
     * 车架号
     */
    @Column(name = "vin")
    private String vin;

    /**
     * record name
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getEstopId() {
        return estopId;
    }

    public void setEstopId(String estopId) {
        this.estopId = estopId;
    }

    public String getEstopReason() {
        return estopReason;
    }

    public void setEstopReason(String estopReason) {
        this.estopReason = estopReason;
    }

    public Long getBeginTimestamp() {
        return beginTimestamp;
    }

    public void setBeginTimestamp(Long beginTimestamp) {
        this.beginTimestamp = beginTimestamp;
    }

    public Long getEndTimestamp() {
        return endTimestamp;
    }

    public void setEndTimestamp(Long endTimestamp) {
        this.endTimestamp = endTimestamp;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getRecordName() {
        return recordName;
    }

    public void setRecordName(String recordName) {
        this.recordName = recordName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}

