package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/12/02
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Table(name = "tag_phenomenon")
public class TagPhenomenon {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 现象名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 现象介绍
     */
    @Column(name = "desc")
    private String desc;

    /**
     * 团队/分组ID
     */
    @Column(name = "group_id")
    private Integer groupId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 父Id
     */
    @Column(name = "parent_id")
    private Integer parentId;

    /**
     * 模块ID
     */
    @Column(name = "module_id")
    private Integer moduleId;

    /**
     * 负责人misId
     */
    @Column(name = "mis_id")
    private String misId;

    /**
     * 排序
     */
    @Column(name = "idx")
    private Integer idx;

    /**
     * 优先级
     */
    @Column(name = "priority")
    private String priority;

    /**
     * 计算指标时忽略此事件，0否，1是
     */
    @Column(name = "ignore_as_metric")
    private Integer ignoreAsMetric;

    /**
     * 是否弃用 0|否 1|是
     */
    @Column(name = "is_abandon")
    private Integer isAbandon;

    /**
     * 标题
     */
    @Column(name = "title")
    private String title;

    /**
     * 抄送人
     */
    @Column(name = "cc")
    private String cc;
}

