package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DrivingModeEnum {

    COMPLETE_MANUAL(0, "complete_manual"),
    COMPLETE_AUTO_DRIVE(1, "complete_auto_drive"),
    AUTO_STEER_ONLY(2, "auto_steer_only"),
    AUTO_SPEED_ONLY(3, "auto_speed_only"),
    EMERGENCY_MODE(4, "emergency_mode"),
    FIELD_CONTROL_MODE(5, "field_control_mode"),
    REMOTE_CONTROL_MODE(6, "remote_control_mode"),
    MANUAL_DRIVER(7, "manual_driver"),
    ;

    private int code;
    private String msg;

    public static String getMsgByCode(int code) {
        for (DrivingModeEnum type : DrivingModeEnum.values()) {
            if (type.getCode() == code) {
                return type.getMsg().toUpperCase();
            }
        }
        return "";
    }
}
