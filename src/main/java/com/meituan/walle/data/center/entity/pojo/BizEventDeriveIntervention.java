package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/08/13
 */
@Table(name = "biz_event_derive_intervention")
@Data
public class BizEventDeriveIntervention {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    /**
     * 事件的业务id
     */
    @Column(name = "event_id")
    private String eventId;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * 接管时间
     */
    @Column(name = "intervention_time")
    private Date interventionTime;

    @Column(name = "intervention_timestamp")
    private Long interventionTimestamp;

    /**
     * Utm坐标的x（来源于Localization的x）
     */
    private String x;

    /**
     * Utm坐标的y（来源于Localization的y）
     */
    private String y;

    /**
     * 已识别，接管时间的上下5秒，与触发tag时间匹配成功，且是同一辆车，设置为1，默认null
     */
    @Column(name = "is_discern")
    private Integer discern;

    /**
     * 是否逻辑删除，0表示否，1表示是
     */
    @Column(name = "is_deleted")
    private String deleted;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "duration")
    private Long duration;

    @Column(name = "mileage")
    private Double mileage;

    @Column(name = "intervention_content")
    private String interventionContent;


    private transient Long interventionTimeNanoSecond;

    public Long getInterventionTimeNanoSecond() {
        if (this.interventionTimeNanoSecond == null) {
            this.interventionTimeNanoSecond = interventionTime == null ?
                    0L : interventionTime.getTime() * 1000000L;
        }
        return this.interventionTimeNanoSecond;
    }
}
