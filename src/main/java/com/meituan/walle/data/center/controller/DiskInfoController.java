package com.meituan.walle.data.center.controller;

import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.dal.wcdpbasedata.entity.DiskInfoResponse;
import com.meituan.walle.data.center.entity.response.CommonResponse;
import com.meituan.walle.data.center.service.WcdpBaseDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/01/08
 */
@Slf4j
@RestController
@RequestMapping("/diskInfo")
public class DiskInfoController {

    @Autowired
    private WcdpBaseDataService wcdpBaseDataService;

    @GetMapping("/getDiskInfo")
    public CommonResponse getDiskInfo(@RequestParam @NotBlank(message = "diskId is not blank") String diskId) {
        log.info("[DiskInfoController#getDiskInfo] call /diskInfo/getDiskInfo api, diskId: {}", diskId);
        try {
            DiskInfoResponse diskInfoResponse = wcdpBaseDataService.getDiskInfo(diskId);
            return CommonResponse.success(diskInfoResponse);
        } catch (Exception exception) {
            log.error("[DiskInfoController#getDiskInfo] get disk info exception, diskId: {}, exception: {}",
                    diskId, exception);
            return CommonResponse.fail(WebResponseStatusEnum.FAILED.getCode(), exception.getMessage());
        }
    }

}
