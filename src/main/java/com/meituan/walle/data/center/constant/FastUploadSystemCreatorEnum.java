package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

@AllArgsConstructor
@Getter
public enum FastUploadSystemCreatorEnum {
    // case
    INTERVENTION_EVENT_SYSTEM("intervention_event_system"),
    MAEB_EVENT_SYSTEM("maeb_event_system"),
    INTERVENTION_ACCIDENT_SYSTEM("intervention_accident_system"),
    // accident
    ACCIDENT_SYSTEM("accident_system"),
    // base
    BIZ_ONBOARD_FILE_SYSTEM("biz_onboard_file_system"),
    // define
    EVENT_SYSTEM("event_system"),
    // coredump
    COREDUMP_EVENT_SYSTEM("coredump_event_system"),
    ;

    private final String name;

    private static final Set<String> SYSTEM_NAMES = new HashSet<>();

    static {
        for (FastUploadSystemCreatorEnum value : values()) {
            SYSTEM_NAMES.add(value.getName());
        }
    }

    public static boolean isSystemCreated(String name) {
        return SYSTEM_NAMES.contains(name);
    }

}
