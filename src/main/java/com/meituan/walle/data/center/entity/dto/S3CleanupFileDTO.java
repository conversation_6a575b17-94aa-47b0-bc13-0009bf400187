package com.meituan.walle.data.center.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.walle.data.center.entity.message.AbstractEventMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class S3CleanupFileDTO extends AbstractEventMessage {
    @JsonProperty(value = "record_name")
    private String recordName;
    @JsonProperty(value = "file_name")
    private String fileName;
    @JsonProperty(value = "file_path")
    private String filePath;
    @JsonProperty(value = "s3_url")
    private String s3Url;
    @JsonProperty(value = "file_size")
    private Long fileSize;
    @JsonProperty(value = "cluster")
    private Integer cluster;
    @JsonProperty(value = "s3_is_deleted")
    private Byte s3IsDeleted;
    @JsonProperty(value = "s3_delete_time")
    private Date s3DeleteTime;
    @JsonProperty(value = "is_deleted")
    private Byte isDeleted;
}