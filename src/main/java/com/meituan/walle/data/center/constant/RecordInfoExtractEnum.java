package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/12/01
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RecordInfoExtractEnum {
    /**
     * 未提取
     */
    NOT_EXTRACTED(0, "未提取"),
    /**
     * 已提取
     */
    EXTRACTED(1, "已提取"),
    /**
     * 提取失败
     */
    PARSE_FAILED(2, "提取失败");

    private Integer code;
    private String msg;
}