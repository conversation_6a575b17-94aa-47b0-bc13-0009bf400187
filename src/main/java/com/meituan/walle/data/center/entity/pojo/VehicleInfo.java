package com.meituan.walle.data.center.entity.pojo;

import java.util.Date;
import javax.persistence.*;

@Table(name = "vehicle_info")
public class VehicleInfo {
    /**
     * 自增ID
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车辆ID
     */
    @Column(name = "vehicle_id")
    private String vehicleId;

    /**
     * 车辆设备ID
     */
    private String vin;

    /**
     * 车辆名称
     */
    private String name;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 车辆型号
     */
    private String type;

    /**
     * 车辆类型[0:unknown, 1:big, 2:middle]
     */
    @Column(name = "vehicle_type")
    private Byte vehicleType;

    /**
     * 用于区分是不是远程遥控车
     */
    @Column(name = "vehicle_category")
    private String vehicleCategory;

    /**
     * 发动机号
     */
    @Column(name = "engine_number")
    private String engineNumber;

    /**
     * 车牌号
     */
    @Column(name = "license_number")
    private String licenseNumber;

    /**
     * 生产年份
     */
    private Integer year;

    /**
     * 能源类型
     */
    @Column(name = "power_type")
    private Byte powerType;

    /**
     * 车辆运营状态[0:不在线|1:在线|2:故障]
     */
    @Column(name = "operation_state")
    private Byte operationState;

    /**
     * 状态[0:不可用|1:可用|2:已删除]
     */
    private Byte status;

    /**
     * 首次添加时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 最近更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * VID
     */
    private String vid;

    /**
     * 负责人
     */
    private String owner;

    /**
     * 产品线
     */
    private Integer product;

    /**
     * 图片URL
     */
    private String image;

    /**
     * 用途 [1:路测B组,2:运营,3:保障车,4:路测A组，5:路测C组
     */
    @Column(name = "purpose_id")
    private Byte purposeId;

    /**
     * 录入者
     */
    private String submitter;

    /**
     * 备注
     */
    private String remark;

    /**
     * 获取自增ID
     *
     * @return id - 自增ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置自增ID
     *
     * @param id 自增ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取车辆ID
     *
     * @return vehicle_id - 车辆ID
     */
    public String getVehicleId() {
        return vehicleId;
    }

    /**
     * 设置车辆ID
     *
     * @param vehicleId 车辆ID
     */
    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    /**
     * 获取车辆设备ID
     *
     * @return vin - 车辆设备ID
     */
    public String getVin() {
        return vin;
    }

    /**
     * 设置车辆设备ID
     *
     * @param vin 车辆设备ID
     */
    public void setVin(String vin) {
        this.vin = vin;
    }

    /**
     * 获取车辆名称
     *
     * @return name - 车辆名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置车辆名称
     *
     * @param name 车辆名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取品牌
     *
     * @return brand - 品牌
     */
    public String getBrand() {
        return brand;
    }

    /**
     * 设置品牌
     *
     * @param brand 品牌
     */
    public void setBrand(String brand) {
        this.brand = brand;
    }

    /**
     * 获取车辆型号
     *
     * @return type - 车辆型号
     */
    public String getType() {
        return type;
    }

    /**
     * 设置车辆型号
     *
     * @param type 车辆型号
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 获取车辆类型[0:unknown, 1:big, 2:middle]
     *
     * @return vehicle_type - 车辆类型[0:unknown, 1:big, 2:middle]
     */
    public Byte getVehicleType() {
        return vehicleType;
    }

    /**
     * 设置车辆类型[0:unknown, 1:big, 2:middle]
     *
     * @param vehicleType 车辆类型[0:unknown, 1:big, 2:middle]
     */
    public void setVehicleType(Byte vehicleType) {
        this.vehicleType = vehicleType;
    }

    /**
     * 获取用于区分是不是远程遥控车
     *
     * @return vehicle_category - 用于区分是不是远程遥控车
     */
    public String getVehicleCategory() {
        return vehicleCategory;
    }

    /**
     * 设置用于区分是不是远程遥控车
     *
     * @param vehicleCategory 用于区分是不是远程遥控车
     */
    public void setVehicleCategory(String vehicleCategory) {
        this.vehicleCategory = vehicleCategory;
    }

    /**
     * 获取发动机号
     *
     * @return engine_number - 发动机号
     */
    public String getEngineNumber() {
        return engineNumber;
    }

    /**
     * 设置发动机号
     *
     * @param engineNumber 发动机号
     */
    public void setEngineNumber(String engineNumber) {
        this.engineNumber = engineNumber;
    }

    /**
     * 获取车牌号
     *
     * @return license_number - 车牌号
     */
    public String getLicenseNumber() {
        return licenseNumber;
    }

    /**
     * 设置车牌号
     *
     * @param licenseNumber 车牌号
     */
    public void setLicenseNumber(String licenseNumber) {
        this.licenseNumber = licenseNumber;
    }

    /**
     * 获取生产年份
     *
     * @return year - 生产年份
     */
    public Integer getYear() {
        return year;
    }

    /**
     * 设置生产年份
     *
     * @param year 生产年份
     */
    public void setYear(Integer year) {
        this.year = year;
    }

    /**
     * 获取能源类型
     *
     * @return power_type - 能源类型
     */
    public Byte getPowerType() {
        return powerType;
    }

    /**
     * 设置能源类型
     *
     * @param powerType 能源类型
     */
    public void setPowerType(Byte powerType) {
        this.powerType = powerType;
    }

    /**
     * 获取车辆运营状态[0:不在线|1:在线|2:故障]
     *
     * @return operation_state - 车辆运营状态[0:不在线|1:在线|2:故障]
     */
    public Byte getOperationState() {
        return operationState;
    }

    /**
     * 设置车辆运营状态[0:不在线|1:在线|2:故障]
     *
     * @param operationState 车辆运营状态[0:不在线|1:在线|2:故障]
     */
    public void setOperationState(Byte operationState) {
        this.operationState = operationState;
    }

    /**
     * 获取状态[0:不可用|1:可用|2:已删除]
     *
     * @return status - 状态[0:不可用|1:可用|2:已删除]
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * 设置状态[0:不可用|1:可用|2:已删除]
     *
     * @param status 状态[0:不可用|1:可用|2:已删除]
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * 获取首次添加时间
     *
     * @return create_time - 首次添加时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置首次添加时间
     *
     * @param createTime 首次添加时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取最近更新时间
     *
     * @return update_time - 最近更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置最近更新时间
     *
     * @param updateTime 最近更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取VID
     *
     * @return vid - VID
     */
    public String getVid() {
        return vid;
    }

    /**
     * 设置VID
     *
     * @param vid VID
     */
    public void setVid(String vid) {
        this.vid = vid;
    }

    /**
     * 获取负责人
     *
     * @return owner - 负责人
     */
    public String getOwner() {
        return owner;
    }

    /**
     * 设置负责人
     *
     * @param owner 负责人
     */
    public void setOwner(String owner) {
        this.owner = owner;
    }

    /**
     * 获取产品线
     *
     * @return product - 产品线
     */
    public Integer getProduct() {
        return product;
    }

    /**
     * 设置产品线
     *
     * @param product 产品线
     */
    public void setProduct(Integer product) {
        this.product = product;
    }

    /**
     * 获取图片URL
     *
     * @return image - 图片URL
     */
    public String getImage() {
        return image;
    }

    /**
     * 设置图片URL
     *
     * @param image 图片URL
     */
    public void setImage(String image) {
        this.image = image;
    }

    public Byte getPurposeId() {
        return purposeId;
    }

    public void setPurposeId(Byte purposeId) {
        this.purposeId = purposeId;
    }

    public String getSubmitter() {
        return submitter;
    }

    public void setSubmitter(String submitter) {
        this.submitter = submitter;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}