package com.meituan.walle.data.center.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class AccidentMessageDTO {

    /**
     * 事故工单唯一ID
     */
    private String eventId;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 事故状态
     */
    private Integer status;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 扩展字段
     */
    private Object content;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ContentDTO{
        /**
         * 事故状态描述
         */
        private String statusDes;

        /**
         * 上报人
         */
        private String reporter;

        /**
         * 操作人
         */
        private String operator;

    }
}
