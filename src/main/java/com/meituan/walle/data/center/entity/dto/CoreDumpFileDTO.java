package com.meituan.walle.data.center.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CoreDumpFileDTO {
    private String recordName;
    private String fileName;
    private String filePath;
    private String s3Url;
    private String s3AccessUrl;
    private Integer fileType;
    private Long fileSize;
    /**
     * 所属集群[0:北京备份|1:中卫自动车]
     */
    private Integer cluster;
}
