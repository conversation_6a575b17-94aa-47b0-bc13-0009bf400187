package com.meituan.walle.data.center.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/02/21
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class FastUploadDTO {

    /**
     * 事件的发生纳秒时间戳
     */
    private Long timestamp;

    /**
     * 需要回收事件发生时间前x秒数据
     */
    private Integer prevSecond;

    /**
     * 需要回收事件发生时间后x秒数据
     */
    private Integer postSecond;

    /**
     * 需要回收的模块信息
     */
    private String modules;

    /**
     * 事件枚举值
     */
    private Integer noticeType;
}
