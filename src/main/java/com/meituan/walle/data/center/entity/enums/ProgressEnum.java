package com.meituan.walle.data.center.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum ProgressEnum {
    S3_UPLOAD_PROGRESS("s3UploadProgress", "S3上传进度"),
    HDFS_UPLOAD_PROGRESS("hdfsUploadProgress", "HDFS上传进度"),
    CREATE_EXTRACT_TASK_PROGRESS("createExtractTaskProgress", "创建抽取任务进度"),
    RECORD_AVAILABILITY_STATUS("recordAvailabilityStatus", "record 可用状态"),

    ;

    private String code;
    private String desc;

    public static ProgressEnum getByCode(String code) {
        for (ProgressEnum en : ProgressEnum.values()) {
            if (Objects.equals(en.code, code)) {
                return en;
            }
        }
        return null;
    }

    public static ProgressEnum getByDesc(String desc) {
        for (ProgressEnum en : ProgressEnum.values()) {
            if (Objects.equals(en.desc, desc)) {
                return en;
            }
        }
        return null;
    }
}
