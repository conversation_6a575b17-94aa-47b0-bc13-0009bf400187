package com.meituan.walle.data.center.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.walle.data.center.config.mcc.OnboardDiskDeleteRuleConfig;
import com.meituan.walle.data.center.constant.CatConstant;
import com.meituan.walle.data.center.constant.CharConstant;
import com.meituan.walle.data.center.constant.DateTimeFormatterConstant;
import com.meituan.walle.data.center.constant.WebResponseStatusEnum;
import com.meituan.walle.data.center.entity.Response;
import com.meituan.walle.data.center.entity.dto.RecordInterventionTimesDTO;
import com.meituan.walle.data.center.entity.pojo.BizVehicleDataRelatedConfig;
import com.meituan.walle.data.center.entity.vo.*;
import com.meituan.walle.data.center.mapper.BizVehicleDataRelatedConfigMapper;
import com.meituan.walle.data.center.service.BizEventDeriveInterventionService;
import com.meituan.walle.data.center.service.DiskDeleterService;
import com.meituan.walle.data.center.util.DatetimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/11/29
 */
@Slf4j
@InterfaceDoc(
        displayName = "车端磁盘清理规则接口",
        type = "restful",
        description = "通过 HTTP/HTTPS 协议访问远程服务的接口，提供车端磁盘清理规则。",
        scenarios = "车端磁盘清理规则所有接口"
)
@RestController
@RequestMapping(value = "/onboard_disk_delete_rule")
public class OnboardDiskDeleteRuleController {

    @Autowired
    private BizEventDeriveInterventionService bizEventDeriveInterventionService;

    @Resource
    private DiskDeleterService diskDeleterService;

    @Resource
    private BizVehicleDataRelatedConfigMapper bizVehicleDataRelatedConfigMapper;

    @MethodDoc(
            displayName = "获取删除规则",
            description = "车端拉取删除规则")
    @GetMapping(value = "/get_trigger_delete_rule")
    public Response getTriggerDeleteRule(@RequestParam("vin") String vin) {
        if (StringUtils.isBlank(vin)) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "vin is null or '' !");
        }
        Transaction t = Cat.newTransaction(CatConstant.ONBOARD_DISK_DELETE_RULE, "get.trigger.delete.rule");
        long start = System.currentTimeMillis();
        try {
            BizVehicleDataRelatedConfig config = bizVehicleDataRelatedConfigMapper.selectByVin(vin);
            boolean isBizVehicle = config != null && !config.getIsSendDisk();
            TriggerRuleVO triggerRuleVO = TriggerRuleVO
                    .builder()
                    .isBizVehicle(isBizVehicle)
                    .lowerLimitDeleteRate(OnboardDiskDeleteRuleConfig.LOWER_LIMIT_DELETE_RATE)
                    .upperLimitDeleteRate(OnboardDiskDeleteRuleConfig.UPPER_LIMIT_DELETE_RATE)
                    .removeRecordDays(OnboardDiskDeleteRuleConfig.REMOVE_RECORD_DAYS)
                    .build();
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(triggerRuleVO);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("get trigger rule fail", e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "get trigger delete rule fail !");
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "车端上报磁盘分布情况",
            description = "车端上报磁盘分布情况")
    @PostMapping(value = "/disk_distribution")
    public Response diskDistribution(@RequestBody DiskDistributionParamVO param) {
        if (param == null) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "param is null !");
        }
        String vin = param.getVin();
        if (StringUtils.isBlank(vin)) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "vin is null or '' !");
        }
        if (CollectionUtils.isEmpty(param.getDiskDistribution())) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "DiskDistribution is null or empty !");
        }
        Transaction t = Cat.newTransaction(CatConstant.ONBOARD_DISK_DELETE_DISTRIBUTION, "disk.distribution");
        long start = System.currentTimeMillis();
        try {
            diskDeleterService.insertDiskDistribution(param);
            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ();
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("Post disk distribution failed, request param is {}", param, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "Post disk distribution failed !");
        } finally {
            t.complete();
        }
    }

    @MethodDoc(
            displayName = "车端获取当前车辆的所有接管",
            description = "车端拉取当前车辆的所有接管")
    @PostMapping(value = "/list_intervention")
    public Response queryIntervention(@RequestBody InterventionParamVO param) {
        if (param == null) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "param is null !");
        }
        String vin = param.getVin();
        if (StringUtils.isBlank(vin)) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "vin is null or '' !");
        }
        if (CollectionUtils.isEmpty(param.getRecordNames())) {
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "recordNames is null or empty !");
        }
        Transaction t = Cat.newTransaction(CatConstant.ONBOARD_DISK_DELETE_RULE, "query.intervention");
        long start = System.currentTimeMillis();
        try {
            BizVehicleDataRelatedConfig config = bizVehicleDataRelatedConfigMapper.selectByVin(vin);
            boolean isNeedDeleted = config != null && !config.getIsSendDisk();
            if (!isNeedDeleted) {
                return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode()
                                .toString(),
                        "this vehicle is not delete file !");
            }

            List<String> recordNames = param.getRecordNames();
            RecordInterventionTimesDTO dto =
                    bizEventDeriveInterventionService.queryIntervention(param.getVin(), recordNames);
            List<String> modules =
                    Arrays.asList(OnboardDiskDeleteRuleConfig.PRIORITY_MODULE.split(CharConstant.CHAR_DD));

            String minRecordName = recordNames.stream().sorted().findFirst().orElse("");
            String[] recordParts = minRecordName.split(CharConstant.CHAR_XH);
            Date minRecordDate = DatetimeUtil.parseDate(recordParts[0], DateTimeFormatterConstant.ymd);

            InterventionResultVO interventionResultVO = InterventionResultVO
                    .builder().priorityModules(modules)
                    .interventionTimes(dto.getRecordInterventionTimes())
                    .notBelongThisVehicle(dto.getRecordsNotBelongThisVehicle())
                    .dayHours(OnboardDiskDeleteRuleConfig.DAY_HOURS)
                    .previousTime(OnboardDiskDeleteRuleConfig.PREVIOUS_TIME)
                    .behindTime(OnboardDiskDeleteRuleConfig.BEHIND_TIME)
                    .freezeTime(diskDeleterService.getFreezeTime(param.getVin(), minRecordDate))
                    .build();

            t.setDurationInMillis(System.currentTimeMillis() - start);
            t.setSuccessStatus();
            return Response.succ(interventionResultVO);
        } catch (Exception e) {
            t.setDurationInMillis(System.currentTimeMillis() - start);
            log.error("query intervention fail, request param is {}", param, e);
            Cat.logError(e);
            t.setStatus(e);
            return Response.fail(WebResponseStatusEnum.ONBOARD_DELETE_FILE_REQUEST_PARAM_ILLEGAL.getCode().toString(),
                    "query intervention fail !");
        } finally {
            t.complete();
        }
    }

}