package com.meituan.walle.data.center.entity.enums;

/**
 * Copyright @2023 Sankuai Technology Inc. All rights reserved
 * <AUTHOR>
 * @date 2023/11/8
 */

public enum EventUploadTaskCreatedEnum {

    TASK_CREATED_SUCCESSFULLY(200, "任务创建成功"),

    INVALID_TASK_PARAMETERS(400, "无效的任务参数"),

    NO_EFFECTIVE_CONFIG(401, "未匹配到生效的配置"),

    VEHICLE_NOT_IN_WHITELIST(402, "车辆无法创建快速回收任务"),

    MAX_DAILY_TASK_LIMIT_REACHED(403, "车辆创建任务数达到每日限额"),

    TASK_CREATED_FAILED(500, "任务创建失败");

    private Integer code;

    private String message;

    EventUploadTaskCreatedEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
