package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "biz_event_coredump")
@Data
public class BizEventCoredump {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    private String eventId;

    /**
     * 表record的record_name外键
     */
    private String recordName;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;

    /**
     * Coredump事件发生时间
     */
    private Date eventTime;

    /**
     * 车端coredump文件夹路径
     */
    private String filePath;

    private Integer status;

    /**
     * 云端coredump的文件路径, 若有多个则用逗号分隔
     */
    private String s3Url;

    /**
     * coredump的栈信息
     */
    private String stack;

    private Date createTime;

    private Date updateTime;
}
