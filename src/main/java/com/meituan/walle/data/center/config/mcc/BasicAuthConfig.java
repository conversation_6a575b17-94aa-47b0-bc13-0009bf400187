package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

/**
 * <AUTHOR>
 * @Date 2023/09/14
 */
public class BasicAuthConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "base.ba.uri")
    public static volatile String BASE_BA_URI = "/vehicle_client/*;/data_upload/cruise_data;/mviz/data_list";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "remote.video.ba.uri")
    public static volatile String REMOTE_VIDEO_BA_URI = "/remote_video/callback";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "base.ba.client.secret.map")
    public static volatile String BASE_BA_CLIENT_SECRET_MAP = "cc28cce751:138207d30bb745269915b6256811e1af";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "base.sso.included.uri.list")
    public static volatile String SSO_INCLUDED_URI_LIST = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "base.sso.rewrite.location")
    public static volatile String SSO_REWRITE_LOCATION = "/walledata";
    
}
