package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> @Beijing
 * @date 2022/03/21
 * Description:
 * Modified by
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum CaseOperateHistoryEnum {

    MODIFY_TEAM(1, "修改团队"),
    MODIFY_MODULE(2, "修改模块"),
    MODIFY_PROBLEM(3, "修改问题"),
    MODIFY_SCENE(4, "修改场景"),
    MODIFY_STATUS(5, "修改状态"),
    MODIFY_ASSIGN(6, "修改指派人"),
    ADD_REMARK(7, "添加备注"),
    UPLOAD_PHOTO(8, "上传图片"),
    DELETE_PHOTO(9, "删除图片"),
    MODIFY_CC(10, "修改抄送人"),
    MODIFY_TITLE(11, "修改标题"),
    CASE(12, "关联case"),
    MODIFY_TAG(13, "修改标签"),
    UPLOAD_VIDEO(14, "上传视频"),
    DELETE_VIDEO(15, "删除视频"),
    MODIFY_INTERVENTION_TYPE(16, "修改人工判断接管类型"),
    MODIFY_FINAL_JUDGE(17, "修改最终判断"),
    MODIFY_CATEGORY(18, "修改分类"),
    ADD_COMMENT(19, "添加评论"),
    DELETE_COMMENT(20, "删除评论"),
    ACCEPT_TASK(21, "接受任务"),
    REFUSE_TASK(22, "拒绝任务"),
    MODIFY_APPEARANCE(23, "修改现象"),
    MODIFY_PRIORITY(24, "修改优先级"),
    MODIFY_OPERATE_STATUS(25, "修改运营状态"),
    RECEIVE_TASK(26, "领取了任务"),
    MODIFY_WEATHER(27, "修改天气"),
    MODIFY_CAMERA_QUALITY(28, "修改相机成像质量"),
    ;

    private Integer code;
    private String msg;

}
