package com.meituan.walle.data.center.algorithm.astar;

import com.meituan.walle.data.center.entity.algorithm.astar.Node;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class MinHeap {
    private final ArrayList<Node> queue = new ArrayList<>();
    private int endPnt = 0;
    private final Map<String, Node> nodeMap = new HashMap<>();

    public Node getAndRemoveMin() {
        if (isEmpty()) {
            return null;
        }

        Node head = queue.get(0);
        Node last = queue.get(endPnt - 1);
        queue.set(0, last);
        endPnt--;
        nodeMap.remove(head.getId());

        topDown();

        return head;
    }

    public Node find(String id) {
        return nodeMap.get(id);
    }

    public void add(Node node) {
        if (queue.size() > endPnt) {
            queue.set(endPnt, node);
        } else {
            queue.add(node);
        }
        endPnt++;

        nodeMap.put(node.getId(), node);

        bottomUp();
    }

    public boolean isEmpty() {
        return endPnt <= 0;
    }

    private void topDown() {
        for (int cur = 0; cur < endPnt; ) {
            int left = 2 * cur + 1;
            int right = 2 * cur + 2;

            Node dc = queue.get(cur);
            Node dl = left < endPnt ? queue.get(left) : null;
            Node dr = right < endPnt ? queue.get(right) : null;

            int next = -1;
            Node dn = dc;
            if (dl != null && dl.f() < dn.f()) {
                next = left;
                dn = dl;
            }
            if (dr != null && dr.f() < dn.f()) {
                next = right;
                dn = dr;
            }

            if (next >= 0 && next < endPnt) {
                queue.set(next, dc);
                queue.set(cur, dn);
                cur = next;
            } else {
                break;
            }
        }
    }

    private void bottomUp() {
        for (int cur = endPnt - 1; cur >= 0; ) {
            int parent = (cur - 1) / 2;
            if (parent < 0) {
                break;
            }

            Node dc = queue.get(cur);
            Node dp = queue.get(parent);

            if (dc.f() < dp.f()) {
                queue.set(parent, dc);
                queue.set(cur, dp);
                cur = parent;
            } else {
                break;
            }
        }
    }
}
