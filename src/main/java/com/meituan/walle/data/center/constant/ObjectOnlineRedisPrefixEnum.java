package com.meituan.walle.data.center.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/12/10
 */
@Getter
@AllArgsConstructor
public enum ObjectOnlineRedisPrefixEnum {

    IPC(1, "vin_ipc_report"),
    PROXY(2, "vin_event_report_time"),
    UPLOAD_FILE(3, "vin_upload_report_time"),
    DISK_DELETER(4, "vin_disk_deleter_report_time"),
    UPLOAD_FILE_INFO(5, "vin_upload_file_info_time"),
    REPORT_AUTO_ARRIVE(6, "vin_report_auto_arrive");

    private Integer code;
    private String redisPrefix;

    public static String getRedisPrefixByCode(int code) {
        for (ObjectOnlineRedisPrefixEnum redisPrefixEnum : ObjectOnlineRedisPrefixEnum.values()) {
            if (code == redisPrefixEnum.getCode()) {
                return redisPrefixEnum.getRedisPrefix();
            }
        }
        return null;
    }
}
