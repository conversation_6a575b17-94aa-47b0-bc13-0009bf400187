package com.meituan.walle.data.center.entity.pojo;

import lombok.*;

import javax.persistence.*;
import java.util.Date;

@Table(name = "spark_job_instance")
@Entity
@Data
public class SparkJobInstance {
    @Id
    @GeneratedValue(strategy=GenerationType.IDENTITY)
    private Integer id;
    private String instanceUuid;
    private String jobUuid;
    private String testArgs;
    private Integer runTimes;
    private Integer status;
    private Integer priority;
    @Column(name = "update_time", insertable = false, updatable = false)
    private Date updateTime;
    @Column(name = "create_time", insertable = false, updatable = false)
    private Date createTime;
}
