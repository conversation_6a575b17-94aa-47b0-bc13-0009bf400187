package com.meituan.walle.data.center.config.mcc;

import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;

public class VehicleTagReportConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "com.meituan.walle.data.center.tag.report.groupId")
    public static volatile long TAG_REPORT_GROUP_ID = 64010309598L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "tags.query.list")
    public static volatile String TAGS_QUERY = "运营接管, 自动驾驶能力, 碰撞风险, 路况问题";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "event.detail.url")
    public static volatile String EVENT_DETAIL_URL =
            "https://walledata.mad.test.sankuai.com/app/events/?source=13";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "tag.report.vehicle.groupId.url")
    public static volatile String VEHICLE_GROUP_URL =
            "https://walledata.mad.test.sankuai.com/app/api/vresv/get_vehicle_group_ba";
}
