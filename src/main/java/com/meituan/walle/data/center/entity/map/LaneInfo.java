package com.meituan.walle.data.center.entity.map;

import com.meituan.walle.data.center.constant.HdmapConstant;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@Builder
public final class LaneInfo {
    private final String id;
    private final List<Point2d> point2ds;

    private volatile List<Point2d> unitDirections;
    private volatile List<LineSegment> segments;
    private volatile double[] accumulatedS;
    private volatile double totalLength;

    private void initCalculateS() {
        if (this.accumulatedS == null) {
            synchronized (this) {
                if (this.accumulatedS == null) {
                    List<Point2d> point2ds = this.point2ds;
                    if (point2ds.size() < 2) {
                        this.accumulatedS = new double[0];
                        return;
                    }

                    int leng = point2ds.size() - 1;
                    double[] accumulatedS = new double[leng];
                    List<LineSegment> segments = new ArrayList<>(leng);
                    List<Point2d> unitDirections = new ArrayList<>(leng);

                    double s = 0;
                    for (int i = 0; i + 1 < point2ds.size(); ++i) {
                        LineSegment lineSegment = new LineSegment(point2ds.get(i), point2ds.get(i+1));
                        segments.add(lineSegment);
                        accumulatedS[i] = s;
                        unitDirections.add(lineSegment.getUnitDirection());
                        s += segments.get(i).getLength();
                    }
                    this.totalLength = s;
                    this.accumulatedS = accumulatedS;
                    this.unitDirections = unitDirections;
                    this.segments = segments;
                }
            }
        }
    }

    public Point2d getSmoothPoint(double s) {
        if (this.accumulatedS == null) {
            initCalculateS();
        }

        if (this.point2ds.size() < 2) {
            return Point2d.builder().build();
        }

        if (s <= 0d) {
            return this.point2ds.get(0);
        }

        if (s > this.totalLength) {
            return this.point2ds.get(this.point2ds.size() - 1);
        }

        int index = Arrays.binarySearch(this.accumulatedS, 0, this.accumulatedS.length - 1, s);
        if (index >= 0) {
            return this.point2ds.get(index);
        }

        // Arrays.binarySearch: @return index of the search key,
        // if it is contained in the array within the specified range;
        // otherwise, <tt>(-(<i>insertion point</i>) - 1)</tt>.
        index = -index -1;

        Point2d point2d = this.point2ds.get(index);

        double delta = this.accumulatedS[index] - s;
        if (delta < HdmapConstant.MATH_EPSILON) {
            return point2d;
        }

        Point2d unitDirection = this.unitDirections.get(index - 1);
        return Point2d.builder()
                .x(point2d.getX() - unitDirection.getX() * delta)
                .y(point2d.getY() - unitDirection.getY() * delta)
                .build();
    }
}
