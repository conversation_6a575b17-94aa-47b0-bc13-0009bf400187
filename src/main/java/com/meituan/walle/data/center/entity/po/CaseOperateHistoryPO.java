package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> @Beijing
 * @date 2022/05/07
 * Description:
 * Modified by
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class CaseOperateHistoryPO implements Serializable {

    private Long id;
    private String caseId;
    private Integer type;
    private String description;
    private String from;
    private String operatorId;
    private String source;
    private Date createTime;
    private Date updateTime;

}