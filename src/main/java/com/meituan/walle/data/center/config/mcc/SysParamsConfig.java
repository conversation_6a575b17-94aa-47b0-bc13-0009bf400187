package com.meituan.walle.data.center.config.mcc;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Sets;
import com.meituan.walle.data.center.config.converter.UploadFileNoticeConverter;
import com.meituan.walle.data.center.constant.MccConstant;
import com.sankuai.meituan.config.annotation.MtConfig;
import com.sankuai.walle.wcdp.core.entity.dto.UploadFileNoticeDTO;
import org.apache.curator.shaded.com.google.common.collect.Lists;

import java.util.*;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/18 下午1:49
 * Description:
 * Modified by
 */
public class SysParamsConfig {

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "ones.cases.projectId")
    public static volatile String ones_cases_projectId = "6238";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "dx.app.push.url")
    public static volatile String dx_app_push_url = "http://api.xm.test.sankuai.com/api/pub/push";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "dx.app.to.group.url")
    public static volatile String dx_app_to_group_url = "http://api.xm.test.sankuai.com/api/pub/pushToRoom";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onCall.duty.persons")
    public static volatile String duty_persons = "谢鹏燕,xiepengyan";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onCall.duty.notice.persons")
    public static volatile String duty_notice_persons = "xiepengyan";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onCall.duty.startdate")
    public static volatile String duty_start_date = "20210106";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "sparkjob.notice.members")
    public static volatile String spark_notice_members = "liuqichun,wangbo71,zhaojinsong";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "diskAndJobStatus.notice.persons")
    public static volatile String diskAndJobStatus_notice_persons = "zhaojinsong";//默认值

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "interCoredumpTagInfo_notice_persons")
    public static volatile String interCoredumpTagInfo_notice_persons = "zhaojinsong";//默认值

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "max.execute.spark.job.num")
    public static volatile int maxExecuteSparkJobNum = 15;//默认值

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "lambda.bucket")
    public static volatile String lambda_bucket = "vehicle-data";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.object.report.offline")
    public static volatile long vehicle_object_report_offline = 5L * 60 * 1000;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.service.offline.based.ipc")
    public static volatile long vehicle_service_offline_based_ipc = 15L * 60 * 1000;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.service.offline.notice.switch")
    public static volatile boolean vehicle_service_offline_notice_switch = true;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "localization.series.break.interval")
    public static volatile long localization_series_break_interval = 300L * 1000_000_000; //5分钟

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "parking.assign.black.list")
    public static volatile String PARKING_ASSIGN_BLACK_LIST = "";

    /**
     * 设置spark任务的优先级：将spark任务按照数据类型先拍脑袋进行简单处理，不一定对，后期不适用可以再进行迭代重构
     **/
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "spark.job.priority")
    public static volatile String spark_job_priority = "{\"RecordRoadTest\": 100, \"RecordRdTest\": 105," +
            " \"RecordOtherTest\": 110, \"RecordSimTask\": 120, \"SimTask\": 115," +
            " \"LambdaTask\": 125, \"General\": 150}";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "pike.tag.bizId")
    public static volatile String pike_tag_bizId = "pike_walledata_test";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "waiting.emit.event.cache.seconds")
    public static volatile int event_cache_seconds = 900;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "device.connection.cache.seconds")
    public static volatile int device_connection_cache_seconds = 72000;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "tag.report.vin.list")
    public static volatile String tag_report_vin_list = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "tag.report.white.list")
    public static volatile String
            tag_report_white_list = "wb_zhangyuwei04,panchuheng,fuyuanbo,liuqichun,zhuguolu,xiepengyan";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "button.match.event.millis")
    public static volatile int button_match_event_millis = 30000;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "abandon.event.millis")
    public static volatile int abandon_event_millis = 10000;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "lambda.modules")
    public static volatile String lambda_modules = "Localization,Planning,ScheduleNode";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "collision.detection.status")
    public static volatile boolean collision_detection_status = true;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "collision.msg.group")
    public static volatile long collision_msg_group = 64010309598L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "video.check.group")
    public static volatile long video_check_group = 64010309598L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "s3.limit.delete.total")
    public static volatile long s3_delete_total_limit = 1000000L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "s3.limit.delete.size")
    public static volatile long s3_delete_size_limit = 500L * 1024 * 1024 * 1024 * 1024;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "s3.limit.delete.days.before")
    public static volatile int s3_delete_days_before_limit = 30 * 10;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "s3.limit.delete.parallelism")
    public static volatile int s3_delete_parallelism_limit = 10;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "specific.event.front.list")
    public static volatile String specific_event_front_list = "302";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "specific.event.freespace.list")
    public static volatile String specific_event_freespace_list = "372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "create.fast.upload.task.events")
    public static volatile String create_fast_upload_task_events = "302,303,306,322,327";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.client.single.disk.purpose")
    public static volatile String single_disk_purpose = "业务运营,路测-测试,校园运营";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.client.double.disk.location")
    public static volatile String double_disk_location = "shenzhenpingshan";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.case.lower.priority.vin.list")
    public static volatile String lower_priority_vehicles = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "real.time.data.notice.person")
    public static volatile String real_time_data_notice_persons = "panchuheng,xiepengyan,liuqichun,leihui03";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "disk.deleter.notice.person")
    public static volatile String disk_deleter_notice_persons = "xiepengyan,liuqichun,leihui03,zhangchenyu07";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.coredump.is.all")
    public static volatile boolean is_all_vehicle_upload_coredump = false;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "realtime.data.recovery.notice.time.before.hour")
    public static volatile int realtime_data_recovery_notice_time_before_hour = -24;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "map.collect.down.sampling.param")
    public static volatile String map_collect_down_sampling_param = "{\"deltaDegree\": 1.0, \"deltaPosition\": 1.0}";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "live.exchange.service.ip.port")
    public static volatile String live_exchange_service_ip_port = "*************:80";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "reboot.reset.task.types")
    public static volatile Set<Integer> reboot_reset_task_types =
            Sets.newConcurrentHashSet(Lists.newArrayList(4, 6));

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle_resume.task.types")
    public static volatile Set<Integer> vehicle_resume_task_types =
            Sets.newConcurrentHashSet(Lists.newArrayList(1, 2, 4, 5, 6, 7));

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "oncall.manager.operator.blacklist")
    public static volatile String oncall_manager_operator_blacklist = "walle,system";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.file.metadata.produce.switch")
    public static volatile boolean onboard_file_metadata_produce_switch = true;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "calibration.save.file.url")
    public static volatile String calibration_save_file_url =
            "https://walledata.mad.test.sankuai.com/api/calibration/updateWithFileBa";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "down.sampling.rec.mps")
    public static volatile double down_sampling_rec_mps = 2.5;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "candata.upload.size.mps")
    public static volatile double candata_upload_mps = 0.005;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "module.map.mps")
    public static volatile String module_map_mps = "{\"CameraFpgaP0H120\": 2.19, \"CameraFpgaP0H30\": 3.35, " +
            "\"CameraFpgaP0H60\": 1.91, \"CameraFpgaP0H70\":4.98, \"CameraFpgaP120H70\": 4.41," +
            " \"CameraFpgaP180H120\": 2.11, \"CameraFpgaP270H120\": 2.4, \"CameraFpgaP90H120\": 1.8, " +
            "\"CameraFpgaP60H70\": 4.51, \"CameraThumbnail\": 6.0, \"Control\": 0.16, \"ControlArbitration\": 0.09," +
            " \"GnssDriver\": 0.03, \"IntegratedPnc\": 0.19, \"LidarPacketCollector\": 6.0, \"Localization\": 0.05, " +
            "\"Perception\": 0.2, \"Planning\": 1.88, \"Prediction\": 0.26, \"ReferenceLine\": 0.95, " +
            "\"ScheduleNode\": 0.6}";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "active.vehicle.group.id")
    public static volatile Long active_vehicle_group_id = 42L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "active.time.gap.millis")
    public static volatile Long active_time_gap_millis = 120000L;

    public static volatile List<String> snapshot_parse_df_whitelist =
            Lion.getList(MccConstant.MCC_CLIENT_ID, "snapshot.parse.df.whitelist", String.class,
                    Lists.newArrayList("/dev/mapper", "/55cd"));

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "collision.detection.group.id")
    public static volatile long collision_detection_group_id = 64010309598L; // 生产66184562429L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle_map_url")
    public static volatile String vehicle_map_url = "https://walle.meituan.com/m/h5-map/vehicle/";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.add.url")
    public static volatile String accident_add_url = "https://walle.meituan.com/oncall/index.html#/accident/add?vin=";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.list.url")
    public static volatile String accident_list_url = "https://walle.meituan.com/oncall/index.html#/accident?vin=";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "hmi.url")
    public static volatile String hmi_url = "https://hmi.meituan.com/index.html#/operation?vin=";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "csm.event.url")
    public static volatile String csm_event_url = "https://walle.sankuai.com/m/csm/event?vin=";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "collision.detection.vin.list")
    public static volatile String collision_detection_vin_list = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "mafka.produce.disk.status")
    public static volatile boolean mafkaProduceDiskStatus = false;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "event.avoid.repeat.timeout")
    public static volatile int event_avoid_repeat_timeout = 36000;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "auto.create.fast.task.demotion.type")
    public static volatile String demotion_types = "TELEOP_COLLISION_RISK,MAEB_COLLISION_RISK";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "event.file.unresolved.group")
    public static volatile long event_file_unresolved_group = 64010309598L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "upload.file.info.vehicle.config")
    public static volatile String vehicle_config = "{\"notWaitFileTypes\": {\"calibration\": 60}}";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "osviz.repo.commit.id")
    public static volatile String osviz_repo_commit_id = "cloud_render_api";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "osviz.compile.script")
    public static volatile String osviz_compile_script = "simulation/simcluster/compile/render_compile.py";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "osviz.submit.job.url")
    public static volatile String osviz_submit_job_url = "https://simcluster.sankuai.com/api/v1/submit_job";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "osviz.task.webviz.version")
    public static volatile String osviz_task_webviz_version = "2.0";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "data.center.base.url")
    public static volatile String data_center_base_url = "http://walledata.mad.test.sankuai.com/walledata";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "upload.candata.vins")
    public static volatile String upload_candata_vins = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "event.default.report.mode")
    public static volatile String event_default_report_mode = "auk-mqtt";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "event.proxy.test.vins")
    public static volatile String proxy_test_vins = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "event.proxy.test.base.url")
    public static volatile String proxy_test_base_url = "https://spider-mad.test.zservey.com/data-center";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "event.proxy.prod.base.url")
    public static volatile String proxy_prod_base_url = "https://walle.zservey.com/data-center";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "tt.create.url")
    public static volatile String tt_create_url = "http://ticket.ee.test.sankuai.com/api/1.0/ticket/fast/create";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "tt.calibration.exception.exception.level")
    public static volatile String tt_create_calibration_exception_sla =
            "{\"203\": \"S2\", \"302\": \"S4\", \"204\": \"S4\"}";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "tt.calibration.exception.assigned")
    public static volatile String tt_calibration_exception_assigned =
            "{\"203\": \"hantiansi\", \"302\": \"hantiansi\", \"204\": \"chenchen117\"}";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "tt.calibration.exception.cc")
    public static volatile String tt_calibration_exception_cc = "nieqiong,chenchen117";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "tt.calibration.exception.reporter")
    public static volatile String tt_calibration_exception_reporter = "hantiansi";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "tt.calibration.exception.item.id")
    public static volatile long tt_calibration_exception_item_id = 30665;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "tt.calibration.exception.title")
    public static volatile String tt_calibration_exception_title =
            "{\"203\": \"标定服务异常\", \"302\": \"标定服务异常\", \"204\": \"运营标定\"}";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "disk.upload.file.notice.mafka.delay.time.ms")
    public static volatile long disk_upload_file_notice_mafka_delay_time_ms = 300_000;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "disk.upload.record.process.stagnate.duration.s")
    public static volatile long disk_upload_record_process_stagnate_duration_s = 900;

    @MtConfig(
            clientId = MccConstant.MCC_CLIENT_ID,
            key = "disk.upload.file.notice.mafka.discard.list",
            converter = UploadFileNoticeConverter.class)
    public static volatile List<UploadFileNoticeDTO> disk_upload_file_notice_mafka_discard_list = new ArrayList<>();

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "disk.record.upload.process.upper.limit.value")
    public static volatile int disk_record_upload_process_upper_limit_value = 2000;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "disk.upload.file.notice.mafka.delay.upper.limit.day")
    public static volatile int disk_upload_file_notice_mafka_delay_upper_limit_day = 7;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "event.specific.export.to.hive")
    public static volatile String event_specific_export_to_hive = "308,309,315,316,323,339,340";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "autodrive.event.to.mafka")
    public static volatile String autodrive_event_to_mafka = "0";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "event.specific.double.write")
    public static volatile boolean event_specific_double_write = true;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "disk.upload.update.record.hdfs.upload.process.is.enabled")
    public static volatile boolean disk_upload_update_record_hdfs_upload_process_is_enabled = false;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "disk.upload.delete.record.upload.process.is.enabled")
    public static volatile boolean disk_upload_delete_record_upload_process_is_enabled = false;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "accident.group.other.robot")
    public static volatile String accident_other_robot = "137439221660";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "dx.group.name.max.len")
    public static volatile int dx_group_name_max_len = 24;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "record.file.for.read.mysql")
    public static volatile boolean record_file_for_read_mysql = false;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "record.file.for.write.mysql")
    public static volatile boolean record_file_for_write_mysql = false;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "record.pkg.for.read.mysql")
    public static volatile boolean record_pkg_for_read_mysql = false;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "record.pkg.for.write.mysql")
    public static volatile boolean record_pkg_for_write_mysql = false;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "record.recording.duration.millis.upper.limit")
    public static volatile long record_recording_duration_millis_upper_limit = 10 * 60 * 60 * 1000L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "drb.record.filter.is.enabled")
    public static volatile boolean drb_record_filter_is_enabled = true;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "base.file.upload.max.size")
    public static volatile long base_file_upload_max_size = 5 * 1024 * 1024 * 1024L;

    public static volatile List<String> disk_upload_file_notice_abnormal_disk_list_filter =
            Lion.getList(MccConstant.MCC_CLIENT_ID,
                    "disk.upload.file.notice.abnormal.disk.list.filter",
                    String.class,
                    Lists.newArrayList());

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "upload.task.days.before")
    public static volatile int upload_task_days_before = 14;
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "ra.batch.push.record.list.size")
    public static volatile int ra_batch_push_record_list_size = 100;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "case.workbench.service.host")
    public static volatile String CASE_WORKBENCH_SERVICE_HOST = "https://walledata.mad.test.sankuai.com";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "record.uploading.process.stagnate.duration.hour")
    public static volatile int record_uploading_process_stagnate_duration_hour = 8;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "record.parsing.process.stagnate.duration.hour")
    public static volatile int record_parsing_process_stagnate_duration_hour = 24;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "record.uploading.process.stagnate.msg.group")
    public static volatile long record_uploading_process_stagnate_msg_group = 65210038174L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "record.parsing.process.stagnate.msg.group")
    public static volatile long record_parsing_process_stagnate_msg_group = 65210038174L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "hardbrake.event.time.diff.mills")
    public static volatile long hardbrake_event_time_diff_mills = 10 * 1000L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "storage.manager.base.url")
    public static volatile String STORAGE_MANAGER_BASE_URL = "https://walledata.mad.test.sankuai.com/storage-manager";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "iiirc.openapi.base.url")
    public static volatile String IIIRC_OPENAPI_BASE_URL = "http://walledata.mad.test.sankuai.com/iiirc/openapi";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "rgs.download.url")
    public static volatile String rgs_download_url = "https://simcluster.sankuai.com/rgs/download_record";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "s3.migrate.flow.control.client.id")
    public static volatile String s3_migrate_flow_control_client_id = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "s3.migrate.flow.control.random.threshold")
    public static volatile Integer s3_migrate_flow_control_random_threshold = 0;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "spark.job.execution.time.hours")
    public static volatile int spark_job_execution_time_hours = 5;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "onboard.record.spark.job.map")
    public static volatile String onboard_record_spark_job_map = "{\"08a738a6f71911ea92d100228cd3ab3e\":\"hadoop-wcdp.spark.PKGInfoExtractFromHDFS2Hive\"," +
            "\"08b579a1f71911ea92d100228cd3ab3e\":\"hadoop-wcdp.spark.ExtractInterventionInfoFromHDFS2Hive\"," +
            "\"08df45a5f71911ea92d100228cd3ab3e\":\"hadoop-wcdp.spark.ModeExtractFromHDFS2Hive\"," +
            "\"0909d523f71911ea92d100228cd3ab3e\":\"hadoop-wcdp.spark.SpeedExtractFromHDFS2Hive\"," +
            "\"4b01273bf85711eca0a3e4434bc91270\":\"hadoop-wcdp.spark.LaneExtractFromHDFS2Hive\"," +
            "\"95f6d39020c5401ca40e8f9646b84831\":\"hadoop-wcdp.spark.OnboardHeaderAndOnboardMessageIndexExtract2S3\"}";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "s3.sign.expiration.ms")
    public static volatile Long s3_sign_expiration_ms = 1000L * 60 * 60 * 24;

    /**
     * 对应spark_job_platform表中标识Spark生成Index的job_uuid。<p>
     * 目前默认值为：spark_job_name = 'hadoop-wcdp.spark.OnboardHeaderAndOnboardMessageIndexExtract2S3'的job_uuid。
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "spark.OnboardIndex.jobUuid")
    public static volatile String SPARK_ONBOARD_INDEX_JOB_UUID = "95f6d39020c5401ca40e8f9646b84831";


    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.case.sortable.switch")
    public static volatile boolean intervention_case_sortable_switch = true;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "s3.stateless.delete.parallelism.limit")
    public static volatile int s3_stateless_delete_parallelism_limit = 15;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "s3.stateless.delete.count.limit")
    public static volatile long s3_stateless_delete_count_limit = 5000000L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "s3.stateless.delete.size.limit")
    public static volatile long s3_stateless_delete_size_limit = 500L * 1024 * 1024 * 1024 * 1024;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "inbound.record.snapshot.redis.key.expire.day")
    public static volatile int inbound_record_snapshot_redis_key_expire_day = 30;

    /**
     * 对应spark_job_platform表中标识Spark生成PKG的job_uuid。<p>
     * 目前默认值为：spark_job_name = 'hadoop-wcdp.spark.PKGInfoExtractFromHDFS2Hive'的job_uuid。
     */
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "spark.pkg.job.uuid")
    public static volatile String SPARK_PKG_JOB_UUID = "08a738a6f71911ea92d100228cd3ab3e";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "inbound.record.parse.process.scan.limit")
    public static volatile int inbound_record_parse_process_scan_limit = 10000;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "inbound.record.parse.redis.key.expire.day")
    public static volatile int inbound_record_parse_redis_key_expire_day = 30;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "mviz.datalist.api.rgs.mode.whitelist")
    public static volatile String mviz_datalist_api_rgs_mode_whitelist = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "mviz.datalist.api.rgs.mode.config.for.webviz")
    public static volatile String mviz_datalist_api_rgs_mode_config_for_webviz = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "mviz.datalist.api.rgs.mode.whitelist.fastid")
    public static volatile String mviz_datalist_api_rgs_mode_whitelist_fastid = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "mviz.datalist.api.rgs.mode.whitelist.fastid.zip")
    public static volatile String mviz_datalist_api_rgs_mode_whitelist_fastid_zip = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "mviz.datalist.api.rgs.mode.whitelist.fastdata")
    public static volatile String mviz_datalist_api_rgs_mode_whitelist_fastdata = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "mviz.datalist.api.rgs.mode.whitelist.fastdata.zip")
    public static volatile String mviz_datalist_api_rgs_mode_whitelist_fastdata_zip = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "mviz.datalist.api.not.rgs.special.module.whitelist.clientid")
    public static volatile String mviz_datalist_api_not_rgs_special_module_whitelist_clientid = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "mviz.datalist.api.not.rgs.special.module.whitelist")
    public static volatile String mviz_datalist_api_not_rgs_special_module_whitelist = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "mviz.datalist.api.rgs.set.client.mapper")
    public static volatile String mviz_datalist_api_rgs_set_client_mapper = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "datalist.api.not.record.starttime.fastid")
    public static volatile String datalist_api_not_record_starttime_fastid = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "datalist.api.qps.limit.whitelist")
    public static volatile String datalist_api_qps_limit_whitelist = "default";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "inbound.record.spark.job.map")
    public static volatile String inbound_record_spark_job_map = "{\"08a738a6f71911ea92d100228cd3ab3e\":\"hadoop-wcdp.spark.PKGInfoExtractFromHDFS2Hive\"}";
    // 最大重试次数
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "exponential.backoff.max.retries")
    public static volatile int exponential_backoff_max_retries = 5;
    // 初始等待时间（毫秒）
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "exponential.backoff.initial.delay.mill")
    public static volatile long exponential_backoff_initial_delay_mill = 10000L;
    // 最大等待时间（毫秒）
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "exponential.backoff.max.delay.mill")
    public static volatile long exponential_backoff_max_delay_mill = 180000L;
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "datalist.api.module.merge.belong.to.whitelist")
    public static volatile String datalist_api_module_merge_belong_to_whitelist = "default";
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "datalist.api.module.merge.log.switch")
    public static volatile boolean datalist_api_module_merge_log_switch = false;
    //  S3 Record File 治理，只能删除 crateTime > aboveDate 的文件。
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "s3.governance.record.file.delete.above.date")
    public static volatile String s3_governance_record_file_delete_above_date = "";
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "filter.inbound.record.by.localization.mode.switch")
    public static volatile boolean filter_inbound_record_by_localization_mode_switch= true;

    // 车辆接管相关的回传任务配置
    // {"vinList":["LM999"],"timeSpanList":["13-24","0-7"],"priority":"50","ConcurrentNum":"5",
    // "moduleList":["perception","camera"]}
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.intervention.config")
    public static volatile String vehicle_upload_intervention_config = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.upload.intervention.pnc.vin.concurrent.num")
    public static volatile int vehicle_upload_intervention_pnc_vin_concurrent_num = 5;
    // OnboardNoticePublisher ：车端落盘事件，磁盘回收不解析时间点
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "disk.collect.event.compress.module.start.parse.date")
    public static final String DISK_COLLECT_EVENT_COMPRESS_MODULE_START_PARSE_DATE = "20250301";

    // 车端落盘速度benchmark
    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.onboard.file.speed.benchmark")
    public static volatile String vehicle_onboard_file_speed_benchmark = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "vehicle.onboard.file.n.minutes.ago")
    public static volatile Integer getVehicle_onboard_file_n_minutes_ago = 20;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "parking.event.post.process")
    public static volatile Boolean parking_event_post_process = false;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "get.performance.data.demotion.reason.string")
    public static volatile String get_performance_data_demotion_reason_string = "";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "parking.event.mstat.fast.task.priority")
    public static volatile Integer parking_event_mstat_fast_task_priority = 110;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "new.logic.intervention.to.downstream.vins")
    public static volatile String new_logic_intervention_to_downstream_vins = "VEHICLETEST01,VEHICLETEST02";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.data.insert.temp.table")
    public static volatile Boolean intervention_data_insert_temp_table = true;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.source.event.codes")
    public static volatile String intervention_source_event_codes = "4000,4001,4002,4003,4004,4005";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.mafka.partitioner.class")
    public static volatile String intervention_mafka_partitioner_class =
            "com.meituan.walle.data.center.config.MafkaVinPartitioner";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.split.event.codes")
    public static volatile String intervention_split_event_codes = "4004";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.event.redis.get.key.batch")
    public static volatile int intervention_event_redis_get_key_batch = 100;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.event.expired.seconds")
    public static volatile int intervention_event_expired_seconds = 900;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.events.delete.seconds")
    public static volatile int intervention_event_delete_seconds = 12 * 60 * 60;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.source.batch.redis.prefix")
    public static volatile String intervention_source_batch_redis_prefix = "intervention_batch.c";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.redis.category.version")
    public static volatile String intervention_redis_category_version = "_0";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "source.intervention.event.code")
    public static volatile Integer source_intervention_event_code = 4001;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "free.space.parking.finished.code")
    public static volatile Integer free_space_parking_finisher_code = 4004;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.redis.hmi.flag.prefix")
    public static volatile String intervention_redis_HMI_flag_prefix = "HMI_flag";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.redis.free.space.prefix")
    public static volatile String intervention_redis_free_space_prefix = "free_space";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "hmi.preprocess.vins")
    public static volatile String hmi_preprocess_vins = "VEHICLETEST01,VEHICLETEST02";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "filter.all.routing.request")
    public static volatile Boolean filter_all_routing_request = false;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "filter.routing.request.vins")
    public static volatile String filter_routing_request_vins = "VEHICLETEST01,VEHICLETEST02";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.redis.post.process.flag.prefix")
    public static volatile String intervention_redis_post_process_flag_prefix = "post_process_flag";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.redis.post.process.expire.seconds")
    public static volatile Integer intervention_redis_post_process_expire_seconds = 2 * 60 * 60;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.content.intervention.types")
    public static volatile String intervention_content_intervention_types =
            "ASSISTANCE_DECISION,REMOTE_INTERVENTION,FIELD_INTERVENTION,MANUAL_INTERVENTION";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "hmi.intervention.types")
    public static volatile String hmi_intervention_types = "FIELD_INTERVENTION,MANUAL_INTERVENTION";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "hmi.intervention.type.vins")
    public static volatile String hmi_intervention_type_vins = "VEHICLETEST01,VEHICLETEST02";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "hmi.base.intervention.type.flag")
    public static volatile Boolean hmi_base_intervention_type_flag = false;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "hmi.test.intervention.type.vins")
    public static volatile String hmi_test_intervention_type_vins = "VEHICLETEST01,VEHICLETEST02";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "redis.intervention.after.free.space.prefix")
    public static volatile String redis_intervention_after_free_space_prefix = "after_free_space";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "redis.has.free.space.flag")
    public static volatile String redis_has_free_space_flag = "has_free_space_flag";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "redis.hmi.flag.delete.seconds")
    public static volatile int redis_hmi_flag_delete_seconds = 900;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.stop.event.code")
    public static volatile int intervention_stop_event_code = 4005;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.auto.event.code")
    public static volatile int intervention_auto_event_code = 4006;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "intervention.stop.split.logic.vins")
    public static volatile String intervention_stop_split_logic_vins = "VEHICLETEST02";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "redis.intervention.after.auto.prefix")
    public static volatile String redis_intervention_after_auto_prefix = "after_auto";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "redis.intervention.has.auto.prefix")
    public static volatile String redis_intervention_has_auto_prefix = "has_auto_flag";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "redis.intervention.has.stop.prefix")
    public static volatile String redis_intervention_has_stop_prefix = "has_stop_flag";

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "planning.generate.intervention.notice.group")
    public static volatile long planning_generate_intervention_notice_group = 64014494240L;

    @MtConfig(clientId = MccConstant.MCC_CLIENT_ID, key = "send.planning.generate.to.topic.retry.times")
    public static volatile int send_planning_generate_to_topic_retry_times = 3;
}