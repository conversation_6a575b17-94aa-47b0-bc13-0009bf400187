package com.meituan.walle.data.center.entity.po;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR> @Beijing
 * @date 2020/2/5 下午4:32
 * Description:
 * Modified by
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RecordPO {
    private Long id;
    private String bid;
    private String recordName;
    private String purpose;
    private Date beginTime;
    private Date endTime;
    private String place;
    private String vin;
    private String vehicleName;
    private Integer vehicleType;
    private String packageVersion;
    private String gitBranch;
    private String gitCommit;
    private String hdmapVersion;
    private String hdmapName;
    private Integer status;
    private Long dataSize;
    private Integer fileCount;
    private Timestamp createTime;
    private Timestamp updateTime;
    private Integer fragmented;
    /**
     * 起始时间
     */
    private String start;
    /**
     * 终止时间
     */
    private String end;
    private String startTime;
    private String stopTime;
}
