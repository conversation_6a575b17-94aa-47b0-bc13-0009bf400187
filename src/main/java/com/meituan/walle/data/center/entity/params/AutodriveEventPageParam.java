package com.meituan.walle.data.center.entity.params;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/06/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutodriveEventPageParam {

    private String eventId;
    private List<Integer> eventCodeList;
    private List<String> vinList;
    private String recordName;
    private String datasource;
    private Long startTimestamp;
    private Long endTimestamp;
    private Integer page;
    private Integer size;

    public int getOffset() {
        if (page == null || size == null || (page - 1) * size < 0) {
            return 0;
        }
        return (page - 1) * size;
    }

}
