package com.meituan.walle.data.center.entity.po;

import com.meituan.walle.data.center.entity.pojo.OnCallList;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/19
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class InterventionAssign {
    private List<OnCallList> listInsertOnCallList;
    private List<OnCallList> listUpdateOnCallList;
}