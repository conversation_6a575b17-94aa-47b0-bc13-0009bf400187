package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "vehicle_auto_mile_offline")
@Data
public class VehicleAutoMileOffline {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 表record的record_name外键
     */
    private String recordName;

    /**
     * 车架号（车辆设备id）
     */
    private String vin;


    /**
     * 当前接管的自动驾驶里程数
     */
    private BigDecimal meter;

    /**
     * 0.不可用|1.可用|2.归档|3.已废弃|4.已删除
     */
    private Byte status;

    /**
     * 数据对应的时间，例如：20200204
     */
    private Integer recordDate;

    private Date createTime;

    private Date updateTime;


}