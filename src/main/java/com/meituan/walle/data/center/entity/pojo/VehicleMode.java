package com.meituan.walle.data.center.entity.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "vehicle_mode")
@Data
public class VehicleMode {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    /**
     * 车架号（车辆设备id）
     */
    @Column(name = "vin")
    private String vin;

    @Column(name = "pre_t")
    private Date preT;

    @Column(name = "t")
    private Date t;

    @Column(name = "pre_nanosecond")
    private Long preNanosecond;

    @Column(name = "nanosecond")
    private Long nanosecond;

    @Column(name = "pre_mode")
    private Byte preMode;

    @Column(name = "mode")
    private Byte mode;

    @Column(name = "latitude")
    private Double latitude;

    @Column(name = "longitude")
    private Double longitude;

    @Column(name = "x")
    private Double x;

    @Column(name = "y")
    private Double y;

    @Column(name = "meter")
    private BigDecimal meter;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 表record的record_name外键
     */
    @Column(name = "record_name")
    private String recordName;


}