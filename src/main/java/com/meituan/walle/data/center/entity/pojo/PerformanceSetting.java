package com.meituan.walle.data.center.entity.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Table(name = "performance_setting")
public class PerformanceSetting {
    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "indicator")
    private String indicator;

    @Column(name = "expression")
    private int expression;

    @Column(name = "threshold")
    private BigDecimal threshold;

    @Column(name = "unit")
    private String unit;

    @Column(name = "color")
    private String color;

    @Column(name = "level")
    private int level;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;
}
