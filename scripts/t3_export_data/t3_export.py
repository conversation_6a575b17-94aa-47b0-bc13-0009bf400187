import math
import os
import sys
import glob
import argparse
import logging
import shutil
from datetime import datetime
from math import sqrt
from tempfile import mkdtemp as TemporaryDirectory
try:
    from queue import PriorityQueue
except ImportError:
    from Queue import PriorityQueue
import concurrent.futures

from onboard_msg_reader import multi_files_event_reader, event_reader
import conversion
import cv2


logger = logging.getLogger(__name__)

TOPIC_DICT = {
    'chassis': '/apollo/canbus/chassis',
    'chassis_detail': '/apollo/canbus/chassis_detail',
    'pose': '/apollo/localization/pose',
    'control': '/apollo/control',
}

time_dis = 8*3600*1000


def parse_args():
    p = argparse.ArgumentParser()
    p.add_argument('path_contains_records', type=os.path.abspath)
    p.add_argument('output_path', type=os.path.abspath)
    p.add_argument('-v', '--verbose', action='count', default=0)
    p.add_argument('-p', '--processor_num', default=8)
    p.add_argument('-d', '--day', type=str)
    p.add_argument('-s', '--start', type=str)
    p.add_argument('-e', '--end', type=str)
    return p.parse_args()


def enumerate_rec_dirs(base_path):
    for name in os.listdir(base_path):
        path = os.path.join(base_path, name, 'rec')
        if os.path.isdir(path):
            yield path


def get_vehicle_id(base_path):
    for name in os.listdir(base_path):
        file = os.path.join(base_path, name, 'record_info.pb.txt')
        if os.path.isfile(file):
            with open(file, 'r') as vehicle_info:
                for line in vehicle_info.readlines():
                    val_list = line.strip().split(':')
                    if val_list[0].strip() == 'vin':
                        return val_list[1].strip().strip('"')


def glob_rec(rec_dirs, pattern):
    rec_dirs = sorted(rec_dirs)
    print(rec_dirs)
    for path in rec_dirs:
        for each in sorted(glob.glob(os.path.join(path, pattern))):
            print(each)
            yield each


def glob_rec_multi_pattern(rec_dirs, patterns):
    for path in rec_dirs:
        for pattern in patterns:
            for each in sorted(glob.glob(os.path.join(path, pattern))):
                yield each


def process_pose(onboard_msg, trk_info):
    pose = onboard_msg.localization_pose.pose
    trk_info['rtk_latitude'], trk_info['rtk_longitude'] = conversion.to_latlon(
        pose.position.x, pose.position.y, 50, 'T')
    trk_info['rtk_height'] = pose.position.z
    heading = pose.heading
    # 航向角，我们的数据是正东为0，顺时针正，逆时针为负，航向角以正北方向为0，顺时针递增，0-360，因此需要做如下转换
    heading_o = heading * 180 / math.pi
    if math.pi / 2 < heading < math.pi:
        heading_o = 450 - heading_o
    else:
        heading_o = 90 - heading_o

    trk_info["rtk_heading"] = heading_o
    # 俯仰角
    pitch = pose.euler_angles.x * 180 / math.pi
    # 横滚角
    rolling = pose.euler_angles.y * 180 / math.pi
    trk_info['rtk_pitch'] = pitch
    trk_info['rtk_rolling'] = rolling

    trk_info['rtk_acceleration_x'] = pose.linear_acceleration.x
    trk_info['rtk_acceleration_y'] = pose.linear_acceleration.y
    trk_info['rtk_acceleration_z'] = pose.linear_acceleration.z

    vx = pose.linear_velocity.x
    vy = pose.linear_velocity.y
    vz = pose.linear_velocity.z
    trk_info['rtk_velocity_east'] = vx
    trk_info['rtk_velocity_north'] = vy
    trk_info['rtk_velocity_sky'] = vz
    trk_info['rtk_velocity'] = sqrt(vx * vx + vy * vy + vz * vz)


def process_control(onboard_msg, can_info):
    can_info['control_turn_light'] = onboard_msg.control.signal.turn_signal
    can_info['control_emergency_light'] = onboard_msg.control.signal.emergency_light


def process_chassis_detail(onboard_msg, can_info):
    data = onboard_msg.canbus_chassis_detail
    if data.HasField("basic"):
        can_info['gas_driver_override'] = data.gas.driver_override
        can_info['brake_driver_override'] = data.brake.driver_override
        can_info['eps_driver_override'] = data.eps.driver_override
        if data.gas.driver_override is True and \
                data.brake.driver_override is True and \
                data.eps.driver_override is True:
            can_info['manu_mode'] = True
        else:
            can_info['manu_mode'] = False
    else:
        can_info['manu_mode'] = True


def process_chassis_trk(onboard_msg, trk_info):
    header = onboard_msg.header
    data = onboard_msg.canbus_chassis
    cvs_list = []
    cvs_list.append(str(int(header.timestamp / 10**6) - time_dis))
    time_bj = datetime.fromtimestamp(
        header.timestamp / 10**9).strftime("%Y%m%d_%H%M%S_%f")[:-3]
    cvs_list.append(time_bj)
    if trk_info['rtk_longitude'] == 0.0:
        cvs_list.append('%.07f' % data.chassis_gps.longitude)
    else:
        cvs_list.append('%.07f' % trk_info['rtk_longitude'])
    if trk_info['rtk_latitude'] == 0.0:
        cvs_list.append('%.07f' % data.chassis_gps.latitude)
    else:
        cvs_list.append('%.07f' % trk_info['rtk_latitude'])

    cvs_list.append('%.02f' % trk_info['rtk_velocity'])
    cvs_list.append('%.02f' % trk_info['rtk_velocity_sky'])
    cvs_list.append('%.02f' % trk_info['rtk_velocity_north'])
    cvs_list.append('%.02f' % trk_info['rtk_velocity_east'])

    cvs_list.append('%.01f' % (trk_info['rtk_heading'] % 360.0))
    cvs_list.append('%.01f' % trk_info['rtk_pitch'])
    cvs_list.append('%.01f' % trk_info['rtk_rolling'])

    cvs_list.append('%.01f' % trk_info['rtk_height'])

    cvs_list.append('%.02f' % trk_info['rtk_acceleration_x'])
    cvs_list.append('%.02f' % trk_info['rtk_acceleration_y'])
    cvs_list.append('%.02f' % trk_info['rtk_acceleration_z'])

    return cvs_list


def process_chassis_can(onboard_msg, can_info):
    header = onboard_msg.header
    data = onboard_msg.canbus_chassis
    cvs_list = []
    cvs_list.append(str(int(header.timestamp / 10**6) - time_dis))
    time_bj = datetime.fromtimestamp(
        header.timestamp / 10**9).strftime("%Y%m%d_%H%M%S_%f")[:-3]
    cvs_list.append(time_bj)
    # park
    if data.gear_location == 3 or data.parking_brake is True:
        park_status = 1
    else:
        park_status = 0
    cvs_list.append(str(park_status))
    # back
    gear_location_map_status = {3: 0, 2: 1, 0: 2, 1: 3}
    gear_status = gear_location_map_status[data.gear_location]
    cvs_list.append(str(gear_status))
    # light

    light_status = 0
    if can_info['control_emergency_light'] is True:
        light_status |= 0x01
    if data.signal.turn_signal == 1 or \
            can_info['control_turn_light'] == 1 or \
            data.left_turn_signal is True or \
            data.steering_percentage > 10.0:
        light_status |= 0x02
    if data.signal.turn_signal == 2 or \
            can_info['control_turn_light'] == 2 or \
            data.right_turn_signal is True or \
            data.steering_percentage < -10.0:
        light_status |= 0x04
    if data.signal.high_beam is True or data.high_beam_signal is True:
        light_status |= 0x100
    if data.signal.low_beam is True or data.low_beam_signal is True:
        light_status |= 0x080
    if data.brake_percentage > 14:
        light_status |= 0x40

    cvs_list.append(str(light_status))

    brake_percentage = 0
    if data.brake_percentage >= 15.5:
        brake_percentage = data.brake_percentage
    if data.driving_mode == 1 or data.driving_mode == 3:
        brake_percentage = 0 - brake_percentage
    cvs_list.append(str(int(brake_percentage)))

    steering_percentage = 0
    if data.steering_percentage > 100.0:
        data.steering_percentage = 100.0
    elif data.steering_percentage < -100.0:
        data.steering_percentage = -100.0
    if data.driving_mode == 1 or data.driving_mode == 2:
        steering_percentage = 1000 + (0 - data.steering_percentage) * 4.7
    else:
        steering_percentage = (0 - data.steering_percentage) * 4.7
    cvs_list.append('%.01f' % steering_percentage)

    throttle_percentage = 0
    if data.throttle_percentage >= 18:
        throttle_percentage = data.throttle_percentage
    if data.driving_mode == 1 or data.driving_mode == 3:
        throttle_percentage = 0 - throttle_percentage
    cvs_list.append(str(int(throttle_percentage)))

    # horn
    horn = 0
    if data.signal.horn is True or data.horn is True:
        horn = 1
    cvs_list.append(str(horn))

    driving_mode = 0
    if data.driving_mode == 1 or data.driving_mode == 2 or data.driving_mode == 3:
        if can_info['gas_driver_override'] is True \
           or can_info['brake_driver_override'] is True \
           or can_info['eps_driver_override'] is True:
            driving_mode = 4
        else:
            driving_mode = 1
    elif data.driving_mode == 0 or data.driving_mode == 5:
        # 近场接管和人工驾驶对应人工驾驶
        driving_mode = 0
    elif data.driving_mode == 6:
        # 远程遥控对应远程驾驶
        driving_mode = 2
    else:
        driving_mode = 4
    takeover_type = 0
    if driving_mode == 0 or driving_mode == 2:
        takeover_type = 4
    elif driving_mode == 1 or driving_mode == 4:
        takeover_type = 0
    cvs_list.append(str("0"))  # 雨刷器没有，直接赋值为0，表示没开
    cvs_list.append(str(driving_mode))
    cvs_list.append(str(takeover_type))
    cvs_list.append("0")  # 故障代码我们没有，直接置为0，表示无故障
    auover_mode = "0" if driving_mode == 1 else "6"
    override = "0" if driving_mode == 1 else "1"
    cvs_list.append(auover_mode)
    cvs_list.append(override)
    lock = "0"
    cvs_list.append(lock)
    door = "7"
    cvs_list.append(door)
    safety_belt = "7"
    cvs_list.append(safety_belt)

    return cvs_list


def process_chassis(onboard_msg, trk_info, can_info):
    data = onboard_msg.canbus_chassis
    # if data.brake_percentage != 0.0 and data.throttle_percentage != 0.0 \
    if (trk_info['rtk_longitude'] != 0.0 or data.chassis_gps.longitude != 0.0) \
       and (trk_info['rtk_latitude'] != 0.0 or data.chassis_gps.latitude != 0.0):
        trk_list = process_chassis_trk(onboard_msg, trk_info)
        can_list = process_chassis_can(onboard_msg, can_info)
        return trk_list, can_list
    # else:
    #     print(data.brake_percentage, data.throttle_percentage, trk_info['rtk_longitude'], trk_info['rtk_latitude'])
    return None, None


def process_topic_data(onboard_msg, trk_info, can_info):
    if TOPIC_DICT['pose'] == onboard_msg.header.topic_name:
        process_pose(onboard_msg, trk_info)
    elif TOPIC_DICT['control'] == onboard_msg.header.topic_name:
        process_control(onboard_msg, can_info)
    elif TOPIC_DICT['chassis_detail'] == onboard_msg.header.topic_name:
        process_chassis_detail(onboard_msg, can_info)
    elif TOPIC_DICT['chassis'] == onboard_msg.header.topic_name:
        return process_chassis(onboard_msg, trk_info, can_info)
    return None, None


def generate_csv_files(args, vin, rec_dirs, output_path, start, end):
    play_queue = PriorityQueue()

    canbus_iter = multi_files_event_reader(glob_rec(rec_dirs, 'Canbus.*.rec'))
    control_iter = multi_files_event_reader(
        glob_rec(rec_dirs, 'Control.*.rec'))
    localization_iter = multi_files_event_reader(
        glob_rec(rec_dirs, 'Localization.*.rec'))

    iter_list = (canbus_iter, control_iter, localization_iter)

    start_nano = int(start.timestamp() * 10 ** 9)
    end_nano = int(end.timestamp() * 10 ** 9)
    print(start_nano, end_nano)
    for iter in iter_list:
        for msg in iter:
            if msg.header.timestamp > end_nano:
                break
            if msg.header.timestamp < start_nano:
                continue
            if msg.header.topic_name in TOPIC_DICT.values():
                play_queue.put(
                    (msg.header.timestamp, msg.header.topic_name, msg, iter))
                break

    trk_info = {
        'rtk_longitude': 0.0,
        'rtk_latitude': 0.0
    }
    can_info = {
        'control_turn_light': 0,
        'control_emergency_light': False,
        'manu_mode': False,
        'gas_driver_override': False,
        'brake_driver_override': False,
        'eps_driver_override': False,
    }

    trk_filename = os.path.join(
        output_path, '_'.join(["TRK", vin, args.day]) + '.csv')
    can_filename = os.path.join(
        output_path, '_'.join(["CAN", vin, args.day]) + '.csv')
    trk = open(trk_filename, 'w')
    can = open(can_filename, 'w')
    can.write("time,time_bj,park_status,gear_status,light_status,braking_info,steering_info,accelerator_info,horn_info,wiper,driving_mode,takeover_type,faultcode,auover_mode,override,lock,door,safety_belt\n")
    trk.write(
        "time,time_bj,longitude,latitude,speed,vu,vn,ve,heading,pitch,roll,altitude,accx,accy,accz\n")

    i = 0
    while not play_queue.empty():
        item = play_queue.get()
        onboard_msg = item[2]
        iter = item[3]
        for msg in iter:
            if msg.header.timestamp > end_nano:
                break
            if msg.header.timestamp < start_nano:
                continue
            if msg.header.topic_name in TOPIC_DICT.values():
                play_queue.put(
                    (msg.header.timestamp, msg.header.topic_name, msg, iter))
                break
        try:
            trk_list, can_list = process_topic_data(
                onboard_msg, trk_info, can_info)
            if trk_list:
                trk.write(','.join(trk_list) + '\n')
            if can_list:
                can.write(','.join(can_list) + '\n')
        except Exception as e:
            i = i + 1
            raise e

    can.close()
    trk.close()
    print('write to {} and {}'.format(trk_filename, can_filename))


def generate_standard_video(iter, standard_video_file, start_nano, end_nano):
    print(f"generating video file: {standard_video_file}")
    filename = os.path.basename(standard_video_file)
    origin_video_file = f"/tmp/{filename}"
    origin_stream = open(origin_video_file, "wb")
    for msg in iter:
        if msg.header.timestamp > end_nano:
            break
        if msg.header.timestamp < start_nano:
            continue
        origin_stream.write(msg.hevc_data.raw_data)
    origin_stream.close()

    cap = cv2.VideoCapture(origin_video_file)
    ret, frame = cap.read()
    height, width, dimension = frame.shape
    fourcc = cv2.VideoWriter_fourcc(*"mp4v")
    video_writer = cv2.VideoWriter(
        standard_video_file, fourcc, 10, [width, height])
    while True:
        if not ret:
            break
        video_writer.write(frame)
        ret, frame = cap.read()

    video_writer.release()
    cap.release()
    os.remove(origin_video_file)
    print(f'write to {standard_video_file}')


def generate_video_files(args, vin, rec_dirs, output_path, start, end):
    # 前方影像
    camera_fr = multi_files_event_reader(
        glob_rec(rec_dirs, 'CameraFpgaP0H120.*.rec'))
    # 左方影像
    # camera_lt = multi_files_event_reader(
    #     glob_rec(rec_dirs, 'CameraFpgaP90H120.*.rec'))
    # 右方影像
    # camera_rt = multi_files_event_reader(
    #     glob_rec(rec_dirs, 'CameraFpgaP270H120.*.rec'))
    # 正后方影像
    # camera_bc = multi_files_event_reader(
    #     glob_rec(rec_dirs, 'CameraFpgaP180H120.*.rec'))

    time_str = start.strftime("%Y%m%d%H%M%S") + "000"
    start_nano = int(start.timestamp() * 10 ** 9)
    end_nano = int(end.timestamp() * 10 ** 9)
    filename = '_'.join(["FR", vin, time_str]) + ".mp4"
    standard_video_file = os.path.join(output_path, filename)
    generate_standard_video(
        camera_fr, standard_video_file, start_nano, end_nano)


def main():
    args = parse_args()
    if args.verbose > 2:
        level = logging.DEBUG
    elif args.verbose > 1:
        level = logging.INFO
    elif args.verbose > 0:
        level = logging.WARN
    else:
        level = logging.ERROR
    logging.basicConfig(level=level)

    rec_dirs = list(enumerate_rec_dirs(args.path_contains_records))
    if not rec_dirs:
        logging.error('Can not find any rec files in given path: {}'.format(
            args.path_contains_records))
        sys.exit(1)

    vin = get_vehicle_id(args.path_contains_records)
    if not vin:
        logging.error('Can not find vin in given path: {}'.format(
            args.path_contains_records))
        sys.exit(1)

    output_path = os.path.join(args.output_path, vin, args.day)
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    elif not os.path.isdir(output_path):
        logging.error("%s exists, but it's not a dir", output_path)
        sys.exit(2)

    start = datetime.strptime(args.start, "%Y%m%d%H%M%S")
    end = datetime.strptime(args.end, "%Y%m%d%H%M%S")
    # start = datetime(2023, 6, 13, 17, 36, 30)
    # end = datetime(2023, 6, 13, 17, 39, 40)
    generate_csv_files(args, vin, rec_dirs, output_path, start, end)
    generate_video_files(args, vin, rec_dirs, output_path, start, end)


if __name__ == '__main__':
    main()
