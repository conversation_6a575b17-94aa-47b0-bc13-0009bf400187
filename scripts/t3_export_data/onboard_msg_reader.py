import struct
import glob
import logging
import argparse
from datetime import datetime

from google.protobuf.message import DecodeError

from common.proto.onboard_message.onboard_message_pb2 import OnboardMessage

logger = logging.getLogger(__name__)


def event_reader(fname):
    with open(fname, 'rb') as fp:
        while 1:
            header = fp.read(8)
            if not header:  # EOF
                break
            try:
                tag, length = struct.unpack('<2i', header)
            except struct.error as exc:
                logger.warn("{}. expect 8, actually got {} bytes. {}:{}".format(
                    str(exc), len(header), fname, fp.tell() - len(header)))
                break

            data = fp.read(length)
            msg = OnboardMessage()
            try:
                msg.ParseFromString(data)
            except DecodeError as exc:
                logger.warn("{}. expect {}, actually got {} bytes. {}:{}".format(
                    str(exc), length, len(data), fname, fp.tell() - len(data)))
                break
            yield msg


def multi_files_event_reader(fnames):
    for each in fnames:
        logger.info("reading rec file: {}".format(each))
        for msg in event_reader(each):
            yield msg


def make_attr_getter(field_name):
    tokens = field_name.split('.')
    def getter(value):
        for token in tokens:
            value = getattr(value, token)
        return value
    return field_name, getter


def make_attr_remover(field_name):
    tokens = field_name.split('.')
    if len(tokens) == 1:
        parent_getter = lambda msg: msg
        attr_name = tokens[0]
    else:
        _, parent_getter = make_attr_getter('.'.join(tokens[:-1]))
        attr_name = tokens[-1]

    def remover(msg):
        par = parent_getter(msg)
        if hasattr(par, attr_name):
            setattr(par, attr_name, b'[[:REMOVED:]]')
    return remover


def make_filter(filter_expr):
    ## only support == here for debug
    field_name, expect_value = filter_expr.split('==')
    field_name = field_name.strip()
    expect_value = expect_value.strip()
    _, getter = make_attr_getter(field_name)
    def filter_(msg):
        got = getter(msg)
        #print('### got', got, expect_value, got == expect_value)
        return got == expect_value
    return filter_


def parse_args():
    p = argparse.ArgumentParser()
    p.add_argument('rec_files', nargs='+')
    p.add_argument('-f', '--filter', nargs='*', type=make_filter)
    p.add_argument('-a', '--attr', nargs='*', type=make_attr_getter)
    p.add_argument('--exclude-attr', nargs='*', type=make_attr_remover)
    p.add_argument('-n', type=int, default=10)
    p.add_argument('--glob', action='store_true')
    return p.parse_args()


def main():
    args = parse_args()
    filenames = args.rec_files
    if args.glob:
        filenames = []
        for pattern in args.rec_files:
            filenames.extend(glob.glob(pattern))
    else:
        filenames = args.rec_files

    found_msg = 0
    st = 0
    prev = None
    cnt = 0
    for i, msg in enumerate(multi_files_event_reader(filenames)):
        if args.filter:
            skip = False
            for filter_func in args.filter:
                skip = not filter_func(msg)
                if skip:
                    break
            if skip:
                continue

        if args.exclude_attr:
            for remover in args.exclude_attr:
                remover(msg)

        print('-'*20, i, '-'*20)
        if args.attr:
            for field_name, getter in args.attr:
                print('{}: {}'.format(field_name, getter(msg)))
        else:
            print(msg)

        found_msg += 1
        if args.n and found_msg >= args.n:
            break
        continue

        if msg.header.topic_name != "/apollo/canbus/chassis":
            continue
        if msg.canbus_chassis.driving_mode == 1:
            st = 'auto'
            prev = msg
        elif st == 'auto' and msg.canbus_chassis.driving_mode == 4:
            st = 'emergency'
            cnt += 1
            t = datetime.fromtimestamp(msg.header.timestamp / 10**9)
            lat, lon = msg.canbus_chassis.chassis_gps.latitude, msg.canbus_chassis.chassis_gps.longitude
            vin = msg.canbus_chassis.license.vin
            # print('BOOM', '-'*40)
            print('[{}]BOOM {} #{} t:{}, loc({}, {})'.format(cnt, vin, i, t, lat, lon))
            print(prev)
            # print('-'*20)
            # print(msg)
            # print('-'*40)


if __name__ == '__main__':
    main()
