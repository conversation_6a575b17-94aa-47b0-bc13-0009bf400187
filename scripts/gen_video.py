import datetime
import sys

import cv2
import os

def gen_video_from_image(base_dir, video_file_name):
    file_list = os.listdir(base_dir)
    if len(file_list) == 0:
        sys.exit(0)

    cap = cv2.VideoCapture(os.path.join(base_dir, file_list[0]))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    file_list.sort(key=lambda x: int(x.split(".")[0]))
    fourcc = cv2.VideoWriter_fourcc(*"XVID")

    video_full_name = os.path.join(base_dir, video_file_name)
    videoWrite = cv2.VideoWriter(video_full_name, fourcc, 10, [width, height])
    i = 0
    for file in file_list:
        full_file = os.path.join(base_dir, file)
        img = cv2.imread(full_file)
        font = cv2.FONT_HERSHEY_SIMPLEX
        time = datetime.datetime.fromtimestamp(float(file.split(".")[0]) / 1000000000) \
            .strftime("%Y-%m-%d %H:%M:%S")
        img = cv2.putText(img, time, (750, 100), font, 1, (0, 255, 255), 2, cv2.LINE_AA)

        videoWrite.write(img)
        i = i + 1
    videoWrite.release()

def gen_video_from_hevc(base_dir, video_file_name):
    file_list = os.listdir(base_dir)
    origin_video = None
    timestamp_file = None
    for file in file_list:
        if not origin_video and ("avi" in file or "mp4" in file or "mkz" in file):
            origin_video = file
        if not timestamp_file and "txt" in file:
            timestamp_file = file
    if not origin_video or not timestamp_file:
        return

    cap = cv2.VideoCapture(origin_video)
    ret, frame = cap.read()
    height, width, dimension = frame.shape
    fourcc = cv2.VideoWriter_fourcc(*"XVID")

    video_full_name = os.path.join(base_dir, video_file_name)
    videoWrite = cv2.VideoWriter(video_full_name, fourcc, 10, [width, height])
    timestamp_stream = open(timestamp_file, "r")
    timestamp = timestamp_stream.readline()
    i = 0
    while True:
        if not ret:
            break
        font = cv2.FONT_HERSHEY_SIMPLEX
        time = datetime.datetime.fromtimestamp(float(timestamp) / 1000000000) \
            .strftime("%Y-%m-%d %H:%M:%S")
        img = cv2.putText(frame, time, (750, 100), font, 1, (0, 255, 255), 2, cv2.LINE_AA)
        videoWrite.write(img)
        i = i + 1
        ret, frame = cap.read()
        timestamp = timestamp_stream.readline()

    videoWrite.release()
    cap.release()

if __name__ == '__main__':
    params = sys.argv[1:]
    video_type = params[0]
    base_dir = params[1]
    video_file_name = params[2]
    if "png" in video_type or "jpg" in video_type or "jpeg" in video_type:
        print("start gen picture video")
        gen_video_from_image(base_dir, video_file_name)
        print("end gen picture video")
    elif "avi" in video_type or "mp4" in video_type or "mkz" in video_type:
        print("start gen hevc video")
        gen_video_from_hevc(base_dir, video_file_name)
        print("end gen hevc video")

