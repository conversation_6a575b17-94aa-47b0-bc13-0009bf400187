#! /bin/bash
# 查看设备
ls -l /dev/disk/by-id/
# 给设备分区
sudo fdisk /dev/"$1" << XXG
n




w
XXG
# 给设备加密
echo "YES" | sudo cryptsetup luksFormat /dev/"$1"1 << XXG
Zy@^qBPLtdI5XGF3!Hg6ycLnPQhCKTx6
Zy@^qBPLtdI5XGF3!Hg6ycLnPQhCKTx6
XXG
# 建立映射
echo 'Zy@^qBPLtdI5XGF3!Hg6ycLnPQhCKTx6' | sudo cryptsetup luksOpen /dev/"$1"1 test
ls -l /dev/mapper/
# 格式化分区
sudo mkfs.ext4 /dev/mapper/test
# 磁盘挂载
sudo mount /dev/mapper/test /test
ls -ls /
# 对walle账户授权
sudo chown walle:walle /test
ls -ls /
df -h
# 卸载
sudo umount /dev/mapper/test
# 关闭映射
sudo cryptsetup luksClose /dev/mapper/test
df -h
ls -l /dev/disk/by-id/